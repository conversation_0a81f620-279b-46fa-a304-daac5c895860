DROP TABLE IF EXISTS `tb_data_source`;
CREATE TABLE `tb_data_source`
(
    `id`              smallint(5) unsigned NOT NULL AUTO_INCREMENT,
    `source_product`  char(30) NOT NULL COMMENT '来源产品',
    `source_scenario` char(30) NOT NULL COMMENT '来源场景',
    `source_channel`  char(30)     DEFAULT NULL COMMENT '来源渠道',
    `source_kind`     tinyint unsigned DEFAULT NULL COMMENT '来源类型',
    `description`     varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `tb_data_source_log`;
CREATE TABLE `tb_data_source_log`
(
    `id`        smallint(5) unsigned NOT NULL AUTO_INCREMENT,
    `name`      char(30) COMMENT '来源名',
    `parent_id` smallint(5) unsigned COMMENT '父级id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


