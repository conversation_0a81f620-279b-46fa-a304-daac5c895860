# bc_menu_info
INSERT INTO `bc_menu_info` (`id`, `name`, `level`, `parent_id`, `url`, `icon`, `sort`, `status`, `menu_code`, `locale`)VALUES (33, '来源归类配置', 1, 1, NULL, NULL, 10, 0, 'dataSourceConfig', NULL);

# bc_permission_info
INSERT INTO `bc_permission_info` (`name`, `url`, `menu_id`, `permission_code`, `status`, `description`, `sort`)VALUES ('查看', NULL, '33', 'dataSourceConfig-getPage', 1, '来源归类配置-查看', 1);
INSERT INTO `bc_permission_info` (`name`, `url`, `menu_id`, `permission_code`, `status`, `description`, `sort`)VALUES ('编辑', NULL, '33', 'dataSourceConfig-update', 1, '来源归类配置-编辑', 2);
INSERT INTO `bc_permission_info` (`name`, `url`, `menu_id`, `permission_code`, `status`, `description`, `sort`)VALUES ('新建', NULL, '33', 'dataSourceConfig-save', 1, '来源归类配置-新建', 3);
INSERT INTO `bc_permission_info` (`name`, `url`, `menu_id`, `permission_code`, `status`, `description`, `sort`)VALUES ('删除', NULL, '33', 'dataSourceConfig-delete', 1, '来源归类配置-删除', 4);

# tb_data_source_log
INSERT INTO `tb_data_source_log` (`id`, `name`,  `parent_id`)VALUES (1, '磐石', NULL);
INSERT INTO `tb_data_source_log` (`id`, `name`,  `parent_id`)VALUES (2, '车生态', NULL);
