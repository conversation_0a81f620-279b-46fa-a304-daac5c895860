INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('ppb', 'saas', 'saas', '1', '澎湃保报价');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('ppb', 'saas', '公众号', '1', '澎湃保报价');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('cby', 'channel', 'tutubaoxian', '1', '车保易渠道途途保险');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('cby', 'main_app', '公众号', '1', '车保易主应用');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('cby', 'main_app', 'iOS', '1', '车保易主应用');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('cby', 'main_app', 'Android', '1', '车保易主应用');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('cby', 'main_app', 'h5', '1', '车保易主应用');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('bedrock', 'impersonate', '', '1', '磐石代客录单');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('bedrock', 'renewal_quote', '', '3', '磐石续保自动报价');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('bedrock', 'import_quote', '', '4', '磐石导入报价');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'lx', '1', '理想汽车');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'hw', '1', '华为极狐');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'cctest', '6', '测试车商');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'avatr', '1', '阿维塔');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'enovate', '1', '天际汽车');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'cheche', '1', '车车自有');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'xp', '1', '小鹏');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'ejoy', '1', '易捷');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'niutron', '1', '自游家');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'SQZL', '1', '首汽');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'cpsdna', '1', '迪纳车联网');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'jidu', '1', '集度');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'wjgjib', '1', '文津经纪');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'aipark', '1', '雄安泊车');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'pazl', '1', '平安租赁');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone', 'jqcb', '1', '集群车宝');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'lx', '1', '理想汽车APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'hw', '1', '华为极狐APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'cctest', '6', '测试车商APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'avatr', '1', '阿维塔APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'enovate', '1', '天际汽车APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'cheche', '1', '车车自有APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'xp', '1', '小鹏APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'ejoy', '1', '易捷APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'niutron', '1', '自游家APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'SQZL', '1', '首汽APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'cpsdna', '1', '迪纳车联网APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'jidu', '1', '集度APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'wjgjib', '1', '文津经纪APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'aipark', '1', '雄安泊车APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'pazl', '1', '平安租赁APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'customer_app', 'jqcb', '1', '集群车宝APP');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'main_app', 'cheche', '1', '车车自有');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('che_eco', 'cornerstone_mobile', 'lx', '1', '理想汽车');
INSERT INTO `tb_data_source` (`source_product`, `source_scenario`, `source_channel`, `source_kind`, `description`)VALUES ('ps', 'ps', '', '1', '未传requestSource字段');


