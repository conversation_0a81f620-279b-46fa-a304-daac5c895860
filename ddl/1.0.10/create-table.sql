DROP TABLE if exists `tb_dama`;
CREATE TABLE `tb_dama` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT '0' COMMENT '保司id',
  `process_type` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '任务类型（robot，edi）',
  `scene` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '使用场景',
  `dama_mode` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '打码平台代码',
  `dama_type` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '打码类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='打码方式表';
