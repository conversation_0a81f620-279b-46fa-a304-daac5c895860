DROP TABLE if exists `auto_task_exception_config`;
CREATE TABLE `auto_task_exception_config`
(
    `id`                   int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id`           varchar(10)       NOT NULL COMMENT '保险公司 code',
    `process_type`         varchar(10)       NOT NULL COMMENT '处理类型：robot；edi',
    `task_type`            varchar(40)      NOT NULL COMMENT '任务类型',
    `exception_keywords`   text             NOT NULL COMMENT '异常信息关键字',
    `exception_conversion` varchar(255)              DEFAULT '' COMMENT '异常信息转换',
    `use_regex`            tinyint(2)       NOT NULL DEFAULT '0' COMMENT '是否使用正则表达式: 0: 否;1: 是; 默认为否',
    `operator_id`          int(11)          NOT NULL COMMENT '操作人姓名',
    `create_time`          datetime                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime                  DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='百川任务异常转换配置表';
