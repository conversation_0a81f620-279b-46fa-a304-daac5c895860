DROP TABLE if exists `auto_task_daily_statistics`;
CREATE TABLE `auto_task_daily_statistics`
(
    `id`                    int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id`            varchar(10)  DEFAULT NULL COMMENT '保险公司 ID',
    `process_type`          varchar(10)  DEFAULT NULL COMMENT '能力类型',
    `task_type`             varchar(40) DEFAULT NULL COMMENT '任务类型',
    `total_count`           int(6)       DEFAULT NULL COMMENT '任务总数',
    `success_count`         int(6)       DEFAULT NULL COMMENT '任务成功数',
    `failed_count`          int(6)       DEFAULT NULL COMMENT '任务失败数',
    `business_issues_count` int(6)       DEFAULT NULL COMMENT '业务异常数',
    `statistics_date`       date         DEFAULT NULL COMMENT '统计日期',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='百川任务每日统计';