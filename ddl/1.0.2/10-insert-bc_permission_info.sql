/* 新增角色权限配置 */
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '3', 'interface-getPage', '1', '接口管理 -查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('编辑', '3', 'interface-update', '1', '接口管理 -编辑', '2');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('新建', '3', 'interface-save', '1', '接口管理 -新建', '3');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('删除', '3', 'interface-delete', '1', '接口管理 -删除', '4');

INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '4', 'template-getPage', '1', '模板管理-查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('编辑', '4', 'template-update', '1', '模板管理-编辑', '2');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('新建', '4', 'template-save', '1', '模板管理-新建', '3');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('删除', '4', 'template-delete', '1', '模板管理-删除', '4');

INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '5', 'autoTaskLog-getPage', '1', '日志管理-查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '8', 'errorCategory-getPage', '1', '异常配置-查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('编辑', '8', 'errorCategory-update', '1', '异常配置-编辑', '2');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('新建', '8', 'errorCategory-save', '1', '异常配置-新建', '3');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('删除', '8', 'errorCategory-delete', '1', '异常配置-删除', '4');

INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '10', 'accountMgn-getPage', '1', 'Actor管理-查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('关闭账号', '10', 'accountMgn-close', '1', 'Actor管理-关闭账号', '2');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('重置次数', '10', 'accountMgn-refresh', '1', 'Actor管理-重置错误次数', '3');

INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('查看', '11', 'workerTime-getPage', '1', '工作时间-查看', '1');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('编辑', '11', 'workerTime-update', '1', '工作时间-编辑', '2');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('新建', '11', 'workerTime-save', '1', '工作时间-新建', '3');
INSERT INTO `bc_permission_info` (`name`, `menu_id`, `permission_code`, `status`, `description`, `sort`) VALUES ('删除', '11', 'workerTime-delete', '1', '工作时间-删除', '4');

