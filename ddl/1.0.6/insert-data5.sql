INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3053', 'jintai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-锦泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-IdCarChekc" //申请验证码    else{        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3053', 'jintai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-锦泰-报价', 'def getTemplateGroup(dataSource) {    return "edi-3053-queryCar,edi-3053-xbQuery,edi-3053-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3053', 'jintai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-锦泰-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3053-queryCar,edi-3053-xbQuery,edi-3053-askCharge,edi-3053-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3053', 'jintai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-锦泰-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3053-queryCar,edi-3053-xbQuery,edi-3053-askCharge,edi-3053-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3053', 'jintai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-锦泰-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3053-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3053', 'jintai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-锦泰-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3053-login,robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3053-login,robot-3053-ObtainConfig,robot-3053-checkInsurePerson,robot-3053-changePerson,robot-3053-checkInsuredPerson,robot-3053-changePerson,robot-3053-prepareEdit," +                    "robot-3053-prepareQueryCode,robot-3053-selectProposalCar,robot-3053-browseProposalCar,robot-3053-browseProposalCarefc,robot-3053-selectRenewalPolicyNo"        }else{            s = "robot-3053-login,robot-3053-ObtainConfig,robot-3053-checkInsurePerson,robot-3053-changePerson,robot-3053-checkInsuredPerson,robot-3053-changePerson,robot-3053-prepareEdit," +                    "robot-3053-prepareQueryCode,robot-3053-browseProposalCar,robot-3053-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3053-VehicleModelList" //上海车型查询        }        s += ",robot-3053-queryPrepare,robot-3053-vehicleQuery,robot-3053-queryTaxAbateForPlat,robot-3053-calActualValue,robot-3053-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3053-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3053-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-nomotor-unitedSaleEdit,robot-3053-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3053-login,robot_3053_bj_initData,robot_3053_bj_queryModel,robot_3053_bj_getSaleTaxInfo,robot_3053_bj_getRealValue,robot_3053_bj_getPersonData,robot_3053_bj_addPersonData,robot_3053_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3053', 'jintai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-锦泰-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3053-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3053-ObtainConfig,robot-3053-selectRenewal,robot-3053-editCengage,robot-3053-editCitemCar,robot-3053-editCinsured,robot-3053-renewalPolicy,robot-3053-renewalPolicyCI,robot-3053-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3053-VehicleModelList" //上海车型查询        }        s += ",robot-3053-vehicleQueryXB,robot-3053-queryTaxAbateForPlat,robot-3053-calActualValue,robot-3053-editCitemKind,robot-3053-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3053-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-refreshPlanByTimes,robot-3053-insert"            }else{                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3053-getMaxCsellFee,robot-3053-getPrpCseller,robot-3053-getPrpCsellerCI,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            s += ",robot-3053-getMaxCsellFee,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"        }    }else{        s +=",robot-3053-ObtainConfig,robot-3053-checkInsurePerson,robot-3053-changePerson,robot-3053-checkInsuredPerson,robot-3053-changePerson,robot-3053-prepareEdit,robot-3053-selectRenewalPolicyNo,robot-3053-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3053-VehicleModelList" //上海车型查询        }        s += ",robot-3053-queryPrepare,robot-3053-vehicleQuery,robot-3053-queryTaxAbateForPlat,robot-3053-calActualValue,robot-3053-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3053-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3053-calAnciInfo,robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            }else{                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-nomotor-unitedSaleEdit,robot-3053-nomotor-saveUnitedSale,robot-3053-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3053-getMaxCsellFee,robot-3053-getPrpCseller,robot-3053-getPrpCsellerCI,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            s += ",robot-3053-getMaxCsellFee,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-nomotor-unitedSaleEdit,robot-3053-nomotor-saveUnitedSale,robot-3053-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3053-login,robot_3053_bj_initData,robot_3053_bj_queryModel,robot_3053_bj_getSaleTaxInfo,robot_3053_bj_getRealValue,robot_3053_bj_getPersonData,robot_3053_bj_addPersonData,robot_3053_bj_askCharge,robot_3053_bj_queryPayForXSFY,robot_3053_bj_getCagentCI,robot_3053_bj_getCagent,robot_3053_bj_queryPayForXSFY_req,robot_3053_bj_queryIlogEngage,robot_3053_bj_insureRefrenshPlan,robot_3053_bj_insure4S,robot-3053-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3053', 'jintai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-锦泰-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ" +            ",robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind,robot-3053-nomotor-query,robot-3053-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3053', 'jintai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-锦泰-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ" +            ",robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind,robot-3053-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3053', 'jintai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-锦泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ" +            ",robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind,robot-3053-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3053', 'jintai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "锦泰财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-锦泰-电销', 'def getTemplateGroup(dataSource){    return "robot-3053-pureESale_Login,robot-3053-pureESale_Welcome,robot-3053-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3053', 'jintai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-锦泰续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3053-login,robot-3053-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3053', 'jintai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-锦泰-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3053-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3053-ObtainConfig,robot-3053-selectRenewal,robot-3053-editCengage,robot-3053-editCitemCar,robot-3053-editCinsured,robot-3053-renewalPolicy,robot-3053-renewalPolicyCI,robot-3053-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3053-VehicleModelList" //上海车型查询        }        s += ",robot-3053-vehicleQueryXB,robot-3053-queryTaxAbateForPlat,robot-3053-calActualValue,robot-3053-editCitemKind,robot-3053-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3053-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-refreshPlanByTimes,robot-3053-insert"            }else{                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3053-calAnciInfo,robot-3053-getMaxCsellFee,robot-3053-getPrpCseller,robot-3053-getPrpCsellerCI,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            s += ",robot-3053-getMaxCsellFee,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"        }    }else{        s += ",robot-3053-ObtainConfig,robot-3053-checkInsurePerson,robot-3053-changePerson,robot-3053-checkInsuredPerson,robot-3053-changePerson,robot-3053-prepareEdit,robot-3053-editCengage,robot-3053-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3053-queryVehiclePMCheck,robot-3053-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3053-VehicleModelList" //上海车型查询        }        s += ",robot-3053-queryPrepare,robot-3053-vehicleQuery,robot-3053-queryTaxAbateForPlat,robot-3053-calActualValue,robot-3053-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3053-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3053-calAnciInfo,robot-3053-queryPayFor,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            }else{                s += ",robot-3053-calAnciInfo,robot-3053-checkAgentType,robot-3053-queryPayForSCMS,robot-3053-getCagent,robot-3053-getCagentCI,robot-3053-refreshPlanByTimes,robot-3053-nomotor-unitedSaleEdit,robot-3053-nomotor-saveUnitedSale,robot-3053-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3053-calAnciInfo,robot-3053-getMaxCsellFee,robot-3053-getPrpCseller,robot-3053-getPrpCsellerCI,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-insert"            s += ",robot-3053-getMaxCsellFee,robot-3053-queryPayForSCMS,robot-3053-refreshPlanByTimes,robot-3053-nomotor-unitedSaleEdit,robot-3053-nomotor-saveUnitedSale,robot-3053-insert"        }    }    s += ",robot-3053-checkRiskCode,robot-3053-editMainUwtFlag,robot-3053-editSubmitUndwrt,robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-showUndwrtMsgQ,robot-3053-showUndwrtMsgS"+            ",robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ,robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind,robot-3053-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3053-login,robot_3053_bj_initData,robot_3053_bj_queryModel,robot_3053_bj_getSaleTaxInfo,robot_3053_bj_getRealValue,robot_3053_bj_getPersonData,robot_3053_bj_addPersonData,robot_3053_bj_askCharge,robot_3053_bj_queryPayForXSFY,robot_3053_bj_getCagentCI,robot_3053_bj_getCagent,robot_3053_bj_queryPayForXSFY_req,robot_3053_bj_queryIlogEngage,robot_3053_bj_insureRefrenshPlan,robot_3053_bj_insure4S,robot-3053-uploadImage,robot_3053_bj_autoInsure,robot_3053_bj_showUndwrtMsgQ,robot_3053_bj_showUndwrtMsgS";       s += ",robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ,robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +                ",robot-3053-showCinsuredS,robot-3053-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3053', 'jintai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-锦泰-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3053-qrcode_login,robot-3053-qrcode_printTwoBarCodeServlet,robot-3053-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3053-qrcode_login,robot-3053-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3053-qrcode_login,robot-3053-qrcode_editCheckFlag,robot-3053-qrcode_gotoJfcd,robot-3053-qrcode_prepareEditByJF,robot-3053-qrcode_getBusinessIn" +                ",robot-3053-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3053-qrcode_login,robot-3053-qrcode_editCheckFlag,robot-3053-qrcode_gotoJfcd,robot-3053-qrcode_prepareEditByJF,robot-3053-qrcode_getBusinessIn" +                ",robot-3053-qrcode_checkBeforeCalculate,robot-3053-qrcode_saveByJF,robot-3053-qrcode_getBusinessIn_alipay,robot-3053-qrcode_editFeeInfor,robot-3053-qrcode_editPayFeeByWeChat,robot-3053-qrcode_saveByWeChat,robot-3053-qrcode_save";		} else {					return  "robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-bj-editIDCardCheck,robot-3053-apply-selectIsNetProp,robot-3053-apply-saveCheckCode,robot-3053-qrcode_editCheckFlag,robot-3053-qrcode_gotoJfcd,robot-3053-qrcode_prepareEditByJF,robot-3053-qrcode_getBusinessIn" +",robot-3053-qrcode_checkBeforeCalculate,robot-3053-qrcode_saveByJF,robot-3053-qrcode_getBusinessIn_alipay,robot-3053-qrcode_editFeeInfor,robot-3053-qrcode_editPayFeeByWeChat,robot-3053-qrcode_saveByWeChat,robot-3053-qrcode_save";		}}    else {              return "robot-3053-qrcode_login,robot-3053-qrcode_editCheckFlag,robot-3053-qrcode_gotoJfcd,robot-3053-qrcode_prepareEditByJF,robot-3053-qrcode_getBusinessIn" +                ",robot-3053-qrcode_checkBeforeCalculate,robot-3053-qrcode_saveByJF,robot-3053-qrcode_getBusinessIn_alipay,robot-3053-qrcode_editFeeInfor,robot-3053-qrcode_editPayFeeByWeChat,robot-3053-qrcode_saveByWeChat,robot-3053-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3053', 'jintai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-锦泰-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3053-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3053-qrcode_query_editCheckFlag,robot-3053-qrcode_query_gotoJfcd,robot-3053-qrcode_query_prepareEditByJF" +                ",robot-3053-qrcode_query_editMainInfor,robot-3053-qrcode_query_getBusinessIn,robot-3053-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ" +            ",robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind,robot-3053-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3053', 'jintai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-锦泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-IdCarChekc" //申请验证码    else{        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3053', 'jintai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-锦泰-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectPolicyefc,robot-3053-selectPolicybiz,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ" +            ",robot-3053-showCitemCarQ,robot-3053-showCinsuredQ,robot-3053-showCitemKindCI,robot-3053-browseProposalS,robot-3053-showCitemCarS" +            ",robot-3053-showCinsuredS,robot-3053-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3053', 'jintai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3053-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectProposalQ,robot-3053-selectProposalS,robot-3053-browseProposalQ,robot-3053-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3053', 'jintai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-锦泰-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-bj-editIDCardCheck,robot-3053-apply-bj-IdCarChekc";    } else {        s = "robot-3053-qrcode_login,robot-3053-qrcode_editCheckFlag,robot-3053-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3053', 'jintai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi锦泰报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3053_ask_charge,edi_3053_noMotor_quote"	} else {		return "edi_3053_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3053', 'jintai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-锦泰-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3053_ask_charge,edi_3053_noMotor_quote,edi_3053_askInsure,edi_3053_uploadImg,edi_3053_submitInsure,edi_3053_noMotor_submit" 	  	} else {		return "edi_3053_ask_chargeold,edi_3053_askInsure,edi_3053_uploadImg,edi_3053_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3053', 'jintai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-锦泰-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3053_ask_charge,edi_3053_noMotor_quote,edi_3053_askInsure,edi_3053_uploadImg" 	} else {		return "edi_3053_ask_chargeold,edi_3053_askInsure,edi_3053_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3053', 'jintai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-锦泰-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3053_efc_policyinfo,edi_3053_biz_policyinfo,edi_3053_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3053', 'jintai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-锦泰-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3053_efc_insurequery,edi_3053_biz_insurequery,edi_3053_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3053', 'jintai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京锦泰短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-bj-editIDCardCheck,robot-3053-apply-saveCheckCode,robot-3053-apply-selectIsNetProp";    } else {        s = "robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3053', 'jintai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-锦泰-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3053-bj-qrcode_login,robot-3053-apply-bj-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-bj-editIDCardCheck,robot-3053-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3053', 'jintai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-锦泰-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3053_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3053', 'jintai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi锦泰北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3053_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3053', 'jintai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi锦泰北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3053_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3053', 'jintai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-锦泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3053-login,robot-3053-prepareQueryCode,robot-3053-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2056', 'zhongan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-众安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-IdCarChekc" //申请验证码    else{        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2056', 'zhongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-众安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2056-queryCar,edi-2056-xbQuery,edi-2056-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2056', 'zhongan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-众安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2056-queryCar,edi-2056-xbQuery,edi-2056-askCharge,edi-2056-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2056', 'zhongan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-众安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2056-queryCar,edi-2056-xbQuery,edi-2056-askCharge,edi-2056-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2056', 'zhongan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-众安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2056-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2056', 'zhongan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-众安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2056-login,robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2056-login,robot-2056-ObtainConfig,robot-2056-checkInsurePerson,robot-2056-changePerson,robot-2056-checkInsuredPerson,robot-2056-changePerson,robot-2056-prepareEdit," +                    "robot-2056-prepareQueryCode,robot-2056-selectProposalCar,robot-2056-browseProposalCar,robot-2056-browseProposalCarefc,robot-2056-selectRenewalPolicyNo"        }else{            s = "robot-2056-login,robot-2056-ObtainConfig,robot-2056-checkInsurePerson,robot-2056-changePerson,robot-2056-checkInsuredPerson,robot-2056-changePerson,robot-2056-prepareEdit," +                    "robot-2056-prepareQueryCode,robot-2056-browseProposalCar,robot-2056-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2056-VehicleModelList" //上海车型查询        }        s += ",robot-2056-queryPrepare,robot-2056-vehicleQuery,robot-2056-queryTaxAbateForPlat,robot-2056-calActualValue,robot-2056-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2056-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2056-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-nomotor-unitedSaleEdit,robot-2056-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2056-login,robot_2056_bj_initData,robot_2056_bj_queryModel,robot_2056_bj_getSaleTaxInfo,robot_2056_bj_getRealValue,robot_2056_bj_getPersonData,robot_2056_bj_addPersonData,robot_2056_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2056', 'zhongan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-众安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2056-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2056-ObtainConfig,robot-2056-selectRenewal,robot-2056-editCengage,robot-2056-editCitemCar,robot-2056-editCinsured,robot-2056-renewalPolicy,robot-2056-renewalPolicyCI,robot-2056-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2056-VehicleModelList" //上海车型查询        }        s += ",robot-2056-vehicleQueryXB,robot-2056-queryTaxAbateForPlat,robot-2056-calActualValue,robot-2056-editCitemKind,robot-2056-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2056-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-refreshPlanByTimes,robot-2056-insert"            }else{                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2056-getMaxCsellFee,robot-2056-getPrpCseller,robot-2056-getPrpCsellerCI,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            s += ",robot-2056-getMaxCsellFee,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"        }    }else{        s +=",robot-2056-ObtainConfig,robot-2056-checkInsurePerson,robot-2056-changePerson,robot-2056-checkInsuredPerson,robot-2056-changePerson,robot-2056-prepareEdit,robot-2056-selectRenewalPolicyNo,robot-2056-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2056-VehicleModelList" //上海车型查询        }        s += ",robot-2056-queryPrepare,robot-2056-vehicleQuery,robot-2056-queryTaxAbateForPlat,robot-2056-calActualValue,robot-2056-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2056-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2056-calAnciInfo,robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            }else{                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-nomotor-unitedSaleEdit,robot-2056-nomotor-saveUnitedSale,robot-2056-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2056-getMaxCsellFee,robot-2056-getPrpCseller,robot-2056-getPrpCsellerCI,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            s += ",robot-2056-getMaxCsellFee,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-nomotor-unitedSaleEdit,robot-2056-nomotor-saveUnitedSale,robot-2056-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2056-login,robot_2056_bj_initData,robot_2056_bj_queryModel,robot_2056_bj_getSaleTaxInfo,robot_2056_bj_getRealValue,robot_2056_bj_getPersonData,robot_2056_bj_addPersonData,robot_2056_bj_askCharge,robot_2056_bj_queryPayForXSFY,robot_2056_bj_getCagentCI,robot_2056_bj_getCagent,robot_2056_bj_queryPayForXSFY_req,robot_2056_bj_queryIlogEngage,robot_2056_bj_insureRefrenshPlan,robot_2056_bj_insure4S,robot-2056-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2056', 'zhongan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-众安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ" +            ",robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind,robot-2056-nomotor-query,robot-2056-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2056', 'zhongan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-众安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ" +            ",robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind,robot-2056-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2056', 'zhongan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-众安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ" +            ",robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind,robot-2056-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2056', 'zhongan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "众安财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-众安-电销', 'def getTemplateGroup(dataSource){    return "robot-2056-pureESale_Login,robot-2056-pureESale_Welcome,robot-2056-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2056', 'zhongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2056-login,robot-2056-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2056', 'zhongan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-众安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2056-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2056-ObtainConfig,robot-2056-selectRenewal,robot-2056-editCengage,robot-2056-editCitemCar,robot-2056-editCinsured,robot-2056-renewalPolicy,robot-2056-renewalPolicyCI,robot-2056-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2056-VehicleModelList" //上海车型查询        }        s += ",robot-2056-vehicleQueryXB,robot-2056-queryTaxAbateForPlat,robot-2056-calActualValue,robot-2056-editCitemKind,robot-2056-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2056-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-refreshPlanByTimes,robot-2056-insert"            }else{                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2056-calAnciInfo,robot-2056-getMaxCsellFee,robot-2056-getPrpCseller,robot-2056-getPrpCsellerCI,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            s += ",robot-2056-getMaxCsellFee,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"        }    }else{        s += ",robot-2056-ObtainConfig,robot-2056-checkInsurePerson,robot-2056-changePerson,robot-2056-checkInsuredPerson,robot-2056-changePerson,robot-2056-prepareEdit,robot-2056-editCengage,robot-2056-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2056-queryVehiclePMCheck,robot-2056-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2056-VehicleModelList" //上海车型查询        }        s += ",robot-2056-queryPrepare,robot-2056-vehicleQuery,robot-2056-queryTaxAbateForPlat,robot-2056-calActualValue,robot-2056-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2056-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2056-calAnciInfo,robot-2056-queryPayFor,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            }else{                s += ",robot-2056-calAnciInfo,robot-2056-checkAgentType,robot-2056-queryPayForSCMS,robot-2056-getCagent,robot-2056-getCagentCI,robot-2056-refreshPlanByTimes,robot-2056-nomotor-unitedSaleEdit,robot-2056-nomotor-saveUnitedSale,robot-2056-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2056-calAnciInfo,robot-2056-getMaxCsellFee,robot-2056-getPrpCseller,robot-2056-getPrpCsellerCI,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-insert"            s += ",robot-2056-getMaxCsellFee,robot-2056-queryPayForSCMS,robot-2056-refreshPlanByTimes,robot-2056-nomotor-unitedSaleEdit,robot-2056-nomotor-saveUnitedSale,robot-2056-insert"        }    }    s += ",robot-2056-checkRiskCode,robot-2056-editMainUwtFlag,robot-2056-editSubmitUndwrt,robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-showUndwrtMsgQ,robot-2056-showUndwrtMsgS"+            ",robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ,robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind,robot-2056-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2056-login,robot_2056_bj_initData,robot_2056_bj_queryModel,robot_2056_bj_getSaleTaxInfo,robot_2056_bj_getRealValue,robot_2056_bj_getPersonData,robot_2056_bj_addPersonData,robot_2056_bj_askCharge,robot_2056_bj_queryPayForXSFY,robot_2056_bj_getCagentCI,robot_2056_bj_getCagent,robot_2056_bj_queryPayForXSFY_req,robot_2056_bj_queryIlogEngage,robot_2056_bj_insureRefrenshPlan,robot_2056_bj_insure4S,robot-2056-uploadImage,robot_2056_bj_autoInsure,robot_2056_bj_showUndwrtMsgQ,robot_2056_bj_showUndwrtMsgS";       s += ",robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ,robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +                ",robot-2056-showCinsuredS,robot-2056-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2056', 'zhongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2056-qrcode_login,robot-2056-qrcode_printTwoBarCodeServlet,robot-2056-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2056-qrcode_login,robot-2056-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2056-qrcode_login,robot-2056-qrcode_editCheckFlag,robot-2056-qrcode_gotoJfcd,robot-2056-qrcode_prepareEditByJF,robot-2056-qrcode_getBusinessIn" +                ",robot-2056-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2056-qrcode_login,robot-2056-qrcode_editCheckFlag,robot-2056-qrcode_gotoJfcd,robot-2056-qrcode_prepareEditByJF,robot-2056-qrcode_getBusinessIn" +                ",robot-2056-qrcode_checkBeforeCalculate,robot-2056-qrcode_saveByJF,robot-2056-qrcode_getBusinessIn_alipay,robot-2056-qrcode_editFeeInfor,robot-2056-qrcode_editPayFeeByWeChat,robot-2056-qrcode_saveByWeChat,robot-2056-qrcode_save";		} else {					return  "robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-bj-editIDCardCheck,robot-2056-apply-selectIsNetProp,robot-2056-apply-saveCheckCode,robot-2056-qrcode_editCheckFlag,robot-2056-qrcode_gotoJfcd,robot-2056-qrcode_prepareEditByJF,robot-2056-qrcode_getBusinessIn" +",robot-2056-qrcode_checkBeforeCalculate,robot-2056-qrcode_saveByJF,robot-2056-qrcode_getBusinessIn_alipay,robot-2056-qrcode_editFeeInfor,robot-2056-qrcode_editPayFeeByWeChat,robot-2056-qrcode_saveByWeChat,robot-2056-qrcode_save";		}}    else {              return "robot-2056-qrcode_login,robot-2056-qrcode_editCheckFlag,robot-2056-qrcode_gotoJfcd,robot-2056-qrcode_prepareEditByJF,robot-2056-qrcode_getBusinessIn" +                ",robot-2056-qrcode_checkBeforeCalculate,robot-2056-qrcode_saveByJF,robot-2056-qrcode_getBusinessIn_alipay,robot-2056-qrcode_editFeeInfor,robot-2056-qrcode_editPayFeeByWeChat,robot-2056-qrcode_saveByWeChat,robot-2056-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2056', 'zhongan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-众安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2056-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2056-qrcode_query_editCheckFlag,robot-2056-qrcode_query_gotoJfcd,robot-2056-qrcode_query_prepareEditByJF" +                ",robot-2056-qrcode_query_editMainInfor,robot-2056-qrcode_query_getBusinessIn,robot-2056-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ" +            ",robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind,robot-2056-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2056', 'zhongan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-众安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-IdCarChekc" //申请验证码    else{        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2056', 'zhongan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-众安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectPolicyefc,robot-2056-selectPolicybiz,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ" +            ",robot-2056-showCitemCarQ,robot-2056-showCinsuredQ,robot-2056-showCitemKindCI,robot-2056-browseProposalS,robot-2056-showCitemCarS" +            ",robot-2056-showCinsuredS,robot-2056-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2056', 'zhongan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2056-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectProposalQ,robot-2056-selectProposalS,robot-2056-browseProposalQ,robot-2056-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2056', 'zhongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-bj-editIDCardCheck,robot-2056-apply-bj-IdCarChekc";    } else {        s = "robot-2056-qrcode_login,robot-2056-qrcode_editCheckFlag,robot-2056-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2056', 'zhongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi众安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2056_ask_charge,edi_2056_noMotor_quote"	} else {		return "edi_2056_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2056', 'zhongan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-众安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2056_ask_charge,edi_2056_noMotor_quote,edi_2056_askInsure,edi_2056_uploadImg,edi_2056_submitInsure,edi_2056_noMotor_submit" 	  	} else {		return "edi_2056_ask_chargeold,edi_2056_askInsure,edi_2056_uploadImg,edi_2056_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2056', 'zhongan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-众安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2056_ask_charge,edi_2056_noMotor_quote,edi_2056_askInsure,edi_2056_uploadImg" 	} else {		return "edi_2056_ask_chargeold,edi_2056_askInsure,edi_2056_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2056', 'zhongan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-众安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2056_efc_policyinfo,edi_2056_biz_policyinfo,edi_2056_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2056', 'zhongan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-众安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2056_efc_insurequery,edi_2056_biz_insurequery,edi_2056_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2056', 'zhongan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京众安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-bj-editIDCardCheck,robot-2056-apply-saveCheckCode,robot-2056-apply-selectIsNetProp";    } else {        s = "robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2056', 'zhongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2056-bj-qrcode_login,robot-2056-apply-bj-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-bj-editIDCardCheck,robot-2056-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2056', 'zhongan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-众安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2056_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2056', 'zhongan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi众安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2056_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2056', 'zhongan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi众安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2056_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2056', 'zhongan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-众安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2056-login,robot-2056-prepareQueryCode,robot-2056-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2086', 'changan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-长安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-IdCarChekc" //申请验证码    else{        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2086', 'changan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-长安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2086-queryCar,edi-2086-xbQuery,edi-2086-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2086', 'changan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-长安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2086-queryCar,edi-2086-xbQuery,edi-2086-askCharge,edi-2086-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2086', 'changan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-长安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2086-queryCar,edi-2086-xbQuery,edi-2086-askCharge,edi-2086-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2086', 'changan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-长安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2086-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2086', 'changan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-长安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2086-login,robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2086-login,robot-2086-ObtainConfig,robot-2086-checkInsurePerson,robot-2086-changePerson,robot-2086-checkInsuredPerson,robot-2086-changePerson,robot-2086-prepareEdit," +                    "robot-2086-prepareQueryCode,robot-2086-selectProposalCar,robot-2086-browseProposalCar,robot-2086-browseProposalCarefc,robot-2086-selectRenewalPolicyNo"        }else{            s = "robot-2086-login,robot-2086-ObtainConfig,robot-2086-checkInsurePerson,robot-2086-changePerson,robot-2086-checkInsuredPerson,robot-2086-changePerson,robot-2086-prepareEdit," +                    "robot-2086-prepareQueryCode,robot-2086-browseProposalCar,robot-2086-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2086-VehicleModelList" //上海车型查询        }        s += ",robot-2086-queryPrepare,robot-2086-vehicleQuery,robot-2086-queryTaxAbateForPlat,robot-2086-calActualValue,robot-2086-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2086-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2086-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-nomotor-unitedSaleEdit,robot-2086-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2086-login,robot_2086_bj_initData,robot_2086_bj_queryModel,robot_2086_bj_getSaleTaxInfo,robot_2086_bj_getRealValue,robot_2086_bj_getPersonData,robot_2086_bj_addPersonData,robot_2086_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2086', 'changan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-长安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2086-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2086-ObtainConfig,robot-2086-selectRenewal,robot-2086-editCengage,robot-2086-editCitemCar,robot-2086-editCinsured,robot-2086-renewalPolicy,robot-2086-renewalPolicyCI,robot-2086-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2086-VehicleModelList" //上海车型查询        }        s += ",robot-2086-vehicleQueryXB,robot-2086-queryTaxAbateForPlat,robot-2086-calActualValue,robot-2086-editCitemKind,robot-2086-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2086-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-refreshPlanByTimes,robot-2086-insert"            }else{                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2086-getMaxCsellFee,robot-2086-getPrpCseller,robot-2086-getPrpCsellerCI,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            s += ",robot-2086-getMaxCsellFee,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"        }    }else{        s +=",robot-2086-ObtainConfig,robot-2086-checkInsurePerson,robot-2086-changePerson,robot-2086-checkInsuredPerson,robot-2086-changePerson,robot-2086-prepareEdit,robot-2086-selectRenewalPolicyNo,robot-2086-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2086-VehicleModelList" //上海车型查询        }        s += ",robot-2086-queryPrepare,robot-2086-vehicleQuery,robot-2086-queryTaxAbateForPlat,robot-2086-calActualValue,robot-2086-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2086-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2086-calAnciInfo,robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            }else{                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-nomotor-unitedSaleEdit,robot-2086-nomotor-saveUnitedSale,robot-2086-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2086-getMaxCsellFee,robot-2086-getPrpCseller,robot-2086-getPrpCsellerCI,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            s += ",robot-2086-getMaxCsellFee,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-nomotor-unitedSaleEdit,robot-2086-nomotor-saveUnitedSale,robot-2086-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2086-login,robot_2086_bj_initData,robot_2086_bj_queryModel,robot_2086_bj_getSaleTaxInfo,robot_2086_bj_getRealValue,robot_2086_bj_getPersonData,robot_2086_bj_addPersonData,robot_2086_bj_askCharge,robot_2086_bj_queryPayForXSFY,robot_2086_bj_getCagentCI,robot_2086_bj_getCagent,robot_2086_bj_queryPayForXSFY_req,robot_2086_bj_queryIlogEngage,robot_2086_bj_insureRefrenshPlan,robot_2086_bj_insure4S,robot-2086-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2086', 'changan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-长安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ" +            ",robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind,robot-2086-nomotor-query,robot-2086-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2086', 'changan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-长安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ" +            ",robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind,robot-2086-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2086', 'changan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-长安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ" +            ",robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind,robot-2086-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2086', 'changan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "长安责任江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-长安-电销', 'def getTemplateGroup(dataSource){    return "robot-2086-pureESale_Login,robot-2086-pureESale_Welcome,robot-2086-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2086', 'changan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2086-login,robot-2086-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2086', 'changan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-长安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2086-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2086-ObtainConfig,robot-2086-selectRenewal,robot-2086-editCengage,robot-2086-editCitemCar,robot-2086-editCinsured,robot-2086-renewalPolicy,robot-2086-renewalPolicyCI,robot-2086-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2086-VehicleModelList" //上海车型查询        }        s += ",robot-2086-vehicleQueryXB,robot-2086-queryTaxAbateForPlat,robot-2086-calActualValue,robot-2086-editCitemKind,robot-2086-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2086-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-refreshPlanByTimes,robot-2086-insert"            }else{                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2086-calAnciInfo,robot-2086-getMaxCsellFee,robot-2086-getPrpCseller,robot-2086-getPrpCsellerCI,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            s += ",robot-2086-getMaxCsellFee,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"        }    }else{        s += ",robot-2086-ObtainConfig,robot-2086-checkInsurePerson,robot-2086-changePerson,robot-2086-checkInsuredPerson,robot-2086-changePerson,robot-2086-prepareEdit,robot-2086-editCengage,robot-2086-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2086-queryVehiclePMCheck,robot-2086-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2086-VehicleModelList" //上海车型查询        }        s += ",robot-2086-queryPrepare,robot-2086-vehicleQuery,robot-2086-queryTaxAbateForPlat,robot-2086-calActualValue,robot-2086-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2086-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2086-calAnciInfo,robot-2086-queryPayFor,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            }else{                s += ",robot-2086-calAnciInfo,robot-2086-checkAgentType,robot-2086-queryPayForSCMS,robot-2086-getCagent,robot-2086-getCagentCI,robot-2086-refreshPlanByTimes,robot-2086-nomotor-unitedSaleEdit,robot-2086-nomotor-saveUnitedSale,robot-2086-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2086-calAnciInfo,robot-2086-getMaxCsellFee,robot-2086-getPrpCseller,robot-2086-getPrpCsellerCI,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-insert"            s += ",robot-2086-getMaxCsellFee,robot-2086-queryPayForSCMS,robot-2086-refreshPlanByTimes,robot-2086-nomotor-unitedSaleEdit,robot-2086-nomotor-saveUnitedSale,robot-2086-insert"        }    }    s += ",robot-2086-checkRiskCode,robot-2086-editMainUwtFlag,robot-2086-editSubmitUndwrt,robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-showUndwrtMsgQ,robot-2086-showUndwrtMsgS"+            ",robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ,robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind,robot-2086-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2086-login,robot_2086_bj_initData,robot_2086_bj_queryModel,robot_2086_bj_getSaleTaxInfo,robot_2086_bj_getRealValue,robot_2086_bj_getPersonData,robot_2086_bj_addPersonData,robot_2086_bj_askCharge,robot_2086_bj_queryPayForXSFY,robot_2086_bj_getCagentCI,robot_2086_bj_getCagent,robot_2086_bj_queryPayForXSFY_req,robot_2086_bj_queryIlogEngage,robot_2086_bj_insureRefrenshPlan,robot_2086_bj_insure4S,robot-2086-uploadImage,robot_2086_bj_autoInsure,robot_2086_bj_showUndwrtMsgQ,robot_2086_bj_showUndwrtMsgS";       s += ",robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ,robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +                ",robot-2086-showCinsuredS,robot-2086-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2086', 'changan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2086-qrcode_login,robot-2086-qrcode_printTwoBarCodeServlet,robot-2086-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2086-qrcode_login,robot-2086-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2086-qrcode_login,robot-2086-qrcode_editCheckFlag,robot-2086-qrcode_gotoJfcd,robot-2086-qrcode_prepareEditByJF,robot-2086-qrcode_getBusinessIn" +                ",robot-2086-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2086-qrcode_login,robot-2086-qrcode_editCheckFlag,robot-2086-qrcode_gotoJfcd,robot-2086-qrcode_prepareEditByJF,robot-2086-qrcode_getBusinessIn" +                ",robot-2086-qrcode_checkBeforeCalculate,robot-2086-qrcode_saveByJF,robot-2086-qrcode_getBusinessIn_alipay,robot-2086-qrcode_editFeeInfor,robot-2086-qrcode_editPayFeeByWeChat,robot-2086-qrcode_saveByWeChat,robot-2086-qrcode_save";		} else {					return  "robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-bj-editIDCardCheck,robot-2086-apply-selectIsNetProp,robot-2086-apply-saveCheckCode,robot-2086-qrcode_editCheckFlag,robot-2086-qrcode_gotoJfcd,robot-2086-qrcode_prepareEditByJF,robot-2086-qrcode_getBusinessIn" +",robot-2086-qrcode_checkBeforeCalculate,robot-2086-qrcode_saveByJF,robot-2086-qrcode_getBusinessIn_alipay,robot-2086-qrcode_editFeeInfor,robot-2086-qrcode_editPayFeeByWeChat,robot-2086-qrcode_saveByWeChat,robot-2086-qrcode_save";		}}    else {              return "robot-2086-qrcode_login,robot-2086-qrcode_editCheckFlag,robot-2086-qrcode_gotoJfcd,robot-2086-qrcode_prepareEditByJF,robot-2086-qrcode_getBusinessIn" +                ",robot-2086-qrcode_checkBeforeCalculate,robot-2086-qrcode_saveByJF,robot-2086-qrcode_getBusinessIn_alipay,robot-2086-qrcode_editFeeInfor,robot-2086-qrcode_editPayFeeByWeChat,robot-2086-qrcode_saveByWeChat,robot-2086-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2086', 'changan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-长安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2086-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2086-qrcode_query_editCheckFlag,robot-2086-qrcode_query_gotoJfcd,robot-2086-qrcode_query_prepareEditByJF" +                ",robot-2086-qrcode_query_editMainInfor,robot-2086-qrcode_query_getBusinessIn,robot-2086-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ" +            ",robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind,robot-2086-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2086', 'changan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-长安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-IdCarChekc" //申请验证码    else{        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2086', 'changan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-长安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectPolicyefc,robot-2086-selectPolicybiz,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ" +            ",robot-2086-showCitemCarQ,robot-2086-showCinsuredQ,robot-2086-showCitemKindCI,robot-2086-browseProposalS,robot-2086-showCitemCarS" +            ",robot-2086-showCinsuredS,robot-2086-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2086', 'changan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2086-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectProposalQ,robot-2086-selectProposalS,robot-2086-browseProposalQ,robot-2086-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2086', 'changan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-bj-editIDCardCheck,robot-2086-apply-bj-IdCarChekc";    } else {        s = "robot-2086-qrcode_login,robot-2086-qrcode_editCheckFlag,robot-2086-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2086', 'changan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi长安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2086_ask_charge,edi_2086_noMotor_quote"	} else {		return "edi_2086_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2086', 'changan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-长安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2086_ask_charge,edi_2086_noMotor_quote,edi_2086_askInsure,edi_2086_uploadImg,edi_2086_submitInsure,edi_2086_noMotor_submit" 	  	} else {		return "edi_2086_ask_chargeold,edi_2086_askInsure,edi_2086_uploadImg,edi_2086_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2086', 'changan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-长安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2086_ask_charge,edi_2086_noMotor_quote,edi_2086_askInsure,edi_2086_uploadImg" 	} else {		return "edi_2086_ask_chargeold,edi_2086_askInsure,edi_2086_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2086', 'changan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-长安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2086_efc_policyinfo,edi_2086_biz_policyinfo,edi_2086_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2086', 'changan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-长安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2086_efc_insurequery,edi_2086_biz_insurequery,edi_2086_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2086', 'changan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京长安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-bj-editIDCardCheck,robot-2086-apply-saveCheckCode,robot-2086-apply-selectIsNetProp";    } else {        s = "robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2086', 'changan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2086-bj-qrcode_login,robot-2086-apply-bj-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-bj-editIDCardCheck,robot-2086-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2086', 'changan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-长安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2086_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2086', 'changan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi长安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2086_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2086', 'changan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi长安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2086_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2086', 'changan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-长安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2086-login,robot-2086-prepareQueryCode,robot-2086-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2058', 'ancheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-IdCarChekc" //申请验证码    else{        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2058', 'ancheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安诚-报价', 'def getTemplateGroup(dataSource) {    return "edi-2058-queryCar,edi-2058-xbQuery,edi-2058-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2058', 'ancheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安诚-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2058-queryCar,edi-2058-xbQuery,edi-2058-askCharge,edi-2058-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2058', 'ancheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安诚-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2058-queryCar,edi-2058-xbQuery,edi-2058-askCharge,edi-2058-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2058', 'ancheng', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安诚-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2058-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2058', 'ancheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安诚-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2058-login,robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2058-login,robot-2058-ObtainConfig,robot-2058-checkInsurePerson,robot-2058-changePerson,robot-2058-checkInsuredPerson,robot-2058-changePerson,robot-2058-prepareEdit," +                    "robot-2058-prepareQueryCode,robot-2058-selectProposalCar,robot-2058-browseProposalCar,robot-2058-browseProposalCarefc,robot-2058-selectRenewalPolicyNo"        }else{            s = "robot-2058-login,robot-2058-ObtainConfig,robot-2058-checkInsurePerson,robot-2058-changePerson,robot-2058-checkInsuredPerson,robot-2058-changePerson,robot-2058-prepareEdit," +                    "robot-2058-prepareQueryCode,robot-2058-browseProposalCar,robot-2058-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2058-VehicleModelList" //上海车型查询        }        s += ",robot-2058-queryPrepare,robot-2058-vehicleQuery,robot-2058-queryTaxAbateForPlat,robot-2058-calActualValue,robot-2058-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2058-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2058-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-nomotor-unitedSaleEdit,robot-2058-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2058-login,robot_2058_bj_initData,robot_2058_bj_queryModel,robot_2058_bj_getSaleTaxInfo,robot_2058_bj_getRealValue,robot_2058_bj_getPersonData,robot_2058_bj_addPersonData,robot_2058_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2058', 'ancheng', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安诚-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2058-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2058-ObtainConfig,robot-2058-selectRenewal,robot-2058-editCengage,robot-2058-editCitemCar,robot-2058-editCinsured,robot-2058-renewalPolicy,robot-2058-renewalPolicyCI,robot-2058-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2058-VehicleModelList" //上海车型查询        }        s += ",robot-2058-vehicleQueryXB,robot-2058-queryTaxAbateForPlat,robot-2058-calActualValue,robot-2058-editCitemKind,robot-2058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2058-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-refreshPlanByTimes,robot-2058-insert"            }else{                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2058-getMaxCsellFee,robot-2058-getPrpCseller,robot-2058-getPrpCsellerCI,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            s += ",robot-2058-getMaxCsellFee,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"        }    }else{        s +=",robot-2058-ObtainConfig,robot-2058-checkInsurePerson,robot-2058-changePerson,robot-2058-checkInsuredPerson,robot-2058-changePerson,robot-2058-prepareEdit,robot-2058-selectRenewalPolicyNo,robot-2058-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2058-VehicleModelList" //上海车型查询        }        s += ",robot-2058-queryPrepare,robot-2058-vehicleQuery,robot-2058-queryTaxAbateForPlat,robot-2058-calActualValue,robot-2058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2058-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2058-calAnciInfo,robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            }else{                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-nomotor-unitedSaleEdit,robot-2058-nomotor-saveUnitedSale,robot-2058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2058-getMaxCsellFee,robot-2058-getPrpCseller,robot-2058-getPrpCsellerCI,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            s += ",robot-2058-getMaxCsellFee,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-nomotor-unitedSaleEdit,robot-2058-nomotor-saveUnitedSale,robot-2058-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2058-login,robot_2058_bj_initData,robot_2058_bj_queryModel,robot_2058_bj_getSaleTaxInfo,robot_2058_bj_getRealValue,robot_2058_bj_getPersonData,robot_2058_bj_addPersonData,robot_2058_bj_askCharge,robot_2058_bj_queryPayForXSFY,robot_2058_bj_getCagentCI,robot_2058_bj_getCagent,robot_2058_bj_queryPayForXSFY_req,robot_2058_bj_queryIlogEngage,robot_2058_bj_insureRefrenshPlan,robot_2058_bj_insure4S,robot-2058-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2058', 'ancheng', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安诚-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ" +            ",robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind,robot-2058-nomotor-query,robot-2058-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2058', 'ancheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安诚-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ" +            ",robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind,robot-2058-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2058', 'ancheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ" +            ",robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind,robot-2058-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2058', 'ancheng', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安诚财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安诚-电销', 'def getTemplateGroup(dataSource){    return "robot-2058-pureESale_Login,robot-2058-pureESale_Welcome,robot-2058-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2058', 'ancheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安诚续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2058-login,robot-2058-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2058', 'ancheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安诚-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2058-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2058-ObtainConfig,robot-2058-selectRenewal,robot-2058-editCengage,robot-2058-editCitemCar,robot-2058-editCinsured,robot-2058-renewalPolicy,robot-2058-renewalPolicyCI,robot-2058-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2058-VehicleModelList" //上海车型查询        }        s += ",robot-2058-vehicleQueryXB,robot-2058-queryTaxAbateForPlat,robot-2058-calActualValue,robot-2058-editCitemKind,robot-2058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2058-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-refreshPlanByTimes,robot-2058-insert"            }else{                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2058-calAnciInfo,robot-2058-getMaxCsellFee,robot-2058-getPrpCseller,robot-2058-getPrpCsellerCI,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            s += ",robot-2058-getMaxCsellFee,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"        }    }else{        s += ",robot-2058-ObtainConfig,robot-2058-checkInsurePerson,robot-2058-changePerson,robot-2058-checkInsuredPerson,robot-2058-changePerson,robot-2058-prepareEdit,robot-2058-editCengage,robot-2058-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2058-queryVehiclePMCheck,robot-2058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2058-VehicleModelList" //上海车型查询        }        s += ",robot-2058-queryPrepare,robot-2058-vehicleQuery,robot-2058-queryTaxAbateForPlat,robot-2058-calActualValue,robot-2058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2058-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2058-calAnciInfo,robot-2058-queryPayFor,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            }else{                s += ",robot-2058-calAnciInfo,robot-2058-checkAgentType,robot-2058-queryPayForSCMS,robot-2058-getCagent,robot-2058-getCagentCI,robot-2058-refreshPlanByTimes,robot-2058-nomotor-unitedSaleEdit,robot-2058-nomotor-saveUnitedSale,robot-2058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2058-calAnciInfo,robot-2058-getMaxCsellFee,robot-2058-getPrpCseller,robot-2058-getPrpCsellerCI,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-insert"            s += ",robot-2058-getMaxCsellFee,robot-2058-queryPayForSCMS,robot-2058-refreshPlanByTimes,robot-2058-nomotor-unitedSaleEdit,robot-2058-nomotor-saveUnitedSale,robot-2058-insert"        }    }    s += ",robot-2058-checkRiskCode,robot-2058-editMainUwtFlag,robot-2058-editSubmitUndwrt,robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-showUndwrtMsgQ,robot-2058-showUndwrtMsgS"+            ",robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ,robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind,robot-2058-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2058-login,robot_2058_bj_initData,robot_2058_bj_queryModel,robot_2058_bj_getSaleTaxInfo,robot_2058_bj_getRealValue,robot_2058_bj_getPersonData,robot_2058_bj_addPersonData,robot_2058_bj_askCharge,robot_2058_bj_queryPayForXSFY,robot_2058_bj_getCagentCI,robot_2058_bj_getCagent,robot_2058_bj_queryPayForXSFY_req,robot_2058_bj_queryIlogEngage,robot_2058_bj_insureRefrenshPlan,robot_2058_bj_insure4S,robot-2058-uploadImage,robot_2058_bj_autoInsure,robot_2058_bj_showUndwrtMsgQ,robot_2058_bj_showUndwrtMsgS";       s += ",robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ,robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +                ",robot-2058-showCinsuredS,robot-2058-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2058', 'ancheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安诚-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2058-qrcode_login,robot-2058-qrcode_printTwoBarCodeServlet,robot-2058-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2058-qrcode_login,robot-2058-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2058-qrcode_login,robot-2058-qrcode_editCheckFlag,robot-2058-qrcode_gotoJfcd,robot-2058-qrcode_prepareEditByJF,robot-2058-qrcode_getBusinessIn" +                ",robot-2058-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2058-qrcode_login,robot-2058-qrcode_editCheckFlag,robot-2058-qrcode_gotoJfcd,robot-2058-qrcode_prepareEditByJF,robot-2058-qrcode_getBusinessIn" +                ",robot-2058-qrcode_checkBeforeCalculate,robot-2058-qrcode_saveByJF,robot-2058-qrcode_getBusinessIn_alipay,robot-2058-qrcode_editFeeInfor,robot-2058-qrcode_editPayFeeByWeChat,robot-2058-qrcode_saveByWeChat,robot-2058-qrcode_save";		} else {					return  "robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-bj-editIDCardCheck,robot-2058-apply-selectIsNetProp,robot-2058-apply-saveCheckCode,robot-2058-qrcode_editCheckFlag,robot-2058-qrcode_gotoJfcd,robot-2058-qrcode_prepareEditByJF,robot-2058-qrcode_getBusinessIn" +",robot-2058-qrcode_checkBeforeCalculate,robot-2058-qrcode_saveByJF,robot-2058-qrcode_getBusinessIn_alipay,robot-2058-qrcode_editFeeInfor,robot-2058-qrcode_editPayFeeByWeChat,robot-2058-qrcode_saveByWeChat,robot-2058-qrcode_save";		}}    else {              return "robot-2058-qrcode_login,robot-2058-qrcode_editCheckFlag,robot-2058-qrcode_gotoJfcd,robot-2058-qrcode_prepareEditByJF,robot-2058-qrcode_getBusinessIn" +                ",robot-2058-qrcode_checkBeforeCalculate,robot-2058-qrcode_saveByJF,robot-2058-qrcode_getBusinessIn_alipay,robot-2058-qrcode_editFeeInfor,robot-2058-qrcode_editPayFeeByWeChat,robot-2058-qrcode_saveByWeChat,robot-2058-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2058', 'ancheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安诚-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2058-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2058-qrcode_query_editCheckFlag,robot-2058-qrcode_query_gotoJfcd,robot-2058-qrcode_query_prepareEditByJF" +                ",robot-2058-qrcode_query_editMainInfor,robot-2058-qrcode_query_getBusinessIn,robot-2058-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ" +            ",robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind,robot-2058-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2058', 'ancheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-IdCarChekc" //申请验证码    else{        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2058', 'ancheng', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安诚-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectPolicyefc,robot-2058-selectPolicybiz,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ" +            ",robot-2058-showCitemCarQ,robot-2058-showCinsuredQ,robot-2058-showCitemKindCI,robot-2058-browseProposalS,robot-2058-showCitemCarS" +            ",robot-2058-showCinsuredS,robot-2058-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2058', 'ancheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2058-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectProposalQ,robot-2058-selectProposalS,robot-2058-browseProposalQ,robot-2058-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2058', 'ancheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安诚-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-bj-editIDCardCheck,robot-2058-apply-bj-IdCarChekc";    } else {        s = "robot-2058-qrcode_login,robot-2058-qrcode_editCheckFlag,robot-2058-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2058', 'ancheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安诚报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2058_ask_charge,edi_2058_noMotor_quote"	} else {		return "edi_2058_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2058', 'ancheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安诚-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2058_ask_charge,edi_2058_noMotor_quote,edi_2058_askInsure,edi_2058_uploadImg,edi_2058_submitInsure,edi_2058_noMotor_submit" 	  	} else {		return "edi_2058_ask_chargeold,edi_2058_askInsure,edi_2058_uploadImg,edi_2058_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2058', 'ancheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安诚-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2058_ask_charge,edi_2058_noMotor_quote,edi_2058_askInsure,edi_2058_uploadImg" 	} else {		return "edi_2058_ask_chargeold,edi_2058_askInsure,edi_2058_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2058', 'ancheng', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安诚-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2058_efc_policyinfo,edi_2058_biz_policyinfo,edi_2058_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2058', 'ancheng', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安诚-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2058_efc_insurequery,edi_2058_biz_insurequery,edi_2058_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2058', 'ancheng', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安诚短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-bj-editIDCardCheck,robot-2058-apply-saveCheckCode,robot-2058-apply-selectIsNetProp";    } else {        s = "robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2058', 'ancheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安诚-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2058-bj-qrcode_login,robot-2058-apply-bj-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-bj-editIDCardCheck,robot-2058-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2058', 'ancheng', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安诚-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2058_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2058', 'ancheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安诚北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2058_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2058', 'ancheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安诚北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2058_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2058', 'ancheng', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2058-login,robot-2058-prepareQueryCode,robot-2058-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2042', 'dubang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-都邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-IdCarChekc" //申请验证码    else{        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2042', 'dubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-都邦-报价', 'def getTemplateGroup(dataSource) {    return "edi-2042-queryCar,edi-2042-xbQuery,edi-2042-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2042', 'dubang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-都邦-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2042-queryCar,edi-2042-xbQuery,edi-2042-askCharge,edi-2042-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2042', 'dubang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-都邦-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2042-queryCar,edi-2042-xbQuery,edi-2042-askCharge,edi-2042-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2042', 'dubang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-都邦-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2042-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2042', 'dubang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-都邦-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2042-login,robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2042-login,robot-2042-ObtainConfig,robot-2042-checkInsurePerson,robot-2042-changePerson,robot-2042-checkInsuredPerson,robot-2042-changePerson,robot-2042-prepareEdit," +                    "robot-2042-prepareQueryCode,robot-2042-selectProposalCar,robot-2042-browseProposalCar,robot-2042-browseProposalCarefc,robot-2042-selectRenewalPolicyNo"        }else{            s = "robot-2042-login,robot-2042-ObtainConfig,robot-2042-checkInsurePerson,robot-2042-changePerson,robot-2042-checkInsuredPerson,robot-2042-changePerson,robot-2042-prepareEdit," +                    "robot-2042-prepareQueryCode,robot-2042-browseProposalCar,robot-2042-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2042-VehicleModelList" //上海车型查询        }        s += ",robot-2042-queryPrepare,robot-2042-vehicleQuery,robot-2042-queryTaxAbateForPlat,robot-2042-calActualValue,robot-2042-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2042-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2042-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-nomotor-unitedSaleEdit,robot-2042-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2042-login,robot_2042_bj_initData,robot_2042_bj_queryModel,robot_2042_bj_getSaleTaxInfo,robot_2042_bj_getRealValue,robot_2042_bj_getPersonData,robot_2042_bj_addPersonData,robot_2042_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2042', 'dubang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-都邦-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2042-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2042-ObtainConfig,robot-2042-selectRenewal,robot-2042-editCengage,robot-2042-editCitemCar,robot-2042-editCinsured,robot-2042-renewalPolicy,robot-2042-renewalPolicyCI,robot-2042-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2042-VehicleModelList" //上海车型查询        }        s += ",robot-2042-vehicleQueryXB,robot-2042-queryTaxAbateForPlat,robot-2042-calActualValue,robot-2042-editCitemKind,robot-2042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2042-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-refreshPlanByTimes,robot-2042-insert"            }else{                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2042-getMaxCsellFee,robot-2042-getPrpCseller,robot-2042-getPrpCsellerCI,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            s += ",robot-2042-getMaxCsellFee,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"        }    }else{        s +=",robot-2042-ObtainConfig,robot-2042-checkInsurePerson,robot-2042-changePerson,robot-2042-checkInsuredPerson,robot-2042-changePerson,robot-2042-prepareEdit,robot-2042-selectRenewalPolicyNo,robot-2042-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2042-VehicleModelList" //上海车型查询        }        s += ",robot-2042-queryPrepare,robot-2042-vehicleQuery,robot-2042-queryTaxAbateForPlat,robot-2042-calActualValue,robot-2042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2042-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2042-calAnciInfo,robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            }else{                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-nomotor-unitedSaleEdit,robot-2042-nomotor-saveUnitedSale,robot-2042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2042-getMaxCsellFee,robot-2042-getPrpCseller,robot-2042-getPrpCsellerCI,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            s += ",robot-2042-getMaxCsellFee,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-nomotor-unitedSaleEdit,robot-2042-nomotor-saveUnitedSale,robot-2042-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2042-login,robot_2042_bj_initData,robot_2042_bj_queryModel,robot_2042_bj_getSaleTaxInfo,robot_2042_bj_getRealValue,robot_2042_bj_getPersonData,robot_2042_bj_addPersonData,robot_2042_bj_askCharge,robot_2042_bj_queryPayForXSFY,robot_2042_bj_getCagentCI,robot_2042_bj_getCagent,robot_2042_bj_queryPayForXSFY_req,robot_2042_bj_queryIlogEngage,robot_2042_bj_insureRefrenshPlan,robot_2042_bj_insure4S,robot-2042-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2042', 'dubang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-都邦-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ" +            ",robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind,robot-2042-nomotor-query,robot-2042-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2042', 'dubang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-都邦-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ" +            ",robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind,robot-2042-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2042', 'dubang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-都邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ" +            ",robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind,robot-2042-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2042', 'dubang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "都邦财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-都邦-电销', 'def getTemplateGroup(dataSource){    return "robot-2042-pureESale_Login,robot-2042-pureESale_Welcome,robot-2042-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2042', 'dubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-都邦续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2042-login,robot-2042-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2042', 'dubang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-都邦-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2042-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2042-ObtainConfig,robot-2042-selectRenewal,robot-2042-editCengage,robot-2042-editCitemCar,robot-2042-editCinsured,robot-2042-renewalPolicy,robot-2042-renewalPolicyCI,robot-2042-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2042-VehicleModelList" //上海车型查询        }        s += ",robot-2042-vehicleQueryXB,robot-2042-queryTaxAbateForPlat,robot-2042-calActualValue,robot-2042-editCitemKind,robot-2042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2042-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-refreshPlanByTimes,robot-2042-insert"            }else{                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2042-calAnciInfo,robot-2042-getMaxCsellFee,robot-2042-getPrpCseller,robot-2042-getPrpCsellerCI,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            s += ",robot-2042-getMaxCsellFee,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"        }    }else{        s += ",robot-2042-ObtainConfig,robot-2042-checkInsurePerson,robot-2042-changePerson,robot-2042-checkInsuredPerson,robot-2042-changePerson,robot-2042-prepareEdit,robot-2042-editCengage,robot-2042-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2042-queryVehiclePMCheck,robot-2042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2042-VehicleModelList" //上海车型查询        }        s += ",robot-2042-queryPrepare,robot-2042-vehicleQuery,robot-2042-queryTaxAbateForPlat,robot-2042-calActualValue,robot-2042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2042-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2042-calAnciInfo,robot-2042-queryPayFor,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            }else{                s += ",robot-2042-calAnciInfo,robot-2042-checkAgentType,robot-2042-queryPayForSCMS,robot-2042-getCagent,robot-2042-getCagentCI,robot-2042-refreshPlanByTimes,robot-2042-nomotor-unitedSaleEdit,robot-2042-nomotor-saveUnitedSale,robot-2042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2042-calAnciInfo,robot-2042-getMaxCsellFee,robot-2042-getPrpCseller,robot-2042-getPrpCsellerCI,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-insert"            s += ",robot-2042-getMaxCsellFee,robot-2042-queryPayForSCMS,robot-2042-refreshPlanByTimes,robot-2042-nomotor-unitedSaleEdit,robot-2042-nomotor-saveUnitedSale,robot-2042-insert"        }    }    s += ",robot-2042-checkRiskCode,robot-2042-editMainUwtFlag,robot-2042-editSubmitUndwrt,robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-showUndwrtMsgQ,robot-2042-showUndwrtMsgS"+            ",robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ,robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind,robot-2042-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2042-login,robot_2042_bj_initData,robot_2042_bj_queryModel,robot_2042_bj_getSaleTaxInfo,robot_2042_bj_getRealValue,robot_2042_bj_getPersonData,robot_2042_bj_addPersonData,robot_2042_bj_askCharge,robot_2042_bj_queryPayForXSFY,robot_2042_bj_getCagentCI,robot_2042_bj_getCagent,robot_2042_bj_queryPayForXSFY_req,robot_2042_bj_queryIlogEngage,robot_2042_bj_insureRefrenshPlan,robot_2042_bj_insure4S,robot-2042-uploadImage,robot_2042_bj_autoInsure,robot_2042_bj_showUndwrtMsgQ,robot_2042_bj_showUndwrtMsgS";       s += ",robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ,robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +                ",robot-2042-showCinsuredS,robot-2042-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2042', 'dubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-都邦-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2042-qrcode_login,robot-2042-qrcode_printTwoBarCodeServlet,robot-2042-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2042-qrcode_login,robot-2042-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2042-qrcode_login,robot-2042-qrcode_editCheckFlag,robot-2042-qrcode_gotoJfcd,robot-2042-qrcode_prepareEditByJF,robot-2042-qrcode_getBusinessIn" +                ",robot-2042-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2042-qrcode_login,robot-2042-qrcode_editCheckFlag,robot-2042-qrcode_gotoJfcd,robot-2042-qrcode_prepareEditByJF,robot-2042-qrcode_getBusinessIn" +                ",robot-2042-qrcode_checkBeforeCalculate,robot-2042-qrcode_saveByJF,robot-2042-qrcode_getBusinessIn_alipay,robot-2042-qrcode_editFeeInfor,robot-2042-qrcode_editPayFeeByWeChat,robot-2042-qrcode_saveByWeChat,robot-2042-qrcode_save";		} else {					return  "robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-bj-editIDCardCheck,robot-2042-apply-selectIsNetProp,robot-2042-apply-saveCheckCode,robot-2042-qrcode_editCheckFlag,robot-2042-qrcode_gotoJfcd,robot-2042-qrcode_prepareEditByJF,robot-2042-qrcode_getBusinessIn" +",robot-2042-qrcode_checkBeforeCalculate,robot-2042-qrcode_saveByJF,robot-2042-qrcode_getBusinessIn_alipay,robot-2042-qrcode_editFeeInfor,robot-2042-qrcode_editPayFeeByWeChat,robot-2042-qrcode_saveByWeChat,robot-2042-qrcode_save";		}}    else {              return "robot-2042-qrcode_login,robot-2042-qrcode_editCheckFlag,robot-2042-qrcode_gotoJfcd,robot-2042-qrcode_prepareEditByJF,robot-2042-qrcode_getBusinessIn" +                ",robot-2042-qrcode_checkBeforeCalculate,robot-2042-qrcode_saveByJF,robot-2042-qrcode_getBusinessIn_alipay,robot-2042-qrcode_editFeeInfor,robot-2042-qrcode_editPayFeeByWeChat,robot-2042-qrcode_saveByWeChat,robot-2042-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2042', 'dubang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-都邦-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2042-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2042-qrcode_query_editCheckFlag,robot-2042-qrcode_query_gotoJfcd,robot-2042-qrcode_query_prepareEditByJF" +                ",robot-2042-qrcode_query_editMainInfor,robot-2042-qrcode_query_getBusinessIn,robot-2042-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ" +            ",robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind,robot-2042-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2042', 'dubang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-都邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-IdCarChekc" //申请验证码    else{        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2042', 'dubang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-都邦-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectPolicyefc,robot-2042-selectPolicybiz,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ" +            ",robot-2042-showCitemCarQ,robot-2042-showCinsuredQ,robot-2042-showCitemKindCI,robot-2042-browseProposalS,robot-2042-showCitemCarS" +            ",robot-2042-showCinsuredS,robot-2042-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2042', 'dubang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2042-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectProposalQ,robot-2042-selectProposalS,robot-2042-browseProposalQ,robot-2042-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2042', 'dubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-都邦-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-bj-editIDCardCheck,robot-2042-apply-bj-IdCarChekc";    } else {        s = "robot-2042-qrcode_login,robot-2042-qrcode_editCheckFlag,robot-2042-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2042', 'dubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi都邦报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2042_ask_charge,edi_2042_noMotor_quote"	} else {		return "edi_2042_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2042', 'dubang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-都邦-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2042_ask_charge,edi_2042_noMotor_quote,edi_2042_askInsure,edi_2042_uploadImg,edi_2042_submitInsure,edi_2042_noMotor_submit" 	  	} else {		return "edi_2042_ask_chargeold,edi_2042_askInsure,edi_2042_uploadImg,edi_2042_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2042', 'dubang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-都邦-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2042_ask_charge,edi_2042_noMotor_quote,edi_2042_askInsure,edi_2042_uploadImg" 	} else {		return "edi_2042_ask_chargeold,edi_2042_askInsure,edi_2042_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2042', 'dubang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-都邦-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2042_efc_policyinfo,edi_2042_biz_policyinfo,edi_2042_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2042', 'dubang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-都邦-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2042_efc_insurequery,edi_2042_biz_insurequery,edi_2042_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2042', 'dubang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京都邦短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-bj-editIDCardCheck,robot-2042-apply-saveCheckCode,robot-2042-apply-selectIsNetProp";    } else {        s = "robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2042', 'dubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-都邦-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2042-bj-qrcode_login,robot-2042-apply-bj-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-bj-editIDCardCheck,robot-2042-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2042', 'dubang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-都邦-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2042_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2042', 'dubang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi都邦北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2042_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2042', 'dubang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi都邦北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2042_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2042', 'dubang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-都邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2042-login,robot-2042-prepareQueryCode,robot-2042-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2072', 'beibuwan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-北部湾-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-IdCarChekc" //申请验证码    else{        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2072', 'beibuwan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-北部湾-报价', 'def getTemplateGroup(dataSource) {    return "edi-2072-queryCar,edi-2072-xbQuery,edi-2072-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2072', 'beibuwan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-北部湾-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2072-queryCar,edi-2072-xbQuery,edi-2072-askCharge,edi-2072-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2072', 'beibuwan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-北部湾-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2072-queryCar,edi-2072-xbQuery,edi-2072-askCharge,edi-2072-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2072', 'beibuwan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-北部湾-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2072-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2072', 'beibuwan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-北部湾-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2072-login,robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2072-login,robot-2072-ObtainConfig,robot-2072-checkInsurePerson,robot-2072-changePerson,robot-2072-checkInsuredPerson,robot-2072-changePerson,robot-2072-prepareEdit," +                    "robot-2072-prepareQueryCode,robot-2072-selectProposalCar,robot-2072-browseProposalCar,robot-2072-browseProposalCarefc,robot-2072-selectRenewalPolicyNo"        }else{            s = "robot-2072-login,robot-2072-ObtainConfig,robot-2072-checkInsurePerson,robot-2072-changePerson,robot-2072-checkInsuredPerson,robot-2072-changePerson,robot-2072-prepareEdit," +                    "robot-2072-prepareQueryCode,robot-2072-browseProposalCar,robot-2072-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2072-VehicleModelList" //上海车型查询        }        s += ",robot-2072-queryPrepare,robot-2072-vehicleQuery,robot-2072-queryTaxAbateForPlat,robot-2072-calActualValue,robot-2072-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2072-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2072-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-nomotor-unitedSaleEdit,robot-2072-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2072-login,robot_2072_bj_initData,robot_2072_bj_queryModel,robot_2072_bj_getSaleTaxInfo,robot_2072_bj_getRealValue,robot_2072_bj_getPersonData,robot_2072_bj_addPersonData,robot_2072_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2072', 'beibuwan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-北部湾-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2072-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2072-ObtainConfig,robot-2072-selectRenewal,robot-2072-editCengage,robot-2072-editCitemCar,robot-2072-editCinsured,robot-2072-renewalPolicy,robot-2072-renewalPolicyCI,robot-2072-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2072-VehicleModelList" //上海车型查询        }        s += ",robot-2072-vehicleQueryXB,robot-2072-queryTaxAbateForPlat,robot-2072-calActualValue,robot-2072-editCitemKind,robot-2072-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2072-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-refreshPlanByTimes,robot-2072-insert"            }else{                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2072-getMaxCsellFee,robot-2072-getPrpCseller,robot-2072-getPrpCsellerCI,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            s += ",robot-2072-getMaxCsellFee,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"        }    }else{        s +=",robot-2072-ObtainConfig,robot-2072-checkInsurePerson,robot-2072-changePerson,robot-2072-checkInsuredPerson,robot-2072-changePerson,robot-2072-prepareEdit,robot-2072-selectRenewalPolicyNo,robot-2072-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2072-VehicleModelList" //上海车型查询        }        s += ",robot-2072-queryPrepare,robot-2072-vehicleQuery,robot-2072-queryTaxAbateForPlat,robot-2072-calActualValue,robot-2072-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2072-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2072-calAnciInfo,robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            }else{                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-nomotor-unitedSaleEdit,robot-2072-nomotor-saveUnitedSale,robot-2072-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2072-getMaxCsellFee,robot-2072-getPrpCseller,robot-2072-getPrpCsellerCI,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            s += ",robot-2072-getMaxCsellFee,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-nomotor-unitedSaleEdit,robot-2072-nomotor-saveUnitedSale,robot-2072-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2072-login,robot_2072_bj_initData,robot_2072_bj_queryModel,robot_2072_bj_getSaleTaxInfo,robot_2072_bj_getRealValue,robot_2072_bj_getPersonData,robot_2072_bj_addPersonData,robot_2072_bj_askCharge,robot_2072_bj_queryPayForXSFY,robot_2072_bj_getCagentCI,robot_2072_bj_getCagent,robot_2072_bj_queryPayForXSFY_req,robot_2072_bj_queryIlogEngage,robot_2072_bj_insureRefrenshPlan,robot_2072_bj_insure4S,robot-2072-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2072', 'beibuwan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-北部湾-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ" +            ",robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind,robot-2072-nomotor-query,robot-2072-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2072', 'beibuwan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-北部湾-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ" +            ",robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind,robot-2072-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2072', 'beibuwan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-北部湾-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ" +            ",robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind,robot-2072-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2072', 'beibuwan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "北部湾财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-北部湾-电销', 'def getTemplateGroup(dataSource){    return "robot-2072-pureESale_Login,robot-2072-pureESale_Welcome,robot-2072-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2072', 'beibuwan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-北部湾续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2072-login,robot-2072-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2072', 'beibuwan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-北部湾-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2072-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2072-ObtainConfig,robot-2072-selectRenewal,robot-2072-editCengage,robot-2072-editCitemCar,robot-2072-editCinsured,robot-2072-renewalPolicy,robot-2072-renewalPolicyCI,robot-2072-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2072-VehicleModelList" //上海车型查询        }        s += ",robot-2072-vehicleQueryXB,robot-2072-queryTaxAbateForPlat,robot-2072-calActualValue,robot-2072-editCitemKind,robot-2072-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2072-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-refreshPlanByTimes,robot-2072-insert"            }else{                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2072-calAnciInfo,robot-2072-getMaxCsellFee,robot-2072-getPrpCseller,robot-2072-getPrpCsellerCI,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            s += ",robot-2072-getMaxCsellFee,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"        }    }else{        s += ",robot-2072-ObtainConfig,robot-2072-checkInsurePerson,robot-2072-changePerson,robot-2072-checkInsuredPerson,robot-2072-changePerson,robot-2072-prepareEdit,robot-2072-editCengage,robot-2072-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2072-queryVehiclePMCheck,robot-2072-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2072-VehicleModelList" //上海车型查询        }        s += ",robot-2072-queryPrepare,robot-2072-vehicleQuery,robot-2072-queryTaxAbateForPlat,robot-2072-calActualValue,robot-2072-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2072-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2072-calAnciInfo,robot-2072-queryPayFor,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            }else{                s += ",robot-2072-calAnciInfo,robot-2072-checkAgentType,robot-2072-queryPayForSCMS,robot-2072-getCagent,robot-2072-getCagentCI,robot-2072-refreshPlanByTimes,robot-2072-nomotor-unitedSaleEdit,robot-2072-nomotor-saveUnitedSale,robot-2072-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2072-calAnciInfo,robot-2072-getMaxCsellFee,robot-2072-getPrpCseller,robot-2072-getPrpCsellerCI,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-insert"            s += ",robot-2072-getMaxCsellFee,robot-2072-queryPayForSCMS,robot-2072-refreshPlanByTimes,robot-2072-nomotor-unitedSaleEdit,robot-2072-nomotor-saveUnitedSale,robot-2072-insert"        }    }    s += ",robot-2072-checkRiskCode,robot-2072-editMainUwtFlag,robot-2072-editSubmitUndwrt,robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-showUndwrtMsgQ,robot-2072-showUndwrtMsgS"+            ",robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ,robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind,robot-2072-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2072-login,robot_2072_bj_initData,robot_2072_bj_queryModel,robot_2072_bj_getSaleTaxInfo,robot_2072_bj_getRealValue,robot_2072_bj_getPersonData,robot_2072_bj_addPersonData,robot_2072_bj_askCharge,robot_2072_bj_queryPayForXSFY,robot_2072_bj_getCagentCI,robot_2072_bj_getCagent,robot_2072_bj_queryPayForXSFY_req,robot_2072_bj_queryIlogEngage,robot_2072_bj_insureRefrenshPlan,robot_2072_bj_insure4S,robot-2072-uploadImage,robot_2072_bj_autoInsure,robot_2072_bj_showUndwrtMsgQ,robot_2072_bj_showUndwrtMsgS";       s += ",robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ,robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +                ",robot-2072-showCinsuredS,robot-2072-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2072', 'beibuwan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-北部湾-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2072-qrcode_login,robot-2072-qrcode_printTwoBarCodeServlet,robot-2072-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2072-qrcode_login,robot-2072-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2072-qrcode_login,robot-2072-qrcode_editCheckFlag,robot-2072-qrcode_gotoJfcd,robot-2072-qrcode_prepareEditByJF,robot-2072-qrcode_getBusinessIn" +                ",robot-2072-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2072-qrcode_login,robot-2072-qrcode_editCheckFlag,robot-2072-qrcode_gotoJfcd,robot-2072-qrcode_prepareEditByJF,robot-2072-qrcode_getBusinessIn" +                ",robot-2072-qrcode_checkBeforeCalculate,robot-2072-qrcode_saveByJF,robot-2072-qrcode_getBusinessIn_alipay,robot-2072-qrcode_editFeeInfor,robot-2072-qrcode_editPayFeeByWeChat,robot-2072-qrcode_saveByWeChat,robot-2072-qrcode_save";		} else {					return  "robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-bj-editIDCardCheck,robot-2072-apply-selectIsNetProp,robot-2072-apply-saveCheckCode,robot-2072-qrcode_editCheckFlag,robot-2072-qrcode_gotoJfcd,robot-2072-qrcode_prepareEditByJF,robot-2072-qrcode_getBusinessIn" +",robot-2072-qrcode_checkBeforeCalculate,robot-2072-qrcode_saveByJF,robot-2072-qrcode_getBusinessIn_alipay,robot-2072-qrcode_editFeeInfor,robot-2072-qrcode_editPayFeeByWeChat,robot-2072-qrcode_saveByWeChat,robot-2072-qrcode_save";		}}    else {              return "robot-2072-qrcode_login,robot-2072-qrcode_editCheckFlag,robot-2072-qrcode_gotoJfcd,robot-2072-qrcode_prepareEditByJF,robot-2072-qrcode_getBusinessIn" +                ",robot-2072-qrcode_checkBeforeCalculate,robot-2072-qrcode_saveByJF,robot-2072-qrcode_getBusinessIn_alipay,robot-2072-qrcode_editFeeInfor,robot-2072-qrcode_editPayFeeByWeChat,robot-2072-qrcode_saveByWeChat,robot-2072-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2072', 'beibuwan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-北部湾-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2072-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2072-qrcode_query_editCheckFlag,robot-2072-qrcode_query_gotoJfcd,robot-2072-qrcode_query_prepareEditByJF" +                ",robot-2072-qrcode_query_editMainInfor,robot-2072-qrcode_query_getBusinessIn,robot-2072-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ" +            ",robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind,robot-2072-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2072', 'beibuwan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-北部湾-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-IdCarChekc" //申请验证码    else{        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2072', 'beibuwan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-北部湾-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectPolicyefc,robot-2072-selectPolicybiz,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ" +            ",robot-2072-showCitemCarQ,robot-2072-showCinsuredQ,robot-2072-showCitemKindCI,robot-2072-browseProposalS,robot-2072-showCitemCarS" +            ",robot-2072-showCinsuredS,robot-2072-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2072', 'beibuwan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2072-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectProposalQ,robot-2072-selectProposalS,robot-2072-browseProposalQ,robot-2072-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2072', 'beibuwan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-北部湾-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-bj-editIDCardCheck,robot-2072-apply-bj-IdCarChekc";    } else {        s = "robot-2072-qrcode_login,robot-2072-qrcode_editCheckFlag,robot-2072-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2072', 'beibuwan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi北部湾报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2072_ask_charge,edi_2072_noMotor_quote"	} else {		return "edi_2072_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2072', 'beibuwan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-北部湾-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2072_ask_charge,edi_2072_noMotor_quote,edi_2072_askInsure,edi_2072_uploadImg,edi_2072_submitInsure,edi_2072_noMotor_submit" 	  	} else {		return "edi_2072_ask_chargeold,edi_2072_askInsure,edi_2072_uploadImg,edi_2072_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2072', 'beibuwan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-北部湾-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2072_ask_charge,edi_2072_noMotor_quote,edi_2072_askInsure,edi_2072_uploadImg" 	} else {		return "edi_2072_ask_chargeold,edi_2072_askInsure,edi_2072_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2072', 'beibuwan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-北部湾-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2072_efc_policyinfo,edi_2072_biz_policyinfo,edi_2072_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2072', 'beibuwan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-北部湾-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2072_efc_insurequery,edi_2072_biz_insurequery,edi_2072_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2072', 'beibuwan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京北部湾短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-bj-editIDCardCheck,robot-2072-apply-saveCheckCode,robot-2072-apply-selectIsNetProp";    } else {        s = "robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2072', 'beibuwan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-北部湾-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2072-bj-qrcode_login,robot-2072-apply-bj-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-bj-editIDCardCheck,robot-2072-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2072', 'beibuwan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-北部湾-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2072_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2072', 'beibuwan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi北部湾北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2072_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2072', 'beibuwan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi北部湾北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2072_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2072', 'beibuwan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-北部湾-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2072-login,robot-2072-prepareQueryCode,robot-2072-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3037', 'fude', '15', '6', 'pro', 'other', b'1', '{}', '精灵-富德-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-IdCarChekc" //申请验证码    else{        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3037', 'fude', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-富德-报价', 'def getTemplateGroup(dataSource) {    return "edi-3037-queryCar,edi-3037-xbQuery,edi-3037-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3037', 'fude', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-富德-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3037-queryCar,edi-3037-xbQuery,edi-3037-askCharge,edi-3037-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3037', 'fude', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-富德-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3037-queryCar,edi-3037-xbQuery,edi-3037-askCharge,edi-3037-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3037', 'fude', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-富德-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3037-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3037', 'fude', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-富德-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3037-login,robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3037-login,robot-3037-ObtainConfig,robot-3037-checkInsurePerson,robot-3037-changePerson,robot-3037-checkInsuredPerson,robot-3037-changePerson,robot-3037-prepareEdit," +                    "robot-3037-prepareQueryCode,robot-3037-selectProposalCar,robot-3037-browseProposalCar,robot-3037-browseProposalCarefc,robot-3037-selectRenewalPolicyNo"        }else{            s = "robot-3037-login,robot-3037-ObtainConfig,robot-3037-checkInsurePerson,robot-3037-changePerson,robot-3037-checkInsuredPerson,robot-3037-changePerson,robot-3037-prepareEdit," +                    "robot-3037-prepareQueryCode,robot-3037-browseProposalCar,robot-3037-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3037-VehicleModelList" //上海车型查询        }        s += ",robot-3037-queryPrepare,robot-3037-vehicleQuery,robot-3037-queryTaxAbateForPlat,robot-3037-calActualValue,robot-3037-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3037-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3037-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-nomotor-unitedSaleEdit,robot-3037-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3037-login,robot_3037_bj_initData,robot_3037_bj_queryModel,robot_3037_bj_getSaleTaxInfo,robot_3037_bj_getRealValue,robot_3037_bj_getPersonData,robot_3037_bj_addPersonData,robot_3037_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3037', 'fude', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-富德-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3037-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3037-ObtainConfig,robot-3037-selectRenewal,robot-3037-editCengage,robot-3037-editCitemCar,robot-3037-editCinsured,robot-3037-renewalPolicy,robot-3037-renewalPolicyCI,robot-3037-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3037-VehicleModelList" //上海车型查询        }        s += ",robot-3037-vehicleQueryXB,robot-3037-queryTaxAbateForPlat,robot-3037-calActualValue,robot-3037-editCitemKind,robot-3037-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3037-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-refreshPlanByTimes,robot-3037-insert"            }else{                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3037-getMaxCsellFee,robot-3037-getPrpCseller,robot-3037-getPrpCsellerCI,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            s += ",robot-3037-getMaxCsellFee,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"        }    }else{        s +=",robot-3037-ObtainConfig,robot-3037-checkInsurePerson,robot-3037-changePerson,robot-3037-checkInsuredPerson,robot-3037-changePerson,robot-3037-prepareEdit,robot-3037-selectRenewalPolicyNo,robot-3037-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3037-VehicleModelList" //上海车型查询        }        s += ",robot-3037-queryPrepare,robot-3037-vehicleQuery,robot-3037-queryTaxAbateForPlat,robot-3037-calActualValue,robot-3037-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3037-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3037-calAnciInfo,robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            }else{                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-nomotor-unitedSaleEdit,robot-3037-nomotor-saveUnitedSale,robot-3037-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3037-getMaxCsellFee,robot-3037-getPrpCseller,robot-3037-getPrpCsellerCI,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            s += ",robot-3037-getMaxCsellFee,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-nomotor-unitedSaleEdit,robot-3037-nomotor-saveUnitedSale,robot-3037-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3037-login,robot_3037_bj_initData,robot_3037_bj_queryModel,robot_3037_bj_getSaleTaxInfo,robot_3037_bj_getRealValue,robot_3037_bj_getPersonData,robot_3037_bj_addPersonData,robot_3037_bj_askCharge,robot_3037_bj_queryPayForXSFY,robot_3037_bj_getCagentCI,robot_3037_bj_getCagent,robot_3037_bj_queryPayForXSFY_req,robot_3037_bj_queryIlogEngage,robot_3037_bj_insureRefrenshPlan,robot_3037_bj_insure4S,robot-3037-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3037', 'fude', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-富德-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ" +            ",robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind,robot-3037-nomotor-query,robot-3037-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3037', 'fude', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-富德-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ" +            ",robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind,robot-3037-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3037', 'fude', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-富德-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ" +            ",robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind,robot-3037-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3037', 'fude', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "富德财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-富德-电销', 'def getTemplateGroup(dataSource){    return "robot-3037-pureESale_Login,robot-3037-pureESale_Welcome,robot-3037-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3037', 'fude', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富德续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3037-login,robot-3037-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3037', 'fude', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-富德-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3037-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3037-ObtainConfig,robot-3037-selectRenewal,robot-3037-editCengage,robot-3037-editCitemCar,robot-3037-editCinsured,robot-3037-renewalPolicy,robot-3037-renewalPolicyCI,robot-3037-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3037-VehicleModelList" //上海车型查询        }        s += ",robot-3037-vehicleQueryXB,robot-3037-queryTaxAbateForPlat,robot-3037-calActualValue,robot-3037-editCitemKind,robot-3037-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3037-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-refreshPlanByTimes,robot-3037-insert"            }else{                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3037-calAnciInfo,robot-3037-getMaxCsellFee,robot-3037-getPrpCseller,robot-3037-getPrpCsellerCI,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            s += ",robot-3037-getMaxCsellFee,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"        }    }else{        s += ",robot-3037-ObtainConfig,robot-3037-checkInsurePerson,robot-3037-changePerson,robot-3037-checkInsuredPerson,robot-3037-changePerson,robot-3037-prepareEdit,robot-3037-editCengage,robot-3037-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3037-queryVehiclePMCheck,robot-3037-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3037-VehicleModelList" //上海车型查询        }        s += ",robot-3037-queryPrepare,robot-3037-vehicleQuery,robot-3037-queryTaxAbateForPlat,robot-3037-calActualValue,robot-3037-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3037-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3037-calAnciInfo,robot-3037-queryPayFor,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            }else{                s += ",robot-3037-calAnciInfo,robot-3037-checkAgentType,robot-3037-queryPayForSCMS,robot-3037-getCagent,robot-3037-getCagentCI,robot-3037-refreshPlanByTimes,robot-3037-nomotor-unitedSaleEdit,robot-3037-nomotor-saveUnitedSale,robot-3037-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3037-calAnciInfo,robot-3037-getMaxCsellFee,robot-3037-getPrpCseller,robot-3037-getPrpCsellerCI,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-insert"            s += ",robot-3037-getMaxCsellFee,robot-3037-queryPayForSCMS,robot-3037-refreshPlanByTimes,robot-3037-nomotor-unitedSaleEdit,robot-3037-nomotor-saveUnitedSale,robot-3037-insert"        }    }    s += ",robot-3037-checkRiskCode,robot-3037-editMainUwtFlag,robot-3037-editSubmitUndwrt,robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-showUndwrtMsgQ,robot-3037-showUndwrtMsgS"+            ",robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ,robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind,robot-3037-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3037-login,robot_3037_bj_initData,robot_3037_bj_queryModel,robot_3037_bj_getSaleTaxInfo,robot_3037_bj_getRealValue,robot_3037_bj_getPersonData,robot_3037_bj_addPersonData,robot_3037_bj_askCharge,robot_3037_bj_queryPayForXSFY,robot_3037_bj_getCagentCI,robot_3037_bj_getCagent,robot_3037_bj_queryPayForXSFY_req,robot_3037_bj_queryIlogEngage,robot_3037_bj_insureRefrenshPlan,robot_3037_bj_insure4S,robot-3037-uploadImage,robot_3037_bj_autoInsure,robot_3037_bj_showUndwrtMsgQ,robot_3037_bj_showUndwrtMsgS";       s += ",robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ,robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +                ",robot-3037-showCinsuredS,robot-3037-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3037', 'fude', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富德-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3037-qrcode_login,robot-3037-qrcode_printTwoBarCodeServlet,robot-3037-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3037-qrcode_login,robot-3037-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3037-qrcode_login,robot-3037-qrcode_editCheckFlag,robot-3037-qrcode_gotoJfcd,robot-3037-qrcode_prepareEditByJF,robot-3037-qrcode_getBusinessIn" +                ",robot-3037-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3037-qrcode_login,robot-3037-qrcode_editCheckFlag,robot-3037-qrcode_gotoJfcd,robot-3037-qrcode_prepareEditByJF,robot-3037-qrcode_getBusinessIn" +                ",robot-3037-qrcode_checkBeforeCalculate,robot-3037-qrcode_saveByJF,robot-3037-qrcode_getBusinessIn_alipay,robot-3037-qrcode_editFeeInfor,robot-3037-qrcode_editPayFeeByWeChat,robot-3037-qrcode_saveByWeChat,robot-3037-qrcode_save";		} else {					return  "robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-bj-editIDCardCheck,robot-3037-apply-selectIsNetProp,robot-3037-apply-saveCheckCode,robot-3037-qrcode_editCheckFlag,robot-3037-qrcode_gotoJfcd,robot-3037-qrcode_prepareEditByJF,robot-3037-qrcode_getBusinessIn" +",robot-3037-qrcode_checkBeforeCalculate,robot-3037-qrcode_saveByJF,robot-3037-qrcode_getBusinessIn_alipay,robot-3037-qrcode_editFeeInfor,robot-3037-qrcode_editPayFeeByWeChat,robot-3037-qrcode_saveByWeChat,robot-3037-qrcode_save";		}}    else {              return "robot-3037-qrcode_login,robot-3037-qrcode_editCheckFlag,robot-3037-qrcode_gotoJfcd,robot-3037-qrcode_prepareEditByJF,robot-3037-qrcode_getBusinessIn" +                ",robot-3037-qrcode_checkBeforeCalculate,robot-3037-qrcode_saveByJF,robot-3037-qrcode_getBusinessIn_alipay,robot-3037-qrcode_editFeeInfor,robot-3037-qrcode_editPayFeeByWeChat,robot-3037-qrcode_saveByWeChat,robot-3037-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3037', 'fude', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-富德-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3037-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3037-qrcode_query_editCheckFlag,robot-3037-qrcode_query_gotoJfcd,robot-3037-qrcode_query_prepareEditByJF" +                ",robot-3037-qrcode_query_editMainInfor,robot-3037-qrcode_query_getBusinessIn,robot-3037-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ" +            ",robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind,robot-3037-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3037', 'fude', '15', '6', 'pro', 'other', b'1', '{}', '精灵-富德-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-IdCarChekc" //申请验证码    else{        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3037', 'fude', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-富德-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectPolicyefc,robot-3037-selectPolicybiz,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ" +            ",robot-3037-showCitemCarQ,robot-3037-showCinsuredQ,robot-3037-showCitemKindCI,robot-3037-browseProposalS,robot-3037-showCitemCarS" +            ",robot-3037-showCinsuredS,robot-3037-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3037', 'fude', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3037-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectProposalQ,robot-3037-selectProposalS,robot-3037-browseProposalQ,robot-3037-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3037', 'fude', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富德-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-bj-editIDCardCheck,robot-3037-apply-bj-IdCarChekc";    } else {        s = "robot-3037-qrcode_login,robot-3037-qrcode_editCheckFlag,robot-3037-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3037', 'fude', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi富德报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3037_ask_charge,edi_3037_noMotor_quote"	} else {		return "edi_3037_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3037', 'fude', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-富德-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3037_ask_charge,edi_3037_noMotor_quote,edi_3037_askInsure,edi_3037_uploadImg,edi_3037_submitInsure,edi_3037_noMotor_submit" 	  	} else {		return "edi_3037_ask_chargeold,edi_3037_askInsure,edi_3037_uploadImg,edi_3037_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3037', 'fude', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-富德-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3037_ask_charge,edi_3037_noMotor_quote,edi_3037_askInsure,edi_3037_uploadImg" 	} else {		return "edi_3037_ask_chargeold,edi_3037_askInsure,edi_3037_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3037', 'fude', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-富德-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3037_efc_policyinfo,edi_3037_biz_policyinfo,edi_3037_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3037', 'fude', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-富德-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3037_efc_insurequery,edi_3037_biz_insurequery,edi_3037_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3037', 'fude', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京富德短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-bj-editIDCardCheck,robot-3037-apply-saveCheckCode,robot-3037-apply-selectIsNetProp";    } else {        s = "robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3037', 'fude', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富德-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3037-bj-qrcode_login,robot-3037-apply-bj-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-bj-editIDCardCheck,robot-3037-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3037', 'fude', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-富德-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3037_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3037', 'fude', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi富德北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3037_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3037', 'fude', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi富德北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3037_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3037', 'fude', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-富德-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3037-login,robot-3037-prepareQueryCode,robot-3037-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3024', 'anxin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安心-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-IdCarChekc" //申请验证码    else{        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3024', 'anxin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安心-报价', 'def getTemplateGroup(dataSource) {    return "edi-3024-queryCar,edi-3024-xbQuery,edi-3024-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3024', 'anxin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安心-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3024-queryCar,edi-3024-xbQuery,edi-3024-askCharge,edi-3024-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3024', 'anxin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安心-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3024-queryCar,edi-3024-xbQuery,edi-3024-askCharge,edi-3024-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3024', 'anxin', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安心-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3024-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3024', 'anxin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安心-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3024-login,robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3024-login,robot-3024-ObtainConfig,robot-3024-checkInsurePerson,robot-3024-changePerson,robot-3024-checkInsuredPerson,robot-3024-changePerson,robot-3024-prepareEdit," +                    "robot-3024-prepareQueryCode,robot-3024-selectProposalCar,robot-3024-browseProposalCar,robot-3024-browseProposalCarefc,robot-3024-selectRenewalPolicyNo"        }else{            s = "robot-3024-login,robot-3024-ObtainConfig,robot-3024-checkInsurePerson,robot-3024-changePerson,robot-3024-checkInsuredPerson,robot-3024-changePerson,robot-3024-prepareEdit," +                    "robot-3024-prepareQueryCode,robot-3024-browseProposalCar,robot-3024-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3024-VehicleModelList" //上海车型查询        }        s += ",robot-3024-queryPrepare,robot-3024-vehicleQuery,robot-3024-queryTaxAbateForPlat,robot-3024-calActualValue,robot-3024-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3024-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3024-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-nomotor-unitedSaleEdit,robot-3024-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3024-login,robot_3024_bj_initData,robot_3024_bj_queryModel,robot_3024_bj_getSaleTaxInfo,robot_3024_bj_getRealValue,robot_3024_bj_getPersonData,robot_3024_bj_addPersonData,robot_3024_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3024', 'anxin', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安心-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3024-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3024-ObtainConfig,robot-3024-selectRenewal,robot-3024-editCengage,robot-3024-editCitemCar,robot-3024-editCinsured,robot-3024-renewalPolicy,robot-3024-renewalPolicyCI,robot-3024-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3024-VehicleModelList" //上海车型查询        }        s += ",robot-3024-vehicleQueryXB,robot-3024-queryTaxAbateForPlat,robot-3024-calActualValue,robot-3024-editCitemKind,robot-3024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3024-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-refreshPlanByTimes,robot-3024-insert"            }else{                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3024-getMaxCsellFee,robot-3024-getPrpCseller,robot-3024-getPrpCsellerCI,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            s += ",robot-3024-getMaxCsellFee,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"        }    }else{        s +=",robot-3024-ObtainConfig,robot-3024-checkInsurePerson,robot-3024-changePerson,robot-3024-checkInsuredPerson,robot-3024-changePerson,robot-3024-prepareEdit,robot-3024-selectRenewalPolicyNo,robot-3024-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3024-VehicleModelList" //上海车型查询        }        s += ",robot-3024-queryPrepare,robot-3024-vehicleQuery,robot-3024-queryTaxAbateForPlat,robot-3024-calActualValue,robot-3024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3024-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3024-calAnciInfo,robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            }else{                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-nomotor-unitedSaleEdit,robot-3024-nomotor-saveUnitedSale,robot-3024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3024-getMaxCsellFee,robot-3024-getPrpCseller,robot-3024-getPrpCsellerCI,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            s += ",robot-3024-getMaxCsellFee,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-nomotor-unitedSaleEdit,robot-3024-nomotor-saveUnitedSale,robot-3024-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3024-login,robot_3024_bj_initData,robot_3024_bj_queryModel,robot_3024_bj_getSaleTaxInfo,robot_3024_bj_getRealValue,robot_3024_bj_getPersonData,robot_3024_bj_addPersonData,robot_3024_bj_askCharge,robot_3024_bj_queryPayForXSFY,robot_3024_bj_getCagentCI,robot_3024_bj_getCagent,robot_3024_bj_queryPayForXSFY_req,robot_3024_bj_queryIlogEngage,robot_3024_bj_insureRefrenshPlan,robot_3024_bj_insure4S,robot-3024-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3024', 'anxin', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安心-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ" +            ",robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind,robot-3024-nomotor-query,robot-3024-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3024', 'anxin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安心-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ" +            ",robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind,robot-3024-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3024', 'anxin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安心-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ" +            ",robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind,robot-3024-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3024', 'anxin', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安心财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安心-电销', 'def getTemplateGroup(dataSource){    return "robot-3024-pureESale_Login,robot-3024-pureESale_Welcome,robot-3024-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3024', 'anxin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安心续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3024-login,robot-3024-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3024', 'anxin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安心-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3024-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3024-ObtainConfig,robot-3024-selectRenewal,robot-3024-editCengage,robot-3024-editCitemCar,robot-3024-editCinsured,robot-3024-renewalPolicy,robot-3024-renewalPolicyCI,robot-3024-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3024-VehicleModelList" //上海车型查询        }        s += ",robot-3024-vehicleQueryXB,robot-3024-queryTaxAbateForPlat,robot-3024-calActualValue,robot-3024-editCitemKind,robot-3024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3024-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-refreshPlanByTimes,robot-3024-insert"            }else{                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3024-calAnciInfo,robot-3024-getMaxCsellFee,robot-3024-getPrpCseller,robot-3024-getPrpCsellerCI,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            s += ",robot-3024-getMaxCsellFee,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"        }    }else{        s += ",robot-3024-ObtainConfig,robot-3024-checkInsurePerson,robot-3024-changePerson,robot-3024-checkInsuredPerson,robot-3024-changePerson,robot-3024-prepareEdit,robot-3024-editCengage,robot-3024-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3024-queryVehiclePMCheck,robot-3024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3024-VehicleModelList" //上海车型查询        }        s += ",robot-3024-queryPrepare,robot-3024-vehicleQuery,robot-3024-queryTaxAbateForPlat,robot-3024-calActualValue,robot-3024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3024-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3024-calAnciInfo,robot-3024-queryPayFor,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            }else{                s += ",robot-3024-calAnciInfo,robot-3024-checkAgentType,robot-3024-queryPayForSCMS,robot-3024-getCagent,robot-3024-getCagentCI,robot-3024-refreshPlanByTimes,robot-3024-nomotor-unitedSaleEdit,robot-3024-nomotor-saveUnitedSale,robot-3024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3024-calAnciInfo,robot-3024-getMaxCsellFee,robot-3024-getPrpCseller,robot-3024-getPrpCsellerCI,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-insert"            s += ",robot-3024-getMaxCsellFee,robot-3024-queryPayForSCMS,robot-3024-refreshPlanByTimes,robot-3024-nomotor-unitedSaleEdit,robot-3024-nomotor-saveUnitedSale,robot-3024-insert"        }    }    s += ",robot-3024-checkRiskCode,robot-3024-editMainUwtFlag,robot-3024-editSubmitUndwrt,robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-showUndwrtMsgQ,robot-3024-showUndwrtMsgS"+            ",robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ,robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind,robot-3024-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3024-login,robot_3024_bj_initData,robot_3024_bj_queryModel,robot_3024_bj_getSaleTaxInfo,robot_3024_bj_getRealValue,robot_3024_bj_getPersonData,robot_3024_bj_addPersonData,robot_3024_bj_askCharge,robot_3024_bj_queryPayForXSFY,robot_3024_bj_getCagentCI,robot_3024_bj_getCagent,robot_3024_bj_queryPayForXSFY_req,robot_3024_bj_queryIlogEngage,robot_3024_bj_insureRefrenshPlan,robot_3024_bj_insure4S,robot-3024-uploadImage,robot_3024_bj_autoInsure,robot_3024_bj_showUndwrtMsgQ,robot_3024_bj_showUndwrtMsgS";       s += ",robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ,robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +                ",robot-3024-showCinsuredS,robot-3024-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3024', 'anxin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安心-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3024-qrcode_login,robot-3024-qrcode_printTwoBarCodeServlet,robot-3024-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3024-qrcode_login,robot-3024-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3024-qrcode_login,robot-3024-qrcode_editCheckFlag,robot-3024-qrcode_gotoJfcd,robot-3024-qrcode_prepareEditByJF,robot-3024-qrcode_getBusinessIn" +                ",robot-3024-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3024-qrcode_login,robot-3024-qrcode_editCheckFlag,robot-3024-qrcode_gotoJfcd,robot-3024-qrcode_prepareEditByJF,robot-3024-qrcode_getBusinessIn" +                ",robot-3024-qrcode_checkBeforeCalculate,robot-3024-qrcode_saveByJF,robot-3024-qrcode_getBusinessIn_alipay,robot-3024-qrcode_editFeeInfor,robot-3024-qrcode_editPayFeeByWeChat,robot-3024-qrcode_saveByWeChat,robot-3024-qrcode_save";		} else {					return  "robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-bj-editIDCardCheck,robot-3024-apply-selectIsNetProp,robot-3024-apply-saveCheckCode,robot-3024-qrcode_editCheckFlag,robot-3024-qrcode_gotoJfcd,robot-3024-qrcode_prepareEditByJF,robot-3024-qrcode_getBusinessIn" +",robot-3024-qrcode_checkBeforeCalculate,robot-3024-qrcode_saveByJF,robot-3024-qrcode_getBusinessIn_alipay,robot-3024-qrcode_editFeeInfor,robot-3024-qrcode_editPayFeeByWeChat,robot-3024-qrcode_saveByWeChat,robot-3024-qrcode_save";		}}    else {              return "robot-3024-qrcode_login,robot-3024-qrcode_editCheckFlag,robot-3024-qrcode_gotoJfcd,robot-3024-qrcode_prepareEditByJF,robot-3024-qrcode_getBusinessIn" +                ",robot-3024-qrcode_checkBeforeCalculate,robot-3024-qrcode_saveByJF,robot-3024-qrcode_getBusinessIn_alipay,robot-3024-qrcode_editFeeInfor,robot-3024-qrcode_editPayFeeByWeChat,robot-3024-qrcode_saveByWeChat,robot-3024-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3024', 'anxin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安心-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3024-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3024-qrcode_query_editCheckFlag,robot-3024-qrcode_query_gotoJfcd,robot-3024-qrcode_query_prepareEditByJF" +                ",robot-3024-qrcode_query_editMainInfor,robot-3024-qrcode_query_getBusinessIn,robot-3024-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ" +            ",robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind,robot-3024-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3024', 'anxin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安心-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-IdCarChekc" //申请验证码    else{        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3024', 'anxin', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安心-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectPolicyefc,robot-3024-selectPolicybiz,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ" +            ",robot-3024-showCitemCarQ,robot-3024-showCinsuredQ,robot-3024-showCitemKindCI,robot-3024-browseProposalS,robot-3024-showCitemCarS" +            ",robot-3024-showCinsuredS,robot-3024-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3024', 'anxin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3024-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectProposalQ,robot-3024-selectProposalS,robot-3024-browseProposalQ,robot-3024-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3024', 'anxin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安心-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-bj-editIDCardCheck,robot-3024-apply-bj-IdCarChekc";    } else {        s = "robot-3024-qrcode_login,robot-3024-qrcode_editCheckFlag,robot-3024-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3024', 'anxin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安心报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3024_ask_charge,edi_3024_noMotor_quote"	} else {		return "edi_3024_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3024', 'anxin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安心-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3024_ask_charge,edi_3024_noMotor_quote,edi_3024_askInsure,edi_3024_uploadImg,edi_3024_submitInsure,edi_3024_noMotor_submit" 	  	} else {		return "edi_3024_ask_chargeold,edi_3024_askInsure,edi_3024_uploadImg,edi_3024_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3024', 'anxin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安心-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3024_ask_charge,edi_3024_noMotor_quote,edi_3024_askInsure,edi_3024_uploadImg" 	} else {		return "edi_3024_ask_chargeold,edi_3024_askInsure,edi_3024_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3024', 'anxin', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安心-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3024_efc_policyinfo,edi_3024_biz_policyinfo,edi_3024_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3024', 'anxin', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安心-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3024_efc_insurequery,edi_3024_biz_insurequery,edi_3024_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3024', 'anxin', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安心短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-bj-editIDCardCheck,robot-3024-apply-saveCheckCode,robot-3024-apply-selectIsNetProp";    } else {        s = "robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3024', 'anxin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安心-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3024-bj-qrcode_login,robot-3024-apply-bj-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-bj-editIDCardCheck,robot-3024-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3024', 'anxin', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安心-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3024_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3024', 'anxin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安心北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3024_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3024', 'anxin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安心北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3024_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3024', 'anxin', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安心-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3024-login,robot-3024-prepareQueryCode,robot-3024-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3011', 'anda', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安达-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-IdCarChekc" //申请验证码    else{        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3011', 'anda', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安达-报价', 'def getTemplateGroup(dataSource) {    return "edi-3011-queryCar,edi-3011-xbQuery,edi-3011-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3011', 'anda', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安达-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3011-queryCar,edi-3011-xbQuery,edi-3011-askCharge,edi-3011-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3011', 'anda', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安达-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3011-queryCar,edi-3011-xbQuery,edi-3011-askCharge,edi-3011-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3011', 'anda', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安达-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3011-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3011', 'anda', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安达-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3011-login,robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3011-login,robot-3011-ObtainConfig,robot-3011-checkInsurePerson,robot-3011-changePerson,robot-3011-checkInsuredPerson,robot-3011-changePerson,robot-3011-prepareEdit," +                    "robot-3011-prepareQueryCode,robot-3011-selectProposalCar,robot-3011-browseProposalCar,robot-3011-browseProposalCarefc,robot-3011-selectRenewalPolicyNo"        }else{            s = "robot-3011-login,robot-3011-ObtainConfig,robot-3011-checkInsurePerson,robot-3011-changePerson,robot-3011-checkInsuredPerson,robot-3011-changePerson,robot-3011-prepareEdit," +                    "robot-3011-prepareQueryCode,robot-3011-browseProposalCar,robot-3011-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3011-VehicleModelList" //上海车型查询        }        s += ",robot-3011-queryPrepare,robot-3011-vehicleQuery,robot-3011-queryTaxAbateForPlat,robot-3011-calActualValue,robot-3011-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3011-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3011-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-nomotor-unitedSaleEdit,robot-3011-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3011-login,robot_3011_bj_initData,robot_3011_bj_queryModel,robot_3011_bj_getSaleTaxInfo,robot_3011_bj_getRealValue,robot_3011_bj_getPersonData,robot_3011_bj_addPersonData,robot_3011_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3011', 'anda', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安达-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3011-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3011-ObtainConfig,robot-3011-selectRenewal,robot-3011-editCengage,robot-3011-editCitemCar,robot-3011-editCinsured,robot-3011-renewalPolicy,robot-3011-renewalPolicyCI,robot-3011-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3011-VehicleModelList" //上海车型查询        }        s += ",robot-3011-vehicleQueryXB,robot-3011-queryTaxAbateForPlat,robot-3011-calActualValue,robot-3011-editCitemKind,robot-3011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3011-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-refreshPlanByTimes,robot-3011-insert"            }else{                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3011-getMaxCsellFee,robot-3011-getPrpCseller,robot-3011-getPrpCsellerCI,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            s += ",robot-3011-getMaxCsellFee,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"        }    }else{        s +=",robot-3011-ObtainConfig,robot-3011-checkInsurePerson,robot-3011-changePerson,robot-3011-checkInsuredPerson,robot-3011-changePerson,robot-3011-prepareEdit,robot-3011-selectRenewalPolicyNo,robot-3011-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3011-VehicleModelList" //上海车型查询        }        s += ",robot-3011-queryPrepare,robot-3011-vehicleQuery,robot-3011-queryTaxAbateForPlat,robot-3011-calActualValue,robot-3011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3011-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3011-calAnciInfo,robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            }else{                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-nomotor-unitedSaleEdit,robot-3011-nomotor-saveUnitedSale,robot-3011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3011-getMaxCsellFee,robot-3011-getPrpCseller,robot-3011-getPrpCsellerCI,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            s += ",robot-3011-getMaxCsellFee,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-nomotor-unitedSaleEdit,robot-3011-nomotor-saveUnitedSale,robot-3011-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3011-login,robot_3011_bj_initData,robot_3011_bj_queryModel,robot_3011_bj_getSaleTaxInfo,robot_3011_bj_getRealValue,robot_3011_bj_getPersonData,robot_3011_bj_addPersonData,robot_3011_bj_askCharge,robot_3011_bj_queryPayForXSFY,robot_3011_bj_getCagentCI,robot_3011_bj_getCagent,robot_3011_bj_queryPayForXSFY_req,robot_3011_bj_queryIlogEngage,robot_3011_bj_insureRefrenshPlan,robot_3011_bj_insure4S,robot-3011-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3011', 'anda', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安达-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ" +            ",robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind,robot-3011-nomotor-query,robot-3011-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3011', 'anda', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安达-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ" +            ",robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind,robot-3011-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3011', 'anda', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安达-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ" +            ",robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind,robot-3011-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3011', 'anda', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安达财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安达-电销', 'def getTemplateGroup(dataSource){    return "robot-3011-pureESale_Login,robot-3011-pureESale_Welcome,robot-3011-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3011', 'anda', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安达续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3011-login,robot-3011-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3011', 'anda', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安达-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3011-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3011-ObtainConfig,robot-3011-selectRenewal,robot-3011-editCengage,robot-3011-editCitemCar,robot-3011-editCinsured,robot-3011-renewalPolicy,robot-3011-renewalPolicyCI,robot-3011-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3011-VehicleModelList" //上海车型查询        }        s += ",robot-3011-vehicleQueryXB,robot-3011-queryTaxAbateForPlat,robot-3011-calActualValue,robot-3011-editCitemKind,robot-3011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3011-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-refreshPlanByTimes,robot-3011-insert"            }else{                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3011-calAnciInfo,robot-3011-getMaxCsellFee,robot-3011-getPrpCseller,robot-3011-getPrpCsellerCI,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            s += ",robot-3011-getMaxCsellFee,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"        }    }else{        s += ",robot-3011-ObtainConfig,robot-3011-checkInsurePerson,robot-3011-changePerson,robot-3011-checkInsuredPerson,robot-3011-changePerson,robot-3011-prepareEdit,robot-3011-editCengage,robot-3011-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3011-queryVehiclePMCheck,robot-3011-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3011-VehicleModelList" //上海车型查询        }        s += ",robot-3011-queryPrepare,robot-3011-vehicleQuery,robot-3011-queryTaxAbateForPlat,robot-3011-calActualValue,robot-3011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3011-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3011-calAnciInfo,robot-3011-queryPayFor,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            }else{                s += ",robot-3011-calAnciInfo,robot-3011-checkAgentType,robot-3011-queryPayForSCMS,robot-3011-getCagent,robot-3011-getCagentCI,robot-3011-refreshPlanByTimes,robot-3011-nomotor-unitedSaleEdit,robot-3011-nomotor-saveUnitedSale,robot-3011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3011-calAnciInfo,robot-3011-getMaxCsellFee,robot-3011-getPrpCseller,robot-3011-getPrpCsellerCI,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-insert"            s += ",robot-3011-getMaxCsellFee,robot-3011-queryPayForSCMS,robot-3011-refreshPlanByTimes,robot-3011-nomotor-unitedSaleEdit,robot-3011-nomotor-saveUnitedSale,robot-3011-insert"        }    }    s += ",robot-3011-checkRiskCode,robot-3011-editMainUwtFlag,robot-3011-editSubmitUndwrt,robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-showUndwrtMsgQ,robot-3011-showUndwrtMsgS"+            ",robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ,robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind,robot-3011-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3011-login,robot_3011_bj_initData,robot_3011_bj_queryModel,robot_3011_bj_getSaleTaxInfo,robot_3011_bj_getRealValue,robot_3011_bj_getPersonData,robot_3011_bj_addPersonData,robot_3011_bj_askCharge,robot_3011_bj_queryPayForXSFY,robot_3011_bj_getCagentCI,robot_3011_bj_getCagent,robot_3011_bj_queryPayForXSFY_req,robot_3011_bj_queryIlogEngage,robot_3011_bj_insureRefrenshPlan,robot_3011_bj_insure4S,robot-3011-uploadImage,robot_3011_bj_autoInsure,robot_3011_bj_showUndwrtMsgQ,robot_3011_bj_showUndwrtMsgS";       s += ",robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ,robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +                ",robot-3011-showCinsuredS,robot-3011-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3011', 'anda', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安达-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3011-qrcode_login,robot-3011-qrcode_printTwoBarCodeServlet,robot-3011-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3011-qrcode_login,robot-3011-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3011-qrcode_login,robot-3011-qrcode_editCheckFlag,robot-3011-qrcode_gotoJfcd,robot-3011-qrcode_prepareEditByJF,robot-3011-qrcode_getBusinessIn" +                ",robot-3011-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3011-qrcode_login,robot-3011-qrcode_editCheckFlag,robot-3011-qrcode_gotoJfcd,robot-3011-qrcode_prepareEditByJF,robot-3011-qrcode_getBusinessIn" +                ",robot-3011-qrcode_checkBeforeCalculate,robot-3011-qrcode_saveByJF,robot-3011-qrcode_getBusinessIn_alipay,robot-3011-qrcode_editFeeInfor,robot-3011-qrcode_editPayFeeByWeChat,robot-3011-qrcode_saveByWeChat,robot-3011-qrcode_save";		} else {					return  "robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-bj-editIDCardCheck,robot-3011-apply-selectIsNetProp,robot-3011-apply-saveCheckCode,robot-3011-qrcode_editCheckFlag,robot-3011-qrcode_gotoJfcd,robot-3011-qrcode_prepareEditByJF,robot-3011-qrcode_getBusinessIn" +",robot-3011-qrcode_checkBeforeCalculate,robot-3011-qrcode_saveByJF,robot-3011-qrcode_getBusinessIn_alipay,robot-3011-qrcode_editFeeInfor,robot-3011-qrcode_editPayFeeByWeChat,robot-3011-qrcode_saveByWeChat,robot-3011-qrcode_save";		}}    else {              return "robot-3011-qrcode_login,robot-3011-qrcode_editCheckFlag,robot-3011-qrcode_gotoJfcd,robot-3011-qrcode_prepareEditByJF,robot-3011-qrcode_getBusinessIn" +                ",robot-3011-qrcode_checkBeforeCalculate,robot-3011-qrcode_saveByJF,robot-3011-qrcode_getBusinessIn_alipay,robot-3011-qrcode_editFeeInfor,robot-3011-qrcode_editPayFeeByWeChat,robot-3011-qrcode_saveByWeChat,robot-3011-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3011', 'anda', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安达-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3011-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3011-qrcode_query_editCheckFlag,robot-3011-qrcode_query_gotoJfcd,robot-3011-qrcode_query_prepareEditByJF" +                ",robot-3011-qrcode_query_editMainInfor,robot-3011-qrcode_query_getBusinessIn,robot-3011-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ" +            ",robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind,robot-3011-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3011', 'anda', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安达-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-IdCarChekc" //申请验证码    else{        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3011', 'anda', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安达-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectPolicyefc,robot-3011-selectPolicybiz,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ" +            ",robot-3011-showCitemCarQ,robot-3011-showCinsuredQ,robot-3011-showCitemKindCI,robot-3011-browseProposalS,robot-3011-showCitemCarS" +            ",robot-3011-showCinsuredS,robot-3011-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3011', 'anda', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3011-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectProposalQ,robot-3011-selectProposalS,robot-3011-browseProposalQ,robot-3011-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3011', 'anda', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安达-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-bj-editIDCardCheck,robot-3011-apply-bj-IdCarChekc";    } else {        s = "robot-3011-qrcode_login,robot-3011-qrcode_editCheckFlag,robot-3011-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3011', 'anda', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安达报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3011_ask_charge,edi_3011_noMotor_quote"	} else {		return "edi_3011_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3011', 'anda', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安达-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3011_ask_charge,edi_3011_noMotor_quote,edi_3011_askInsure,edi_3011_uploadImg,edi_3011_submitInsure,edi_3011_noMotor_submit" 	  	} else {		return "edi_3011_ask_chargeold,edi_3011_askInsure,edi_3011_uploadImg,edi_3011_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3011', 'anda', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安达-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3011_ask_charge,edi_3011_noMotor_quote,edi_3011_askInsure,edi_3011_uploadImg" 	} else {		return "edi_3011_ask_chargeold,edi_3011_askInsure,edi_3011_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3011', 'anda', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安达-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3011_efc_policyinfo,edi_3011_biz_policyinfo,edi_3011_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3011', 'anda', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安达-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3011_efc_insurequery,edi_3011_biz_insurequery,edi_3011_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3011', 'anda', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安达短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-bj-editIDCardCheck,robot-3011-apply-saveCheckCode,robot-3011-apply-selectIsNetProp";    } else {        s = "robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3011', 'anda', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安达-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3011-bj-qrcode_login,robot-3011-apply-bj-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-bj-editIDCardCheck,robot-3011-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3011', 'anda', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安达-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3011_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3011', 'anda', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安达北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3011_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3011', 'anda', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安达北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3011_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3011', 'anda', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安达-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3011-login,robot-3011-prepareQueryCode,robot-3011-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3079', 'zhongyin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中银-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-IdCarChekc" //申请验证码    else{        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3079', 'zhongyin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中银-报价', 'def getTemplateGroup(dataSource) {    return "edi-3079-queryCar,edi-3079-xbQuery,edi-3079-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3079', 'zhongyin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中银-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3079-queryCar,edi-3079-xbQuery,edi-3079-askCharge,edi-3079-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3079', 'zhongyin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中银-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3079-queryCar,edi-3079-xbQuery,edi-3079-askCharge,edi-3079-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3079', 'zhongyin', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-中银-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3079-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3079', 'zhongyin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中银-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3079-login,robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3079-login,robot-3079-ObtainConfig,robot-3079-checkInsurePerson,robot-3079-changePerson,robot-3079-checkInsuredPerson,robot-3079-changePerson,robot-3079-prepareEdit," +                    "robot-3079-prepareQueryCode,robot-3079-selectProposalCar,robot-3079-browseProposalCar,robot-3079-browseProposalCarefc,robot-3079-selectRenewalPolicyNo"        }else{            s = "robot-3079-login,robot-3079-ObtainConfig,robot-3079-checkInsurePerson,robot-3079-changePerson,robot-3079-checkInsuredPerson,robot-3079-changePerson,robot-3079-prepareEdit," +                    "robot-3079-prepareQueryCode,robot-3079-browseProposalCar,robot-3079-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3079-VehicleModelList" //上海车型查询        }        s += ",robot-3079-queryPrepare,robot-3079-vehicleQuery,robot-3079-queryTaxAbateForPlat,robot-3079-calActualValue,robot-3079-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3079-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3079-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-nomotor-unitedSaleEdit,robot-3079-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3079-login,robot_3079_bj_initData,robot_3079_bj_queryModel,robot_3079_bj_getSaleTaxInfo,robot_3079_bj_getRealValue,robot_3079_bj_getPersonData,robot_3079_bj_addPersonData,robot_3079_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3079', 'zhongyin', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-中银-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3079-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3079-ObtainConfig,robot-3079-selectRenewal,robot-3079-editCengage,robot-3079-editCitemCar,robot-3079-editCinsured,robot-3079-renewalPolicy,robot-3079-renewalPolicyCI,robot-3079-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3079-VehicleModelList" //上海车型查询        }        s += ",robot-3079-vehicleQueryXB,robot-3079-queryTaxAbateForPlat,robot-3079-calActualValue,robot-3079-editCitemKind,robot-3079-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3079-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-refreshPlanByTimes,robot-3079-insert"            }else{                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3079-getMaxCsellFee,robot-3079-getPrpCseller,robot-3079-getPrpCsellerCI,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            s += ",robot-3079-getMaxCsellFee,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"        }    }else{        s +=",robot-3079-ObtainConfig,robot-3079-checkInsurePerson,robot-3079-changePerson,robot-3079-checkInsuredPerson,robot-3079-changePerson,robot-3079-prepareEdit,robot-3079-selectRenewalPolicyNo,robot-3079-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3079-VehicleModelList" //上海车型查询        }        s += ",robot-3079-queryPrepare,robot-3079-vehicleQuery,robot-3079-queryTaxAbateForPlat,robot-3079-calActualValue,robot-3079-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3079-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3079-calAnciInfo,robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            }else{                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-nomotor-unitedSaleEdit,robot-3079-nomotor-saveUnitedSale,robot-3079-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3079-getMaxCsellFee,robot-3079-getPrpCseller,robot-3079-getPrpCsellerCI,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            s += ",robot-3079-getMaxCsellFee,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-nomotor-unitedSaleEdit,robot-3079-nomotor-saveUnitedSale,robot-3079-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3079-login,robot_3079_bj_initData,robot_3079_bj_queryModel,robot_3079_bj_getSaleTaxInfo,robot_3079_bj_getRealValue,robot_3079_bj_getPersonData,robot_3079_bj_addPersonData,robot_3079_bj_askCharge,robot_3079_bj_queryPayForXSFY,robot_3079_bj_getCagentCI,robot_3079_bj_getCagent,robot_3079_bj_queryPayForXSFY_req,robot_3079_bj_queryIlogEngage,robot_3079_bj_insureRefrenshPlan,robot_3079_bj_insure4S,robot-3079-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3079', 'zhongyin', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-中银-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ" +            ",robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind,robot-3079-nomotor-query,robot-3079-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3079', 'zhongyin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中银-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ" +            ",robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind,robot-3079-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3079', 'zhongyin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中银-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ" +            ",robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind,robot-3079-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3079', 'zhongyin', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "中银保险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-中银-电销', 'def getTemplateGroup(dataSource){    return "robot-3079-pureESale_Login,robot-3079-pureESale_Welcome,robot-3079-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3079', 'zhongyin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中银续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3079-login,robot-3079-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3079', 'zhongyin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-中银-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3079-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3079-ObtainConfig,robot-3079-selectRenewal,robot-3079-editCengage,robot-3079-editCitemCar,robot-3079-editCinsured,robot-3079-renewalPolicy,robot-3079-renewalPolicyCI,robot-3079-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3079-VehicleModelList" //上海车型查询        }        s += ",robot-3079-vehicleQueryXB,robot-3079-queryTaxAbateForPlat,robot-3079-calActualValue,robot-3079-editCitemKind,robot-3079-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3079-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-refreshPlanByTimes,robot-3079-insert"            }else{                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3079-calAnciInfo,robot-3079-getMaxCsellFee,robot-3079-getPrpCseller,robot-3079-getPrpCsellerCI,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            s += ",robot-3079-getMaxCsellFee,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"        }    }else{        s += ",robot-3079-ObtainConfig,robot-3079-checkInsurePerson,robot-3079-changePerson,robot-3079-checkInsuredPerson,robot-3079-changePerson,robot-3079-prepareEdit,robot-3079-editCengage,robot-3079-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3079-queryVehiclePMCheck,robot-3079-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3079-VehicleModelList" //上海车型查询        }        s += ",robot-3079-queryPrepare,robot-3079-vehicleQuery,robot-3079-queryTaxAbateForPlat,robot-3079-calActualValue,robot-3079-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3079-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3079-calAnciInfo,robot-3079-queryPayFor,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            }else{                s += ",robot-3079-calAnciInfo,robot-3079-checkAgentType,robot-3079-queryPayForSCMS,robot-3079-getCagent,robot-3079-getCagentCI,robot-3079-refreshPlanByTimes,robot-3079-nomotor-unitedSaleEdit,robot-3079-nomotor-saveUnitedSale,robot-3079-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3079-calAnciInfo,robot-3079-getMaxCsellFee,robot-3079-getPrpCseller,robot-3079-getPrpCsellerCI,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-insert"            s += ",robot-3079-getMaxCsellFee,robot-3079-queryPayForSCMS,robot-3079-refreshPlanByTimes,robot-3079-nomotor-unitedSaleEdit,robot-3079-nomotor-saveUnitedSale,robot-3079-insert"        }    }    s += ",robot-3079-checkRiskCode,robot-3079-editMainUwtFlag,robot-3079-editSubmitUndwrt,robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-showUndwrtMsgQ,robot-3079-showUndwrtMsgS"+            ",robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ,robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind,robot-3079-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3079-login,robot_3079_bj_initData,robot_3079_bj_queryModel,robot_3079_bj_getSaleTaxInfo,robot_3079_bj_getRealValue,robot_3079_bj_getPersonData,robot_3079_bj_addPersonData,robot_3079_bj_askCharge,robot_3079_bj_queryPayForXSFY,robot_3079_bj_getCagentCI,robot_3079_bj_getCagent,robot_3079_bj_queryPayForXSFY_req,robot_3079_bj_queryIlogEngage,robot_3079_bj_insureRefrenshPlan,robot_3079_bj_insure4S,robot-3079-uploadImage,robot_3079_bj_autoInsure,robot_3079_bj_showUndwrtMsgQ,robot_3079_bj_showUndwrtMsgS";       s += ",robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ,robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +                ",robot-3079-showCinsuredS,robot-3079-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3079', 'zhongyin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中银-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3079-qrcode_login,robot-3079-qrcode_printTwoBarCodeServlet,robot-3079-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3079-qrcode_login,robot-3079-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3079-qrcode_login,robot-3079-qrcode_editCheckFlag,robot-3079-qrcode_gotoJfcd,robot-3079-qrcode_prepareEditByJF,robot-3079-qrcode_getBusinessIn" +                ",robot-3079-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3079-qrcode_login,robot-3079-qrcode_editCheckFlag,robot-3079-qrcode_gotoJfcd,robot-3079-qrcode_prepareEditByJF,robot-3079-qrcode_getBusinessIn" +                ",robot-3079-qrcode_checkBeforeCalculate,robot-3079-qrcode_saveByJF,robot-3079-qrcode_getBusinessIn_alipay,robot-3079-qrcode_editFeeInfor,robot-3079-qrcode_editPayFeeByWeChat,robot-3079-qrcode_saveByWeChat,robot-3079-qrcode_save";		} else {					return  "robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-bj-editIDCardCheck,robot-3079-apply-selectIsNetProp,robot-3079-apply-saveCheckCode,robot-3079-qrcode_editCheckFlag,robot-3079-qrcode_gotoJfcd,robot-3079-qrcode_prepareEditByJF,robot-3079-qrcode_getBusinessIn" +",robot-3079-qrcode_checkBeforeCalculate,robot-3079-qrcode_saveByJF,robot-3079-qrcode_getBusinessIn_alipay,robot-3079-qrcode_editFeeInfor,robot-3079-qrcode_editPayFeeByWeChat,robot-3079-qrcode_saveByWeChat,robot-3079-qrcode_save";		}}    else {              return "robot-3079-qrcode_login,robot-3079-qrcode_editCheckFlag,robot-3079-qrcode_gotoJfcd,robot-3079-qrcode_prepareEditByJF,robot-3079-qrcode_getBusinessIn" +                ",robot-3079-qrcode_checkBeforeCalculate,robot-3079-qrcode_saveByJF,robot-3079-qrcode_getBusinessIn_alipay,robot-3079-qrcode_editFeeInfor,robot-3079-qrcode_editPayFeeByWeChat,robot-3079-qrcode_saveByWeChat,robot-3079-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3079', 'zhongyin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中银-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3079-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3079-qrcode_query_editCheckFlag,robot-3079-qrcode_query_gotoJfcd,robot-3079-qrcode_query_prepareEditByJF" +                ",robot-3079-qrcode_query_editMainInfor,robot-3079-qrcode_query_getBusinessIn,robot-3079-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ" +            ",robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind,robot-3079-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3079', 'zhongyin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中银-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-IdCarChekc" //申请验证码    else{        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3079', 'zhongyin', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-中银-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectPolicyefc,robot-3079-selectPolicybiz,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ" +            ",robot-3079-showCitemCarQ,robot-3079-showCinsuredQ,robot-3079-showCitemKindCI,robot-3079-browseProposalS,robot-3079-showCitemCarS" +            ",robot-3079-showCinsuredS,robot-3079-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3079', 'zhongyin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3079-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectProposalQ,robot-3079-selectProposalS,robot-3079-browseProposalQ,robot-3079-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3079', 'zhongyin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中银-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-bj-editIDCardCheck,robot-3079-apply-bj-IdCarChekc";    } else {        s = "robot-3079-qrcode_login,robot-3079-qrcode_editCheckFlag,robot-3079-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3079', 'zhongyin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi中银报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3079_ask_charge,edi_3079_noMotor_quote"	} else {		return "edi_3079_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3079', 'zhongyin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中银-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3079_ask_charge,edi_3079_noMotor_quote,edi_3079_askInsure,edi_3079_uploadImg,edi_3079_submitInsure,edi_3079_noMotor_submit" 	  	} else {		return "edi_3079_ask_chargeold,edi_3079_askInsure,edi_3079_uploadImg,edi_3079_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3079', 'zhongyin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中银-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3079_ask_charge,edi_3079_noMotor_quote,edi_3079_askInsure,edi_3079_uploadImg" 	} else {		return "edi_3079_ask_chargeold,edi_3079_askInsure,edi_3079_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3079', 'zhongyin', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-中银-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3079_efc_policyinfo,edi_3079_biz_policyinfo,edi_3079_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3079', 'zhongyin', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-中银-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3079_efc_insurequery,edi_3079_biz_insurequery,edi_3079_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3079', 'zhongyin', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京中银短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-bj-editIDCardCheck,robot-3079-apply-saveCheckCode,robot-3079-apply-selectIsNetProp";    } else {        s = "robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3079', 'zhongyin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中银-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3079-bj-qrcode_login,robot-3079-apply-bj-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-bj-editIDCardCheck,robot-3079-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3079', 'zhongyin', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-中银-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3079_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3079', 'zhongyin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中银北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3079_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3079', 'zhongyin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中银北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3079_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3079', 'zhongyin', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-中银-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3079-login,robot-3079-prepareQueryCode,robot-3079-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3042', 'guotai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-IdCarChekc" //申请验证码    else{        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3042', 'guotai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国泰-报价', 'def getTemplateGroup(dataSource) {    return "edi-3042-queryCar,edi-3042-xbQuery,edi-3042-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3042', 'guotai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国泰-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3042-queryCar,edi-3042-xbQuery,edi-3042-askCharge,edi-3042-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3042', 'guotai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国泰-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3042-queryCar,edi-3042-xbQuery,edi-3042-askCharge,edi-3042-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3042', 'guotai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-国泰-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3042-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3042', 'guotai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国泰-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3042-login,robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3042-login,robot-3042-ObtainConfig,robot-3042-checkInsurePerson,robot-3042-changePerson,robot-3042-checkInsuredPerson,robot-3042-changePerson,robot-3042-prepareEdit," +                    "robot-3042-prepareQueryCode,robot-3042-selectProposalCar,robot-3042-browseProposalCar,robot-3042-browseProposalCarefc,robot-3042-selectRenewalPolicyNo"        }else{            s = "robot-3042-login,robot-3042-ObtainConfig,robot-3042-checkInsurePerson,robot-3042-changePerson,robot-3042-checkInsuredPerson,robot-3042-changePerson,robot-3042-prepareEdit," +                    "robot-3042-prepareQueryCode,robot-3042-browseProposalCar,robot-3042-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3042-VehicleModelList" //上海车型查询        }        s += ",robot-3042-queryPrepare,robot-3042-vehicleQuery,robot-3042-queryTaxAbateForPlat,robot-3042-calActualValue,robot-3042-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3042-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3042-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-nomotor-unitedSaleEdit,robot-3042-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3042-login,robot_3042_bj_initData,robot_3042_bj_queryModel,robot_3042_bj_getSaleTaxInfo,robot_3042_bj_getRealValue,robot_3042_bj_getPersonData,robot_3042_bj_addPersonData,robot_3042_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3042', 'guotai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-国泰-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3042-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3042-ObtainConfig,robot-3042-selectRenewal,robot-3042-editCengage,robot-3042-editCitemCar,robot-3042-editCinsured,robot-3042-renewalPolicy,robot-3042-renewalPolicyCI,robot-3042-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3042-VehicleModelList" //上海车型查询        }        s += ",robot-3042-vehicleQueryXB,robot-3042-queryTaxAbateForPlat,robot-3042-calActualValue,robot-3042-editCitemKind,robot-3042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3042-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-refreshPlanByTimes,robot-3042-insert"            }else{                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3042-getMaxCsellFee,robot-3042-getPrpCseller,robot-3042-getPrpCsellerCI,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            s += ",robot-3042-getMaxCsellFee,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"        }    }else{        s +=",robot-3042-ObtainConfig,robot-3042-checkInsurePerson,robot-3042-changePerson,robot-3042-checkInsuredPerson,robot-3042-changePerson,robot-3042-prepareEdit,robot-3042-selectRenewalPolicyNo,robot-3042-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3042-VehicleModelList" //上海车型查询        }        s += ",robot-3042-queryPrepare,robot-3042-vehicleQuery,robot-3042-queryTaxAbateForPlat,robot-3042-calActualValue,robot-3042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3042-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3042-calAnciInfo,robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            }else{                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-nomotor-unitedSaleEdit,robot-3042-nomotor-saveUnitedSale,robot-3042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3042-getMaxCsellFee,robot-3042-getPrpCseller,robot-3042-getPrpCsellerCI,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            s += ",robot-3042-getMaxCsellFee,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-nomotor-unitedSaleEdit,robot-3042-nomotor-saveUnitedSale,robot-3042-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3042-login,robot_3042_bj_initData,robot_3042_bj_queryModel,robot_3042_bj_getSaleTaxInfo,robot_3042_bj_getRealValue,robot_3042_bj_getPersonData,robot_3042_bj_addPersonData,robot_3042_bj_askCharge,robot_3042_bj_queryPayForXSFY,robot_3042_bj_getCagentCI,robot_3042_bj_getCagent,robot_3042_bj_queryPayForXSFY_req,robot_3042_bj_queryIlogEngage,robot_3042_bj_insureRefrenshPlan,robot_3042_bj_insure4S,robot-3042-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3042', 'guotai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-国泰-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ" +            ",robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind,robot-3042-nomotor-query,robot-3042-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3042', 'guotai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国泰-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ" +            ",robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind,robot-3042-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3042', 'guotai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ" +            ",robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind,robot-3042-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3042', 'guotai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "国泰财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-国泰-电销', 'def getTemplateGroup(dataSource){    return "robot-3042-pureESale_Login,robot-3042-pureESale_Welcome,robot-3042-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3042', 'guotai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国泰续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3042-login,robot-3042-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3042', 'guotai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-国泰-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3042-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3042-ObtainConfig,robot-3042-selectRenewal,robot-3042-editCengage,robot-3042-editCitemCar,robot-3042-editCinsured,robot-3042-renewalPolicy,robot-3042-renewalPolicyCI,robot-3042-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3042-VehicleModelList" //上海车型查询        }        s += ",robot-3042-vehicleQueryXB,robot-3042-queryTaxAbateForPlat,robot-3042-calActualValue,robot-3042-editCitemKind,robot-3042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3042-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-refreshPlanByTimes,robot-3042-insert"            }else{                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3042-calAnciInfo,robot-3042-getMaxCsellFee,robot-3042-getPrpCseller,robot-3042-getPrpCsellerCI,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            s += ",robot-3042-getMaxCsellFee,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"        }    }else{        s += ",robot-3042-ObtainConfig,robot-3042-checkInsurePerson,robot-3042-changePerson,robot-3042-checkInsuredPerson,robot-3042-changePerson,robot-3042-prepareEdit,robot-3042-editCengage,robot-3042-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3042-queryVehiclePMCheck,robot-3042-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3042-VehicleModelList" //上海车型查询        }        s += ",robot-3042-queryPrepare,robot-3042-vehicleQuery,robot-3042-queryTaxAbateForPlat,robot-3042-calActualValue,robot-3042-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3042-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3042-calAnciInfo,robot-3042-queryPayFor,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            }else{                s += ",robot-3042-calAnciInfo,robot-3042-checkAgentType,robot-3042-queryPayForSCMS,robot-3042-getCagent,robot-3042-getCagentCI,robot-3042-refreshPlanByTimes,robot-3042-nomotor-unitedSaleEdit,robot-3042-nomotor-saveUnitedSale,robot-3042-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3042-calAnciInfo,robot-3042-getMaxCsellFee,robot-3042-getPrpCseller,robot-3042-getPrpCsellerCI,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-insert"            s += ",robot-3042-getMaxCsellFee,robot-3042-queryPayForSCMS,robot-3042-refreshPlanByTimes,robot-3042-nomotor-unitedSaleEdit,robot-3042-nomotor-saveUnitedSale,robot-3042-insert"        }    }    s += ",robot-3042-checkRiskCode,robot-3042-editMainUwtFlag,robot-3042-editSubmitUndwrt,robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-showUndwrtMsgQ,robot-3042-showUndwrtMsgS"+            ",robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ,robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind,robot-3042-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3042-login,robot_3042_bj_initData,robot_3042_bj_queryModel,robot_3042_bj_getSaleTaxInfo,robot_3042_bj_getRealValue,robot_3042_bj_getPersonData,robot_3042_bj_addPersonData,robot_3042_bj_askCharge,robot_3042_bj_queryPayForXSFY,robot_3042_bj_getCagentCI,robot_3042_bj_getCagent,robot_3042_bj_queryPayForXSFY_req,robot_3042_bj_queryIlogEngage,robot_3042_bj_insureRefrenshPlan,robot_3042_bj_insure4S,robot-3042-uploadImage,robot_3042_bj_autoInsure,robot_3042_bj_showUndwrtMsgQ,robot_3042_bj_showUndwrtMsgS";       s += ",robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ,robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +                ",robot-3042-showCinsuredS,robot-3042-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3042', 'guotai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国泰-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3042-qrcode_login,robot-3042-qrcode_printTwoBarCodeServlet,robot-3042-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3042-qrcode_login,robot-3042-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3042-qrcode_login,robot-3042-qrcode_editCheckFlag,robot-3042-qrcode_gotoJfcd,robot-3042-qrcode_prepareEditByJF,robot-3042-qrcode_getBusinessIn" +                ",robot-3042-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3042-qrcode_login,robot-3042-qrcode_editCheckFlag,robot-3042-qrcode_gotoJfcd,robot-3042-qrcode_prepareEditByJF,robot-3042-qrcode_getBusinessIn" +                ",robot-3042-qrcode_checkBeforeCalculate,robot-3042-qrcode_saveByJF,robot-3042-qrcode_getBusinessIn_alipay,robot-3042-qrcode_editFeeInfor,robot-3042-qrcode_editPayFeeByWeChat,robot-3042-qrcode_saveByWeChat,robot-3042-qrcode_save";		} else {					return  "robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-bj-editIDCardCheck,robot-3042-apply-selectIsNetProp,robot-3042-apply-saveCheckCode,robot-3042-qrcode_editCheckFlag,robot-3042-qrcode_gotoJfcd,robot-3042-qrcode_prepareEditByJF,robot-3042-qrcode_getBusinessIn" +",robot-3042-qrcode_checkBeforeCalculate,robot-3042-qrcode_saveByJF,robot-3042-qrcode_getBusinessIn_alipay,robot-3042-qrcode_editFeeInfor,robot-3042-qrcode_editPayFeeByWeChat,robot-3042-qrcode_saveByWeChat,robot-3042-qrcode_save";		}}    else {              return "robot-3042-qrcode_login,robot-3042-qrcode_editCheckFlag,robot-3042-qrcode_gotoJfcd,robot-3042-qrcode_prepareEditByJF,robot-3042-qrcode_getBusinessIn" +                ",robot-3042-qrcode_checkBeforeCalculate,robot-3042-qrcode_saveByJF,robot-3042-qrcode_getBusinessIn_alipay,robot-3042-qrcode_editFeeInfor,robot-3042-qrcode_editPayFeeByWeChat,robot-3042-qrcode_saveByWeChat,robot-3042-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3042', 'guotai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国泰-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3042-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3042-qrcode_query_editCheckFlag,robot-3042-qrcode_query_gotoJfcd,robot-3042-qrcode_query_prepareEditByJF" +                ",robot-3042-qrcode_query_editMainInfor,robot-3042-qrcode_query_getBusinessIn,robot-3042-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ" +            ",robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind,robot-3042-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3042', 'guotai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-IdCarChekc" //申请验证码    else{        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3042', 'guotai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-国泰-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectPolicyefc,robot-3042-selectPolicybiz,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ" +            ",robot-3042-showCitemCarQ,robot-3042-showCinsuredQ,robot-3042-showCitemKindCI,robot-3042-browseProposalS,robot-3042-showCitemCarS" +            ",robot-3042-showCinsuredS,robot-3042-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3042', 'guotai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3042-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectProposalQ,robot-3042-selectProposalS,robot-3042-browseProposalQ,robot-3042-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3042', 'guotai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国泰-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-bj-editIDCardCheck,robot-3042-apply-bj-IdCarChekc";    } else {        s = "robot-3042-qrcode_login,robot-3042-qrcode_editCheckFlag,robot-3042-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3042', 'guotai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi国泰报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3042_ask_charge,edi_3042_noMotor_quote"	} else {		return "edi_3042_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3042', 'guotai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国泰-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3042_ask_charge,edi_3042_noMotor_quote,edi_3042_askInsure,edi_3042_uploadImg,edi_3042_submitInsure,edi_3042_noMotor_submit" 	  	} else {		return "edi_3042_ask_chargeold,edi_3042_askInsure,edi_3042_uploadImg,edi_3042_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3042', 'guotai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国泰-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3042_ask_charge,edi_3042_noMotor_quote,edi_3042_askInsure,edi_3042_uploadImg" 	} else {		return "edi_3042_ask_chargeold,edi_3042_askInsure,edi_3042_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3042', 'guotai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-国泰-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3042_efc_policyinfo,edi_3042_biz_policyinfo,edi_3042_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3042', 'guotai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-国泰-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3042_efc_insurequery,edi_3042_biz_insurequery,edi_3042_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3042', 'guotai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京国泰短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-bj-editIDCardCheck,robot-3042-apply-saveCheckCode,robot-3042-apply-selectIsNetProp";    } else {        s = "robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3042', 'guotai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国泰-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3042-bj-qrcode_login,robot-3042-apply-bj-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-bj-editIDCardCheck,robot-3042-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3042', 'guotai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-国泰-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3042_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3042', 'guotai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国泰北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3042_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3042', 'guotai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国泰北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3042_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3042', 'guotai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-国泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3042-login,robot-3042-prepareQueryCode,robot-3042-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3047', 'haixiajinqiao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-海峡金桥-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-IdCarChekc" //申请验证码    else{        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3047', 'haixiajinqiao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-报价', 'def getTemplateGroup(dataSource) {    return "edi-3047-queryCar,edi-3047-xbQuery,edi-3047-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3047', 'haixiajinqiao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3047-queryCar,edi-3047-xbQuery,edi-3047-askCharge,edi-3047-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3047', 'haixiajinqiao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3047-queryCar,edi-3047-xbQuery,edi-3047-askCharge,edi-3047-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3047', 'haixiajinqiao', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3047-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3047', 'haixiajinqiao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3047-login,robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3047-login,robot-3047-ObtainConfig,robot-3047-checkInsurePerson,robot-3047-changePerson,robot-3047-checkInsuredPerson,robot-3047-changePerson,robot-3047-prepareEdit," +                    "robot-3047-prepareQueryCode,robot-3047-selectProposalCar,robot-3047-browseProposalCar,robot-3047-browseProposalCarefc,robot-3047-selectRenewalPolicyNo"        }else{            s = "robot-3047-login,robot-3047-ObtainConfig,robot-3047-checkInsurePerson,robot-3047-changePerson,robot-3047-checkInsuredPerson,robot-3047-changePerson,robot-3047-prepareEdit," +                    "robot-3047-prepareQueryCode,robot-3047-browseProposalCar,robot-3047-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3047-VehicleModelList" //上海车型查询        }        s += ",robot-3047-queryPrepare,robot-3047-vehicleQuery,robot-3047-queryTaxAbateForPlat,robot-3047-calActualValue,robot-3047-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3047-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3047-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-nomotor-unitedSaleEdit,robot-3047-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3047-login,robot_3047_bj_initData,robot_3047_bj_queryModel,robot_3047_bj_getSaleTaxInfo,robot_3047_bj_getRealValue,robot_3047_bj_getPersonData,robot_3047_bj_addPersonData,robot_3047_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3047', 'haixiajinqiao', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3047-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3047-ObtainConfig,robot-3047-selectRenewal,robot-3047-editCengage,robot-3047-editCitemCar,robot-3047-editCinsured,robot-3047-renewalPolicy,robot-3047-renewalPolicyCI,robot-3047-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3047-VehicleModelList" //上海车型查询        }        s += ",robot-3047-vehicleQueryXB,robot-3047-queryTaxAbateForPlat,robot-3047-calActualValue,robot-3047-editCitemKind,robot-3047-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3047-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-refreshPlanByTimes,robot-3047-insert"            }else{                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3047-getMaxCsellFee,robot-3047-getPrpCseller,robot-3047-getPrpCsellerCI,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            s += ",robot-3047-getMaxCsellFee,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"        }    }else{        s +=",robot-3047-ObtainConfig,robot-3047-checkInsurePerson,robot-3047-changePerson,robot-3047-checkInsuredPerson,robot-3047-changePerson,robot-3047-prepareEdit,robot-3047-selectRenewalPolicyNo,robot-3047-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3047-VehicleModelList" //上海车型查询        }        s += ",robot-3047-queryPrepare,robot-3047-vehicleQuery,robot-3047-queryTaxAbateForPlat,robot-3047-calActualValue,robot-3047-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3047-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3047-calAnciInfo,robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            }else{                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-nomotor-unitedSaleEdit,robot-3047-nomotor-saveUnitedSale,robot-3047-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3047-getMaxCsellFee,robot-3047-getPrpCseller,robot-3047-getPrpCsellerCI,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            s += ",robot-3047-getMaxCsellFee,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-nomotor-unitedSaleEdit,robot-3047-nomotor-saveUnitedSale,robot-3047-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3047-login,robot_3047_bj_initData,robot_3047_bj_queryModel,robot_3047_bj_getSaleTaxInfo,robot_3047_bj_getRealValue,robot_3047_bj_getPersonData,robot_3047_bj_addPersonData,robot_3047_bj_askCharge,robot_3047_bj_queryPayForXSFY,robot_3047_bj_getCagentCI,robot_3047_bj_getCagent,robot_3047_bj_queryPayForXSFY_req,robot_3047_bj_queryIlogEngage,robot_3047_bj_insureRefrenshPlan,robot_3047_bj_insure4S,robot-3047-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3047', 'haixiajinqiao', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ" +            ",robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind,robot-3047-nomotor-query,robot-3047-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3047', 'haixiajinqiao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ" +            ",robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind,robot-3047-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3047', 'haixiajinqiao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ" +            ",robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind,robot-3047-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3047', 'haixiajinqiao', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "海峡金桥财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-海峡金桥-电销', 'def getTemplateGroup(dataSource){    return "robot-3047-pureESale_Login,robot-3047-pureESale_Welcome,robot-3047-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3047', 'haixiajinqiao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3047-login,robot-3047-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3047', 'haixiajinqiao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3047-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3047-ObtainConfig,robot-3047-selectRenewal,robot-3047-editCengage,robot-3047-editCitemCar,robot-3047-editCinsured,robot-3047-renewalPolicy,robot-3047-renewalPolicyCI,robot-3047-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3047-VehicleModelList" //上海车型查询        }        s += ",robot-3047-vehicleQueryXB,robot-3047-queryTaxAbateForPlat,robot-3047-calActualValue,robot-3047-editCitemKind,robot-3047-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3047-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-refreshPlanByTimes,robot-3047-insert"            }else{                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3047-calAnciInfo,robot-3047-getMaxCsellFee,robot-3047-getPrpCseller,robot-3047-getPrpCsellerCI,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            s += ",robot-3047-getMaxCsellFee,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"        }    }else{        s += ",robot-3047-ObtainConfig,robot-3047-checkInsurePerson,robot-3047-changePerson,robot-3047-checkInsuredPerson,robot-3047-changePerson,robot-3047-prepareEdit,robot-3047-editCengage,robot-3047-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3047-queryVehiclePMCheck,robot-3047-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3047-VehicleModelList" //上海车型查询        }        s += ",robot-3047-queryPrepare,robot-3047-vehicleQuery,robot-3047-queryTaxAbateForPlat,robot-3047-calActualValue,robot-3047-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3047-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3047-calAnciInfo,robot-3047-queryPayFor,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            }else{                s += ",robot-3047-calAnciInfo,robot-3047-checkAgentType,robot-3047-queryPayForSCMS,robot-3047-getCagent,robot-3047-getCagentCI,robot-3047-refreshPlanByTimes,robot-3047-nomotor-unitedSaleEdit,robot-3047-nomotor-saveUnitedSale,robot-3047-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3047-calAnciInfo,robot-3047-getMaxCsellFee,robot-3047-getPrpCseller,robot-3047-getPrpCsellerCI,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-insert"            s += ",robot-3047-getMaxCsellFee,robot-3047-queryPayForSCMS,robot-3047-refreshPlanByTimes,robot-3047-nomotor-unitedSaleEdit,robot-3047-nomotor-saveUnitedSale,robot-3047-insert"        }    }    s += ",robot-3047-checkRiskCode,robot-3047-editMainUwtFlag,robot-3047-editSubmitUndwrt,robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-showUndwrtMsgQ,robot-3047-showUndwrtMsgS"+            ",robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ,robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind,robot-3047-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3047-login,robot_3047_bj_initData,robot_3047_bj_queryModel,robot_3047_bj_getSaleTaxInfo,robot_3047_bj_getRealValue,robot_3047_bj_getPersonData,robot_3047_bj_addPersonData,robot_3047_bj_askCharge,robot_3047_bj_queryPayForXSFY,robot_3047_bj_getCagentCI,robot_3047_bj_getCagent,robot_3047_bj_queryPayForXSFY_req,robot_3047_bj_queryIlogEngage,robot_3047_bj_insureRefrenshPlan,robot_3047_bj_insure4S,robot-3047-uploadImage,robot_3047_bj_autoInsure,robot_3047_bj_showUndwrtMsgQ,robot_3047_bj_showUndwrtMsgS";       s += ",robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ,robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +                ",robot-3047-showCinsuredS,robot-3047-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3047', 'haixiajinqiao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3047-qrcode_login,robot-3047-qrcode_printTwoBarCodeServlet,robot-3047-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3047-qrcode_login,robot-3047-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3047-qrcode_login,robot-3047-qrcode_editCheckFlag,robot-3047-qrcode_gotoJfcd,robot-3047-qrcode_prepareEditByJF,robot-3047-qrcode_getBusinessIn" +                ",robot-3047-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3047-qrcode_login,robot-3047-qrcode_editCheckFlag,robot-3047-qrcode_gotoJfcd,robot-3047-qrcode_prepareEditByJF,robot-3047-qrcode_getBusinessIn" +                ",robot-3047-qrcode_checkBeforeCalculate,robot-3047-qrcode_saveByJF,robot-3047-qrcode_getBusinessIn_alipay,robot-3047-qrcode_editFeeInfor,robot-3047-qrcode_editPayFeeByWeChat,robot-3047-qrcode_saveByWeChat,robot-3047-qrcode_save";		} else {					return  "robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-bj-editIDCardCheck,robot-3047-apply-selectIsNetProp,robot-3047-apply-saveCheckCode,robot-3047-qrcode_editCheckFlag,robot-3047-qrcode_gotoJfcd,robot-3047-qrcode_prepareEditByJF,robot-3047-qrcode_getBusinessIn" +",robot-3047-qrcode_checkBeforeCalculate,robot-3047-qrcode_saveByJF,robot-3047-qrcode_getBusinessIn_alipay,robot-3047-qrcode_editFeeInfor,robot-3047-qrcode_editPayFeeByWeChat,robot-3047-qrcode_saveByWeChat,robot-3047-qrcode_save";		}}    else {              return "robot-3047-qrcode_login,robot-3047-qrcode_editCheckFlag,robot-3047-qrcode_gotoJfcd,robot-3047-qrcode_prepareEditByJF,robot-3047-qrcode_getBusinessIn" +                ",robot-3047-qrcode_checkBeforeCalculate,robot-3047-qrcode_saveByJF,robot-3047-qrcode_getBusinessIn_alipay,robot-3047-qrcode_editFeeInfor,robot-3047-qrcode_editPayFeeByWeChat,robot-3047-qrcode_saveByWeChat,robot-3047-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3047', 'haixiajinqiao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3047-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3047-qrcode_query_editCheckFlag,robot-3047-qrcode_query_gotoJfcd,robot-3047-qrcode_query_prepareEditByJF" +                ",robot-3047-qrcode_query_editMainInfor,robot-3047-qrcode_query_getBusinessIn,robot-3047-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ" +            ",robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind,robot-3047-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3047', 'haixiajinqiao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-海峡金桥-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-IdCarChekc" //申请验证码    else{        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3047', 'haixiajinqiao', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectPolicyefc,robot-3047-selectPolicybiz,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ" +            ",robot-3047-showCitemCarQ,robot-3047-showCinsuredQ,robot-3047-showCitemKindCI,robot-3047-browseProposalS,robot-3047-showCitemCarS" +            ",robot-3047-showCinsuredS,robot-3047-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3047', 'haixiajinqiao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3047-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectProposalQ,robot-3047-selectProposalS,robot-3047-browseProposalQ,robot-3047-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3047', 'haixiajinqiao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-bj-editIDCardCheck,robot-3047-apply-bj-IdCarChekc";    } else {        s = "robot-3047-qrcode_login,robot-3047-qrcode_editCheckFlag,robot-3047-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3047', 'haixiajinqiao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi海峡金桥报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3047_ask_charge,edi_3047_noMotor_quote"	} else {		return "edi_3047_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3047', 'haixiajinqiao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3047_ask_charge,edi_3047_noMotor_quote,edi_3047_askInsure,edi_3047_uploadImg,edi_3047_submitInsure,edi_3047_noMotor_submit" 	  	} else {		return "edi_3047_ask_chargeold,edi_3047_askInsure,edi_3047_uploadImg,edi_3047_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3047', 'haixiajinqiao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3047_ask_charge,edi_3047_noMotor_quote,edi_3047_askInsure,edi_3047_uploadImg" 	} else {		return "edi_3047_ask_chargeold,edi_3047_askInsure,edi_3047_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3047', 'haixiajinqiao', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3047_efc_policyinfo,edi_3047_biz_policyinfo,edi_3047_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3047', 'haixiajinqiao', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3047_efc_insurequery,edi_3047_biz_insurequery,edi_3047_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3047', 'haixiajinqiao', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京海峡金桥短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-bj-editIDCardCheck,robot-3047-apply-saveCheckCode,robot-3047-apply-selectIsNetProp";    } else {        s = "robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3047', 'haixiajinqiao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3047-bj-qrcode_login,robot-3047-apply-bj-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-bj-editIDCardCheck,robot-3047-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3047', 'haixiajinqiao', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3047_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3047', 'haixiajinqiao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi海峡金桥北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3047_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3047', 'haixiajinqiao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi海峡金桥北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3047_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3047', 'haixiajinqiao', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-海峡金桥-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3047-login,robot-3047-prepareQueryCode,robot-3047-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
