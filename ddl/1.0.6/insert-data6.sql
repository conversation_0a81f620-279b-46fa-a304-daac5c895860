INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2005', 'renbao', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-人保-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-IdCarChekc,robot-2005-apply-download" //电子保单下载    else{        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2005', 'renbao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-人保-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2005-queryCar,edi-2005-xbQuery,edi-2005-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2026', 'ansheng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安盛-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-IdCarChekc,robot-2026-apply-download" //电子保单下载    else{        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2026', 'ansheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安盛-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2026-queryCar,edi-2026-xbQuery,edi-2026-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2021', 'dadi', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-大地-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-IdCarChekc,robot-2021-apply-download" //电子保单下载    else{        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2021', 'dadi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-大地-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2021-queryCar,edi-2021-xbQuery,edi-2021-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2088', 'dinghe', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-鼎和-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-IdCarChekc,robot-2088-apply-download" //电子保单下载    else{        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2088', 'dinghe', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-鼎和-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2088-queryCar,edi-2088-xbQuery,edi-2088-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2085', 'libao', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-利宝-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-IdCarChekc,robot-2085-apply-download" //电子保单下载    else{        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2085', 'libao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-利宝-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2085-queryCar,edi-2085-xbQuery,edi-2085-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2016', 'taiping', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-太平-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-IdCarChekc,robot-2016-apply-download" //电子保单下载    else{        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2016', 'taiping', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-太平-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2016-queryCar,edi-2016-xbQuery,edi-2016-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2011', 'taipingyang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-太平洋-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-IdCarChekc,robot-2011-apply-download" //电子保单下载    else{        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2011', 'taipingyang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-太平洋-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2011-queryCar,edi-2011-xbQuery,edi-2011-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2066', 'yatai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-亚太-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-IdCarChekc,robot-2066-apply-download" //电子保单下载    else{        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2066', 'yatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-亚太-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2066-queryCar,edi-2066-xbQuery,edi-2066-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2044', 'zhongcheng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-众诚-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-IdCarChekc,robot-2044-apply-download" //电子保单下载    else{        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-众诚-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2044-queryCar,edi-2044-xbQuery,edi-2044-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2007', 'pingan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-平安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-IdCarChekc,robot-2007-apply-download" //电子保单下载    else{        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2007', 'pingan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-平安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2007-queryCar,edi-2007-xbQuery,edi-2007-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2046', 'yongan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-永安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-IdCarChekc,robot-2046-apply-download" //电子保单下载    else{        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2046', 'yongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-永安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2046-queryCar,edi-2046-xbQuery,edi-2046-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2043', 'huaan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-华安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-IdCarChekc,robot-2043-apply-download" //电子保单下载    else{        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2043', 'huaan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2043-queryCar,edi-2043-xbQuery,edi-2043-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2045', 'tianan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-天安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-IdCarChekc,robot-2045-apply-download" //电子保单下载    else{        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2045', 'tianan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-天安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2045-queryCar,edi-2045-xbQuery,edi-2045-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2019', 'yangguang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-阳光-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-IdCarChekc,robot-2019-apply-download" //电子保单下载    else{        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2019', 'yangguang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-阳光-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2019-queryCar,edi-2019-xbQuery,edi-2019-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2022', 'yongcheng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-永诚-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-IdCarChekc,robot-2022-apply-download" //电子保单下载    else{        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2022', 'yongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-永诚-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2022-queryCar,edi-2022-xbQuery,edi-2022-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2027', 'zhonghua', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-中华-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-IdCarChekc,robot-2027-apply-download" //电子保单下载    else{        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2027', 'zhonghua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中华-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2027-queryCar,edi-2027-xbQuery,edi-2027-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2095', 'zijin', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-紫金-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-IdCarChekc,robot-2095-apply-download" //电子保单下载    else{        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2095', 'zijin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-紫金-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2095-queryCar,edi-2095-xbQuery,edi-2095-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2096', 'guoren', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-国任-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-IdCarChekc,robot-2096-apply-download" //电子保单下载    else{        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2096', 'guoren', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国任-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2096-queryCar,edi-2096-xbQuery,edi-2096-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2002', 'guoshou', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-国寿-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-IdCarChekc,robot-2002-apply-download" //电子保单下载    else{        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2002', 'guoshou', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国寿-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2002-queryCar,edi-2002-xbQuery,edi-2002-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2023', 'huatai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-华泰-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-IdCarChekc,robot-2023-apply-download" //电子保单下载    else{        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2023', 'huatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华泰-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2023-queryCar,edi-2023-xbQuery,edi-2023-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2065', 'chengtai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-诚泰-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-IdCarChekc,robot-2065-apply-download" //电子保单下载    else{        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2065', 'chengtai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-诚泰-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2065-queryCar,edi-2065-xbQuery,edi-2065-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2062', 'hengbang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-恒邦-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-IdCarChekc,robot-2062-apply-download" //电子保单下载    else{        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2062', 'hengbang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-恒邦-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2062-queryCar,edi-2062-xbQuery,edi-2062-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3053', 'jintai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-锦泰-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-IdCarChekc,robot-3053-apply-download" //电子保单下载    else{        return"robot-3053-qrcode_login,robot-3053-apply-prepare,robot-3053-apply-select-jq,robot-3053-apply-select-sy,robot-3053-apply-editIDCardCheck,robot-3053-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3053', 'jintai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-锦泰-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3053-queryCar,edi-3053-xbQuery,edi-3053-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2056', 'zhongan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-众安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-IdCarChekc,robot-2056-apply-download" //电子保单下载    else{        return"robot-2056-qrcode_login,robot-2056-apply-prepare,robot-2056-apply-select-jq,robot-2056-apply-select-sy,robot-2056-apply-editIDCardCheck,robot-2056-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2056', 'zhongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-众安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2056-queryCar,edi-2056-xbQuery,edi-2056-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2086', 'changan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-长安-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-IdCarChekc,robot-2086-apply-download" //电子保单下载    else{        return"robot-2086-qrcode_login,robot-2086-apply-prepare,robot-2086-apply-select-jq,robot-2086-apply-select-sy,robot-2086-apply-editIDCardCheck,robot-2086-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2086', 'changan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-长安-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2086-queryCar,edi-2086-xbQuery,edi-2086-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2058', 'ancheng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安诚-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-IdCarChekc,robot-2058-apply-download" //电子保单下载    else{        return"robot-2058-qrcode_login,robot-2058-apply-prepare,robot-2058-apply-select-jq,robot-2058-apply-select-sy,robot-2058-apply-editIDCardCheck,robot-2058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2058', 'ancheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安诚-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2058-queryCar,edi-2058-xbQuery,edi-2058-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2042', 'dubang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-都邦-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-IdCarChekc,robot-2042-apply-download" //电子保单下载    else{        return"robot-2042-qrcode_login,robot-2042-apply-prepare,robot-2042-apply-select-jq,robot-2042-apply-select-sy,robot-2042-apply-editIDCardCheck,robot-2042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2042', 'dubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-都邦-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2042-queryCar,edi-2042-xbQuery,edi-2042-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2072', 'beibuwan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-北部湾-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-IdCarChekc,robot-2072-apply-download" //电子保单下载    else{        return"robot-2072-qrcode_login,robot-2072-apply-prepare,robot-2072-apply-select-jq,robot-2072-apply-select-sy,robot-2072-apply-editIDCardCheck,robot-2072-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2072', 'beibuwan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-北部湾-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2072-queryCar,edi-2072-xbQuery,edi-2072-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3037', 'fude', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-富徳-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-IdCarChekc,robot-3037-apply-download" //电子保单下载    else{        return"robot-3037-qrcode_login,robot-3037-apply-prepare,robot-3037-apply-select-jq,robot-3037-apply-select-sy,robot-3037-apply-editIDCardCheck,robot-3037-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3037', 'fude', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-富徳-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3037-queryCar,edi-3037-xbQuery,edi-3037-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3024', 'anxin', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安心-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-IdCarChekc,robot-3024-apply-download" //电子保单下载    else{        return"robot-3024-qrcode_login,robot-3024-apply-prepare,robot-3024-apply-select-jq,robot-3024-apply-select-sy,robot-3024-apply-editIDCardCheck,robot-3024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3024', 'anxin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安心-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3024-queryCar,edi-3024-xbQuery,edi-3024-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3011', 'anda', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安达-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-IdCarChekc,robot-3011-apply-download" //电子保单下载    else{        return"robot-3011-qrcode_login,robot-3011-apply-prepare,robot-3011-apply-select-jq,robot-3011-apply-select-sy,robot-3011-apply-editIDCardCheck,robot-3011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3011', 'anda', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安达-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3011-queryCar,edi-3011-xbQuery,edi-3011-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3079', 'zhongyin', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-中银-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-IdCarChekc,robot-3079-apply-download" //电子保单下载    else{        return"robot-3079-qrcode_login,robot-3079-apply-prepare,robot-3079-apply-select-jq,robot-3079-apply-select-sy,robot-3079-apply-editIDCardCheck,robot-3079-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3079', 'zhongyin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中银-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3079-queryCar,edi-3079-xbQuery,edi-3079-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3042', 'guotai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-国泰-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-IdCarChekc,robot-3042-apply-download" //电子保单下载    else{        return"robot-3042-qrcode_login,robot-3042-apply-prepare,robot-3042-apply-select-jq,robot-3042-apply-select-sy,robot-3042-apply-editIDCardCheck,robot-3042-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3042', 'guotai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国泰-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3042-queryCar,edi-3042-xbQuery,edi-3042-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3047', 'haixia', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-海峡金桥-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-IdCarChekc,robot-3047-apply-download" //电子保单下载    else{        return"robot-3047-qrcode_login,robot-3047-apply-prepare,robot-3047-apply-select-jq,robot-3047-apply-select-sy,robot-3047-apply-editIDCardCheck,robot-3047-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3047', 'haixia', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-海峡金桥-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3047-queryCar,edi-3047-xbQuery,edi-3047-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3048', 'hezhong', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-合众-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-IdCarChekc,robot-3048-apply-download" //电子保单下载    else{        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3048', 'hezhong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-合众-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3048-queryCar,edi-3048-xbQuery,edi-3048-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3048', 'huanong', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-华农-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-IdCarChekc,robot-3048-apply-download" //电子保单下载    else{        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3048', 'huanong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华农-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3048-queryCar,edi-3048-xbQuery,edi-3048-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3051', 'huanong', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-华农-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-IdCarChekc,robot-3051-apply-download" //电子保单下载    else{        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3051', 'huanong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华农-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3051-queryCar,edi-3051-xbQuery,edi-3051-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3055', 'rongsheng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-融盛-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-IdCarChekc,robot-3055-apply-download" //电子保单下载    else{        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3055', 'rongsheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-融盛-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3055-queryCar,edi-3055-xbQuery,edi-3055-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3014', 'anhua', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安华-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-IdCarChekc,robot-3014-apply-download" //电子保单下载    else{        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3014', 'anhua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安华-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3014-queryCar,edi-3014-xbQuery,edi-3014-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '4002', 'taikang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-泰康-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-IdCarChekc,robot-4002-apply-download" //电子保单下载    else{        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '4002', 'taikang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-泰康-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-4002-queryCar,edi-4002-xbQuery,edi-4002-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3058', 'taishan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-泰山-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-IdCarChekc,robot-3058-apply-download" //电子保单下载    else{        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3058', 'taishan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-泰山-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3058-queryCar,edi-3058-xbQuery,edi-3058-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3058', 'taishan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-英大-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-IdCarChekc,robot-3058-apply-download" //电子保单下载    else{        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3058', 'taishan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-英大-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3058-queryCar,edi-3058-xbQuery,edi-3058-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3066', 'yingda', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-英大-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-IdCarChekc,robot-3066-apply-download" //电子保单下载    else{        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3066', 'yingda', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-英大-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3066-queryCar,edi-3066-xbQuery,edi-3066-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3067', 'changjiang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-长江-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-IdCarChekc,robot-3067-apply-download" //电子保单下载    else{        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3067', 'changjiang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-长江-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3067-queryCar,edi-3067-xbQuery,edi-3067-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3020', 'anlian', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-安联-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-IdCarChekc,robot-3020-apply-download" //电子保单下载    else{        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3020', 'anlian', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安联-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3020-queryCar,edi-3020-xbQuery,edi-3020-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3077', 'zhongyi', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-中意-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-IdCarChekc,robot-3077-apply-download" //电子保单下载    else{        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3077', 'zhongyi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中意-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3077-queryCar,edi-3077-xbQuery,edi-3077-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3081', 'zhufeng', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-珠峰-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-IdCarChekc,robot-3081-apply-download" //电子保单下载    else{        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3081', 'zhufeng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-珠峰-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3081-queryCar,edi-3081-xbQuery,edi-3081-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3081', 'xiandai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-现代保险-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-IdCarChekc,robot-3081-apply-download" //电子保单下载    else{        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3081', 'xiandai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-现代保险-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3081-queryCar,edi-3081-xbQuery,edi-3081-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3059', 'xiandai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-现代保险-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-IdCarChekc,robot-3059-apply-download" //电子保单下载    else{        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3059', 'xiandai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-现代保险-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3059-queryCar,edi-3059-xbQuery,edi-3059-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3046', 'guoyuan', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-国元农业-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-IdCarChekc,robot-3046-apply-download" //电子保单下载    else{        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3046', 'guoyuan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国元农业-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3046-queryCar,edi-3046-xbQuery,edi-3046-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2076', 'zhongmei', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-中煤-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-IdCarChekc,robot-2076-apply-download" //电子保单下载    else{        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2076', 'zhongmei', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中煤-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2076-queryCar,edi-2076-xbQuery,edi-2076-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3028', 'bohai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-渤海-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-IdCarChekc,robot-3028-apply-download" //电子保单下载    else{        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3028', 'bohai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-渤海-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3028-queryCar,edi-3028-xbQuery,edi-3028-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2024', 'dajia', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-大家-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-IdCarChekc,robot-2024-apply-download" //电子保单下载    else{        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2024', 'dajia', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-大家-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2024-queryCar,edi-2024-xbQuery,edi-2024-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '2024', 'fubang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-IdCarChekc,robot-2024-apply-download" //电子保单下载    else{        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2024', 'fubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-富邦-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-2024-queryCar,edi-2024-xbQuery,edi-2024-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3033', 'fubang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-IdCarChekc,robot-3033-apply-download" //电子保单下载    else{        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3033', 'fubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-富邦-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3033-queryCar,edi-3033-xbQuery,edi-3033-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3069', 'zheshang', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-浙商-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-IdCarChekc,robot-3069-apply-download" //电子保单下载    else{        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3069', 'zheshang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-浙商-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3069-queryCar,edi-3069-xbQuery,edi-3069-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3050', 'huahai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-华海-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-IdCarChekc,robot-3050-apply-download" //电子保单下载    else{        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3050', 'huahai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华海-北京新车备案', 'def getTemplateGroup(dataSource) {    return "edi-3050-queryCar,edi-3050-xbQuery,edi-3050-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3061', 'qianhai', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-新疆前海-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-IdCarChekc,robot-3061-apply-download" //电子保单下载    else{        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('download', '3063', 'yanzhao', '15', '6', 'pro', 'robot', b'1', '{}', '精灵-燕赵财险-电子保单', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-IdCarChekc,robot-3063-apply-download" //电子保单下载    else{        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
