INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3048', 'hezhong', '15', '6', 'pro', 'other', b'1', '{}', '精灵-合众-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-IdCarChekc" //申请验证码    else{        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3048', 'hezhong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-合众-报价', 'def getTemplateGroup(dataSource) {    return "edi-3048-queryCar,edi-3048-xbQuery,edi-3048-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3048', 'hezhong', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-合众-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3048-queryCar,edi-3048-xbQuery,edi-3048-askCharge,edi-3048-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3048', 'hezhong', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-合众-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3048-queryCar,edi-3048-xbQuery,edi-3048-askCharge,edi-3048-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3048', 'hezhong', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-合众-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3048-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3048', 'hezhong', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-合众-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3048-login,robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3048-login,robot-3048-ObtainConfig,robot-3048-checkInsurePerson,robot-3048-changePerson,robot-3048-checkInsuredPerson,robot-3048-changePerson,robot-3048-prepareEdit," +                    "robot-3048-prepareQueryCode,robot-3048-selectProposalCar,robot-3048-browseProposalCar,robot-3048-browseProposalCarefc,robot-3048-selectRenewalPolicyNo"        }else{            s = "robot-3048-login,robot-3048-ObtainConfig,robot-3048-checkInsurePerson,robot-3048-changePerson,robot-3048-checkInsuredPerson,robot-3048-changePerson,robot-3048-prepareEdit," +                    "robot-3048-prepareQueryCode,robot-3048-browseProposalCar,robot-3048-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3048-VehicleModelList" //上海车型查询        }        s += ",robot-3048-queryPrepare,robot-3048-vehicleQuery,robot-3048-queryTaxAbateForPlat,robot-3048-calActualValue,robot-3048-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3048-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3048-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-nomotor-unitedSaleEdit,robot-3048-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3048-login,robot_3048_bj_initData,robot_3048_bj_queryModel,robot_3048_bj_getSaleTaxInfo,robot_3048_bj_getRealValue,robot_3048_bj_getPersonData,robot_3048_bj_addPersonData,robot_3048_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3048', 'hezhong', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-合众-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3048-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3048-ObtainConfig,robot-3048-selectRenewal,robot-3048-editCengage,robot-3048-editCitemCar,robot-3048-editCinsured,robot-3048-renewalPolicy,robot-3048-renewalPolicyCI,robot-3048-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3048-VehicleModelList" //上海车型查询        }        s += ",robot-3048-vehicleQueryXB,robot-3048-queryTaxAbateForPlat,robot-3048-calActualValue,robot-3048-editCitemKind,robot-3048-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3048-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-refreshPlanByTimes,robot-3048-insert"            }else{                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3048-getMaxCsellFee,robot-3048-getPrpCseller,robot-3048-getPrpCsellerCI,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            s += ",robot-3048-getMaxCsellFee,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"        }    }else{        s +=",robot-3048-ObtainConfig,robot-3048-checkInsurePerson,robot-3048-changePerson,robot-3048-checkInsuredPerson,robot-3048-changePerson,robot-3048-prepareEdit,robot-3048-selectRenewalPolicyNo,robot-3048-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3048-VehicleModelList" //上海车型查询        }        s += ",robot-3048-queryPrepare,robot-3048-vehicleQuery,robot-3048-queryTaxAbateForPlat,robot-3048-calActualValue,robot-3048-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3048-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3048-calAnciInfo,robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            }else{                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-nomotor-unitedSaleEdit,robot-3048-nomotor-saveUnitedSale,robot-3048-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3048-getMaxCsellFee,robot-3048-getPrpCseller,robot-3048-getPrpCsellerCI,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            s += ",robot-3048-getMaxCsellFee,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-nomotor-unitedSaleEdit,robot-3048-nomotor-saveUnitedSale,robot-3048-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3048-login,robot_3048_bj_initData,robot_3048_bj_queryModel,robot_3048_bj_getSaleTaxInfo,robot_3048_bj_getRealValue,robot_3048_bj_getPersonData,robot_3048_bj_addPersonData,robot_3048_bj_askCharge,robot_3048_bj_queryPayForXSFY,robot_3048_bj_getCagentCI,robot_3048_bj_getCagent,robot_3048_bj_queryPayForXSFY_req,robot_3048_bj_queryIlogEngage,robot_3048_bj_insureRefrenshPlan,robot_3048_bj_insure4S,robot-3048-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3048', 'hezhong', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-合众-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ" +            ",robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind,robot-3048-nomotor-query,robot-3048-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3048', 'hezhong', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-合众-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ" +            ",robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind,robot-3048-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3048', 'hezhong', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-合众-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ" +            ",robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind,robot-3048-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3048', 'hezhong', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "合众财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-合众-电销', 'def getTemplateGroup(dataSource){    return "robot-3048-pureESale_Login,robot-3048-pureESale_Welcome,robot-3048-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3048', 'hezhong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-合众续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3048-login,robot-3048-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3048', 'hezhong', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-合众-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3048-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3048-ObtainConfig,robot-3048-selectRenewal,robot-3048-editCengage,robot-3048-editCitemCar,robot-3048-editCinsured,robot-3048-renewalPolicy,robot-3048-renewalPolicyCI,robot-3048-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3048-VehicleModelList" //上海车型查询        }        s += ",robot-3048-vehicleQueryXB,robot-3048-queryTaxAbateForPlat,robot-3048-calActualValue,robot-3048-editCitemKind,robot-3048-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3048-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-refreshPlanByTimes,robot-3048-insert"            }else{                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3048-calAnciInfo,robot-3048-getMaxCsellFee,robot-3048-getPrpCseller,robot-3048-getPrpCsellerCI,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            s += ",robot-3048-getMaxCsellFee,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"        }    }else{        s += ",robot-3048-ObtainConfig,robot-3048-checkInsurePerson,robot-3048-changePerson,robot-3048-checkInsuredPerson,robot-3048-changePerson,robot-3048-prepareEdit,robot-3048-editCengage,robot-3048-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3048-queryVehiclePMCheck,robot-3048-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3048-VehicleModelList" //上海车型查询        }        s += ",robot-3048-queryPrepare,robot-3048-vehicleQuery,robot-3048-queryTaxAbateForPlat,robot-3048-calActualValue,robot-3048-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3048-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3048-calAnciInfo,robot-3048-queryPayFor,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            }else{                s += ",robot-3048-calAnciInfo,robot-3048-checkAgentType,robot-3048-queryPayForSCMS,robot-3048-getCagent,robot-3048-getCagentCI,robot-3048-refreshPlanByTimes,robot-3048-nomotor-unitedSaleEdit,robot-3048-nomotor-saveUnitedSale,robot-3048-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3048-calAnciInfo,robot-3048-getMaxCsellFee,robot-3048-getPrpCseller,robot-3048-getPrpCsellerCI,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-insert"            s += ",robot-3048-getMaxCsellFee,robot-3048-queryPayForSCMS,robot-3048-refreshPlanByTimes,robot-3048-nomotor-unitedSaleEdit,robot-3048-nomotor-saveUnitedSale,robot-3048-insert"        }    }    s += ",robot-3048-checkRiskCode,robot-3048-editMainUwtFlag,robot-3048-editSubmitUndwrt,robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-showUndwrtMsgQ,robot-3048-showUndwrtMsgS"+            ",robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ,robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind,robot-3048-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3048-login,robot_3048_bj_initData,robot_3048_bj_queryModel,robot_3048_bj_getSaleTaxInfo,robot_3048_bj_getRealValue,robot_3048_bj_getPersonData,robot_3048_bj_addPersonData,robot_3048_bj_askCharge,robot_3048_bj_queryPayForXSFY,robot_3048_bj_getCagentCI,robot_3048_bj_getCagent,robot_3048_bj_queryPayForXSFY_req,robot_3048_bj_queryIlogEngage,robot_3048_bj_insureRefrenshPlan,robot_3048_bj_insure4S,robot-3048-uploadImage,robot_3048_bj_autoInsure,robot_3048_bj_showUndwrtMsgQ,robot_3048_bj_showUndwrtMsgS";       s += ",robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ,robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +                ",robot-3048-showCinsuredS,robot-3048-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3048', 'hezhong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-合众-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3048-qrcode_login,robot-3048-qrcode_printTwoBarCodeServlet,robot-3048-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3048-qrcode_login,robot-3048-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3048-qrcode_login,robot-3048-qrcode_editCheckFlag,robot-3048-qrcode_gotoJfcd,robot-3048-qrcode_prepareEditByJF,robot-3048-qrcode_getBusinessIn" +                ",robot-3048-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3048-qrcode_login,robot-3048-qrcode_editCheckFlag,robot-3048-qrcode_gotoJfcd,robot-3048-qrcode_prepareEditByJF,robot-3048-qrcode_getBusinessIn" +                ",robot-3048-qrcode_checkBeforeCalculate,robot-3048-qrcode_saveByJF,robot-3048-qrcode_getBusinessIn_alipay,robot-3048-qrcode_editFeeInfor,robot-3048-qrcode_editPayFeeByWeChat,robot-3048-qrcode_saveByWeChat,robot-3048-qrcode_save";		} else {					return  "robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-bj-editIDCardCheck,robot-3048-apply-selectIsNetProp,robot-3048-apply-saveCheckCode,robot-3048-qrcode_editCheckFlag,robot-3048-qrcode_gotoJfcd,robot-3048-qrcode_prepareEditByJF,robot-3048-qrcode_getBusinessIn" +",robot-3048-qrcode_checkBeforeCalculate,robot-3048-qrcode_saveByJF,robot-3048-qrcode_getBusinessIn_alipay,robot-3048-qrcode_editFeeInfor,robot-3048-qrcode_editPayFeeByWeChat,robot-3048-qrcode_saveByWeChat,robot-3048-qrcode_save";		}}    else {              return "robot-3048-qrcode_login,robot-3048-qrcode_editCheckFlag,robot-3048-qrcode_gotoJfcd,robot-3048-qrcode_prepareEditByJF,robot-3048-qrcode_getBusinessIn" +                ",robot-3048-qrcode_checkBeforeCalculate,robot-3048-qrcode_saveByJF,robot-3048-qrcode_getBusinessIn_alipay,robot-3048-qrcode_editFeeInfor,robot-3048-qrcode_editPayFeeByWeChat,robot-3048-qrcode_saveByWeChat,robot-3048-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3048', 'hezhong', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-合众-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3048-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3048-qrcode_query_editCheckFlag,robot-3048-qrcode_query_gotoJfcd,robot-3048-qrcode_query_prepareEditByJF" +                ",robot-3048-qrcode_query_editMainInfor,robot-3048-qrcode_query_getBusinessIn,robot-3048-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ" +            ",robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind,robot-3048-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3048', 'hezhong', '15', '6', 'pro', 'other', b'1', '{}', '精灵-合众-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-IdCarChekc" //申请验证码    else{        return"robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-editIDCardCheck,robot-3048-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3048', 'hezhong', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-合众-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectPolicyefc,robot-3048-selectPolicybiz,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ" +            ",robot-3048-showCitemCarQ,robot-3048-showCinsuredQ,robot-3048-showCitemKindCI,robot-3048-browseProposalS,robot-3048-showCitemCarS" +            ",robot-3048-showCinsuredS,robot-3048-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3048', 'hezhong', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3048-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectProposalQ,robot-3048-selectProposalS,robot-3048-browseProposalQ,robot-3048-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3048', 'hezhong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-合众-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-bj-editIDCardCheck,robot-3048-apply-bj-IdCarChekc";    } else {        s = "robot-3048-qrcode_login,robot-3048-qrcode_editCheckFlag,robot-3048-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3048', 'hezhong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi合众报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3048_ask_charge,edi_3048_noMotor_quote"	} else {		return "edi_3048_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3048', 'hezhong', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-合众-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3048_ask_charge,edi_3048_noMotor_quote,edi_3048_askInsure,edi_3048_uploadImg,edi_3048_submitInsure,edi_3048_noMotor_submit" 	  	} else {		return "edi_3048_ask_chargeold,edi_3048_askInsure,edi_3048_uploadImg,edi_3048_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3048', 'hezhong', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-合众-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3048_ask_charge,edi_3048_noMotor_quote,edi_3048_askInsure,edi_3048_uploadImg" 	} else {		return "edi_3048_ask_chargeold,edi_3048_askInsure,edi_3048_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3048', 'hezhong', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-合众-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3048_efc_policyinfo,edi_3048_biz_policyinfo,edi_3048_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3048', 'hezhong', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-合众-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3048_efc_insurequery,edi_3048_biz_insurequery,edi_3048_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3048', 'hezhong', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京合众短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-bj-editIDCardCheck,robot-3048-apply-saveCheckCode,robot-3048-apply-selectIsNetProp";    } else {        s = "robot-3048-qrcode_login,robot-3048-apply-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3048', 'hezhong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-合众-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3048-bj-qrcode_login,robot-3048-apply-bj-prepare,robot-3048-apply-select-jq,robot-3048-apply-select-sy,robot-3048-apply-bj-editIDCardCheck,robot-3048-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3048', 'hezhong', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-合众-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3048_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3048', 'hezhong', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi合众北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3048_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3048', 'hezhong', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi合众北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3048_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3048', 'hezhong', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-合众-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3048-login,robot-3048-prepareQueryCode,robot-3048-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3051', 'huanong', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华农-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-IdCarChekc" //申请验证码    else{        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3051', 'huanong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华农-报价', 'def getTemplateGroup(dataSource) {    return "edi-3051-queryCar,edi-3051-xbQuery,edi-3051-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3051', 'huanong', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华农-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3051-queryCar,edi-3051-xbQuery,edi-3051-askCharge,edi-3051-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3051', 'huanong', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华农-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3051-queryCar,edi-3051-xbQuery,edi-3051-askCharge,edi-3051-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3051', 'huanong', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-华农-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3051-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3051', 'huanong', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华农-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3051-login,robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3051-login,robot-3051-ObtainConfig,robot-3051-checkInsurePerson,robot-3051-changePerson,robot-3051-checkInsuredPerson,robot-3051-changePerson,robot-3051-prepareEdit," +                    "robot-3051-prepareQueryCode,robot-3051-selectProposalCar,robot-3051-browseProposalCar,robot-3051-browseProposalCarefc,robot-3051-selectRenewalPolicyNo"        }else{            s = "robot-3051-login,robot-3051-ObtainConfig,robot-3051-checkInsurePerson,robot-3051-changePerson,robot-3051-checkInsuredPerson,robot-3051-changePerson,robot-3051-prepareEdit," +                    "robot-3051-prepareQueryCode,robot-3051-browseProposalCar,robot-3051-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3051-VehicleModelList" //上海车型查询        }        s += ",robot-3051-queryPrepare,robot-3051-vehicleQuery,robot-3051-queryTaxAbateForPlat,robot-3051-calActualValue,robot-3051-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3051-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3051-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-nomotor-unitedSaleEdit,robot-3051-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3051-login,robot_3051_bj_initData,robot_3051_bj_queryModel,robot_3051_bj_getSaleTaxInfo,robot_3051_bj_getRealValue,robot_3051_bj_getPersonData,robot_3051_bj_addPersonData,robot_3051_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3051', 'huanong', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-华农-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3051-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3051-ObtainConfig,robot-3051-selectRenewal,robot-3051-editCengage,robot-3051-editCitemCar,robot-3051-editCinsured,robot-3051-renewalPolicy,robot-3051-renewalPolicyCI,robot-3051-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3051-VehicleModelList" //上海车型查询        }        s += ",robot-3051-vehicleQueryXB,robot-3051-queryTaxAbateForPlat,robot-3051-calActualValue,robot-3051-editCitemKind,robot-3051-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3051-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-refreshPlanByTimes,robot-3051-insert"            }else{                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3051-getMaxCsellFee,robot-3051-getPrpCseller,robot-3051-getPrpCsellerCI,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            s += ",robot-3051-getMaxCsellFee,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"        }    }else{        s +=",robot-3051-ObtainConfig,robot-3051-checkInsurePerson,robot-3051-changePerson,robot-3051-checkInsuredPerson,robot-3051-changePerson,robot-3051-prepareEdit,robot-3051-selectRenewalPolicyNo,robot-3051-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3051-VehicleModelList" //上海车型查询        }        s += ",robot-3051-queryPrepare,robot-3051-vehicleQuery,robot-3051-queryTaxAbateForPlat,robot-3051-calActualValue,robot-3051-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3051-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3051-calAnciInfo,robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            }else{                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-nomotor-unitedSaleEdit,robot-3051-nomotor-saveUnitedSale,robot-3051-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3051-getMaxCsellFee,robot-3051-getPrpCseller,robot-3051-getPrpCsellerCI,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            s += ",robot-3051-getMaxCsellFee,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-nomotor-unitedSaleEdit,robot-3051-nomotor-saveUnitedSale,robot-3051-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3051-login,robot_3051_bj_initData,robot_3051_bj_queryModel,robot_3051_bj_getSaleTaxInfo,robot_3051_bj_getRealValue,robot_3051_bj_getPersonData,robot_3051_bj_addPersonData,robot_3051_bj_askCharge,robot_3051_bj_queryPayForXSFY,robot_3051_bj_getCagentCI,robot_3051_bj_getCagent,robot_3051_bj_queryPayForXSFY_req,robot_3051_bj_queryIlogEngage,robot_3051_bj_insureRefrenshPlan,robot_3051_bj_insure4S,robot-3051-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3051', 'huanong', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-华农-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ" +            ",robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind,robot-3051-nomotor-query,robot-3051-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3051', 'huanong', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华农-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ" +            ",robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind,robot-3051-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3051', 'huanong', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华农-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ" +            ",robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind,robot-3051-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3051', 'huanong', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "华农财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-华农-电销', 'def getTemplateGroup(dataSource){    return "robot-3051-pureESale_Login,robot-3051-pureESale_Welcome,robot-3051-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3051', 'huanong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华农续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3051-login,robot-3051-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3051', 'huanong', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-华农-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3051-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3051-ObtainConfig,robot-3051-selectRenewal,robot-3051-editCengage,robot-3051-editCitemCar,robot-3051-editCinsured,robot-3051-renewalPolicy,robot-3051-renewalPolicyCI,robot-3051-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3051-VehicleModelList" //上海车型查询        }        s += ",robot-3051-vehicleQueryXB,robot-3051-queryTaxAbateForPlat,robot-3051-calActualValue,robot-3051-editCitemKind,robot-3051-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3051-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-refreshPlanByTimes,robot-3051-insert"            }else{                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3051-calAnciInfo,robot-3051-getMaxCsellFee,robot-3051-getPrpCseller,robot-3051-getPrpCsellerCI,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            s += ",robot-3051-getMaxCsellFee,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"        }    }else{        s += ",robot-3051-ObtainConfig,robot-3051-checkInsurePerson,robot-3051-changePerson,robot-3051-checkInsuredPerson,robot-3051-changePerson,robot-3051-prepareEdit,robot-3051-editCengage,robot-3051-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3051-queryVehiclePMCheck,robot-3051-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3051-VehicleModelList" //上海车型查询        }        s += ",robot-3051-queryPrepare,robot-3051-vehicleQuery,robot-3051-queryTaxAbateForPlat,robot-3051-calActualValue,robot-3051-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3051-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3051-calAnciInfo,robot-3051-queryPayFor,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            }else{                s += ",robot-3051-calAnciInfo,robot-3051-checkAgentType,robot-3051-queryPayForSCMS,robot-3051-getCagent,robot-3051-getCagentCI,robot-3051-refreshPlanByTimes,robot-3051-nomotor-unitedSaleEdit,robot-3051-nomotor-saveUnitedSale,robot-3051-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3051-calAnciInfo,robot-3051-getMaxCsellFee,robot-3051-getPrpCseller,robot-3051-getPrpCsellerCI,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-insert"            s += ",robot-3051-getMaxCsellFee,robot-3051-queryPayForSCMS,robot-3051-refreshPlanByTimes,robot-3051-nomotor-unitedSaleEdit,robot-3051-nomotor-saveUnitedSale,robot-3051-insert"        }    }    s += ",robot-3051-checkRiskCode,robot-3051-editMainUwtFlag,robot-3051-editSubmitUndwrt,robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-showUndwrtMsgQ,robot-3051-showUndwrtMsgS"+            ",robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ,robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind,robot-3051-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3051-login,robot_3051_bj_initData,robot_3051_bj_queryModel,robot_3051_bj_getSaleTaxInfo,robot_3051_bj_getRealValue,robot_3051_bj_getPersonData,robot_3051_bj_addPersonData,robot_3051_bj_askCharge,robot_3051_bj_queryPayForXSFY,robot_3051_bj_getCagentCI,robot_3051_bj_getCagent,robot_3051_bj_queryPayForXSFY_req,robot_3051_bj_queryIlogEngage,robot_3051_bj_insureRefrenshPlan,robot_3051_bj_insure4S,robot-3051-uploadImage,robot_3051_bj_autoInsure,robot_3051_bj_showUndwrtMsgQ,robot_3051_bj_showUndwrtMsgS";       s += ",robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ,robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +                ",robot-3051-showCinsuredS,robot-3051-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3051', 'huanong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华农-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3051-qrcode_login,robot-3051-qrcode_printTwoBarCodeServlet,robot-3051-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3051-qrcode_login,robot-3051-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3051-qrcode_login,robot-3051-qrcode_editCheckFlag,robot-3051-qrcode_gotoJfcd,robot-3051-qrcode_prepareEditByJF,robot-3051-qrcode_getBusinessIn" +                ",robot-3051-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3051-qrcode_login,robot-3051-qrcode_editCheckFlag,robot-3051-qrcode_gotoJfcd,robot-3051-qrcode_prepareEditByJF,robot-3051-qrcode_getBusinessIn" +                ",robot-3051-qrcode_checkBeforeCalculate,robot-3051-qrcode_saveByJF,robot-3051-qrcode_getBusinessIn_alipay,robot-3051-qrcode_editFeeInfor,robot-3051-qrcode_editPayFeeByWeChat,robot-3051-qrcode_saveByWeChat,robot-3051-qrcode_save";		} else {					return  "robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-bj-editIDCardCheck,robot-3051-apply-selectIsNetProp,robot-3051-apply-saveCheckCode,robot-3051-qrcode_editCheckFlag,robot-3051-qrcode_gotoJfcd,robot-3051-qrcode_prepareEditByJF,robot-3051-qrcode_getBusinessIn" +",robot-3051-qrcode_checkBeforeCalculate,robot-3051-qrcode_saveByJF,robot-3051-qrcode_getBusinessIn_alipay,robot-3051-qrcode_editFeeInfor,robot-3051-qrcode_editPayFeeByWeChat,robot-3051-qrcode_saveByWeChat,robot-3051-qrcode_save";		}}    else {              return "robot-3051-qrcode_login,robot-3051-qrcode_editCheckFlag,robot-3051-qrcode_gotoJfcd,robot-3051-qrcode_prepareEditByJF,robot-3051-qrcode_getBusinessIn" +                ",robot-3051-qrcode_checkBeforeCalculate,robot-3051-qrcode_saveByJF,robot-3051-qrcode_getBusinessIn_alipay,robot-3051-qrcode_editFeeInfor,robot-3051-qrcode_editPayFeeByWeChat,robot-3051-qrcode_saveByWeChat,robot-3051-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3051', 'huanong', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华农-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3051-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3051-qrcode_query_editCheckFlag,robot-3051-qrcode_query_gotoJfcd,robot-3051-qrcode_query_prepareEditByJF" +                ",robot-3051-qrcode_query_editMainInfor,robot-3051-qrcode_query_getBusinessIn,robot-3051-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ" +            ",robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind,robot-3051-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3051', 'huanong', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华农-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-IdCarChekc" //申请验证码    else{        return"robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-editIDCardCheck,robot-3051-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3051', 'huanong', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-华农-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectPolicyefc,robot-3051-selectPolicybiz,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ" +            ",robot-3051-showCitemCarQ,robot-3051-showCinsuredQ,robot-3051-showCitemKindCI,robot-3051-browseProposalS,robot-3051-showCitemCarS" +            ",robot-3051-showCinsuredS,robot-3051-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3051', 'huanong', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3051-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectProposalQ,robot-3051-selectProposalS,robot-3051-browseProposalQ,robot-3051-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3051', 'huanong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华农-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-bj-editIDCardCheck,robot-3051-apply-bj-IdCarChekc";    } else {        s = "robot-3051-qrcode_login,robot-3051-qrcode_editCheckFlag,robot-3051-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3051', 'huanong', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi华农报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3051_ask_charge,edi_3051_noMotor_quote"	} else {		return "edi_3051_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3051', 'huanong', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华农-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3051_ask_charge,edi_3051_noMotor_quote,edi_3051_askInsure,edi_3051_uploadImg,edi_3051_submitInsure,edi_3051_noMotor_submit" 	  	} else {		return "edi_3051_ask_chargeold,edi_3051_askInsure,edi_3051_uploadImg,edi_3051_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3051', 'huanong', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华农-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3051_ask_charge,edi_3051_noMotor_quote,edi_3051_askInsure,edi_3051_uploadImg" 	} else {		return "edi_3051_ask_chargeold,edi_3051_askInsure,edi_3051_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3051', 'huanong', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-华农-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3051_efc_policyinfo,edi_3051_biz_policyinfo,edi_3051_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3051', 'huanong', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-华农-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3051_efc_insurequery,edi_3051_biz_insurequery,edi_3051_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3051', 'huanong', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京华农短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-bj-editIDCardCheck,robot-3051-apply-saveCheckCode,robot-3051-apply-selectIsNetProp";    } else {        s = "robot-3051-qrcode_login,robot-3051-apply-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3051', 'huanong', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华农-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3051-bj-qrcode_login,robot-3051-apply-bj-prepare,robot-3051-apply-select-jq,robot-3051-apply-select-sy,robot-3051-apply-bj-editIDCardCheck,robot-3051-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3051', 'huanong', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-华农-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3051_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3051', 'huanong', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华农北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3051_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3051', 'huanong', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华农北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3051_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3051', 'huanong', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-华农-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3051-login,robot-3051-prepareQueryCode,robot-3051-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3055', 'rongsheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-融盛-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-IdCarChekc" //申请验证码    else{        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3055', 'rongsheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-融盛-报价', 'def getTemplateGroup(dataSource) {    return "edi-3055-queryCar,edi-3055-xbQuery,edi-3055-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3055', 'rongsheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-融盛-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3055-queryCar,edi-3055-xbQuery,edi-3055-askCharge,edi-3055-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3055', 'rongsheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-融盛-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3055-queryCar,edi-3055-xbQuery,edi-3055-askCharge,edi-3055-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3055', 'rongsheng', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-融盛-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3055-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3055', 'rongsheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-融盛-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3055-login,robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3055-login,robot-3055-ObtainConfig,robot-3055-checkInsurePerson,robot-3055-changePerson,robot-3055-checkInsuredPerson,robot-3055-changePerson,robot-3055-prepareEdit," +                    "robot-3055-prepareQueryCode,robot-3055-selectProposalCar,robot-3055-browseProposalCar,robot-3055-browseProposalCarefc,robot-3055-selectRenewalPolicyNo"        }else{            s = "robot-3055-login,robot-3055-ObtainConfig,robot-3055-checkInsurePerson,robot-3055-changePerson,robot-3055-checkInsuredPerson,robot-3055-changePerson,robot-3055-prepareEdit," +                    "robot-3055-prepareQueryCode,robot-3055-browseProposalCar,robot-3055-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3055-VehicleModelList" //上海车型查询        }        s += ",robot-3055-queryPrepare,robot-3055-vehicleQuery,robot-3055-queryTaxAbateForPlat,robot-3055-calActualValue,robot-3055-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3055-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3055-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-nomotor-unitedSaleEdit,robot-3055-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3055-login,robot_3055_bj_initData,robot_3055_bj_queryModel,robot_3055_bj_getSaleTaxInfo,robot_3055_bj_getRealValue,robot_3055_bj_getPersonData,robot_3055_bj_addPersonData,robot_3055_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3055', 'rongsheng', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-融盛-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3055-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3055-ObtainConfig,robot-3055-selectRenewal,robot-3055-editCengage,robot-3055-editCitemCar,robot-3055-editCinsured,robot-3055-renewalPolicy,robot-3055-renewalPolicyCI,robot-3055-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3055-VehicleModelList" //上海车型查询        }        s += ",robot-3055-vehicleQueryXB,robot-3055-queryTaxAbateForPlat,robot-3055-calActualValue,robot-3055-editCitemKind,robot-3055-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3055-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-refreshPlanByTimes,robot-3055-insert"            }else{                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3055-getMaxCsellFee,robot-3055-getPrpCseller,robot-3055-getPrpCsellerCI,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            s += ",robot-3055-getMaxCsellFee,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"        }    }else{        s +=",robot-3055-ObtainConfig,robot-3055-checkInsurePerson,robot-3055-changePerson,robot-3055-checkInsuredPerson,robot-3055-changePerson,robot-3055-prepareEdit,robot-3055-selectRenewalPolicyNo,robot-3055-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3055-VehicleModelList" //上海车型查询        }        s += ",robot-3055-queryPrepare,robot-3055-vehicleQuery,robot-3055-queryTaxAbateForPlat,robot-3055-calActualValue,robot-3055-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3055-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3055-calAnciInfo,robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            }else{                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-nomotor-unitedSaleEdit,robot-3055-nomotor-saveUnitedSale,robot-3055-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3055-getMaxCsellFee,robot-3055-getPrpCseller,robot-3055-getPrpCsellerCI,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            s += ",robot-3055-getMaxCsellFee,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-nomotor-unitedSaleEdit,robot-3055-nomotor-saveUnitedSale,robot-3055-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3055-login,robot_3055_bj_initData,robot_3055_bj_queryModel,robot_3055_bj_getSaleTaxInfo,robot_3055_bj_getRealValue,robot_3055_bj_getPersonData,robot_3055_bj_addPersonData,robot_3055_bj_askCharge,robot_3055_bj_queryPayForXSFY,robot_3055_bj_getCagentCI,robot_3055_bj_getCagent,robot_3055_bj_queryPayForXSFY_req,robot_3055_bj_queryIlogEngage,robot_3055_bj_insureRefrenshPlan,robot_3055_bj_insure4S,robot-3055-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3055', 'rongsheng', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-融盛-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ" +            ",robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind,robot-3055-nomotor-query,robot-3055-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3055', 'rongsheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-融盛-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ" +            ",robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind,robot-3055-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3055', 'rongsheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-融盛-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ" +            ",robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind,robot-3055-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3055', 'rongsheng', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "融盛财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-融盛-电销', 'def getTemplateGroup(dataSource){    return "robot-3055-pureESale_Login,robot-3055-pureESale_Welcome,robot-3055-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3055', 'rongsheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-融盛续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3055-login,robot-3055-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3055', 'rongsheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-融盛-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3055-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3055-ObtainConfig,robot-3055-selectRenewal,robot-3055-editCengage,robot-3055-editCitemCar,robot-3055-editCinsured,robot-3055-renewalPolicy,robot-3055-renewalPolicyCI,robot-3055-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3055-VehicleModelList" //上海车型查询        }        s += ",robot-3055-vehicleQueryXB,robot-3055-queryTaxAbateForPlat,robot-3055-calActualValue,robot-3055-editCitemKind,robot-3055-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3055-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-refreshPlanByTimes,robot-3055-insert"            }else{                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3055-calAnciInfo,robot-3055-getMaxCsellFee,robot-3055-getPrpCseller,robot-3055-getPrpCsellerCI,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            s += ",robot-3055-getMaxCsellFee,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"        }    }else{        s += ",robot-3055-ObtainConfig,robot-3055-checkInsurePerson,robot-3055-changePerson,robot-3055-checkInsuredPerson,robot-3055-changePerson,robot-3055-prepareEdit,robot-3055-editCengage,robot-3055-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3055-queryVehiclePMCheck,robot-3055-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3055-VehicleModelList" //上海车型查询        }        s += ",robot-3055-queryPrepare,robot-3055-vehicleQuery,robot-3055-queryTaxAbateForPlat,robot-3055-calActualValue,robot-3055-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3055-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3055-calAnciInfo,robot-3055-queryPayFor,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            }else{                s += ",robot-3055-calAnciInfo,robot-3055-checkAgentType,robot-3055-queryPayForSCMS,robot-3055-getCagent,robot-3055-getCagentCI,robot-3055-refreshPlanByTimes,robot-3055-nomotor-unitedSaleEdit,robot-3055-nomotor-saveUnitedSale,robot-3055-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3055-calAnciInfo,robot-3055-getMaxCsellFee,robot-3055-getPrpCseller,robot-3055-getPrpCsellerCI,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-insert"            s += ",robot-3055-getMaxCsellFee,robot-3055-queryPayForSCMS,robot-3055-refreshPlanByTimes,robot-3055-nomotor-unitedSaleEdit,robot-3055-nomotor-saveUnitedSale,robot-3055-insert"        }    }    s += ",robot-3055-checkRiskCode,robot-3055-editMainUwtFlag,robot-3055-editSubmitUndwrt,robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-showUndwrtMsgQ,robot-3055-showUndwrtMsgS"+            ",robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ,robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind,robot-3055-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3055-login,robot_3055_bj_initData,robot_3055_bj_queryModel,robot_3055_bj_getSaleTaxInfo,robot_3055_bj_getRealValue,robot_3055_bj_getPersonData,robot_3055_bj_addPersonData,robot_3055_bj_askCharge,robot_3055_bj_queryPayForXSFY,robot_3055_bj_getCagentCI,robot_3055_bj_getCagent,robot_3055_bj_queryPayForXSFY_req,robot_3055_bj_queryIlogEngage,robot_3055_bj_insureRefrenshPlan,robot_3055_bj_insure4S,robot-3055-uploadImage,robot_3055_bj_autoInsure,robot_3055_bj_showUndwrtMsgQ,robot_3055_bj_showUndwrtMsgS";       s += ",robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ,robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +                ",robot-3055-showCinsuredS,robot-3055-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3055', 'rongsheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-融盛-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3055-qrcode_login,robot-3055-qrcode_printTwoBarCodeServlet,robot-3055-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3055-qrcode_login,robot-3055-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3055-qrcode_login,robot-3055-qrcode_editCheckFlag,robot-3055-qrcode_gotoJfcd,robot-3055-qrcode_prepareEditByJF,robot-3055-qrcode_getBusinessIn" +                ",robot-3055-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3055-qrcode_login,robot-3055-qrcode_editCheckFlag,robot-3055-qrcode_gotoJfcd,robot-3055-qrcode_prepareEditByJF,robot-3055-qrcode_getBusinessIn" +                ",robot-3055-qrcode_checkBeforeCalculate,robot-3055-qrcode_saveByJF,robot-3055-qrcode_getBusinessIn_alipay,robot-3055-qrcode_editFeeInfor,robot-3055-qrcode_editPayFeeByWeChat,robot-3055-qrcode_saveByWeChat,robot-3055-qrcode_save";		} else {					return  "robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-bj-editIDCardCheck,robot-3055-apply-selectIsNetProp,robot-3055-apply-saveCheckCode,robot-3055-qrcode_editCheckFlag,robot-3055-qrcode_gotoJfcd,robot-3055-qrcode_prepareEditByJF,robot-3055-qrcode_getBusinessIn" +",robot-3055-qrcode_checkBeforeCalculate,robot-3055-qrcode_saveByJF,robot-3055-qrcode_getBusinessIn_alipay,robot-3055-qrcode_editFeeInfor,robot-3055-qrcode_editPayFeeByWeChat,robot-3055-qrcode_saveByWeChat,robot-3055-qrcode_save";		}}    else {              return "robot-3055-qrcode_login,robot-3055-qrcode_editCheckFlag,robot-3055-qrcode_gotoJfcd,robot-3055-qrcode_prepareEditByJF,robot-3055-qrcode_getBusinessIn" +                ",robot-3055-qrcode_checkBeforeCalculate,robot-3055-qrcode_saveByJF,robot-3055-qrcode_getBusinessIn_alipay,robot-3055-qrcode_editFeeInfor,robot-3055-qrcode_editPayFeeByWeChat,robot-3055-qrcode_saveByWeChat,robot-3055-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3055', 'rongsheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-融盛-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3055-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3055-qrcode_query_editCheckFlag,robot-3055-qrcode_query_gotoJfcd,robot-3055-qrcode_query_prepareEditByJF" +                ",robot-3055-qrcode_query_editMainInfor,robot-3055-qrcode_query_getBusinessIn,robot-3055-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ" +            ",robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind,robot-3055-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3055', 'rongsheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-融盛-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-IdCarChekc" //申请验证码    else{        return"robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-editIDCardCheck,robot-3055-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3055', 'rongsheng', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-融盛-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectPolicyefc,robot-3055-selectPolicybiz,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ" +            ",robot-3055-showCitemCarQ,robot-3055-showCinsuredQ,robot-3055-showCitemKindCI,robot-3055-browseProposalS,robot-3055-showCitemCarS" +            ",robot-3055-showCinsuredS,robot-3055-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3055', 'rongsheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3055-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectProposalQ,robot-3055-selectProposalS,robot-3055-browseProposalQ,robot-3055-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3055', 'rongsheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-融盛-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-bj-editIDCardCheck,robot-3055-apply-bj-IdCarChekc";    } else {        s = "robot-3055-qrcode_login,robot-3055-qrcode_editCheckFlag,robot-3055-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3055', 'rongsheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi融盛报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3055_ask_charge,edi_3055_noMotor_quote"	} else {		return "edi_3055_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3055', 'rongsheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-融盛-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3055_ask_charge,edi_3055_noMotor_quote,edi_3055_askInsure,edi_3055_uploadImg,edi_3055_submitInsure,edi_3055_noMotor_submit" 	  	} else {		return "edi_3055_ask_chargeold,edi_3055_askInsure,edi_3055_uploadImg,edi_3055_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3055', 'rongsheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-融盛-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3055_ask_charge,edi_3055_noMotor_quote,edi_3055_askInsure,edi_3055_uploadImg" 	} else {		return "edi_3055_ask_chargeold,edi_3055_askInsure,edi_3055_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3055', 'rongsheng', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-融盛-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3055_efc_policyinfo,edi_3055_biz_policyinfo,edi_3055_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3055', 'rongsheng', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-融盛-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3055_efc_insurequery,edi_3055_biz_insurequery,edi_3055_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3055', 'rongsheng', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京融盛短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-bj-editIDCardCheck,robot-3055-apply-saveCheckCode,robot-3055-apply-selectIsNetProp";    } else {        s = "robot-3055-qrcode_login,robot-3055-apply-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3055', 'rongsheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-融盛-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3055-bj-qrcode_login,robot-3055-apply-bj-prepare,robot-3055-apply-select-jq,robot-3055-apply-select-sy,robot-3055-apply-bj-editIDCardCheck,robot-3055-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3055', 'rongsheng', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-融盛-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3055_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3055', 'rongsheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi融盛北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3055_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3055', 'rongsheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi融盛北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3055_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3055', 'rongsheng', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-融盛-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3055-login,robot-3055-prepareQueryCode,robot-3055-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3014', 'anhua', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安华-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-IdCarChekc" //申请验证码    else{        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3014', 'anhua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安华-报价', 'def getTemplateGroup(dataSource) {    return "edi-3014-queryCar,edi-3014-xbQuery,edi-3014-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3014', 'anhua', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安华-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3014-queryCar,edi-3014-xbQuery,edi-3014-askCharge,edi-3014-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3014', 'anhua', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安华-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3014-queryCar,edi-3014-xbQuery,edi-3014-askCharge,edi-3014-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3014', 'anhua', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安华-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3014-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3014', 'anhua', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安华-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3014-login,robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3014-login,robot-3014-ObtainConfig,robot-3014-checkInsurePerson,robot-3014-changePerson,robot-3014-checkInsuredPerson,robot-3014-changePerson,robot-3014-prepareEdit," +                    "robot-3014-prepareQueryCode,robot-3014-selectProposalCar,robot-3014-browseProposalCar,robot-3014-browseProposalCarefc,robot-3014-selectRenewalPolicyNo"        }else{            s = "robot-3014-login,robot-3014-ObtainConfig,robot-3014-checkInsurePerson,robot-3014-changePerson,robot-3014-checkInsuredPerson,robot-3014-changePerson,robot-3014-prepareEdit," +                    "robot-3014-prepareQueryCode,robot-3014-browseProposalCar,robot-3014-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3014-VehicleModelList" //上海车型查询        }        s += ",robot-3014-queryPrepare,robot-3014-vehicleQuery,robot-3014-queryTaxAbateForPlat,robot-3014-calActualValue,robot-3014-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3014-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3014-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-nomotor-unitedSaleEdit,robot-3014-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3014-login,robot_3014_bj_initData,robot_3014_bj_queryModel,robot_3014_bj_getSaleTaxInfo,robot_3014_bj_getRealValue,robot_3014_bj_getPersonData,robot_3014_bj_addPersonData,robot_3014_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3014', 'anhua', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安华-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3014-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3014-ObtainConfig,robot-3014-selectRenewal,robot-3014-editCengage,robot-3014-editCitemCar,robot-3014-editCinsured,robot-3014-renewalPolicy,robot-3014-renewalPolicyCI,robot-3014-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3014-VehicleModelList" //上海车型查询        }        s += ",robot-3014-vehicleQueryXB,robot-3014-queryTaxAbateForPlat,robot-3014-calActualValue,robot-3014-editCitemKind,robot-3014-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3014-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-refreshPlanByTimes,robot-3014-insert"            }else{                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3014-getMaxCsellFee,robot-3014-getPrpCseller,robot-3014-getPrpCsellerCI,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            s += ",robot-3014-getMaxCsellFee,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"        }    }else{        s +=",robot-3014-ObtainConfig,robot-3014-checkInsurePerson,robot-3014-changePerson,robot-3014-checkInsuredPerson,robot-3014-changePerson,robot-3014-prepareEdit,robot-3014-selectRenewalPolicyNo,robot-3014-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3014-VehicleModelList" //上海车型查询        }        s += ",robot-3014-queryPrepare,robot-3014-vehicleQuery,robot-3014-queryTaxAbateForPlat,robot-3014-calActualValue,robot-3014-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3014-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3014-calAnciInfo,robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            }else{                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-nomotor-unitedSaleEdit,robot-3014-nomotor-saveUnitedSale,robot-3014-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3014-getMaxCsellFee,robot-3014-getPrpCseller,robot-3014-getPrpCsellerCI,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            s += ",robot-3014-getMaxCsellFee,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-nomotor-unitedSaleEdit,robot-3014-nomotor-saveUnitedSale,robot-3014-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3014-login,robot_3014_bj_initData,robot_3014_bj_queryModel,robot_3014_bj_getSaleTaxInfo,robot_3014_bj_getRealValue,robot_3014_bj_getPersonData,robot_3014_bj_addPersonData,robot_3014_bj_askCharge,robot_3014_bj_queryPayForXSFY,robot_3014_bj_getCagentCI,robot_3014_bj_getCagent,robot_3014_bj_queryPayForXSFY_req,robot_3014_bj_queryIlogEngage,robot_3014_bj_insureRefrenshPlan,robot_3014_bj_insure4S,robot-3014-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3014', 'anhua', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安华-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ" +            ",robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind,robot-3014-nomotor-query,robot-3014-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3014', 'anhua', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安华-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ" +            ",robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind,robot-3014-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3014', 'anhua', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安华-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ" +            ",robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind,robot-3014-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3014', 'anhua', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安华农业江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安华-电销', 'def getTemplateGroup(dataSource){    return "robot-3014-pureESale_Login,robot-3014-pureESale_Welcome,robot-3014-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3014', 'anhua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安华续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3014-login,robot-3014-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3014', 'anhua', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安华-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3014-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3014-ObtainConfig,robot-3014-selectRenewal,robot-3014-editCengage,robot-3014-editCitemCar,robot-3014-editCinsured,robot-3014-renewalPolicy,robot-3014-renewalPolicyCI,robot-3014-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3014-VehicleModelList" //上海车型查询        }        s += ",robot-3014-vehicleQueryXB,robot-3014-queryTaxAbateForPlat,robot-3014-calActualValue,robot-3014-editCitemKind,robot-3014-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3014-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-refreshPlanByTimes,robot-3014-insert"            }else{                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3014-calAnciInfo,robot-3014-getMaxCsellFee,robot-3014-getPrpCseller,robot-3014-getPrpCsellerCI,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            s += ",robot-3014-getMaxCsellFee,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"        }    }else{        s += ",robot-3014-ObtainConfig,robot-3014-checkInsurePerson,robot-3014-changePerson,robot-3014-checkInsuredPerson,robot-3014-changePerson,robot-3014-prepareEdit,robot-3014-editCengage,robot-3014-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3014-queryVehiclePMCheck,robot-3014-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3014-VehicleModelList" //上海车型查询        }        s += ",robot-3014-queryPrepare,robot-3014-vehicleQuery,robot-3014-queryTaxAbateForPlat,robot-3014-calActualValue,robot-3014-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3014-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3014-calAnciInfo,robot-3014-queryPayFor,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            }else{                s += ",robot-3014-calAnciInfo,robot-3014-checkAgentType,robot-3014-queryPayForSCMS,robot-3014-getCagent,robot-3014-getCagentCI,robot-3014-refreshPlanByTimes,robot-3014-nomotor-unitedSaleEdit,robot-3014-nomotor-saveUnitedSale,robot-3014-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3014-calAnciInfo,robot-3014-getMaxCsellFee,robot-3014-getPrpCseller,robot-3014-getPrpCsellerCI,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-insert"            s += ",robot-3014-getMaxCsellFee,robot-3014-queryPayForSCMS,robot-3014-refreshPlanByTimes,robot-3014-nomotor-unitedSaleEdit,robot-3014-nomotor-saveUnitedSale,robot-3014-insert"        }    }    s += ",robot-3014-checkRiskCode,robot-3014-editMainUwtFlag,robot-3014-editSubmitUndwrt,robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-showUndwrtMsgQ,robot-3014-showUndwrtMsgS"+            ",robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ,robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind,robot-3014-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3014-login,robot_3014_bj_initData,robot_3014_bj_queryModel,robot_3014_bj_getSaleTaxInfo,robot_3014_bj_getRealValue,robot_3014_bj_getPersonData,robot_3014_bj_addPersonData,robot_3014_bj_askCharge,robot_3014_bj_queryPayForXSFY,robot_3014_bj_getCagentCI,robot_3014_bj_getCagent,robot_3014_bj_queryPayForXSFY_req,robot_3014_bj_queryIlogEngage,robot_3014_bj_insureRefrenshPlan,robot_3014_bj_insure4S,robot-3014-uploadImage,robot_3014_bj_autoInsure,robot_3014_bj_showUndwrtMsgQ,robot_3014_bj_showUndwrtMsgS";       s += ",robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ,robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +                ",robot-3014-showCinsuredS,robot-3014-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3014', 'anhua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安华-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3014-qrcode_login,robot-3014-qrcode_printTwoBarCodeServlet,robot-3014-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3014-qrcode_login,robot-3014-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3014-qrcode_login,robot-3014-qrcode_editCheckFlag,robot-3014-qrcode_gotoJfcd,robot-3014-qrcode_prepareEditByJF,robot-3014-qrcode_getBusinessIn" +                ",robot-3014-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3014-qrcode_login,robot-3014-qrcode_editCheckFlag,robot-3014-qrcode_gotoJfcd,robot-3014-qrcode_prepareEditByJF,robot-3014-qrcode_getBusinessIn" +                ",robot-3014-qrcode_checkBeforeCalculate,robot-3014-qrcode_saveByJF,robot-3014-qrcode_getBusinessIn_alipay,robot-3014-qrcode_editFeeInfor,robot-3014-qrcode_editPayFeeByWeChat,robot-3014-qrcode_saveByWeChat,robot-3014-qrcode_save";		} else {					return  "robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-bj-editIDCardCheck,robot-3014-apply-selectIsNetProp,robot-3014-apply-saveCheckCode,robot-3014-qrcode_editCheckFlag,robot-3014-qrcode_gotoJfcd,robot-3014-qrcode_prepareEditByJF,robot-3014-qrcode_getBusinessIn" +",robot-3014-qrcode_checkBeforeCalculate,robot-3014-qrcode_saveByJF,robot-3014-qrcode_getBusinessIn_alipay,robot-3014-qrcode_editFeeInfor,robot-3014-qrcode_editPayFeeByWeChat,robot-3014-qrcode_saveByWeChat,robot-3014-qrcode_save";		}}    else {              return "robot-3014-qrcode_login,robot-3014-qrcode_editCheckFlag,robot-3014-qrcode_gotoJfcd,robot-3014-qrcode_prepareEditByJF,robot-3014-qrcode_getBusinessIn" +                ",robot-3014-qrcode_checkBeforeCalculate,robot-3014-qrcode_saveByJF,robot-3014-qrcode_getBusinessIn_alipay,robot-3014-qrcode_editFeeInfor,robot-3014-qrcode_editPayFeeByWeChat,robot-3014-qrcode_saveByWeChat,robot-3014-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3014', 'anhua', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安华-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3014-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3014-qrcode_query_editCheckFlag,robot-3014-qrcode_query_gotoJfcd,robot-3014-qrcode_query_prepareEditByJF" +                ",robot-3014-qrcode_query_editMainInfor,robot-3014-qrcode_query_getBusinessIn,robot-3014-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ" +            ",robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind,robot-3014-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3014', 'anhua', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安华-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-IdCarChekc" //申请验证码    else{        return"robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-editIDCardCheck,robot-3014-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3014', 'anhua', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安华-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectPolicyefc,robot-3014-selectPolicybiz,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ" +            ",robot-3014-showCitemCarQ,robot-3014-showCinsuredQ,robot-3014-showCitemKindCI,robot-3014-browseProposalS,robot-3014-showCitemCarS" +            ",robot-3014-showCinsuredS,robot-3014-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3014', 'anhua', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3014-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectProposalQ,robot-3014-selectProposalS,robot-3014-browseProposalQ,robot-3014-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3014', 'anhua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安华-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-bj-editIDCardCheck,robot-3014-apply-bj-IdCarChekc";    } else {        s = "robot-3014-qrcode_login,robot-3014-qrcode_editCheckFlag,robot-3014-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3014', 'anhua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安华报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3014_ask_charge,edi_3014_noMotor_quote"	} else {		return "edi_3014_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3014', 'anhua', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安华-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3014_ask_charge,edi_3014_noMotor_quote,edi_3014_askInsure,edi_3014_uploadImg,edi_3014_submitInsure,edi_3014_noMotor_submit" 	  	} else {		return "edi_3014_ask_chargeold,edi_3014_askInsure,edi_3014_uploadImg,edi_3014_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3014', 'anhua', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安华-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3014_ask_charge,edi_3014_noMotor_quote,edi_3014_askInsure,edi_3014_uploadImg" 	} else {		return "edi_3014_ask_chargeold,edi_3014_askInsure,edi_3014_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3014', 'anhua', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安华-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3014_efc_policyinfo,edi_3014_biz_policyinfo,edi_3014_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3014', 'anhua', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安华-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3014_efc_insurequery,edi_3014_biz_insurequery,edi_3014_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3014', 'anhua', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安华短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-bj-editIDCardCheck,robot-3014-apply-saveCheckCode,robot-3014-apply-selectIsNetProp";    } else {        s = "robot-3014-qrcode_login,robot-3014-apply-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3014', 'anhua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安华-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3014-bj-qrcode_login,robot-3014-apply-bj-prepare,robot-3014-apply-select-jq,robot-3014-apply-select-sy,robot-3014-apply-bj-editIDCardCheck,robot-3014-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3014', 'anhua', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安华-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3014_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3014', 'anhua', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安华北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3014_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3014', 'anhua', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安华北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3014_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3014', 'anhua', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安华-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3014-login,robot-3014-prepareQueryCode,robot-3014-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '4002', 'taikang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-泰康-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-IdCarChekc" //申请验证码    else{        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '4002', 'taikang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-泰康-报价', 'def getTemplateGroup(dataSource) {    return "edi-4002-queryCar,edi-4002-xbQuery,edi-4002-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '4002', 'taikang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-泰康-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-4002-queryCar,edi-4002-xbQuery,edi-4002-askCharge,edi-4002-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '4002', 'taikang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-泰康-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-4002-queryCar,edi-4002-xbQuery,edi-4002-askCharge,edi-4002-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '4002', 'taikang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-泰康-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-4002-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '4002', 'taikang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-泰康-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-4002-login,robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-4002-login,robot-4002-ObtainConfig,robot-4002-checkInsurePerson,robot-4002-changePerson,robot-4002-checkInsuredPerson,robot-4002-changePerson,robot-4002-prepareEdit," +                    "robot-4002-prepareQueryCode,robot-4002-selectProposalCar,robot-4002-browseProposalCar,robot-4002-browseProposalCarefc,robot-4002-selectRenewalPolicyNo"        }else{            s = "robot-4002-login,robot-4002-ObtainConfig,robot-4002-checkInsurePerson,robot-4002-changePerson,robot-4002-checkInsuredPerson,robot-4002-changePerson,robot-4002-prepareEdit," +                    "robot-4002-prepareQueryCode,robot-4002-browseProposalCar,robot-4002-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-4002-VehicleModelList" //上海车型查询        }        s += ",robot-4002-queryPrepare,robot-4002-vehicleQuery,robot-4002-queryTaxAbateForPlat,robot-4002-calActualValue,robot-4002-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-4002-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-4002-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-nomotor-unitedSaleEdit,robot-4002-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-4002-login,robot_4002_bj_initData,robot_4002_bj_queryModel,robot_4002_bj_getSaleTaxInfo,robot_4002_bj_getRealValue,robot_4002_bj_getPersonData,robot_4002_bj_addPersonData,robot_4002_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '4002', 'taikang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-泰康-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-4002-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-4002-ObtainConfig,robot-4002-selectRenewal,robot-4002-editCengage,robot-4002-editCitemCar,robot-4002-editCinsured,robot-4002-renewalPolicy,robot-4002-renewalPolicyCI,robot-4002-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-4002-VehicleModelList" //上海车型查询        }        s += ",robot-4002-vehicleQueryXB,robot-4002-queryTaxAbateForPlat,robot-4002-calActualValue,robot-4002-editCitemKind,robot-4002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-4002-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-refreshPlanByTimes,robot-4002-insert"            }else{                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-4002-getMaxCsellFee,robot-4002-getPrpCseller,robot-4002-getPrpCsellerCI,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            s += ",robot-4002-getMaxCsellFee,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"        }    }else{        s +=",robot-4002-ObtainConfig,robot-4002-checkInsurePerson,robot-4002-changePerson,robot-4002-checkInsuredPerson,robot-4002-changePerson,robot-4002-prepareEdit,robot-4002-selectRenewalPolicyNo,robot-4002-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-4002-VehicleModelList" //上海车型查询        }        s += ",robot-4002-queryPrepare,robot-4002-vehicleQuery,robot-4002-queryTaxAbateForPlat,robot-4002-calActualValue,robot-4002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-4002-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-4002-calAnciInfo,robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            }else{                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-nomotor-unitedSaleEdit,robot-4002-nomotor-saveUnitedSale,robot-4002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-4002-getMaxCsellFee,robot-4002-getPrpCseller,robot-4002-getPrpCsellerCI,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            s += ",robot-4002-getMaxCsellFee,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-nomotor-unitedSaleEdit,robot-4002-nomotor-saveUnitedSale,robot-4002-insert"        }         if("1211000000".equals(orgId)){            s = "robot-4002-login,robot_4002_bj_initData,robot_4002_bj_queryModel,robot_4002_bj_getSaleTaxInfo,robot_4002_bj_getRealValue,robot_4002_bj_getPersonData,robot_4002_bj_addPersonData,robot_4002_bj_askCharge,robot_4002_bj_queryPayForXSFY,robot_4002_bj_getCagentCI,robot_4002_bj_getCagent,robot_4002_bj_queryPayForXSFY_req,robot_4002_bj_queryIlogEngage,robot_4002_bj_insureRefrenshPlan,robot_4002_bj_insure4S,robot-4002-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '4002', 'taikang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-泰康-核保查询', 'def getTemplateGroup(dataSource){    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ" +            ",robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind,robot-4002-nomotor-query,robot-4002-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '4002', 'taikang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-泰康-报价查询', 'def getTemplateGroup(dataSource){    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ" +            ",robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind,robot-4002-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '4002', 'taikang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-泰康-承保查询', 'def getTemplateGroup(dataSource){    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ" +            ",robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind,robot-4002-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '4002', 'taikang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "泰康财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-泰康-电销', 'def getTemplateGroup(dataSource){    return "robot-4002-pureESale_Login,robot-4002-pureESale_Welcome,robot-4002-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '4002', 'taikang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰康续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-4002-login,robot-4002-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '4002', 'taikang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-泰康-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-4002-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-4002-ObtainConfig,robot-4002-selectRenewal,robot-4002-editCengage,robot-4002-editCitemCar,robot-4002-editCinsured,robot-4002-renewalPolicy,robot-4002-renewalPolicyCI,robot-4002-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-4002-VehicleModelList" //上海车型查询        }        s += ",robot-4002-vehicleQueryXB,robot-4002-queryTaxAbateForPlat,robot-4002-calActualValue,robot-4002-editCitemKind,robot-4002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-4002-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-refreshPlanByTimes,robot-4002-insert"            }else{                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-4002-calAnciInfo,robot-4002-getMaxCsellFee,robot-4002-getPrpCseller,robot-4002-getPrpCsellerCI,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            s += ",robot-4002-getMaxCsellFee,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"        }    }else{        s += ",robot-4002-ObtainConfig,robot-4002-checkInsurePerson,robot-4002-changePerson,robot-4002-checkInsuredPerson,robot-4002-changePerson,robot-4002-prepareEdit,robot-4002-editCengage,robot-4002-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-4002-queryVehiclePMCheck,robot-4002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-4002-VehicleModelList" //上海车型查询        }        s += ",robot-4002-queryPrepare,robot-4002-vehicleQuery,robot-4002-queryTaxAbateForPlat,robot-4002-calActualValue,robot-4002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-4002-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-4002-calAnciInfo,robot-4002-queryPayFor,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            }else{                s += ",robot-4002-calAnciInfo,robot-4002-checkAgentType,robot-4002-queryPayForSCMS,robot-4002-getCagent,robot-4002-getCagentCI,robot-4002-refreshPlanByTimes,robot-4002-nomotor-unitedSaleEdit,robot-4002-nomotor-saveUnitedSale,robot-4002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-4002-calAnciInfo,robot-4002-getMaxCsellFee,robot-4002-getPrpCseller,robot-4002-getPrpCsellerCI,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-insert"            s += ",robot-4002-getMaxCsellFee,robot-4002-queryPayForSCMS,robot-4002-refreshPlanByTimes,robot-4002-nomotor-unitedSaleEdit,robot-4002-nomotor-saveUnitedSale,robot-4002-insert"        }    }    s += ",robot-4002-checkRiskCode,robot-4002-editMainUwtFlag,robot-4002-editSubmitUndwrt,robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-showUndwrtMsgQ,robot-4002-showUndwrtMsgS"+            ",robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ,robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind,robot-4002-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-4002-login,robot_4002_bj_initData,robot_4002_bj_queryModel,robot_4002_bj_getSaleTaxInfo,robot_4002_bj_getRealValue,robot_4002_bj_getPersonData,robot_4002_bj_addPersonData,robot_4002_bj_askCharge,robot_4002_bj_queryPayForXSFY,robot_4002_bj_getCagentCI,robot_4002_bj_getCagent,robot_4002_bj_queryPayForXSFY_req,robot_4002_bj_queryIlogEngage,robot_4002_bj_insureRefrenshPlan,robot_4002_bj_insure4S,robot-4002-uploadImage,robot_4002_bj_autoInsure,robot_4002_bj_showUndwrtMsgQ,robot_4002_bj_showUndwrtMsgS";       s += ",robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ,robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +                ",robot-4002-showCinsuredS,robot-4002-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '4002', 'taikang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰康-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-4002-qrcode_login,robot-4002-qrcode_printTwoBarCodeServlet,robot-4002-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-4002-qrcode_login,robot-4002-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-4002-qrcode_login,robot-4002-qrcode_editCheckFlag,robot-4002-qrcode_gotoJfcd,robot-4002-qrcode_prepareEditByJF,robot-4002-qrcode_getBusinessIn" +                ",robot-4002-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-4002-qrcode_login,robot-4002-qrcode_editCheckFlag,robot-4002-qrcode_gotoJfcd,robot-4002-qrcode_prepareEditByJF,robot-4002-qrcode_getBusinessIn" +                ",robot-4002-qrcode_checkBeforeCalculate,robot-4002-qrcode_saveByJF,robot-4002-qrcode_getBusinessIn_alipay,robot-4002-qrcode_editFeeInfor,robot-4002-qrcode_editPayFeeByWeChat,robot-4002-qrcode_saveByWeChat,robot-4002-qrcode_save";		} else {					return  "robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-bj-editIDCardCheck,robot-4002-apply-selectIsNetProp,robot-4002-apply-saveCheckCode,robot-4002-qrcode_editCheckFlag,robot-4002-qrcode_gotoJfcd,robot-4002-qrcode_prepareEditByJF,robot-4002-qrcode_getBusinessIn" +",robot-4002-qrcode_checkBeforeCalculate,robot-4002-qrcode_saveByJF,robot-4002-qrcode_getBusinessIn_alipay,robot-4002-qrcode_editFeeInfor,robot-4002-qrcode_editPayFeeByWeChat,robot-4002-qrcode_saveByWeChat,robot-4002-qrcode_save";		}}    else {              return "robot-4002-qrcode_login,robot-4002-qrcode_editCheckFlag,robot-4002-qrcode_gotoJfcd,robot-4002-qrcode_prepareEditByJF,robot-4002-qrcode_getBusinessIn" +                ",robot-4002-qrcode_checkBeforeCalculate,robot-4002-qrcode_saveByJF,robot-4002-qrcode_getBusinessIn_alipay,robot-4002-qrcode_editFeeInfor,robot-4002-qrcode_editPayFeeByWeChat,robot-4002-qrcode_saveByWeChat,robot-4002-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '4002', 'taikang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-泰康-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-4002-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-4002-qrcode_query_editCheckFlag,robot-4002-qrcode_query_gotoJfcd,robot-4002-qrcode_query_prepareEditByJF" +                ",robot-4002-qrcode_query_editMainInfor,robot-4002-qrcode_query_getBusinessIn,robot-4002-qrcode_query_proposalToPolicy";    }    s +=  ",robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ" +            ",robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind,robot-4002-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '4002', 'taikang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-泰康-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-IdCarChekc" //申请验证码    else{        return"robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-editIDCardCheck,robot-4002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '4002', 'taikang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-泰康-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectPolicyefc,robot-4002-selectPolicybiz,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ" +            ",robot-4002-showCitemCarQ,robot-4002-showCinsuredQ,robot-4002-showCitemKindCI,robot-4002-browseProposalS,robot-4002-showCitemCarS" +            ",robot-4002-showCinsuredS,robot-4002-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '4002', 'taikang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-4002-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectProposalQ,robot-4002-selectProposalS,robot-4002-browseProposalQ,robot-4002-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '4002', 'taikang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰康-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-bj-editIDCardCheck,robot-4002-apply-bj-IdCarChekc";    } else {        s = "robot-4002-qrcode_login,robot-4002-qrcode_editCheckFlag,robot-4002-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '4002', 'taikang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi泰康报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_4002_ask_charge,edi_4002_noMotor_quote"	} else {		return "edi_4002_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '4002', 'taikang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-泰康-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_4002_ask_charge,edi_4002_noMotor_quote,edi_4002_askInsure,edi_4002_uploadImg,edi_4002_submitInsure,edi_4002_noMotor_submit" 	  	} else {		return "edi_4002_ask_chargeold,edi_4002_askInsure,edi_4002_uploadImg,edi_4002_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '4002', 'taikang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-泰康-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_4002_ask_charge,edi_4002_noMotor_quote,edi_4002_askInsure,edi_4002_uploadImg" 	} else {		return "edi_4002_ask_chargeold,edi_4002_askInsure,edi_4002_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '4002', 'taikang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-泰康-承保查询', 'def getTemplateGroup(dataSource){  return "edi_4002_efc_policyinfo,edi_4002_biz_policyinfo,edi_4002_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '4002', 'taikang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-泰康-核保查询', 'def getTemplateGroup(dataSource){    return "edi_4002_efc_insurequery,edi_4002_biz_insurequery,edi_4002_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '4002', 'taikang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京泰康短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-bj-editIDCardCheck,robot-4002-apply-saveCheckCode,robot-4002-apply-selectIsNetProp";    } else {        s = "robot-4002-qrcode_login,robot-4002-apply-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '4002', 'taikang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰康-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-4002-bj-qrcode_login,robot-4002-apply-bj-prepare,robot-4002-apply-select-jq,robot-4002-apply-select-sy,robot-4002-apply-bj-editIDCardCheck,robot-4002-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '4002', 'taikang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-泰康-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_4002_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '4002', 'taikang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi泰康北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_4002_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '4002', 'taikang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi泰康北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_4002_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '4002', 'taikang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-泰康-承保查询', 'def getTemplateGroup(dataSource){    return "robot-4002-login,robot-4002-prepareQueryCode,robot-4002-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3058', 'taishan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-泰山-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-IdCarChekc" //申请验证码    else{        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3058', 'taishan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-泰山-报价', 'def getTemplateGroup(dataSource) {    return "edi-3058-queryCar,edi-3058-xbQuery,edi-3058-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3058', 'taishan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-泰山-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3058-queryCar,edi-3058-xbQuery,edi-3058-askCharge,edi-3058-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3058', 'taishan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-泰山-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3058-queryCar,edi-3058-xbQuery,edi-3058-askCharge,edi-3058-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3058', 'taishan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-泰山-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3058-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3058', 'taishan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-泰山-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3058-login,robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3058-login,robot-3058-ObtainConfig,robot-3058-checkInsurePerson,robot-3058-changePerson,robot-3058-checkInsuredPerson,robot-3058-changePerson,robot-3058-prepareEdit," +                    "robot-3058-prepareQueryCode,robot-3058-selectProposalCar,robot-3058-browseProposalCar,robot-3058-browseProposalCarefc,robot-3058-selectRenewalPolicyNo"        }else{            s = "robot-3058-login,robot-3058-ObtainConfig,robot-3058-checkInsurePerson,robot-3058-changePerson,robot-3058-checkInsuredPerson,robot-3058-changePerson,robot-3058-prepareEdit," +                    "robot-3058-prepareQueryCode,robot-3058-browseProposalCar,robot-3058-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3058-VehicleModelList" //上海车型查询        }        s += ",robot-3058-queryPrepare,robot-3058-vehicleQuery,robot-3058-queryTaxAbateForPlat,robot-3058-calActualValue,robot-3058-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3058-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3058-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-nomotor-unitedSaleEdit,robot-3058-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3058-login,robot_3058_bj_initData,robot_3058_bj_queryModel,robot_3058_bj_getSaleTaxInfo,robot_3058_bj_getRealValue,robot_3058_bj_getPersonData,robot_3058_bj_addPersonData,robot_3058_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3058', 'taishan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-泰山-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3058-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3058-ObtainConfig,robot-3058-selectRenewal,robot-3058-editCengage,robot-3058-editCitemCar,robot-3058-editCinsured,robot-3058-renewalPolicy,robot-3058-renewalPolicyCI,robot-3058-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3058-VehicleModelList" //上海车型查询        }        s += ",robot-3058-vehicleQueryXB,robot-3058-queryTaxAbateForPlat,robot-3058-calActualValue,robot-3058-editCitemKind,robot-3058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3058-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-refreshPlanByTimes,robot-3058-insert"            }else{                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3058-getMaxCsellFee,robot-3058-getPrpCseller,robot-3058-getPrpCsellerCI,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            s += ",robot-3058-getMaxCsellFee,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"        }    }else{        s +=",robot-3058-ObtainConfig,robot-3058-checkInsurePerson,robot-3058-changePerson,robot-3058-checkInsuredPerson,robot-3058-changePerson,robot-3058-prepareEdit,robot-3058-selectRenewalPolicyNo,robot-3058-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3058-VehicleModelList" //上海车型查询        }        s += ",robot-3058-queryPrepare,robot-3058-vehicleQuery,robot-3058-queryTaxAbateForPlat,robot-3058-calActualValue,robot-3058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3058-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3058-calAnciInfo,robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            }else{                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-nomotor-unitedSaleEdit,robot-3058-nomotor-saveUnitedSale,robot-3058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3058-getMaxCsellFee,robot-3058-getPrpCseller,robot-3058-getPrpCsellerCI,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            s += ",robot-3058-getMaxCsellFee,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-nomotor-unitedSaleEdit,robot-3058-nomotor-saveUnitedSale,robot-3058-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3058-login,robot_3058_bj_initData,robot_3058_bj_queryModel,robot_3058_bj_getSaleTaxInfo,robot_3058_bj_getRealValue,robot_3058_bj_getPersonData,robot_3058_bj_addPersonData,robot_3058_bj_askCharge,robot_3058_bj_queryPayForXSFY,robot_3058_bj_getCagentCI,robot_3058_bj_getCagent,robot_3058_bj_queryPayForXSFY_req,robot_3058_bj_queryIlogEngage,robot_3058_bj_insureRefrenshPlan,robot_3058_bj_insure4S,robot-3058-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3058', 'taishan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-泰山-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ" +            ",robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind,robot-3058-nomotor-query,robot-3058-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3058', 'taishan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-泰山-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ" +            ",robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind,robot-3058-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3058', 'taishan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-泰山-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ" +            ",robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind,robot-3058-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3058', 'taishan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "泰山财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-泰山-电销', 'def getTemplateGroup(dataSource){    return "robot-3058-pureESale_Login,robot-3058-pureESale_Welcome,robot-3058-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3058', 'taishan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰山续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3058-login,robot-3058-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3058', 'taishan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-泰山-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3058-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3058-ObtainConfig,robot-3058-selectRenewal,robot-3058-editCengage,robot-3058-editCitemCar,robot-3058-editCinsured,robot-3058-renewalPolicy,robot-3058-renewalPolicyCI,robot-3058-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3058-VehicleModelList" //上海车型查询        }        s += ",robot-3058-vehicleQueryXB,robot-3058-queryTaxAbateForPlat,robot-3058-calActualValue,robot-3058-editCitemKind,robot-3058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3058-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-refreshPlanByTimes,robot-3058-insert"            }else{                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3058-calAnciInfo,robot-3058-getMaxCsellFee,robot-3058-getPrpCseller,robot-3058-getPrpCsellerCI,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            s += ",robot-3058-getMaxCsellFee,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"        }    }else{        s += ",robot-3058-ObtainConfig,robot-3058-checkInsurePerson,robot-3058-changePerson,robot-3058-checkInsuredPerson,robot-3058-changePerson,robot-3058-prepareEdit,robot-3058-editCengage,robot-3058-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3058-queryVehiclePMCheck,robot-3058-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3058-VehicleModelList" //上海车型查询        }        s += ",robot-3058-queryPrepare,robot-3058-vehicleQuery,robot-3058-queryTaxAbateForPlat,robot-3058-calActualValue,robot-3058-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3058-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3058-calAnciInfo,robot-3058-queryPayFor,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            }else{                s += ",robot-3058-calAnciInfo,robot-3058-checkAgentType,robot-3058-queryPayForSCMS,robot-3058-getCagent,robot-3058-getCagentCI,robot-3058-refreshPlanByTimes,robot-3058-nomotor-unitedSaleEdit,robot-3058-nomotor-saveUnitedSale,robot-3058-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3058-calAnciInfo,robot-3058-getMaxCsellFee,robot-3058-getPrpCseller,robot-3058-getPrpCsellerCI,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-insert"            s += ",robot-3058-getMaxCsellFee,robot-3058-queryPayForSCMS,robot-3058-refreshPlanByTimes,robot-3058-nomotor-unitedSaleEdit,robot-3058-nomotor-saveUnitedSale,robot-3058-insert"        }    }    s += ",robot-3058-checkRiskCode,robot-3058-editMainUwtFlag,robot-3058-editSubmitUndwrt,robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-showUndwrtMsgQ,robot-3058-showUndwrtMsgS"+            ",robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ,robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind,robot-3058-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3058-login,robot_3058_bj_initData,robot_3058_bj_queryModel,robot_3058_bj_getSaleTaxInfo,robot_3058_bj_getRealValue,robot_3058_bj_getPersonData,robot_3058_bj_addPersonData,robot_3058_bj_askCharge,robot_3058_bj_queryPayForXSFY,robot_3058_bj_getCagentCI,robot_3058_bj_getCagent,robot_3058_bj_queryPayForXSFY_req,robot_3058_bj_queryIlogEngage,robot_3058_bj_insureRefrenshPlan,robot_3058_bj_insure4S,robot-3058-uploadImage,robot_3058_bj_autoInsure,robot_3058_bj_showUndwrtMsgQ,robot_3058_bj_showUndwrtMsgS";       s += ",robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ,robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +                ",robot-3058-showCinsuredS,robot-3058-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3058', 'taishan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰山-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3058-qrcode_login,robot-3058-qrcode_printTwoBarCodeServlet,robot-3058-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3058-qrcode_login,robot-3058-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3058-qrcode_login,robot-3058-qrcode_editCheckFlag,robot-3058-qrcode_gotoJfcd,robot-3058-qrcode_prepareEditByJF,robot-3058-qrcode_getBusinessIn" +                ",robot-3058-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3058-qrcode_login,robot-3058-qrcode_editCheckFlag,robot-3058-qrcode_gotoJfcd,robot-3058-qrcode_prepareEditByJF,robot-3058-qrcode_getBusinessIn" +                ",robot-3058-qrcode_checkBeforeCalculate,robot-3058-qrcode_saveByJF,robot-3058-qrcode_getBusinessIn_alipay,robot-3058-qrcode_editFeeInfor,robot-3058-qrcode_editPayFeeByWeChat,robot-3058-qrcode_saveByWeChat,robot-3058-qrcode_save";		} else {					return  "robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-bj-editIDCardCheck,robot-3058-apply-selectIsNetProp,robot-3058-apply-saveCheckCode,robot-3058-qrcode_editCheckFlag,robot-3058-qrcode_gotoJfcd,robot-3058-qrcode_prepareEditByJF,robot-3058-qrcode_getBusinessIn" +",robot-3058-qrcode_checkBeforeCalculate,robot-3058-qrcode_saveByJF,robot-3058-qrcode_getBusinessIn_alipay,robot-3058-qrcode_editFeeInfor,robot-3058-qrcode_editPayFeeByWeChat,robot-3058-qrcode_saveByWeChat,robot-3058-qrcode_save";		}}    else {              return "robot-3058-qrcode_login,robot-3058-qrcode_editCheckFlag,robot-3058-qrcode_gotoJfcd,robot-3058-qrcode_prepareEditByJF,robot-3058-qrcode_getBusinessIn" +                ",robot-3058-qrcode_checkBeforeCalculate,robot-3058-qrcode_saveByJF,robot-3058-qrcode_getBusinessIn_alipay,robot-3058-qrcode_editFeeInfor,robot-3058-qrcode_editPayFeeByWeChat,robot-3058-qrcode_saveByWeChat,robot-3058-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3058', 'taishan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-泰山-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3058-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3058-qrcode_query_editCheckFlag,robot-3058-qrcode_query_gotoJfcd,robot-3058-qrcode_query_prepareEditByJF" +                ",robot-3058-qrcode_query_editMainInfor,robot-3058-qrcode_query_getBusinessIn,robot-3058-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ" +            ",robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind,robot-3058-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3058', 'taishan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-泰山-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-IdCarChekc" //申请验证码    else{        return"robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-editIDCardCheck,robot-3058-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3058', 'taishan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-泰山-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectPolicyefc,robot-3058-selectPolicybiz,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ" +            ",robot-3058-showCitemCarQ,robot-3058-showCinsuredQ,robot-3058-showCitemKindCI,robot-3058-browseProposalS,robot-3058-showCitemCarS" +            ",robot-3058-showCinsuredS,robot-3058-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3058', 'taishan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3058-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectProposalQ,robot-3058-selectProposalS,robot-3058-browseProposalQ,robot-3058-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3058', 'taishan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰山-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-bj-editIDCardCheck,robot-3058-apply-bj-IdCarChekc";    } else {        s = "robot-3058-qrcode_login,robot-3058-qrcode_editCheckFlag,robot-3058-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3058', 'taishan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi泰山报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3058_ask_charge,edi_3058_noMotor_quote"	} else {		return "edi_3058_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3058', 'taishan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-泰山-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3058_ask_charge,edi_3058_noMotor_quote,edi_3058_askInsure,edi_3058_uploadImg,edi_3058_submitInsure,edi_3058_noMotor_submit" 	  	} else {		return "edi_3058_ask_chargeold,edi_3058_askInsure,edi_3058_uploadImg,edi_3058_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3058', 'taishan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-泰山-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3058_ask_charge,edi_3058_noMotor_quote,edi_3058_askInsure,edi_3058_uploadImg" 	} else {		return "edi_3058_ask_chargeold,edi_3058_askInsure,edi_3058_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3058', 'taishan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-泰山-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3058_efc_policyinfo,edi_3058_biz_policyinfo,edi_3058_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3058', 'taishan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-泰山-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3058_efc_insurequery,edi_3058_biz_insurequery,edi_3058_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3058', 'taishan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京泰山短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-bj-editIDCardCheck,robot-3058-apply-saveCheckCode,robot-3058-apply-selectIsNetProp";    } else {        s = "robot-3058-qrcode_login,robot-3058-apply-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3058', 'taishan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-泰山-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3058-bj-qrcode_login,robot-3058-apply-bj-prepare,robot-3058-apply-select-jq,robot-3058-apply-select-sy,robot-3058-apply-bj-editIDCardCheck,robot-3058-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3058', 'taishan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-泰山-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3058_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3058', 'taishan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi泰山北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3058_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3058', 'taishan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi泰山北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3058_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3058', 'taishan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-泰山-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3058-login,robot-3058-prepareQueryCode,robot-3058-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3066', 'yingdataihe', '15', '6', 'pro', 'other', b'1', '{}', '精灵-英大泰和-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-IdCarChekc" //申请验证码    else{        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3066', 'yingdataihe', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-报价', 'def getTemplateGroup(dataSource) {    return "edi-3066-queryCar,edi-3066-xbQuery,edi-3066-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3066', 'yingdataihe', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3066-queryCar,edi-3066-xbQuery,edi-3066-askCharge,edi-3066-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3066', 'yingdataihe', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3066-queryCar,edi-3066-xbQuery,edi-3066-askCharge,edi-3066-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3066', 'yingdataihe', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3066-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3066', 'yingdataihe', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3066-login,robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3066-login,robot-3066-ObtainConfig,robot-3066-checkInsurePerson,robot-3066-changePerson,robot-3066-checkInsuredPerson,robot-3066-changePerson,robot-3066-prepareEdit," +                    "robot-3066-prepareQueryCode,robot-3066-selectProposalCar,robot-3066-browseProposalCar,robot-3066-browseProposalCarefc,robot-3066-selectRenewalPolicyNo"        }else{            s = "robot-3066-login,robot-3066-ObtainConfig,robot-3066-checkInsurePerson,robot-3066-changePerson,robot-3066-checkInsuredPerson,robot-3066-changePerson,robot-3066-prepareEdit," +                    "robot-3066-prepareQueryCode,robot-3066-browseProposalCar,robot-3066-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3066-VehicleModelList" //上海车型查询        }        s += ",robot-3066-queryPrepare,robot-3066-vehicleQuery,robot-3066-queryTaxAbateForPlat,robot-3066-calActualValue,robot-3066-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3066-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3066-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-nomotor-unitedSaleEdit,robot-3066-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3066-login,robot_3066_bj_initData,robot_3066_bj_queryModel,robot_3066_bj_getSaleTaxInfo,robot_3066_bj_getRealValue,robot_3066_bj_getPersonData,robot_3066_bj_addPersonData,robot_3066_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3066', 'yingdataihe', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3066-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3066-ObtainConfig,robot-3066-selectRenewal,robot-3066-editCengage,robot-3066-editCitemCar,robot-3066-editCinsured,robot-3066-renewalPolicy,robot-3066-renewalPolicyCI,robot-3066-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3066-VehicleModelList" //上海车型查询        }        s += ",robot-3066-vehicleQueryXB,robot-3066-queryTaxAbateForPlat,robot-3066-calActualValue,robot-3066-editCitemKind,robot-3066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3066-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-refreshPlanByTimes,robot-3066-insert"            }else{                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3066-getMaxCsellFee,robot-3066-getPrpCseller,robot-3066-getPrpCsellerCI,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            s += ",robot-3066-getMaxCsellFee,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"        }    }else{        s +=",robot-3066-ObtainConfig,robot-3066-checkInsurePerson,robot-3066-changePerson,robot-3066-checkInsuredPerson,robot-3066-changePerson,robot-3066-prepareEdit,robot-3066-selectRenewalPolicyNo,robot-3066-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3066-VehicleModelList" //上海车型查询        }        s += ",robot-3066-queryPrepare,robot-3066-vehicleQuery,robot-3066-queryTaxAbateForPlat,robot-3066-calActualValue,robot-3066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3066-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3066-calAnciInfo,robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            }else{                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-nomotor-unitedSaleEdit,robot-3066-nomotor-saveUnitedSale,robot-3066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3066-getMaxCsellFee,robot-3066-getPrpCseller,robot-3066-getPrpCsellerCI,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            s += ",robot-3066-getMaxCsellFee,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-nomotor-unitedSaleEdit,robot-3066-nomotor-saveUnitedSale,robot-3066-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3066-login,robot_3066_bj_initData,robot_3066_bj_queryModel,robot_3066_bj_getSaleTaxInfo,robot_3066_bj_getRealValue,robot_3066_bj_getPersonData,robot_3066_bj_addPersonData,robot_3066_bj_askCharge,robot_3066_bj_queryPayForXSFY,robot_3066_bj_getCagentCI,robot_3066_bj_getCagent,robot_3066_bj_queryPayForXSFY_req,robot_3066_bj_queryIlogEngage,robot_3066_bj_insureRefrenshPlan,robot_3066_bj_insure4S,robot-3066-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3066', 'yingdataihe', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ" +            ",robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind,robot-3066-nomotor-query,robot-3066-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3066', 'yingdataihe', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ" +            ",robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind,robot-3066-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3066', 'yingdataihe', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ" +            ",robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind,robot-3066-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3066', 'yingdataihe', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "英大泰和财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-英大泰和-电销', 'def getTemplateGroup(dataSource){    return "robot-3066-pureESale_Login,robot-3066-pureESale_Welcome,robot-3066-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3066', 'yingdataihe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-英大泰和续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3066-login,robot-3066-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3066', 'yingdataihe', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3066-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3066-ObtainConfig,robot-3066-selectRenewal,robot-3066-editCengage,robot-3066-editCitemCar,robot-3066-editCinsured,robot-3066-renewalPolicy,robot-3066-renewalPolicyCI,robot-3066-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3066-VehicleModelList" //上海车型查询        }        s += ",robot-3066-vehicleQueryXB,robot-3066-queryTaxAbateForPlat,robot-3066-calActualValue,robot-3066-editCitemKind,robot-3066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3066-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-refreshPlanByTimes,robot-3066-insert"            }else{                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3066-calAnciInfo,robot-3066-getMaxCsellFee,robot-3066-getPrpCseller,robot-3066-getPrpCsellerCI,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            s += ",robot-3066-getMaxCsellFee,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"        }    }else{        s += ",robot-3066-ObtainConfig,robot-3066-checkInsurePerson,robot-3066-changePerson,robot-3066-checkInsuredPerson,robot-3066-changePerson,robot-3066-prepareEdit,robot-3066-editCengage,robot-3066-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3066-queryVehiclePMCheck,robot-3066-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3066-VehicleModelList" //上海车型查询        }        s += ",robot-3066-queryPrepare,robot-3066-vehicleQuery,robot-3066-queryTaxAbateForPlat,robot-3066-calActualValue,robot-3066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3066-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3066-calAnciInfo,robot-3066-queryPayFor,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            }else{                s += ",robot-3066-calAnciInfo,robot-3066-checkAgentType,robot-3066-queryPayForSCMS,robot-3066-getCagent,robot-3066-getCagentCI,robot-3066-refreshPlanByTimes,robot-3066-nomotor-unitedSaleEdit,robot-3066-nomotor-saveUnitedSale,robot-3066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3066-calAnciInfo,robot-3066-getMaxCsellFee,robot-3066-getPrpCseller,robot-3066-getPrpCsellerCI,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-insert"            s += ",robot-3066-getMaxCsellFee,robot-3066-queryPayForSCMS,robot-3066-refreshPlanByTimes,robot-3066-nomotor-unitedSaleEdit,robot-3066-nomotor-saveUnitedSale,robot-3066-insert"        }    }    s += ",robot-3066-checkRiskCode,robot-3066-editMainUwtFlag,robot-3066-editSubmitUndwrt,robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-showUndwrtMsgQ,robot-3066-showUndwrtMsgS"+            ",robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ,robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind,robot-3066-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3066-login,robot_3066_bj_initData,robot_3066_bj_queryModel,robot_3066_bj_getSaleTaxInfo,robot_3066_bj_getRealValue,robot_3066_bj_getPersonData,robot_3066_bj_addPersonData,robot_3066_bj_askCharge,robot_3066_bj_queryPayForXSFY,robot_3066_bj_getCagentCI,robot_3066_bj_getCagent,robot_3066_bj_queryPayForXSFY_req,robot_3066_bj_queryIlogEngage,robot_3066_bj_insureRefrenshPlan,robot_3066_bj_insure4S,robot-3066-uploadImage,robot_3066_bj_autoInsure,robot_3066_bj_showUndwrtMsgQ,robot_3066_bj_showUndwrtMsgS";       s += ",robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ,robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +                ",robot-3066-showCinsuredS,robot-3066-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3066', 'yingdataihe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3066-qrcode_login,robot-3066-qrcode_printTwoBarCodeServlet,robot-3066-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3066-qrcode_login,robot-3066-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3066-qrcode_login,robot-3066-qrcode_editCheckFlag,robot-3066-qrcode_gotoJfcd,robot-3066-qrcode_prepareEditByJF,robot-3066-qrcode_getBusinessIn" +                ",robot-3066-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3066-qrcode_login,robot-3066-qrcode_editCheckFlag,robot-3066-qrcode_gotoJfcd,robot-3066-qrcode_prepareEditByJF,robot-3066-qrcode_getBusinessIn" +                ",robot-3066-qrcode_checkBeforeCalculate,robot-3066-qrcode_saveByJF,robot-3066-qrcode_getBusinessIn_alipay,robot-3066-qrcode_editFeeInfor,robot-3066-qrcode_editPayFeeByWeChat,robot-3066-qrcode_saveByWeChat,robot-3066-qrcode_save";		} else {					return  "robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-bj-editIDCardCheck,robot-3066-apply-selectIsNetProp,robot-3066-apply-saveCheckCode,robot-3066-qrcode_editCheckFlag,robot-3066-qrcode_gotoJfcd,robot-3066-qrcode_prepareEditByJF,robot-3066-qrcode_getBusinessIn" +",robot-3066-qrcode_checkBeforeCalculate,robot-3066-qrcode_saveByJF,robot-3066-qrcode_getBusinessIn_alipay,robot-3066-qrcode_editFeeInfor,robot-3066-qrcode_editPayFeeByWeChat,robot-3066-qrcode_saveByWeChat,robot-3066-qrcode_save";		}}    else {              return "robot-3066-qrcode_login,robot-3066-qrcode_editCheckFlag,robot-3066-qrcode_gotoJfcd,robot-3066-qrcode_prepareEditByJF,robot-3066-qrcode_getBusinessIn" +                ",robot-3066-qrcode_checkBeforeCalculate,robot-3066-qrcode_saveByJF,robot-3066-qrcode_getBusinessIn_alipay,robot-3066-qrcode_editFeeInfor,robot-3066-qrcode_editPayFeeByWeChat,robot-3066-qrcode_saveByWeChat,robot-3066-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3066', 'yingdataihe', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3066-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3066-qrcode_query_editCheckFlag,robot-3066-qrcode_query_gotoJfcd,robot-3066-qrcode_query_prepareEditByJF" +                ",robot-3066-qrcode_query_editMainInfor,robot-3066-qrcode_query_getBusinessIn,robot-3066-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ" +            ",robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind,robot-3066-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3066', 'yingdataihe', '15', '6', 'pro', 'other', b'1', '{}', '精灵-英大泰和-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-IdCarChekc" //申请验证码    else{        return"robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-editIDCardCheck,robot-3066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3066', 'yingdataihe', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectPolicyefc,robot-3066-selectPolicybiz,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ" +            ",robot-3066-showCitemCarQ,robot-3066-showCinsuredQ,robot-3066-showCitemKindCI,robot-3066-browseProposalS,robot-3066-showCitemCarS" +            ",robot-3066-showCinsuredS,robot-3066-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3066', 'yingdataihe', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3066-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectProposalQ,robot-3066-selectProposalS,robot-3066-browseProposalQ,robot-3066-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3066', 'yingdataihe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-bj-editIDCardCheck,robot-3066-apply-bj-IdCarChekc";    } else {        s = "robot-3066-qrcode_login,robot-3066-qrcode_editCheckFlag,robot-3066-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3066', 'yingdataihe', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi英大泰和报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3066_ask_charge,edi_3066_noMotor_quote"	} else {		return "edi_3066_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3066', 'yingdataihe', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3066_ask_charge,edi_3066_noMotor_quote,edi_3066_askInsure,edi_3066_uploadImg,edi_3066_submitInsure,edi_3066_noMotor_submit" 	  	} else {		return "edi_3066_ask_chargeold,edi_3066_askInsure,edi_3066_uploadImg,edi_3066_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3066', 'yingdataihe', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3066_ask_charge,edi_3066_noMotor_quote,edi_3066_askInsure,edi_3066_uploadImg" 	} else {		return "edi_3066_ask_chargeold,edi_3066_askInsure,edi_3066_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3066', 'yingdataihe', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3066_efc_policyinfo,edi_3066_biz_policyinfo,edi_3066_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3066', 'yingdataihe', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3066_efc_insurequery,edi_3066_biz_insurequery,edi_3066_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3066', 'yingdataihe', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京英大泰和短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-bj-editIDCardCheck,robot-3066-apply-saveCheckCode,robot-3066-apply-selectIsNetProp";    } else {        s = "robot-3066-qrcode_login,robot-3066-apply-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3066', 'yingdataihe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-英大泰和-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3066-bj-qrcode_login,robot-3066-apply-bj-prepare,robot-3066-apply-select-jq,robot-3066-apply-select-sy,robot-3066-apply-bj-editIDCardCheck,robot-3066-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3066', 'yingdataihe', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-英大泰和-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3066_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3066', 'yingdataihe', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi英大泰和北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3066_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3066', 'yingdataihe', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi英大泰和北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3066_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3066', 'yingdataihe', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-英大泰和-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3066-login,robot-3066-prepareQueryCode,robot-3066-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3067', 'changjiang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-长江-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-IdCarChekc" //申请验证码    else{        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3067', 'changjiang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-长江-报价', 'def getTemplateGroup(dataSource) {    return "edi-3067-queryCar,edi-3067-xbQuery,edi-3067-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3067', 'changjiang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-长江-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3067-queryCar,edi-3067-xbQuery,edi-3067-askCharge,edi-3067-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3067', 'changjiang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-长江-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3067-queryCar,edi-3067-xbQuery,edi-3067-askCharge,edi-3067-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3067', 'changjiang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-长江-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3067-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3067', 'changjiang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-长江-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3067-login,robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3067-login,robot-3067-ObtainConfig,robot-3067-checkInsurePerson,robot-3067-changePerson,robot-3067-checkInsuredPerson,robot-3067-changePerson,robot-3067-prepareEdit," +                    "robot-3067-prepareQueryCode,robot-3067-selectProposalCar,robot-3067-browseProposalCar,robot-3067-browseProposalCarefc,robot-3067-selectRenewalPolicyNo"        }else{            s = "robot-3067-login,robot-3067-ObtainConfig,robot-3067-checkInsurePerson,robot-3067-changePerson,robot-3067-checkInsuredPerson,robot-3067-changePerson,robot-3067-prepareEdit," +                    "robot-3067-prepareQueryCode,robot-3067-browseProposalCar,robot-3067-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3067-VehicleModelList" //上海车型查询        }        s += ",robot-3067-queryPrepare,robot-3067-vehicleQuery,robot-3067-queryTaxAbateForPlat,robot-3067-calActualValue,robot-3067-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3067-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3067-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-nomotor-unitedSaleEdit,robot-3067-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3067-login,robot_3067_bj_initData,robot_3067_bj_queryModel,robot_3067_bj_getSaleTaxInfo,robot_3067_bj_getRealValue,robot_3067_bj_getPersonData,robot_3067_bj_addPersonData,robot_3067_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3067', 'changjiang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-长江-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3067-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3067-ObtainConfig,robot-3067-selectRenewal,robot-3067-editCengage,robot-3067-editCitemCar,robot-3067-editCinsured,robot-3067-renewalPolicy,robot-3067-renewalPolicyCI,robot-3067-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3067-VehicleModelList" //上海车型查询        }        s += ",robot-3067-vehicleQueryXB,robot-3067-queryTaxAbateForPlat,robot-3067-calActualValue,robot-3067-editCitemKind,robot-3067-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3067-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-refreshPlanByTimes,robot-3067-insert"            }else{                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3067-getMaxCsellFee,robot-3067-getPrpCseller,robot-3067-getPrpCsellerCI,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            s += ",robot-3067-getMaxCsellFee,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"        }    }else{        s +=",robot-3067-ObtainConfig,robot-3067-checkInsurePerson,robot-3067-changePerson,robot-3067-checkInsuredPerson,robot-3067-changePerson,robot-3067-prepareEdit,robot-3067-selectRenewalPolicyNo,robot-3067-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3067-VehicleModelList" //上海车型查询        }        s += ",robot-3067-queryPrepare,robot-3067-vehicleQuery,robot-3067-queryTaxAbateForPlat,robot-3067-calActualValue,robot-3067-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3067-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3067-calAnciInfo,robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            }else{                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-nomotor-unitedSaleEdit,robot-3067-nomotor-saveUnitedSale,robot-3067-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3067-getMaxCsellFee,robot-3067-getPrpCseller,robot-3067-getPrpCsellerCI,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            s += ",robot-3067-getMaxCsellFee,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-nomotor-unitedSaleEdit,robot-3067-nomotor-saveUnitedSale,robot-3067-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3067-login,robot_3067_bj_initData,robot_3067_bj_queryModel,robot_3067_bj_getSaleTaxInfo,robot_3067_bj_getRealValue,robot_3067_bj_getPersonData,robot_3067_bj_addPersonData,robot_3067_bj_askCharge,robot_3067_bj_queryPayForXSFY,robot_3067_bj_getCagentCI,robot_3067_bj_getCagent,robot_3067_bj_queryPayForXSFY_req,robot_3067_bj_queryIlogEngage,robot_3067_bj_insureRefrenshPlan,robot_3067_bj_insure4S,robot-3067-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3067', 'changjiang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-长江-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ" +            ",robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind,robot-3067-nomotor-query,robot-3067-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3067', 'changjiang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-长江-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ" +            ",robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind,robot-3067-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3067', 'changjiang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-长江-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ" +            ",robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind,robot-3067-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3067', 'changjiang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "长江财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-长江-电销', 'def getTemplateGroup(dataSource){    return "robot-3067-pureESale_Login,robot-3067-pureESale_Welcome,robot-3067-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3067', 'changjiang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长江续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3067-login,robot-3067-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3067', 'changjiang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-长江-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3067-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3067-ObtainConfig,robot-3067-selectRenewal,robot-3067-editCengage,robot-3067-editCitemCar,robot-3067-editCinsured,robot-3067-renewalPolicy,robot-3067-renewalPolicyCI,robot-3067-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3067-VehicleModelList" //上海车型查询        }        s += ",robot-3067-vehicleQueryXB,robot-3067-queryTaxAbateForPlat,robot-3067-calActualValue,robot-3067-editCitemKind,robot-3067-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3067-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-refreshPlanByTimes,robot-3067-insert"            }else{                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3067-calAnciInfo,robot-3067-getMaxCsellFee,robot-3067-getPrpCseller,robot-3067-getPrpCsellerCI,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            s += ",robot-3067-getMaxCsellFee,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"        }    }else{        s += ",robot-3067-ObtainConfig,robot-3067-checkInsurePerson,robot-3067-changePerson,robot-3067-checkInsuredPerson,robot-3067-changePerson,robot-3067-prepareEdit,robot-3067-editCengage,robot-3067-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3067-queryVehiclePMCheck,robot-3067-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3067-VehicleModelList" //上海车型查询        }        s += ",robot-3067-queryPrepare,robot-3067-vehicleQuery,robot-3067-queryTaxAbateForPlat,robot-3067-calActualValue,robot-3067-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3067-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3067-calAnciInfo,robot-3067-queryPayFor,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            }else{                s += ",robot-3067-calAnciInfo,robot-3067-checkAgentType,robot-3067-queryPayForSCMS,robot-3067-getCagent,robot-3067-getCagentCI,robot-3067-refreshPlanByTimes,robot-3067-nomotor-unitedSaleEdit,robot-3067-nomotor-saveUnitedSale,robot-3067-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3067-calAnciInfo,robot-3067-getMaxCsellFee,robot-3067-getPrpCseller,robot-3067-getPrpCsellerCI,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-insert"            s += ",robot-3067-getMaxCsellFee,robot-3067-queryPayForSCMS,robot-3067-refreshPlanByTimes,robot-3067-nomotor-unitedSaleEdit,robot-3067-nomotor-saveUnitedSale,robot-3067-insert"        }    }    s += ",robot-3067-checkRiskCode,robot-3067-editMainUwtFlag,robot-3067-editSubmitUndwrt,robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-showUndwrtMsgQ,robot-3067-showUndwrtMsgS"+            ",robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ,robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind,robot-3067-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3067-login,robot_3067_bj_initData,robot_3067_bj_queryModel,robot_3067_bj_getSaleTaxInfo,robot_3067_bj_getRealValue,robot_3067_bj_getPersonData,robot_3067_bj_addPersonData,robot_3067_bj_askCharge,robot_3067_bj_queryPayForXSFY,robot_3067_bj_getCagentCI,robot_3067_bj_getCagent,robot_3067_bj_queryPayForXSFY_req,robot_3067_bj_queryIlogEngage,robot_3067_bj_insureRefrenshPlan,robot_3067_bj_insure4S,robot-3067-uploadImage,robot_3067_bj_autoInsure,robot_3067_bj_showUndwrtMsgQ,robot_3067_bj_showUndwrtMsgS";       s += ",robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ,robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +                ",robot-3067-showCinsuredS,robot-3067-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3067', 'changjiang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长江-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3067-qrcode_login,robot-3067-qrcode_printTwoBarCodeServlet,robot-3067-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3067-qrcode_login,robot-3067-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3067-qrcode_login,robot-3067-qrcode_editCheckFlag,robot-3067-qrcode_gotoJfcd,robot-3067-qrcode_prepareEditByJF,robot-3067-qrcode_getBusinessIn" +                ",robot-3067-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3067-qrcode_login,robot-3067-qrcode_editCheckFlag,robot-3067-qrcode_gotoJfcd,robot-3067-qrcode_prepareEditByJF,robot-3067-qrcode_getBusinessIn" +                ",robot-3067-qrcode_checkBeforeCalculate,robot-3067-qrcode_saveByJF,robot-3067-qrcode_getBusinessIn_alipay,robot-3067-qrcode_editFeeInfor,robot-3067-qrcode_editPayFeeByWeChat,robot-3067-qrcode_saveByWeChat,robot-3067-qrcode_save";		} else {					return  "robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-bj-editIDCardCheck,robot-3067-apply-selectIsNetProp,robot-3067-apply-saveCheckCode,robot-3067-qrcode_editCheckFlag,robot-3067-qrcode_gotoJfcd,robot-3067-qrcode_prepareEditByJF,robot-3067-qrcode_getBusinessIn" +",robot-3067-qrcode_checkBeforeCalculate,robot-3067-qrcode_saveByJF,robot-3067-qrcode_getBusinessIn_alipay,robot-3067-qrcode_editFeeInfor,robot-3067-qrcode_editPayFeeByWeChat,robot-3067-qrcode_saveByWeChat,robot-3067-qrcode_save";		}}    else {              return "robot-3067-qrcode_login,robot-3067-qrcode_editCheckFlag,robot-3067-qrcode_gotoJfcd,robot-3067-qrcode_prepareEditByJF,robot-3067-qrcode_getBusinessIn" +                ",robot-3067-qrcode_checkBeforeCalculate,robot-3067-qrcode_saveByJF,robot-3067-qrcode_getBusinessIn_alipay,robot-3067-qrcode_editFeeInfor,robot-3067-qrcode_editPayFeeByWeChat,robot-3067-qrcode_saveByWeChat,robot-3067-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3067', 'changjiang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-长江-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3067-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3067-qrcode_query_editCheckFlag,robot-3067-qrcode_query_gotoJfcd,robot-3067-qrcode_query_prepareEditByJF" +                ",robot-3067-qrcode_query_editMainInfor,robot-3067-qrcode_query_getBusinessIn,robot-3067-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ" +            ",robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind,robot-3067-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3067', 'changjiang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-长江-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-IdCarChekc" //申请验证码    else{        return"robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-editIDCardCheck,robot-3067-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3067', 'changjiang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-长江-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectPolicyefc,robot-3067-selectPolicybiz,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ" +            ",robot-3067-showCitemCarQ,robot-3067-showCinsuredQ,robot-3067-showCitemKindCI,robot-3067-browseProposalS,robot-3067-showCitemCarS" +            ",robot-3067-showCinsuredS,robot-3067-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3067', 'changjiang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3067-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectProposalQ,robot-3067-selectProposalS,robot-3067-browseProposalQ,robot-3067-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3067', 'changjiang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长江-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-bj-editIDCardCheck,robot-3067-apply-bj-IdCarChekc";    } else {        s = "robot-3067-qrcode_login,robot-3067-qrcode_editCheckFlag,robot-3067-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3067', 'changjiang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi长江报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3067_ask_charge,edi_3067_noMotor_quote"	} else {		return "edi_3067_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3067', 'changjiang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-长江-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3067_ask_charge,edi_3067_noMotor_quote,edi_3067_askInsure,edi_3067_uploadImg,edi_3067_submitInsure,edi_3067_noMotor_submit" 	  	} else {		return "edi_3067_ask_chargeold,edi_3067_askInsure,edi_3067_uploadImg,edi_3067_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3067', 'changjiang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-长江-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3067_ask_charge,edi_3067_noMotor_quote,edi_3067_askInsure,edi_3067_uploadImg" 	} else {		return "edi_3067_ask_chargeold,edi_3067_askInsure,edi_3067_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3067', 'changjiang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-长江-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3067_efc_policyinfo,edi_3067_biz_policyinfo,edi_3067_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3067', 'changjiang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-长江-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3067_efc_insurequery,edi_3067_biz_insurequery,edi_3067_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3067', 'changjiang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京长江短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-bj-editIDCardCheck,robot-3067-apply-saveCheckCode,robot-3067-apply-selectIsNetProp";    } else {        s = "robot-3067-qrcode_login,robot-3067-apply-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3067', 'changjiang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-长江-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3067-bj-qrcode_login,robot-3067-apply-bj-prepare,robot-3067-apply-select-jq,robot-3067-apply-select-sy,robot-3067-apply-bj-editIDCardCheck,robot-3067-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3067', 'changjiang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-长江-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3067_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3067', 'changjiang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi长江北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3067_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3067', 'changjiang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi长江北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3067_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3067', 'changjiang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-长江-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3067-login,robot-3067-prepareQueryCode,robot-3067-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3020', 'anlian', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安联-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-IdCarChekc" //申请验证码    else{        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3020', 'anlian', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安联-报价', 'def getTemplateGroup(dataSource) {    return "edi-3020-queryCar,edi-3020-xbQuery,edi-3020-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3020', 'anlian', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安联-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3020-queryCar,edi-3020-xbQuery,edi-3020-askCharge,edi-3020-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3020', 'anlian', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安联-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3020-queryCar,edi-3020-xbQuery,edi-3020-askCharge,edi-3020-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3020', 'anlian', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安联-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3020-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3020', 'anlian', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安联-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3020-login,robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3020-login,robot-3020-ObtainConfig,robot-3020-checkInsurePerson,robot-3020-changePerson,robot-3020-checkInsuredPerson,robot-3020-changePerson,robot-3020-prepareEdit," +                    "robot-3020-prepareQueryCode,robot-3020-selectProposalCar,robot-3020-browseProposalCar,robot-3020-browseProposalCarefc,robot-3020-selectRenewalPolicyNo"        }else{            s = "robot-3020-login,robot-3020-ObtainConfig,robot-3020-checkInsurePerson,robot-3020-changePerson,robot-3020-checkInsuredPerson,robot-3020-changePerson,robot-3020-prepareEdit," +                    "robot-3020-prepareQueryCode,robot-3020-browseProposalCar,robot-3020-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3020-VehicleModelList" //上海车型查询        }        s += ",robot-3020-queryPrepare,robot-3020-vehicleQuery,robot-3020-queryTaxAbateForPlat,robot-3020-calActualValue,robot-3020-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3020-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3020-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-nomotor-unitedSaleEdit,robot-3020-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3020-login,robot_3020_bj_initData,robot_3020_bj_queryModel,robot_3020_bj_getSaleTaxInfo,robot_3020_bj_getRealValue,robot_3020_bj_getPersonData,robot_3020_bj_addPersonData,robot_3020_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3020', 'anlian', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安联-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3020-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3020-ObtainConfig,robot-3020-selectRenewal,robot-3020-editCengage,robot-3020-editCitemCar,robot-3020-editCinsured,robot-3020-renewalPolicy,robot-3020-renewalPolicyCI,robot-3020-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3020-VehicleModelList" //上海车型查询        }        s += ",robot-3020-vehicleQueryXB,robot-3020-queryTaxAbateForPlat,robot-3020-calActualValue,robot-3020-editCitemKind,robot-3020-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3020-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-refreshPlanByTimes,robot-3020-insert"            }else{                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3020-getMaxCsellFee,robot-3020-getPrpCseller,robot-3020-getPrpCsellerCI,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            s += ",robot-3020-getMaxCsellFee,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"        }    }else{        s +=",robot-3020-ObtainConfig,robot-3020-checkInsurePerson,robot-3020-changePerson,robot-3020-checkInsuredPerson,robot-3020-changePerson,robot-3020-prepareEdit,robot-3020-selectRenewalPolicyNo,robot-3020-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3020-VehicleModelList" //上海车型查询        }        s += ",robot-3020-queryPrepare,robot-3020-vehicleQuery,robot-3020-queryTaxAbateForPlat,robot-3020-calActualValue,robot-3020-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3020-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3020-calAnciInfo,robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            }else{                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-nomotor-unitedSaleEdit,robot-3020-nomotor-saveUnitedSale,robot-3020-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3020-getMaxCsellFee,robot-3020-getPrpCseller,robot-3020-getPrpCsellerCI,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            s += ",robot-3020-getMaxCsellFee,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-nomotor-unitedSaleEdit,robot-3020-nomotor-saveUnitedSale,robot-3020-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3020-login,robot_3020_bj_initData,robot_3020_bj_queryModel,robot_3020_bj_getSaleTaxInfo,robot_3020_bj_getRealValue,robot_3020_bj_getPersonData,robot_3020_bj_addPersonData,robot_3020_bj_askCharge,robot_3020_bj_queryPayForXSFY,robot_3020_bj_getCagentCI,robot_3020_bj_getCagent,robot_3020_bj_queryPayForXSFY_req,robot_3020_bj_queryIlogEngage,robot_3020_bj_insureRefrenshPlan,robot_3020_bj_insure4S,robot-3020-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3020', 'anlian', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安联-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ" +            ",robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind,robot-3020-nomotor-query,robot-3020-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3020', 'anlian', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安联-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ" +            ",robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind,robot-3020-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3020', 'anlian', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安联-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ" +            ",robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind,robot-3020-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3020', 'anlian', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安联财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安联-电销', 'def getTemplateGroup(dataSource){    return "robot-3020-pureESale_Login,robot-3020-pureESale_Welcome,robot-3020-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3020', 'anlian', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安联续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3020-login,robot-3020-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3020', 'anlian', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安联-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3020-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3020-ObtainConfig,robot-3020-selectRenewal,robot-3020-editCengage,robot-3020-editCitemCar,robot-3020-editCinsured,robot-3020-renewalPolicy,robot-3020-renewalPolicyCI,robot-3020-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3020-VehicleModelList" //上海车型查询        }        s += ",robot-3020-vehicleQueryXB,robot-3020-queryTaxAbateForPlat,robot-3020-calActualValue,robot-3020-editCitemKind,robot-3020-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3020-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-refreshPlanByTimes,robot-3020-insert"            }else{                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3020-calAnciInfo,robot-3020-getMaxCsellFee,robot-3020-getPrpCseller,robot-3020-getPrpCsellerCI,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            s += ",robot-3020-getMaxCsellFee,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"        }    }else{        s += ",robot-3020-ObtainConfig,robot-3020-checkInsurePerson,robot-3020-changePerson,robot-3020-checkInsuredPerson,robot-3020-changePerson,robot-3020-prepareEdit,robot-3020-editCengage,robot-3020-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3020-queryVehiclePMCheck,robot-3020-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3020-VehicleModelList" //上海车型查询        }        s += ",robot-3020-queryPrepare,robot-3020-vehicleQuery,robot-3020-queryTaxAbateForPlat,robot-3020-calActualValue,robot-3020-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3020-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3020-calAnciInfo,robot-3020-queryPayFor,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            }else{                s += ",robot-3020-calAnciInfo,robot-3020-checkAgentType,robot-3020-queryPayForSCMS,robot-3020-getCagent,robot-3020-getCagentCI,robot-3020-refreshPlanByTimes,robot-3020-nomotor-unitedSaleEdit,robot-3020-nomotor-saveUnitedSale,robot-3020-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3020-calAnciInfo,robot-3020-getMaxCsellFee,robot-3020-getPrpCseller,robot-3020-getPrpCsellerCI,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-insert"            s += ",robot-3020-getMaxCsellFee,robot-3020-queryPayForSCMS,robot-3020-refreshPlanByTimes,robot-3020-nomotor-unitedSaleEdit,robot-3020-nomotor-saveUnitedSale,robot-3020-insert"        }    }    s += ",robot-3020-checkRiskCode,robot-3020-editMainUwtFlag,robot-3020-editSubmitUndwrt,robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-showUndwrtMsgQ,robot-3020-showUndwrtMsgS"+            ",robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ,robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind,robot-3020-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3020-login,robot_3020_bj_initData,robot_3020_bj_queryModel,robot_3020_bj_getSaleTaxInfo,robot_3020_bj_getRealValue,robot_3020_bj_getPersonData,robot_3020_bj_addPersonData,robot_3020_bj_askCharge,robot_3020_bj_queryPayForXSFY,robot_3020_bj_getCagentCI,robot_3020_bj_getCagent,robot_3020_bj_queryPayForXSFY_req,robot_3020_bj_queryIlogEngage,robot_3020_bj_insureRefrenshPlan,robot_3020_bj_insure4S,robot-3020-uploadImage,robot_3020_bj_autoInsure,robot_3020_bj_showUndwrtMsgQ,robot_3020_bj_showUndwrtMsgS";       s += ",robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ,robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +                ",robot-3020-showCinsuredS,robot-3020-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3020', 'anlian', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安联-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3020-qrcode_login,robot-3020-qrcode_printTwoBarCodeServlet,robot-3020-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3020-qrcode_login,robot-3020-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3020-qrcode_login,robot-3020-qrcode_editCheckFlag,robot-3020-qrcode_gotoJfcd,robot-3020-qrcode_prepareEditByJF,robot-3020-qrcode_getBusinessIn" +                ",robot-3020-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3020-qrcode_login,robot-3020-qrcode_editCheckFlag,robot-3020-qrcode_gotoJfcd,robot-3020-qrcode_prepareEditByJF,robot-3020-qrcode_getBusinessIn" +                ",robot-3020-qrcode_checkBeforeCalculate,robot-3020-qrcode_saveByJF,robot-3020-qrcode_getBusinessIn_alipay,robot-3020-qrcode_editFeeInfor,robot-3020-qrcode_editPayFeeByWeChat,robot-3020-qrcode_saveByWeChat,robot-3020-qrcode_save";		} else {					return  "robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-bj-editIDCardCheck,robot-3020-apply-selectIsNetProp,robot-3020-apply-saveCheckCode,robot-3020-qrcode_editCheckFlag,robot-3020-qrcode_gotoJfcd,robot-3020-qrcode_prepareEditByJF,robot-3020-qrcode_getBusinessIn" +",robot-3020-qrcode_checkBeforeCalculate,robot-3020-qrcode_saveByJF,robot-3020-qrcode_getBusinessIn_alipay,robot-3020-qrcode_editFeeInfor,robot-3020-qrcode_editPayFeeByWeChat,robot-3020-qrcode_saveByWeChat,robot-3020-qrcode_save";		}}    else {              return "robot-3020-qrcode_login,robot-3020-qrcode_editCheckFlag,robot-3020-qrcode_gotoJfcd,robot-3020-qrcode_prepareEditByJF,robot-3020-qrcode_getBusinessIn" +                ",robot-3020-qrcode_checkBeforeCalculate,robot-3020-qrcode_saveByJF,robot-3020-qrcode_getBusinessIn_alipay,robot-3020-qrcode_editFeeInfor,robot-3020-qrcode_editPayFeeByWeChat,robot-3020-qrcode_saveByWeChat,robot-3020-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3020', 'anlian', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安联-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3020-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3020-qrcode_query_editCheckFlag,robot-3020-qrcode_query_gotoJfcd,robot-3020-qrcode_query_prepareEditByJF" +                ",robot-3020-qrcode_query_editMainInfor,robot-3020-qrcode_query_getBusinessIn,robot-3020-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ" +            ",robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind,robot-3020-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3020', 'anlian', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安联-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-IdCarChekc" //申请验证码    else{        return"robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-editIDCardCheck,robot-3020-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3020', 'anlian', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安联-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectPolicyefc,robot-3020-selectPolicybiz,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ" +            ",robot-3020-showCitemCarQ,robot-3020-showCinsuredQ,robot-3020-showCitemKindCI,robot-3020-browseProposalS,robot-3020-showCitemCarS" +            ",robot-3020-showCinsuredS,robot-3020-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3020', 'anlian', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3020-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectProposalQ,robot-3020-selectProposalS,robot-3020-browseProposalQ,robot-3020-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3020', 'anlian', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安联-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-bj-editIDCardCheck,robot-3020-apply-bj-IdCarChekc";    } else {        s = "robot-3020-qrcode_login,robot-3020-qrcode_editCheckFlag,robot-3020-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3020', 'anlian', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安联报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3020_ask_charge,edi_3020_noMotor_quote"	} else {		return "edi_3020_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3020', 'anlian', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安联-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3020_ask_charge,edi_3020_noMotor_quote,edi_3020_askInsure,edi_3020_uploadImg,edi_3020_submitInsure,edi_3020_noMotor_submit" 	  	} else {		return "edi_3020_ask_chargeold,edi_3020_askInsure,edi_3020_uploadImg,edi_3020_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3020', 'anlian', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安联-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3020_ask_charge,edi_3020_noMotor_quote,edi_3020_askInsure,edi_3020_uploadImg" 	} else {		return "edi_3020_ask_chargeold,edi_3020_askInsure,edi_3020_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3020', 'anlian', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安联-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3020_efc_policyinfo,edi_3020_biz_policyinfo,edi_3020_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3020', 'anlian', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安联-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3020_efc_insurequery,edi_3020_biz_insurequery,edi_3020_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3020', 'anlian', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安联短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-bj-editIDCardCheck,robot-3020-apply-saveCheckCode,robot-3020-apply-selectIsNetProp";    } else {        s = "robot-3020-qrcode_login,robot-3020-apply-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3020', 'anlian', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安联-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3020-bj-qrcode_login,robot-3020-apply-bj-prepare,robot-3020-apply-select-jq,robot-3020-apply-select-sy,robot-3020-apply-bj-editIDCardCheck,robot-3020-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3020', 'anlian', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安联-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3020_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3020', 'anlian', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安联北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3020_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3020', 'anlian', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安联北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3020_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3020', 'anlian', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安联-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3020-login,robot-3020-prepareQueryCode,robot-3020-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3077', 'zhongyi', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中意-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-IdCarChekc" //申请验证码    else{        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3077', 'zhongyi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中意-报价', 'def getTemplateGroup(dataSource) {    return "edi-3077-queryCar,edi-3077-xbQuery,edi-3077-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3077', 'zhongyi', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中意-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3077-queryCar,edi-3077-xbQuery,edi-3077-askCharge,edi-3077-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3077', 'zhongyi', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中意-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3077-queryCar,edi-3077-xbQuery,edi-3077-askCharge,edi-3077-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3077', 'zhongyi', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-中意-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3077-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3077', 'zhongyi', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中意-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3077-login,robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3077-login,robot-3077-ObtainConfig,robot-3077-checkInsurePerson,robot-3077-changePerson,robot-3077-checkInsuredPerson,robot-3077-changePerson,robot-3077-prepareEdit," +                    "robot-3077-prepareQueryCode,robot-3077-selectProposalCar,robot-3077-browseProposalCar,robot-3077-browseProposalCarefc,robot-3077-selectRenewalPolicyNo"        }else{            s = "robot-3077-login,robot-3077-ObtainConfig,robot-3077-checkInsurePerson,robot-3077-changePerson,robot-3077-checkInsuredPerson,robot-3077-changePerson,robot-3077-prepareEdit," +                    "robot-3077-prepareQueryCode,robot-3077-browseProposalCar,robot-3077-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3077-VehicleModelList" //上海车型查询        }        s += ",robot-3077-queryPrepare,robot-3077-vehicleQuery,robot-3077-queryTaxAbateForPlat,robot-3077-calActualValue,robot-3077-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3077-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3077-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-nomotor-unitedSaleEdit,robot-3077-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3077-login,robot_3077_bj_initData,robot_3077_bj_queryModel,robot_3077_bj_getSaleTaxInfo,robot_3077_bj_getRealValue,robot_3077_bj_getPersonData,robot_3077_bj_addPersonData,robot_3077_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3077', 'zhongyi', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-中意-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3077-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3077-ObtainConfig,robot-3077-selectRenewal,robot-3077-editCengage,robot-3077-editCitemCar,robot-3077-editCinsured,robot-3077-renewalPolicy,robot-3077-renewalPolicyCI,robot-3077-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3077-VehicleModelList" //上海车型查询        }        s += ",robot-3077-vehicleQueryXB,robot-3077-queryTaxAbateForPlat,robot-3077-calActualValue,robot-3077-editCitemKind,robot-3077-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3077-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-refreshPlanByTimes,robot-3077-insert"            }else{                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3077-getMaxCsellFee,robot-3077-getPrpCseller,robot-3077-getPrpCsellerCI,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            s += ",robot-3077-getMaxCsellFee,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"        }    }else{        s +=",robot-3077-ObtainConfig,robot-3077-checkInsurePerson,robot-3077-changePerson,robot-3077-checkInsuredPerson,robot-3077-changePerson,robot-3077-prepareEdit,robot-3077-selectRenewalPolicyNo,robot-3077-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3077-VehicleModelList" //上海车型查询        }        s += ",robot-3077-queryPrepare,robot-3077-vehicleQuery,robot-3077-queryTaxAbateForPlat,robot-3077-calActualValue,robot-3077-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3077-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3077-calAnciInfo,robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            }else{                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-nomotor-unitedSaleEdit,robot-3077-nomotor-saveUnitedSale,robot-3077-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3077-getMaxCsellFee,robot-3077-getPrpCseller,robot-3077-getPrpCsellerCI,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            s += ",robot-3077-getMaxCsellFee,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-nomotor-unitedSaleEdit,robot-3077-nomotor-saveUnitedSale,robot-3077-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3077-login,robot_3077_bj_initData,robot_3077_bj_queryModel,robot_3077_bj_getSaleTaxInfo,robot_3077_bj_getRealValue,robot_3077_bj_getPersonData,robot_3077_bj_addPersonData,robot_3077_bj_askCharge,robot_3077_bj_queryPayForXSFY,robot_3077_bj_getCagentCI,robot_3077_bj_getCagent,robot_3077_bj_queryPayForXSFY_req,robot_3077_bj_queryIlogEngage,robot_3077_bj_insureRefrenshPlan,robot_3077_bj_insure4S,robot-3077-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3077', 'zhongyi', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-中意-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ" +            ",robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind,robot-3077-nomotor-query,robot-3077-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3077', 'zhongyi', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中意-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ" +            ",robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind,robot-3077-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3077', 'zhongyi', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中意-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ" +            ",robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind,robot-3077-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3077', 'zhongyi', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "中意财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-中意-电销', 'def getTemplateGroup(dataSource){    return "robot-3077-pureESale_Login,robot-3077-pureESale_Welcome,robot-3077-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3077', 'zhongyi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中意续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3077-login,robot-3077-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3077', 'zhongyi', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-中意-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3077-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3077-ObtainConfig,robot-3077-selectRenewal,robot-3077-editCengage,robot-3077-editCitemCar,robot-3077-editCinsured,robot-3077-renewalPolicy,robot-3077-renewalPolicyCI,robot-3077-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3077-VehicleModelList" //上海车型查询        }        s += ",robot-3077-vehicleQueryXB,robot-3077-queryTaxAbateForPlat,robot-3077-calActualValue,robot-3077-editCitemKind,robot-3077-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3077-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-refreshPlanByTimes,robot-3077-insert"            }else{                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3077-calAnciInfo,robot-3077-getMaxCsellFee,robot-3077-getPrpCseller,robot-3077-getPrpCsellerCI,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            s += ",robot-3077-getMaxCsellFee,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"        }    }else{        s += ",robot-3077-ObtainConfig,robot-3077-checkInsurePerson,robot-3077-changePerson,robot-3077-checkInsuredPerson,robot-3077-changePerson,robot-3077-prepareEdit,robot-3077-editCengage,robot-3077-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3077-queryVehiclePMCheck,robot-3077-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3077-VehicleModelList" //上海车型查询        }        s += ",robot-3077-queryPrepare,robot-3077-vehicleQuery,robot-3077-queryTaxAbateForPlat,robot-3077-calActualValue,robot-3077-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3077-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3077-calAnciInfo,robot-3077-queryPayFor,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            }else{                s += ",robot-3077-calAnciInfo,robot-3077-checkAgentType,robot-3077-queryPayForSCMS,robot-3077-getCagent,robot-3077-getCagentCI,robot-3077-refreshPlanByTimes,robot-3077-nomotor-unitedSaleEdit,robot-3077-nomotor-saveUnitedSale,robot-3077-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3077-calAnciInfo,robot-3077-getMaxCsellFee,robot-3077-getPrpCseller,robot-3077-getPrpCsellerCI,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-insert"            s += ",robot-3077-getMaxCsellFee,robot-3077-queryPayForSCMS,robot-3077-refreshPlanByTimes,robot-3077-nomotor-unitedSaleEdit,robot-3077-nomotor-saveUnitedSale,robot-3077-insert"        }    }    s += ",robot-3077-checkRiskCode,robot-3077-editMainUwtFlag,robot-3077-editSubmitUndwrt,robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-showUndwrtMsgQ,robot-3077-showUndwrtMsgS"+            ",robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ,robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind,robot-3077-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3077-login,robot_3077_bj_initData,robot_3077_bj_queryModel,robot_3077_bj_getSaleTaxInfo,robot_3077_bj_getRealValue,robot_3077_bj_getPersonData,robot_3077_bj_addPersonData,robot_3077_bj_askCharge,robot_3077_bj_queryPayForXSFY,robot_3077_bj_getCagentCI,robot_3077_bj_getCagent,robot_3077_bj_queryPayForXSFY_req,robot_3077_bj_queryIlogEngage,robot_3077_bj_insureRefrenshPlan,robot_3077_bj_insure4S,robot-3077-uploadImage,robot_3077_bj_autoInsure,robot_3077_bj_showUndwrtMsgQ,robot_3077_bj_showUndwrtMsgS";       s += ",robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ,robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +                ",robot-3077-showCinsuredS,robot-3077-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3077', 'zhongyi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中意-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3077-qrcode_login,robot-3077-qrcode_printTwoBarCodeServlet,robot-3077-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3077-qrcode_login,robot-3077-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3077-qrcode_login,robot-3077-qrcode_editCheckFlag,robot-3077-qrcode_gotoJfcd,robot-3077-qrcode_prepareEditByJF,robot-3077-qrcode_getBusinessIn" +                ",robot-3077-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3077-qrcode_login,robot-3077-qrcode_editCheckFlag,robot-3077-qrcode_gotoJfcd,robot-3077-qrcode_prepareEditByJF,robot-3077-qrcode_getBusinessIn" +                ",robot-3077-qrcode_checkBeforeCalculate,robot-3077-qrcode_saveByJF,robot-3077-qrcode_getBusinessIn_alipay,robot-3077-qrcode_editFeeInfor,robot-3077-qrcode_editPayFeeByWeChat,robot-3077-qrcode_saveByWeChat,robot-3077-qrcode_save";		} else {					return  "robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-bj-editIDCardCheck,robot-3077-apply-selectIsNetProp,robot-3077-apply-saveCheckCode,robot-3077-qrcode_editCheckFlag,robot-3077-qrcode_gotoJfcd,robot-3077-qrcode_prepareEditByJF,robot-3077-qrcode_getBusinessIn" +",robot-3077-qrcode_checkBeforeCalculate,robot-3077-qrcode_saveByJF,robot-3077-qrcode_getBusinessIn_alipay,robot-3077-qrcode_editFeeInfor,robot-3077-qrcode_editPayFeeByWeChat,robot-3077-qrcode_saveByWeChat,robot-3077-qrcode_save";		}}    else {              return "robot-3077-qrcode_login,robot-3077-qrcode_editCheckFlag,robot-3077-qrcode_gotoJfcd,robot-3077-qrcode_prepareEditByJF,robot-3077-qrcode_getBusinessIn" +                ",robot-3077-qrcode_checkBeforeCalculate,robot-3077-qrcode_saveByJF,robot-3077-qrcode_getBusinessIn_alipay,robot-3077-qrcode_editFeeInfor,robot-3077-qrcode_editPayFeeByWeChat,robot-3077-qrcode_saveByWeChat,robot-3077-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3077', 'zhongyi', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中意-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3077-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3077-qrcode_query_editCheckFlag,robot-3077-qrcode_query_gotoJfcd,robot-3077-qrcode_query_prepareEditByJF" +                ",robot-3077-qrcode_query_editMainInfor,robot-3077-qrcode_query_getBusinessIn,robot-3077-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ" +            ",robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind,robot-3077-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3077', 'zhongyi', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中意-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-IdCarChekc" //申请验证码    else{        return"robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-editIDCardCheck,robot-3077-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3077', 'zhongyi', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-中意-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectPolicyefc,robot-3077-selectPolicybiz,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ" +            ",robot-3077-showCitemCarQ,robot-3077-showCinsuredQ,robot-3077-showCitemKindCI,robot-3077-browseProposalS,robot-3077-showCitemCarS" +            ",robot-3077-showCinsuredS,robot-3077-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3077', 'zhongyi', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3077-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectProposalQ,robot-3077-selectProposalS,robot-3077-browseProposalQ,robot-3077-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3077', 'zhongyi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中意-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-bj-editIDCardCheck,robot-3077-apply-bj-IdCarChekc";    } else {        s = "robot-3077-qrcode_login,robot-3077-qrcode_editCheckFlag,robot-3077-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3077', 'zhongyi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi中意报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3077_ask_charge,edi_3077_noMotor_quote"	} else {		return "edi_3077_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3077', 'zhongyi', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中意-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3077_ask_charge,edi_3077_noMotor_quote,edi_3077_askInsure,edi_3077_uploadImg,edi_3077_submitInsure,edi_3077_noMotor_submit" 	  	} else {		return "edi_3077_ask_chargeold,edi_3077_askInsure,edi_3077_uploadImg,edi_3077_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3077', 'zhongyi', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中意-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3077_ask_charge,edi_3077_noMotor_quote,edi_3077_askInsure,edi_3077_uploadImg" 	} else {		return "edi_3077_ask_chargeold,edi_3077_askInsure,edi_3077_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3077', 'zhongyi', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-中意-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3077_efc_policyinfo,edi_3077_biz_policyinfo,edi_3077_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3077', 'zhongyi', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-中意-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3077_efc_insurequery,edi_3077_biz_insurequery,edi_3077_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3077', 'zhongyi', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京中意短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-bj-editIDCardCheck,robot-3077-apply-saveCheckCode,robot-3077-apply-selectIsNetProp";    } else {        s = "robot-3077-qrcode_login,robot-3077-apply-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3077', 'zhongyi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中意-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3077-bj-qrcode_login,robot-3077-apply-bj-prepare,robot-3077-apply-select-jq,robot-3077-apply-select-sy,robot-3077-apply-bj-editIDCardCheck,robot-3077-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3077', 'zhongyi', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-中意-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3077_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3077', 'zhongyi', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中意北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3077_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3077', 'zhongyi', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中意北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3077_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3077', 'zhongyi', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-中意-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3077-login,robot-3077-prepareQueryCode,robot-3077-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3081', 'zhufeng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-珠峰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-IdCarChekc" //申请验证码    else{        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3081', 'zhufeng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-珠峰-报价', 'def getTemplateGroup(dataSource) {    return "edi-3081-queryCar,edi-3081-xbQuery,edi-3081-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3081', 'zhufeng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-珠峰-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3081-queryCar,edi-3081-xbQuery,edi-3081-askCharge,edi-3081-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3081', 'zhufeng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-珠峰-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3081-queryCar,edi-3081-xbQuery,edi-3081-askCharge,edi-3081-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3081', 'zhufeng', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-珠峰-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3081-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3081', 'zhufeng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-珠峰-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3081-login,robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3081-login,robot-3081-ObtainConfig,robot-3081-checkInsurePerson,robot-3081-changePerson,robot-3081-checkInsuredPerson,robot-3081-changePerson,robot-3081-prepareEdit," +                    "robot-3081-prepareQueryCode,robot-3081-selectProposalCar,robot-3081-browseProposalCar,robot-3081-browseProposalCarefc,robot-3081-selectRenewalPolicyNo"        }else{            s = "robot-3081-login,robot-3081-ObtainConfig,robot-3081-checkInsurePerson,robot-3081-changePerson,robot-3081-checkInsuredPerson,robot-3081-changePerson,robot-3081-prepareEdit," +                    "robot-3081-prepareQueryCode,robot-3081-browseProposalCar,robot-3081-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3081-VehicleModelList" //上海车型查询        }        s += ",robot-3081-queryPrepare,robot-3081-vehicleQuery,robot-3081-queryTaxAbateForPlat,robot-3081-calActualValue,robot-3081-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3081-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3081-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-nomotor-unitedSaleEdit,robot-3081-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3081-login,robot_3081_bj_initData,robot_3081_bj_queryModel,robot_3081_bj_getSaleTaxInfo,robot_3081_bj_getRealValue,robot_3081_bj_getPersonData,robot_3081_bj_addPersonData,robot_3081_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3081', 'zhufeng', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-珠峰-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3081-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3081-ObtainConfig,robot-3081-selectRenewal,robot-3081-editCengage,robot-3081-editCitemCar,robot-3081-editCinsured,robot-3081-renewalPolicy,robot-3081-renewalPolicyCI,robot-3081-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3081-VehicleModelList" //上海车型查询        }        s += ",robot-3081-vehicleQueryXB,robot-3081-queryTaxAbateForPlat,robot-3081-calActualValue,robot-3081-editCitemKind,robot-3081-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3081-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-refreshPlanByTimes,robot-3081-insert"            }else{                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3081-getMaxCsellFee,robot-3081-getPrpCseller,robot-3081-getPrpCsellerCI,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            s += ",robot-3081-getMaxCsellFee,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"        }    }else{        s +=",robot-3081-ObtainConfig,robot-3081-checkInsurePerson,robot-3081-changePerson,robot-3081-checkInsuredPerson,robot-3081-changePerson,robot-3081-prepareEdit,robot-3081-selectRenewalPolicyNo,robot-3081-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3081-VehicleModelList" //上海车型查询        }        s += ",robot-3081-queryPrepare,robot-3081-vehicleQuery,robot-3081-queryTaxAbateForPlat,robot-3081-calActualValue,robot-3081-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3081-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3081-calAnciInfo,robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            }else{                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-nomotor-unitedSaleEdit,robot-3081-nomotor-saveUnitedSale,robot-3081-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3081-getMaxCsellFee,robot-3081-getPrpCseller,robot-3081-getPrpCsellerCI,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            s += ",robot-3081-getMaxCsellFee,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-nomotor-unitedSaleEdit,robot-3081-nomotor-saveUnitedSale,robot-3081-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3081-login,robot_3081_bj_initData,robot_3081_bj_queryModel,robot_3081_bj_getSaleTaxInfo,robot_3081_bj_getRealValue,robot_3081_bj_getPersonData,robot_3081_bj_addPersonData,robot_3081_bj_askCharge,robot_3081_bj_queryPayForXSFY,robot_3081_bj_getCagentCI,robot_3081_bj_getCagent,robot_3081_bj_queryPayForXSFY_req,robot_3081_bj_queryIlogEngage,robot_3081_bj_insureRefrenshPlan,robot_3081_bj_insure4S,robot-3081-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3081', 'zhufeng', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-珠峰-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ" +            ",robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind,robot-3081-nomotor-query,robot-3081-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3081', 'zhufeng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-珠峰-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ" +            ",robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind,robot-3081-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3081', 'zhufeng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-珠峰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ" +            ",robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind,robot-3081-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3081', 'zhufeng', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "珠峰财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-珠峰-电销', 'def getTemplateGroup(dataSource){    return "robot-3081-pureESale_Login,robot-3081-pureESale_Welcome,robot-3081-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3081', 'zhufeng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-珠峰续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3081-login,robot-3081-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3081', 'zhufeng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-珠峰-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3081-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3081-ObtainConfig,robot-3081-selectRenewal,robot-3081-editCengage,robot-3081-editCitemCar,robot-3081-editCinsured,robot-3081-renewalPolicy,robot-3081-renewalPolicyCI,robot-3081-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3081-VehicleModelList" //上海车型查询        }        s += ",robot-3081-vehicleQueryXB,robot-3081-queryTaxAbateForPlat,robot-3081-calActualValue,robot-3081-editCitemKind,robot-3081-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3081-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-refreshPlanByTimes,robot-3081-insert"            }else{                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3081-calAnciInfo,robot-3081-getMaxCsellFee,robot-3081-getPrpCseller,robot-3081-getPrpCsellerCI,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            s += ",robot-3081-getMaxCsellFee,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"        }    }else{        s += ",robot-3081-ObtainConfig,robot-3081-checkInsurePerson,robot-3081-changePerson,robot-3081-checkInsuredPerson,robot-3081-changePerson,robot-3081-prepareEdit,robot-3081-editCengage,robot-3081-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3081-queryVehiclePMCheck,robot-3081-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3081-VehicleModelList" //上海车型查询        }        s += ",robot-3081-queryPrepare,robot-3081-vehicleQuery,robot-3081-queryTaxAbateForPlat,robot-3081-calActualValue,robot-3081-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3081-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3081-calAnciInfo,robot-3081-queryPayFor,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            }else{                s += ",robot-3081-calAnciInfo,robot-3081-checkAgentType,robot-3081-queryPayForSCMS,robot-3081-getCagent,robot-3081-getCagentCI,robot-3081-refreshPlanByTimes,robot-3081-nomotor-unitedSaleEdit,robot-3081-nomotor-saveUnitedSale,robot-3081-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3081-calAnciInfo,robot-3081-getMaxCsellFee,robot-3081-getPrpCseller,robot-3081-getPrpCsellerCI,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-insert"            s += ",robot-3081-getMaxCsellFee,robot-3081-queryPayForSCMS,robot-3081-refreshPlanByTimes,robot-3081-nomotor-unitedSaleEdit,robot-3081-nomotor-saveUnitedSale,robot-3081-insert"        }    }    s += ",robot-3081-checkRiskCode,robot-3081-editMainUwtFlag,robot-3081-editSubmitUndwrt,robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-showUndwrtMsgQ,robot-3081-showUndwrtMsgS"+            ",robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ,robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind,robot-3081-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3081-login,robot_3081_bj_initData,robot_3081_bj_queryModel,robot_3081_bj_getSaleTaxInfo,robot_3081_bj_getRealValue,robot_3081_bj_getPersonData,robot_3081_bj_addPersonData,robot_3081_bj_askCharge,robot_3081_bj_queryPayForXSFY,robot_3081_bj_getCagentCI,robot_3081_bj_getCagent,robot_3081_bj_queryPayForXSFY_req,robot_3081_bj_queryIlogEngage,robot_3081_bj_insureRefrenshPlan,robot_3081_bj_insure4S,robot-3081-uploadImage,robot_3081_bj_autoInsure,robot_3081_bj_showUndwrtMsgQ,robot_3081_bj_showUndwrtMsgS";       s += ",robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ,robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +                ",robot-3081-showCinsuredS,robot-3081-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3081', 'zhufeng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-珠峰-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3081-qrcode_login,robot-3081-qrcode_printTwoBarCodeServlet,robot-3081-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3081-qrcode_login,robot-3081-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3081-qrcode_login,robot-3081-qrcode_editCheckFlag,robot-3081-qrcode_gotoJfcd,robot-3081-qrcode_prepareEditByJF,robot-3081-qrcode_getBusinessIn" +                ",robot-3081-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3081-qrcode_login,robot-3081-qrcode_editCheckFlag,robot-3081-qrcode_gotoJfcd,robot-3081-qrcode_prepareEditByJF,robot-3081-qrcode_getBusinessIn" +                ",robot-3081-qrcode_checkBeforeCalculate,robot-3081-qrcode_saveByJF,robot-3081-qrcode_getBusinessIn_alipay,robot-3081-qrcode_editFeeInfor,robot-3081-qrcode_editPayFeeByWeChat,robot-3081-qrcode_saveByWeChat,robot-3081-qrcode_save";		} else {					return  "robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-bj-editIDCardCheck,robot-3081-apply-selectIsNetProp,robot-3081-apply-saveCheckCode,robot-3081-qrcode_editCheckFlag,robot-3081-qrcode_gotoJfcd,robot-3081-qrcode_prepareEditByJF,robot-3081-qrcode_getBusinessIn" +",robot-3081-qrcode_checkBeforeCalculate,robot-3081-qrcode_saveByJF,robot-3081-qrcode_getBusinessIn_alipay,robot-3081-qrcode_editFeeInfor,robot-3081-qrcode_editPayFeeByWeChat,robot-3081-qrcode_saveByWeChat,robot-3081-qrcode_save";		}}    else {              return "robot-3081-qrcode_login,robot-3081-qrcode_editCheckFlag,robot-3081-qrcode_gotoJfcd,robot-3081-qrcode_prepareEditByJF,robot-3081-qrcode_getBusinessIn" +                ",robot-3081-qrcode_checkBeforeCalculate,robot-3081-qrcode_saveByJF,robot-3081-qrcode_getBusinessIn_alipay,robot-3081-qrcode_editFeeInfor,robot-3081-qrcode_editPayFeeByWeChat,robot-3081-qrcode_saveByWeChat,robot-3081-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3081', 'zhufeng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-珠峰-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3081-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3081-qrcode_query_editCheckFlag,robot-3081-qrcode_query_gotoJfcd,robot-3081-qrcode_query_prepareEditByJF" +                ",robot-3081-qrcode_query_editMainInfor,robot-3081-qrcode_query_getBusinessIn,robot-3081-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ" +            ",robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind,robot-3081-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3081', 'zhufeng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-珠峰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-IdCarChekc" //申请验证码    else{        return"robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-editIDCardCheck,robot-3081-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3081', 'zhufeng', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-珠峰-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectPolicyefc,robot-3081-selectPolicybiz,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ" +            ",robot-3081-showCitemCarQ,robot-3081-showCinsuredQ,robot-3081-showCitemKindCI,robot-3081-browseProposalS,robot-3081-showCitemCarS" +            ",robot-3081-showCinsuredS,robot-3081-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3081', 'zhufeng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3081-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectProposalQ,robot-3081-selectProposalS,robot-3081-browseProposalQ,robot-3081-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3081', 'zhufeng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-珠峰-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-bj-editIDCardCheck,robot-3081-apply-bj-IdCarChekc";    } else {        s = "robot-3081-qrcode_login,robot-3081-qrcode_editCheckFlag,robot-3081-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3081', 'zhufeng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi珠峰报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3081_ask_charge,edi_3081_noMotor_quote"	} else {		return "edi_3081_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3081', 'zhufeng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-珠峰-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3081_ask_charge,edi_3081_noMotor_quote,edi_3081_askInsure,edi_3081_uploadImg,edi_3081_submitInsure,edi_3081_noMotor_submit" 	  	} else {		return "edi_3081_ask_chargeold,edi_3081_askInsure,edi_3081_uploadImg,edi_3081_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3081', 'zhufeng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-珠峰-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3081_ask_charge,edi_3081_noMotor_quote,edi_3081_askInsure,edi_3081_uploadImg" 	} else {		return "edi_3081_ask_chargeold,edi_3081_askInsure,edi_3081_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3081', 'zhufeng', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-珠峰-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3081_efc_policyinfo,edi_3081_biz_policyinfo,edi_3081_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3081', 'zhufeng', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-珠峰-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3081_efc_insurequery,edi_3081_biz_insurequery,edi_3081_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3081', 'zhufeng', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京珠峰短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-bj-editIDCardCheck,robot-3081-apply-saveCheckCode,robot-3081-apply-selectIsNetProp";    } else {        s = "robot-3081-qrcode_login,robot-3081-apply-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3081', 'zhufeng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-珠峰-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3081-bj-qrcode_login,robot-3081-apply-bj-prepare,robot-3081-apply-select-jq,robot-3081-apply-select-sy,robot-3081-apply-bj-editIDCardCheck,robot-3081-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3081', 'zhufeng', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-珠峰-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3081_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3081', 'zhufeng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi珠峰北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3081_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3081', 'zhufeng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi珠峰北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3081_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3081', 'zhufeng', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-珠峰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3081-login,robot-3081-prepareQueryCode,robot-3081-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3059', 'xiandai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-现代-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-IdCarChekc" //申请验证码    else{        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3059', 'xiandai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-现代-报价', 'def getTemplateGroup(dataSource) {    return "edi-3059-queryCar,edi-3059-xbQuery,edi-3059-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3059', 'xiandai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-现代-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3059-queryCar,edi-3059-xbQuery,edi-3059-askCharge,edi-3059-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3059', 'xiandai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-现代-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3059-queryCar,edi-3059-xbQuery,edi-3059-askCharge,edi-3059-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3059', 'xiandai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-现代-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3059-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3059', 'xiandai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-现代-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3059-login,robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3059-login,robot-3059-ObtainConfig,robot-3059-checkInsurePerson,robot-3059-changePerson,robot-3059-checkInsuredPerson,robot-3059-changePerson,robot-3059-prepareEdit," +                    "robot-3059-prepareQueryCode,robot-3059-selectProposalCar,robot-3059-browseProposalCar,robot-3059-browseProposalCarefc,robot-3059-selectRenewalPolicyNo"        }else{            s = "robot-3059-login,robot-3059-ObtainConfig,robot-3059-checkInsurePerson,robot-3059-changePerson,robot-3059-checkInsuredPerson,robot-3059-changePerson,robot-3059-prepareEdit," +                    "robot-3059-prepareQueryCode,robot-3059-browseProposalCar,robot-3059-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3059-VehicleModelList" //上海车型查询        }        s += ",robot-3059-queryPrepare,robot-3059-vehicleQuery,robot-3059-queryTaxAbateForPlat,robot-3059-calActualValue,robot-3059-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3059-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3059-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-nomotor-unitedSaleEdit,robot-3059-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3059-login,robot_3059_bj_initData,robot_3059_bj_queryModel,robot_3059_bj_getSaleTaxInfo,robot_3059_bj_getRealValue,robot_3059_bj_getPersonData,robot_3059_bj_addPersonData,robot_3059_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3059', 'xiandai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-现代-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3059-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3059-ObtainConfig,robot-3059-selectRenewal,robot-3059-editCengage,robot-3059-editCitemCar,robot-3059-editCinsured,robot-3059-renewalPolicy,robot-3059-renewalPolicyCI,robot-3059-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3059-VehicleModelList" //上海车型查询        }        s += ",robot-3059-vehicleQueryXB,robot-3059-queryTaxAbateForPlat,robot-3059-calActualValue,robot-3059-editCitemKind,robot-3059-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3059-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-refreshPlanByTimes,robot-3059-insert"            }else{                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3059-getMaxCsellFee,robot-3059-getPrpCseller,robot-3059-getPrpCsellerCI,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            s += ",robot-3059-getMaxCsellFee,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"        }    }else{        s +=",robot-3059-ObtainConfig,robot-3059-checkInsurePerson,robot-3059-changePerson,robot-3059-checkInsuredPerson,robot-3059-changePerson,robot-3059-prepareEdit,robot-3059-selectRenewalPolicyNo,robot-3059-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3059-VehicleModelList" //上海车型查询        }        s += ",robot-3059-queryPrepare,robot-3059-vehicleQuery,robot-3059-queryTaxAbateForPlat,robot-3059-calActualValue,robot-3059-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3059-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3059-calAnciInfo,robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            }else{                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-nomotor-unitedSaleEdit,robot-3059-nomotor-saveUnitedSale,robot-3059-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3059-getMaxCsellFee,robot-3059-getPrpCseller,robot-3059-getPrpCsellerCI,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            s += ",robot-3059-getMaxCsellFee,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-nomotor-unitedSaleEdit,robot-3059-nomotor-saveUnitedSale,robot-3059-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3059-login,robot_3059_bj_initData,robot_3059_bj_queryModel,robot_3059_bj_getSaleTaxInfo,robot_3059_bj_getRealValue,robot_3059_bj_getPersonData,robot_3059_bj_addPersonData,robot_3059_bj_askCharge,robot_3059_bj_queryPayForXSFY,robot_3059_bj_getCagentCI,robot_3059_bj_getCagent,robot_3059_bj_queryPayForXSFY_req,robot_3059_bj_queryIlogEngage,robot_3059_bj_insureRefrenshPlan,robot_3059_bj_insure4S,robot-3059-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3059', 'xiandai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-现代-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ" +            ",robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind,robot-3059-nomotor-query,robot-3059-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3059', 'xiandai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-现代-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ" +            ",robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind,robot-3059-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3059', 'xiandai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-现代-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ" +            ",robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind,robot-3059-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3059', 'xiandai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "现代财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-现代-电销', 'def getTemplateGroup(dataSource){    return "robot-3059-pureESale_Login,robot-3059-pureESale_Welcome,robot-3059-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3059', 'xiandai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-现代续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3059-login,robot-3059-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3059', 'xiandai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-现代-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3059-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3059-ObtainConfig,robot-3059-selectRenewal,robot-3059-editCengage,robot-3059-editCitemCar,robot-3059-editCinsured,robot-3059-renewalPolicy,robot-3059-renewalPolicyCI,robot-3059-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3059-VehicleModelList" //上海车型查询        }        s += ",robot-3059-vehicleQueryXB,robot-3059-queryTaxAbateForPlat,robot-3059-calActualValue,robot-3059-editCitemKind,robot-3059-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3059-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-refreshPlanByTimes,robot-3059-insert"            }else{                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3059-calAnciInfo,robot-3059-getMaxCsellFee,robot-3059-getPrpCseller,robot-3059-getPrpCsellerCI,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            s += ",robot-3059-getMaxCsellFee,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"        }    }else{        s += ",robot-3059-ObtainConfig,robot-3059-checkInsurePerson,robot-3059-changePerson,robot-3059-checkInsuredPerson,robot-3059-changePerson,robot-3059-prepareEdit,robot-3059-editCengage,robot-3059-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3059-queryVehiclePMCheck,robot-3059-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3059-VehicleModelList" //上海车型查询        }        s += ",robot-3059-queryPrepare,robot-3059-vehicleQuery,robot-3059-queryTaxAbateForPlat,robot-3059-calActualValue,robot-3059-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3059-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3059-calAnciInfo,robot-3059-queryPayFor,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            }else{                s += ",robot-3059-calAnciInfo,robot-3059-checkAgentType,robot-3059-queryPayForSCMS,robot-3059-getCagent,robot-3059-getCagentCI,robot-3059-refreshPlanByTimes,robot-3059-nomotor-unitedSaleEdit,robot-3059-nomotor-saveUnitedSale,robot-3059-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3059-calAnciInfo,robot-3059-getMaxCsellFee,robot-3059-getPrpCseller,robot-3059-getPrpCsellerCI,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-insert"            s += ",robot-3059-getMaxCsellFee,robot-3059-queryPayForSCMS,robot-3059-refreshPlanByTimes,robot-3059-nomotor-unitedSaleEdit,robot-3059-nomotor-saveUnitedSale,robot-3059-insert"        }    }    s += ",robot-3059-checkRiskCode,robot-3059-editMainUwtFlag,robot-3059-editSubmitUndwrt,robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-showUndwrtMsgQ,robot-3059-showUndwrtMsgS"+            ",robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ,robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind,robot-3059-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3059-login,robot_3059_bj_initData,robot_3059_bj_queryModel,robot_3059_bj_getSaleTaxInfo,robot_3059_bj_getRealValue,robot_3059_bj_getPersonData,robot_3059_bj_addPersonData,robot_3059_bj_askCharge,robot_3059_bj_queryPayForXSFY,robot_3059_bj_getCagentCI,robot_3059_bj_getCagent,robot_3059_bj_queryPayForXSFY_req,robot_3059_bj_queryIlogEngage,robot_3059_bj_insureRefrenshPlan,robot_3059_bj_insure4S,robot-3059-uploadImage,robot_3059_bj_autoInsure,robot_3059_bj_showUndwrtMsgQ,robot_3059_bj_showUndwrtMsgS";       s += ",robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ,robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +                ",robot-3059-showCinsuredS,robot-3059-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3059', 'xiandai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-现代-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3059-qrcode_login,robot-3059-qrcode_printTwoBarCodeServlet,robot-3059-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3059-qrcode_login,robot-3059-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3059-qrcode_login,robot-3059-qrcode_editCheckFlag,robot-3059-qrcode_gotoJfcd,robot-3059-qrcode_prepareEditByJF,robot-3059-qrcode_getBusinessIn" +                ",robot-3059-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3059-qrcode_login,robot-3059-qrcode_editCheckFlag,robot-3059-qrcode_gotoJfcd,robot-3059-qrcode_prepareEditByJF,robot-3059-qrcode_getBusinessIn" +                ",robot-3059-qrcode_checkBeforeCalculate,robot-3059-qrcode_saveByJF,robot-3059-qrcode_getBusinessIn_alipay,robot-3059-qrcode_editFeeInfor,robot-3059-qrcode_editPayFeeByWeChat,robot-3059-qrcode_saveByWeChat,robot-3059-qrcode_save";		} else {					return  "robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-bj-editIDCardCheck,robot-3059-apply-selectIsNetProp,robot-3059-apply-saveCheckCode,robot-3059-qrcode_editCheckFlag,robot-3059-qrcode_gotoJfcd,robot-3059-qrcode_prepareEditByJF,robot-3059-qrcode_getBusinessIn" +",robot-3059-qrcode_checkBeforeCalculate,robot-3059-qrcode_saveByJF,robot-3059-qrcode_getBusinessIn_alipay,robot-3059-qrcode_editFeeInfor,robot-3059-qrcode_editPayFeeByWeChat,robot-3059-qrcode_saveByWeChat,robot-3059-qrcode_save";		}}    else {              return "robot-3059-qrcode_login,robot-3059-qrcode_editCheckFlag,robot-3059-qrcode_gotoJfcd,robot-3059-qrcode_prepareEditByJF,robot-3059-qrcode_getBusinessIn" +                ",robot-3059-qrcode_checkBeforeCalculate,robot-3059-qrcode_saveByJF,robot-3059-qrcode_getBusinessIn_alipay,robot-3059-qrcode_editFeeInfor,robot-3059-qrcode_editPayFeeByWeChat,robot-3059-qrcode_saveByWeChat,robot-3059-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3059', 'xiandai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-现代-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3059-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3059-qrcode_query_editCheckFlag,robot-3059-qrcode_query_gotoJfcd,robot-3059-qrcode_query_prepareEditByJF" +                ",robot-3059-qrcode_query_editMainInfor,robot-3059-qrcode_query_getBusinessIn,robot-3059-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ" +            ",robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind,robot-3059-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3059', 'xiandai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-现代-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-IdCarChekc" //申请验证码    else{        return"robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-editIDCardCheck,robot-3059-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3059', 'xiandai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-现代-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectPolicyefc,robot-3059-selectPolicybiz,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ" +            ",robot-3059-showCitemCarQ,robot-3059-showCinsuredQ,robot-3059-showCitemKindCI,robot-3059-browseProposalS,robot-3059-showCitemCarS" +            ",robot-3059-showCinsuredS,robot-3059-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3059', 'xiandai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3059-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectProposalQ,robot-3059-selectProposalS,robot-3059-browseProposalQ,robot-3059-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3059', 'xiandai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-现代-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-bj-editIDCardCheck,robot-3059-apply-bj-IdCarChekc";    } else {        s = "robot-3059-qrcode_login,robot-3059-qrcode_editCheckFlag,robot-3059-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3059', 'xiandai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi现代报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3059_ask_charge,edi_3059_noMotor_quote"	} else {		return "edi_3059_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3059', 'xiandai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-现代-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3059_ask_charge,edi_3059_noMotor_quote,edi_3059_askInsure,edi_3059_uploadImg,edi_3059_submitInsure,edi_3059_noMotor_submit" 	  	} else {		return "edi_3059_ask_chargeold,edi_3059_askInsure,edi_3059_uploadImg,edi_3059_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3059', 'xiandai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-现代-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3059_ask_charge,edi_3059_noMotor_quote,edi_3059_askInsure,edi_3059_uploadImg" 	} else {		return "edi_3059_ask_chargeold,edi_3059_askInsure,edi_3059_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3059', 'xiandai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-现代-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3059_efc_policyinfo,edi_3059_biz_policyinfo,edi_3059_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3059', 'xiandai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-现代-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3059_efc_insurequery,edi_3059_biz_insurequery,edi_3059_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3059', 'xiandai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京现代短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-bj-editIDCardCheck,robot-3059-apply-saveCheckCode,robot-3059-apply-selectIsNetProp";    } else {        s = "robot-3059-qrcode_login,robot-3059-apply-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3059', 'xiandai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-现代-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3059-bj-qrcode_login,robot-3059-apply-bj-prepare,robot-3059-apply-select-jq,robot-3059-apply-select-sy,robot-3059-apply-bj-editIDCardCheck,robot-3059-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3059', 'xiandai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-现代-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3059_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3059', 'xiandai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi现代北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3059_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3059', 'xiandai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi现代北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3059_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3059', 'xiandai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-现代-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3059-login,robot-3059-prepareQueryCode,robot-3059-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
