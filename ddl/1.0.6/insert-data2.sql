INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2043', 'huaan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-IdCarChekc" //申请验证码    else{        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2043', 'huaan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2043-queryCar,edi-2043-xbQuery,edi-2043-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2043', 'huaan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2043-queryCar,edi-2043-xbQuery,edi-2043-askCharge,edi-2043-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2043', 'huaan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2043-queryCar,edi-2043-xbQuery,edi-2043-askCharge,edi-2043-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2043', 'huaan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-华安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2043-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2043', 'huaan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2043-login,robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2043-login,robot-2043-ObtainConfig,robot-2043-checkInsurePerson,robot-2043-changePerson,robot-2043-checkInsuredPerson,robot-2043-changePerson,robot-2043-prepareEdit," +                    "robot-2043-prepareQueryCode,robot-2043-selectProposalCar,robot-2043-browseProposalCar,robot-2043-browseProposalCarefc,robot-2043-selectRenewalPolicyNo"        }else{            s = "robot-2043-login,robot-2043-ObtainConfig,robot-2043-checkInsurePerson,robot-2043-changePerson,robot-2043-checkInsuredPerson,robot-2043-changePerson,robot-2043-prepareEdit," +                    "robot-2043-prepareQueryCode,robot-2043-browseProposalCar,robot-2043-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2043-VehicleModelList" //上海车型查询        }        s += ",robot-2043-queryPrepare,robot-2043-vehicleQuery,robot-2043-queryTaxAbateForPlat,robot-2043-calActualValue,robot-2043-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2043-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2043-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-nomotor-unitedSaleEdit,robot-2043-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2043-login,robot_2043_bj_initData,robot_2043_bj_queryModel,robot_2043_bj_getSaleTaxInfo,robot_2043_bj_getRealValue,robot_2043_bj_getPersonData,robot_2043_bj_addPersonData,robot_2043_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2043', 'huaan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-华安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2043-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2043-ObtainConfig,robot-2043-selectRenewal,robot-2043-editCengage,robot-2043-editCitemCar,robot-2043-editCinsured,robot-2043-renewalPolicy,robot-2043-renewalPolicyCI,robot-2043-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2043-VehicleModelList" //上海车型查询        }        s += ",robot-2043-vehicleQueryXB,robot-2043-queryTaxAbateForPlat,robot-2043-calActualValue,robot-2043-editCitemKind,robot-2043-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2043-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-refreshPlanByTimes,robot-2043-insert"            }else{                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2043-getMaxCsellFee,robot-2043-getPrpCseller,robot-2043-getPrpCsellerCI,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            s += ",robot-2043-getMaxCsellFee,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"        }    }else{        s +=",robot-2043-ObtainConfig,robot-2043-checkInsurePerson,robot-2043-changePerson,robot-2043-checkInsuredPerson,robot-2043-changePerson,robot-2043-prepareEdit,robot-2043-selectRenewalPolicyNo,robot-2043-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2043-VehicleModelList" //上海车型查询        }        s += ",robot-2043-queryPrepare,robot-2043-vehicleQuery,robot-2043-queryTaxAbateForPlat,robot-2043-calActualValue,robot-2043-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2043-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2043-calAnciInfo,robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            }else{                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-nomotor-unitedSaleEdit,robot-2043-nomotor-saveUnitedSale,robot-2043-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2043-getMaxCsellFee,robot-2043-getPrpCseller,robot-2043-getPrpCsellerCI,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            s += ",robot-2043-getMaxCsellFee,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-nomotor-unitedSaleEdit,robot-2043-nomotor-saveUnitedSale,robot-2043-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2043-login,robot_2043_bj_initData,robot_2043_bj_queryModel,robot_2043_bj_getSaleTaxInfo,robot_2043_bj_getRealValue,robot_2043_bj_getPersonData,robot_2043_bj_addPersonData,robot_2043_bj_askCharge,robot_2043_bj_queryPayForXSFY,robot_2043_bj_getCagentCI,robot_2043_bj_getCagent,robot_2043_bj_queryPayForXSFY_req,robot_2043_bj_queryIlogEngage,robot_2043_bj_insureRefrenshPlan,robot_2043_bj_insure4S,robot-2043-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2043', 'huaan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-华安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ" +            ",robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind,robot-2043-nomotor-query,robot-2043-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2043', 'huaan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ" +            ",robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind,robot-2043-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2043', 'huaan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ" +            ",robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind,robot-2043-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2043', 'huaan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "华安财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-华安-电销', 'def getTemplateGroup(dataSource){    return "robot-2043-pureESale_Login,robot-2043-pureESale_Welcome,robot-2043-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2043', 'huaan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2043-login,robot-2043-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2043', 'huaan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-华安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2043-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2043-ObtainConfig,robot-2043-selectRenewal,robot-2043-editCengage,robot-2043-editCitemCar,robot-2043-editCinsured,robot-2043-renewalPolicy,robot-2043-renewalPolicyCI,robot-2043-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2043-VehicleModelList" //上海车型查询        }        s += ",robot-2043-vehicleQueryXB,robot-2043-queryTaxAbateForPlat,robot-2043-calActualValue,robot-2043-editCitemKind,robot-2043-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2043-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-refreshPlanByTimes,robot-2043-insert"            }else{                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2043-calAnciInfo,robot-2043-getMaxCsellFee,robot-2043-getPrpCseller,robot-2043-getPrpCsellerCI,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            s += ",robot-2043-getMaxCsellFee,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"        }    }else{        s += ",robot-2043-ObtainConfig,robot-2043-checkInsurePerson,robot-2043-changePerson,robot-2043-checkInsuredPerson,robot-2043-changePerson,robot-2043-prepareEdit,robot-2043-editCengage,robot-2043-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2043-queryVehiclePMCheck,robot-2043-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2043-VehicleModelList" //上海车型查询        }        s += ",robot-2043-queryPrepare,robot-2043-vehicleQuery,robot-2043-queryTaxAbateForPlat,robot-2043-calActualValue,robot-2043-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2043-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2043-calAnciInfo,robot-2043-queryPayFor,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            }else{                s += ",robot-2043-calAnciInfo,robot-2043-checkAgentType,robot-2043-queryPayForSCMS,robot-2043-getCagent,robot-2043-getCagentCI,robot-2043-refreshPlanByTimes,robot-2043-nomotor-unitedSaleEdit,robot-2043-nomotor-saveUnitedSale,robot-2043-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2043-calAnciInfo,robot-2043-getMaxCsellFee,robot-2043-getPrpCseller,robot-2043-getPrpCsellerCI,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-insert"            s += ",robot-2043-getMaxCsellFee,robot-2043-queryPayForSCMS,robot-2043-refreshPlanByTimes,robot-2043-nomotor-unitedSaleEdit,robot-2043-nomotor-saveUnitedSale,robot-2043-insert"        }    }    s += ",robot-2043-checkRiskCode,robot-2043-editMainUwtFlag,robot-2043-editSubmitUndwrt,robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-showUndwrtMsgQ,robot-2043-showUndwrtMsgS"+            ",robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ,robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind,robot-2043-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2043-login,robot_2043_bj_initData,robot_2043_bj_queryModel,robot_2043_bj_getSaleTaxInfo,robot_2043_bj_getRealValue,robot_2043_bj_getPersonData,robot_2043_bj_addPersonData,robot_2043_bj_askCharge,robot_2043_bj_queryPayForXSFY,robot_2043_bj_getCagentCI,robot_2043_bj_getCagent,robot_2043_bj_queryPayForXSFY_req,robot_2043_bj_queryIlogEngage,robot_2043_bj_insureRefrenshPlan,robot_2043_bj_insure4S,robot-2043-uploadImage,robot_2043_bj_autoInsure,robot_2043_bj_showUndwrtMsgQ,robot_2043_bj_showUndwrtMsgS";       s += ",robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ,robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +                ",robot-2043-showCinsuredS,robot-2043-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2043', 'huaan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2043-qrcode_login,robot-2043-qrcode_printTwoBarCodeServlet,robot-2043-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2043-qrcode_login,robot-2043-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2043-qrcode_login,robot-2043-qrcode_editCheckFlag,robot-2043-qrcode_gotoJfcd,robot-2043-qrcode_prepareEditByJF,robot-2043-qrcode_getBusinessIn" +                ",robot-2043-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2043-qrcode_login,robot-2043-qrcode_editCheckFlag,robot-2043-qrcode_gotoJfcd,robot-2043-qrcode_prepareEditByJF,robot-2043-qrcode_getBusinessIn" +                ",robot-2043-qrcode_checkBeforeCalculate,robot-2043-qrcode_saveByJF,robot-2043-qrcode_getBusinessIn_alipay,robot-2043-qrcode_editFeeInfor,robot-2043-qrcode_editPayFeeByWeChat,robot-2043-qrcode_saveByWeChat,robot-2043-qrcode_save";		} else {					return  "robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-bj-editIDCardCheck,robot-2043-apply-selectIsNetProp,robot-2043-apply-saveCheckCode,robot-2043-qrcode_editCheckFlag,robot-2043-qrcode_gotoJfcd,robot-2043-qrcode_prepareEditByJF,robot-2043-qrcode_getBusinessIn" +",robot-2043-qrcode_checkBeforeCalculate,robot-2043-qrcode_saveByJF,robot-2043-qrcode_getBusinessIn_alipay,robot-2043-qrcode_editFeeInfor,robot-2043-qrcode_editPayFeeByWeChat,robot-2043-qrcode_saveByWeChat,robot-2043-qrcode_save";		}}    else {              return "robot-2043-qrcode_login,robot-2043-qrcode_editCheckFlag,robot-2043-qrcode_gotoJfcd,robot-2043-qrcode_prepareEditByJF,robot-2043-qrcode_getBusinessIn" +                ",robot-2043-qrcode_checkBeforeCalculate,robot-2043-qrcode_saveByJF,robot-2043-qrcode_getBusinessIn_alipay,robot-2043-qrcode_editFeeInfor,robot-2043-qrcode_editPayFeeByWeChat,robot-2043-qrcode_saveByWeChat,robot-2043-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2043', 'huaan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2043-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2043-qrcode_query_editCheckFlag,robot-2043-qrcode_query_gotoJfcd,robot-2043-qrcode_query_prepareEditByJF" +                ",robot-2043-qrcode_query_editMainInfor,robot-2043-qrcode_query_getBusinessIn,robot-2043-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ" +            ",robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind,robot-2043-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2043', 'huaan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-IdCarChekc" //申请验证码    else{        return"robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-editIDCardCheck,robot-2043-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2043', 'huaan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-华安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectPolicyefc,robot-2043-selectPolicybiz,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ" +            ",robot-2043-showCitemCarQ,robot-2043-showCinsuredQ,robot-2043-showCitemKindCI,robot-2043-browseProposalS,robot-2043-showCitemCarS" +            ",robot-2043-showCinsuredS,robot-2043-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2043', 'huaan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2043-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectProposalQ,robot-2043-selectProposalS,robot-2043-browseProposalQ,robot-2043-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2043', 'huaan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-bj-editIDCardCheck,robot-2043-apply-bj-IdCarChekc";    } else {        s = "robot-2043-qrcode_login,robot-2043-qrcode_editCheckFlag,robot-2043-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2043', 'huaan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi华安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2043_ask_charge,edi_2043_noMotor_quote"	} else {		return "edi_2043_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2043', 'huaan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2043_ask_charge,edi_2043_noMotor_quote,edi_2043_askInsure,edi_2043_uploadImg,edi_2043_submitInsure,edi_2043_noMotor_submit" 	  	} else {		return "edi_2043_ask_chargeold,edi_2043_askInsure,edi_2043_uploadImg,edi_2043_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2043', 'huaan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2043_ask_charge,edi_2043_noMotor_quote,edi_2043_askInsure,edi_2043_uploadImg" 	} else {		return "edi_2043_ask_chargeold,edi_2043_askInsure,edi_2043_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2043', 'huaan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-华安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2043_efc_policyinfo,edi_2043_biz_policyinfo,edi_2043_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2043', 'huaan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-华安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2043_efc_insurequery,edi_2043_biz_insurequery,edi_2043_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2043', 'huaan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京华安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-bj-editIDCardCheck,robot-2043-apply-saveCheckCode,robot-2043-apply-selectIsNetProp";    } else {        s = "robot-2043-qrcode_login,robot-2043-apply-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2043', 'huaan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2043-bj-qrcode_login,robot-2043-apply-bj-prepare,robot-2043-apply-select-jq,robot-2043-apply-select-sy,robot-2043-apply-bj-editIDCardCheck,robot-2043-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2043', 'huaan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-华安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2043_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2043', 'huaan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2043_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2043', 'huaan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2043_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2043', 'huaan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-华安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2043-login,robot-2043-prepareQueryCode,robot-2043-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2045', 'tianan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-天安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-IdCarChekc" //申请验证码    else{        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2045', 'tianan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-天安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2045-queryCar,edi-2045-xbQuery,edi-2045-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2045', 'tianan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-天安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2045-queryCar,edi-2045-xbQuery,edi-2045-askCharge,edi-2045-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2045', 'tianan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-天安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2045-queryCar,edi-2045-xbQuery,edi-2045-askCharge,edi-2045-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2045', 'tianan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-天安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2045-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2045', 'tianan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-天安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2045-login,robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2045-login,robot-2045-ObtainConfig,robot-2045-checkInsurePerson,robot-2045-changePerson,robot-2045-checkInsuredPerson,robot-2045-changePerson,robot-2045-prepareEdit," +                    "robot-2045-prepareQueryCode,robot-2045-selectProposalCar,robot-2045-browseProposalCar,robot-2045-browseProposalCarefc,robot-2045-selectRenewalPolicyNo"        }else{            s = "robot-2045-login,robot-2045-ObtainConfig,robot-2045-checkInsurePerson,robot-2045-changePerson,robot-2045-checkInsuredPerson,robot-2045-changePerson,robot-2045-prepareEdit," +                    "robot-2045-prepareQueryCode,robot-2045-browseProposalCar,robot-2045-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2045-VehicleModelList" //上海车型查询        }        s += ",robot-2045-queryPrepare,robot-2045-vehicleQuery,robot-2045-queryTaxAbateForPlat,robot-2045-calActualValue,robot-2045-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2045-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2045-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-nomotor-unitedSaleEdit,robot-2045-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2045-login,robot_2045_bj_initData,robot_2045_bj_queryModel,robot_2045_bj_getSaleTaxInfo,robot_2045_bj_getRealValue,robot_2045_bj_getPersonData,robot_2045_bj_addPersonData,robot_2045_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2045', 'tianan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-天安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2045-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2045-ObtainConfig,robot-2045-selectRenewal,robot-2045-editCengage,robot-2045-editCitemCar,robot-2045-editCinsured,robot-2045-renewalPolicy,robot-2045-renewalPolicyCI,robot-2045-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2045-VehicleModelList" //上海车型查询        }        s += ",robot-2045-vehicleQueryXB,robot-2045-queryTaxAbateForPlat,robot-2045-calActualValue,robot-2045-editCitemKind,robot-2045-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2045-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-refreshPlanByTimes,robot-2045-insert"            }else{                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2045-getMaxCsellFee,robot-2045-getPrpCseller,robot-2045-getPrpCsellerCI,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            s += ",robot-2045-getMaxCsellFee,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"        }    }else{        s +=",robot-2045-ObtainConfig,robot-2045-checkInsurePerson,robot-2045-changePerson,robot-2045-checkInsuredPerson,robot-2045-changePerson,robot-2045-prepareEdit,robot-2045-selectRenewalPolicyNo,robot-2045-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2045-VehicleModelList" //上海车型查询        }        s += ",robot-2045-queryPrepare,robot-2045-vehicleQuery,robot-2045-queryTaxAbateForPlat,robot-2045-calActualValue,robot-2045-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2045-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2045-calAnciInfo,robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            }else{                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-nomotor-unitedSaleEdit,robot-2045-nomotor-saveUnitedSale,robot-2045-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2045-getMaxCsellFee,robot-2045-getPrpCseller,robot-2045-getPrpCsellerCI,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            s += ",robot-2045-getMaxCsellFee,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-nomotor-unitedSaleEdit,robot-2045-nomotor-saveUnitedSale,robot-2045-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2045-login,robot_2045_bj_initData,robot_2045_bj_queryModel,robot_2045_bj_getSaleTaxInfo,robot_2045_bj_getRealValue,robot_2045_bj_getPersonData,robot_2045_bj_addPersonData,robot_2045_bj_askCharge,robot_2045_bj_queryPayForXSFY,robot_2045_bj_getCagentCI,robot_2045_bj_getCagent,robot_2045_bj_queryPayForXSFY_req,robot_2045_bj_queryIlogEngage,robot_2045_bj_insureRefrenshPlan,robot_2045_bj_insure4S,robot-2045-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2045', 'tianan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-天安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ" +            ",robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind,robot-2045-nomotor-query,robot-2045-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2045', 'tianan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-天安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ" +            ",robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind,robot-2045-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2045', 'tianan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-天安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ" +            ",robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind,robot-2045-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2045', 'tianan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "天安财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-天安-电销', 'def getTemplateGroup(dataSource){    return "robot-2045-pureESale_Login,robot-2045-pureESale_Welcome,robot-2045-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2045', 'tianan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-天安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2045-login,robot-2045-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2045', 'tianan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-天安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2045-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2045-ObtainConfig,robot-2045-selectRenewal,robot-2045-editCengage,robot-2045-editCitemCar,robot-2045-editCinsured,robot-2045-renewalPolicy,robot-2045-renewalPolicyCI,robot-2045-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2045-VehicleModelList" //上海车型查询        }        s += ",robot-2045-vehicleQueryXB,robot-2045-queryTaxAbateForPlat,robot-2045-calActualValue,robot-2045-editCitemKind,robot-2045-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2045-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-refreshPlanByTimes,robot-2045-insert"            }else{                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2045-calAnciInfo,robot-2045-getMaxCsellFee,robot-2045-getPrpCseller,robot-2045-getPrpCsellerCI,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            s += ",robot-2045-getMaxCsellFee,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"        }    }else{        s += ",robot-2045-ObtainConfig,robot-2045-checkInsurePerson,robot-2045-changePerson,robot-2045-checkInsuredPerson,robot-2045-changePerson,robot-2045-prepareEdit,robot-2045-editCengage,robot-2045-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2045-queryVehiclePMCheck,robot-2045-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2045-VehicleModelList" //上海车型查询        }        s += ",robot-2045-queryPrepare,robot-2045-vehicleQuery,robot-2045-queryTaxAbateForPlat,robot-2045-calActualValue,robot-2045-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2045-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2045-calAnciInfo,robot-2045-queryPayFor,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            }else{                s += ",robot-2045-calAnciInfo,robot-2045-checkAgentType,robot-2045-queryPayForSCMS,robot-2045-getCagent,robot-2045-getCagentCI,robot-2045-refreshPlanByTimes,robot-2045-nomotor-unitedSaleEdit,robot-2045-nomotor-saveUnitedSale,robot-2045-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2045-calAnciInfo,robot-2045-getMaxCsellFee,robot-2045-getPrpCseller,robot-2045-getPrpCsellerCI,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-insert"            s += ",robot-2045-getMaxCsellFee,robot-2045-queryPayForSCMS,robot-2045-refreshPlanByTimes,robot-2045-nomotor-unitedSaleEdit,robot-2045-nomotor-saveUnitedSale,robot-2045-insert"        }    }    s += ",robot-2045-checkRiskCode,robot-2045-editMainUwtFlag,robot-2045-editSubmitUndwrt,robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-showUndwrtMsgQ,robot-2045-showUndwrtMsgS"+            ",robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ,robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind,robot-2045-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2045-login,robot_2045_bj_initData,robot_2045_bj_queryModel,robot_2045_bj_getSaleTaxInfo,robot_2045_bj_getRealValue,robot_2045_bj_getPersonData,robot_2045_bj_addPersonData,robot_2045_bj_askCharge,robot_2045_bj_queryPayForXSFY,robot_2045_bj_getCagentCI,robot_2045_bj_getCagent,robot_2045_bj_queryPayForXSFY_req,robot_2045_bj_queryIlogEngage,robot_2045_bj_insureRefrenshPlan,robot_2045_bj_insure4S,robot-2045-uploadImage,robot_2045_bj_autoInsure,robot_2045_bj_showUndwrtMsgQ,robot_2045_bj_showUndwrtMsgS";       s += ",robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ,robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +                ",robot-2045-showCinsuredS,robot-2045-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2045', 'tianan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-天安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2045-qrcode_login,robot-2045-qrcode_printTwoBarCodeServlet,robot-2045-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2045-qrcode_login,robot-2045-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2045-qrcode_login,robot-2045-qrcode_editCheckFlag,robot-2045-qrcode_gotoJfcd,robot-2045-qrcode_prepareEditByJF,robot-2045-qrcode_getBusinessIn" +                ",robot-2045-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2045-qrcode_login,robot-2045-qrcode_editCheckFlag,robot-2045-qrcode_gotoJfcd,robot-2045-qrcode_prepareEditByJF,robot-2045-qrcode_getBusinessIn" +                ",robot-2045-qrcode_checkBeforeCalculate,robot-2045-qrcode_saveByJF,robot-2045-qrcode_getBusinessIn_alipay,robot-2045-qrcode_editFeeInfor,robot-2045-qrcode_editPayFeeByWeChat,robot-2045-qrcode_saveByWeChat,robot-2045-qrcode_save";		} else {					return  "robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-bj-editIDCardCheck,robot-2045-apply-selectIsNetProp,robot-2045-apply-saveCheckCode,robot-2045-qrcode_editCheckFlag,robot-2045-qrcode_gotoJfcd,robot-2045-qrcode_prepareEditByJF,robot-2045-qrcode_getBusinessIn" +",robot-2045-qrcode_checkBeforeCalculate,robot-2045-qrcode_saveByJF,robot-2045-qrcode_getBusinessIn_alipay,robot-2045-qrcode_editFeeInfor,robot-2045-qrcode_editPayFeeByWeChat,robot-2045-qrcode_saveByWeChat,robot-2045-qrcode_save";		}}    else {              return "robot-2045-qrcode_login,robot-2045-qrcode_editCheckFlag,robot-2045-qrcode_gotoJfcd,robot-2045-qrcode_prepareEditByJF,robot-2045-qrcode_getBusinessIn" +                ",robot-2045-qrcode_checkBeforeCalculate,robot-2045-qrcode_saveByJF,robot-2045-qrcode_getBusinessIn_alipay,robot-2045-qrcode_editFeeInfor,robot-2045-qrcode_editPayFeeByWeChat,robot-2045-qrcode_saveByWeChat,robot-2045-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2045', 'tianan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-天安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2045-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2045-qrcode_query_editCheckFlag,robot-2045-qrcode_query_gotoJfcd,robot-2045-qrcode_query_prepareEditByJF" +                ",robot-2045-qrcode_query_editMainInfor,robot-2045-qrcode_query_getBusinessIn,robot-2045-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ" +            ",robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind,robot-2045-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2045', 'tianan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-天安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-IdCarChekc" //申请验证码    else{        return"robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-editIDCardCheck,robot-2045-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2045', 'tianan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-天安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectPolicyefc,robot-2045-selectPolicybiz,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ" +            ",robot-2045-showCitemCarQ,robot-2045-showCinsuredQ,robot-2045-showCitemKindCI,robot-2045-browseProposalS,robot-2045-showCitemCarS" +            ",robot-2045-showCinsuredS,robot-2045-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2045', 'tianan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2045-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectProposalQ,robot-2045-selectProposalS,robot-2045-browseProposalQ,robot-2045-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2045', 'tianan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-天安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-bj-editIDCardCheck,robot-2045-apply-bj-IdCarChekc";    } else {        s = "robot-2045-qrcode_login,robot-2045-qrcode_editCheckFlag,robot-2045-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2045', 'tianan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi天安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2045_ask_charge,edi_2045_noMotor_quote"	} else {		return "edi_2045_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2045', 'tianan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-天安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2045_ask_charge,edi_2045_noMotor_quote,edi_2045_askInsure,edi_2045_uploadImg,edi_2045_submitInsure,edi_2045_noMotor_submit" 	  	} else {		return "edi_2045_ask_chargeold,edi_2045_askInsure,edi_2045_uploadImg,edi_2045_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2045', 'tianan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-天安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2045_ask_charge,edi_2045_noMotor_quote,edi_2045_askInsure,edi_2045_uploadImg" 	} else {		return "edi_2045_ask_chargeold,edi_2045_askInsure,edi_2045_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2045', 'tianan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-天安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2045_efc_policyinfo,edi_2045_biz_policyinfo,edi_2045_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2045', 'tianan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-天安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2045_efc_insurequery,edi_2045_biz_insurequery,edi_2045_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2045', 'tianan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京天安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-bj-editIDCardCheck,robot-2045-apply-saveCheckCode,robot-2045-apply-selectIsNetProp";    } else {        s = "robot-2045-qrcode_login,robot-2045-apply-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2045', 'tianan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-天安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2045-bj-qrcode_login,robot-2045-apply-bj-prepare,robot-2045-apply-select-jq,robot-2045-apply-select-sy,robot-2045-apply-bj-editIDCardCheck,robot-2045-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2045', 'tianan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-天安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2045_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2045', 'tianan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi天安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2045_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2045', 'tianan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi天安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2045_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2045', 'tianan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-天安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2045-login,robot-2045-prepareQueryCode,robot-2045-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2019', 'yangguang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-阳光-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-IdCarChekc" //申请验证码    else{        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2019', 'yangguang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-阳光-报价', 'def getTemplateGroup(dataSource) {    return "edi-2019-queryCar,edi-2019-xbQuery,edi-2019-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2019', 'yangguang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-阳光-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2019-queryCar,edi-2019-xbQuery,edi-2019-askCharge,edi-2019-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2019', 'yangguang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-阳光-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2019-queryCar,edi-2019-xbQuery,edi-2019-askCharge,edi-2019-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2019', 'yangguang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-阳光-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2019-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2019', 'yangguang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-阳光-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2019-login,robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2019-login,robot-2019-ObtainConfig,robot-2019-checkInsurePerson,robot-2019-changePerson,robot-2019-checkInsuredPerson,robot-2019-changePerson,robot-2019-prepareEdit," +                    "robot-2019-prepareQueryCode,robot-2019-selectProposalCar,robot-2019-browseProposalCar,robot-2019-browseProposalCarefc,robot-2019-selectRenewalPolicyNo"        }else{            s = "robot-2019-login,robot-2019-ObtainConfig,robot-2019-checkInsurePerson,robot-2019-changePerson,robot-2019-checkInsuredPerson,robot-2019-changePerson,robot-2019-prepareEdit," +                    "robot-2019-prepareQueryCode,robot-2019-browseProposalCar,robot-2019-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2019-VehicleModelList" //上海车型查询        }        s += ",robot-2019-queryPrepare,robot-2019-vehicleQuery,robot-2019-queryTaxAbateForPlat,robot-2019-calActualValue,robot-2019-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2019-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2019-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-nomotor-unitedSaleEdit,robot-2019-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2019-login,robot_2019_bj_initData,robot_2019_bj_queryModel,robot_2019_bj_getSaleTaxInfo,robot_2019_bj_getRealValue,robot_2019_bj_getPersonData,robot_2019_bj_addPersonData,robot_2019_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2019', 'yangguang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-阳光-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2019-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2019-ObtainConfig,robot-2019-selectRenewal,robot-2019-editCengage,robot-2019-editCitemCar,robot-2019-editCinsured,robot-2019-renewalPolicy,robot-2019-renewalPolicyCI,robot-2019-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2019-VehicleModelList" //上海车型查询        }        s += ",robot-2019-vehicleQueryXB,robot-2019-queryTaxAbateForPlat,robot-2019-calActualValue,robot-2019-editCitemKind,robot-2019-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2019-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-refreshPlanByTimes,robot-2019-insert"            }else{                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2019-getMaxCsellFee,robot-2019-getPrpCseller,robot-2019-getPrpCsellerCI,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            s += ",robot-2019-getMaxCsellFee,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"        }    }else{        s +=",robot-2019-ObtainConfig,robot-2019-checkInsurePerson,robot-2019-changePerson,robot-2019-checkInsuredPerson,robot-2019-changePerson,robot-2019-prepareEdit,robot-2019-selectRenewalPolicyNo,robot-2019-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2019-VehicleModelList" //上海车型查询        }        s += ",robot-2019-queryPrepare,robot-2019-vehicleQuery,robot-2019-queryTaxAbateForPlat,robot-2019-calActualValue,robot-2019-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2019-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2019-calAnciInfo,robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            }else{                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-nomotor-unitedSaleEdit,robot-2019-nomotor-saveUnitedSale,robot-2019-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2019-getMaxCsellFee,robot-2019-getPrpCseller,robot-2019-getPrpCsellerCI,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            s += ",robot-2019-getMaxCsellFee,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-nomotor-unitedSaleEdit,robot-2019-nomotor-saveUnitedSale,robot-2019-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2019-login,robot_2019_bj_initData,robot_2019_bj_queryModel,robot_2019_bj_getSaleTaxInfo,robot_2019_bj_getRealValue,robot_2019_bj_getPersonData,robot_2019_bj_addPersonData,robot_2019_bj_askCharge,robot_2019_bj_queryPayForXSFY,robot_2019_bj_getCagentCI,robot_2019_bj_getCagent,robot_2019_bj_queryPayForXSFY_req,robot_2019_bj_queryIlogEngage,robot_2019_bj_insureRefrenshPlan,robot_2019_bj_insure4S,robot-2019-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2019', 'yangguang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-阳光-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ" +            ",robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind,robot-2019-nomotor-query,robot-2019-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2019', 'yangguang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-阳光-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ" +            ",robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind,robot-2019-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2019', 'yangguang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-阳光-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ" +            ",robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind,robot-2019-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2019', 'yangguang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "阳光财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-阳光-电销', 'def getTemplateGroup(dataSource){    return "robot-2019-pureESale_Login,robot-2019-pureESale_Welcome,robot-2019-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2019', 'yangguang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-阳光续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2019-login,robot-2019-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2019', 'yangguang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-阳光-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2019-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2019-ObtainConfig,robot-2019-selectRenewal,robot-2019-editCengage,robot-2019-editCitemCar,robot-2019-editCinsured,robot-2019-renewalPolicy,robot-2019-renewalPolicyCI,robot-2019-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2019-VehicleModelList" //上海车型查询        }        s += ",robot-2019-vehicleQueryXB,robot-2019-queryTaxAbateForPlat,robot-2019-calActualValue,robot-2019-editCitemKind,robot-2019-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2019-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-refreshPlanByTimes,robot-2019-insert"            }else{                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2019-calAnciInfo,robot-2019-getMaxCsellFee,robot-2019-getPrpCseller,robot-2019-getPrpCsellerCI,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            s += ",robot-2019-getMaxCsellFee,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"        }    }else{        s += ",robot-2019-ObtainConfig,robot-2019-checkInsurePerson,robot-2019-changePerson,robot-2019-checkInsuredPerson,robot-2019-changePerson,robot-2019-prepareEdit,robot-2019-editCengage,robot-2019-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2019-queryVehiclePMCheck,robot-2019-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2019-VehicleModelList" //上海车型查询        }        s += ",robot-2019-queryPrepare,robot-2019-vehicleQuery,robot-2019-queryTaxAbateForPlat,robot-2019-calActualValue,robot-2019-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2019-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2019-calAnciInfo,robot-2019-queryPayFor,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            }else{                s += ",robot-2019-calAnciInfo,robot-2019-checkAgentType,robot-2019-queryPayForSCMS,robot-2019-getCagent,robot-2019-getCagentCI,robot-2019-refreshPlanByTimes,robot-2019-nomotor-unitedSaleEdit,robot-2019-nomotor-saveUnitedSale,robot-2019-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2019-calAnciInfo,robot-2019-getMaxCsellFee,robot-2019-getPrpCseller,robot-2019-getPrpCsellerCI,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-insert"            s += ",robot-2019-getMaxCsellFee,robot-2019-queryPayForSCMS,robot-2019-refreshPlanByTimes,robot-2019-nomotor-unitedSaleEdit,robot-2019-nomotor-saveUnitedSale,robot-2019-insert"        }    }    s += ",robot-2019-checkRiskCode,robot-2019-editMainUwtFlag,robot-2019-editSubmitUndwrt,robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-showUndwrtMsgQ,robot-2019-showUndwrtMsgS"+            ",robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ,robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind,robot-2019-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2019-login,robot_2019_bj_initData,robot_2019_bj_queryModel,robot_2019_bj_getSaleTaxInfo,robot_2019_bj_getRealValue,robot_2019_bj_getPersonData,robot_2019_bj_addPersonData,robot_2019_bj_askCharge,robot_2019_bj_queryPayForXSFY,robot_2019_bj_getCagentCI,robot_2019_bj_getCagent,robot_2019_bj_queryPayForXSFY_req,robot_2019_bj_queryIlogEngage,robot_2019_bj_insureRefrenshPlan,robot_2019_bj_insure4S,robot-2019-uploadImage,robot_2019_bj_autoInsure,robot_2019_bj_showUndwrtMsgQ,robot_2019_bj_showUndwrtMsgS";       s += ",robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ,robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +                ",robot-2019-showCinsuredS,robot-2019-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2019', 'yangguang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-阳光-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2019-qrcode_login,robot-2019-qrcode_printTwoBarCodeServlet,robot-2019-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2019-qrcode_login,robot-2019-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2019-qrcode_login,robot-2019-qrcode_editCheckFlag,robot-2019-qrcode_gotoJfcd,robot-2019-qrcode_prepareEditByJF,robot-2019-qrcode_getBusinessIn" +                ",robot-2019-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2019-qrcode_login,robot-2019-qrcode_editCheckFlag,robot-2019-qrcode_gotoJfcd,robot-2019-qrcode_prepareEditByJF,robot-2019-qrcode_getBusinessIn" +                ",robot-2019-qrcode_checkBeforeCalculate,robot-2019-qrcode_saveByJF,robot-2019-qrcode_getBusinessIn_alipay,robot-2019-qrcode_editFeeInfor,robot-2019-qrcode_editPayFeeByWeChat,robot-2019-qrcode_saveByWeChat,robot-2019-qrcode_save";		} else {					return  "robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-bj-editIDCardCheck,robot-2019-apply-selectIsNetProp,robot-2019-apply-saveCheckCode,robot-2019-qrcode_editCheckFlag,robot-2019-qrcode_gotoJfcd,robot-2019-qrcode_prepareEditByJF,robot-2019-qrcode_getBusinessIn" +",robot-2019-qrcode_checkBeforeCalculate,robot-2019-qrcode_saveByJF,robot-2019-qrcode_getBusinessIn_alipay,robot-2019-qrcode_editFeeInfor,robot-2019-qrcode_editPayFeeByWeChat,robot-2019-qrcode_saveByWeChat,robot-2019-qrcode_save";		}}    else {              return "robot-2019-qrcode_login,robot-2019-qrcode_editCheckFlag,robot-2019-qrcode_gotoJfcd,robot-2019-qrcode_prepareEditByJF,robot-2019-qrcode_getBusinessIn" +                ",robot-2019-qrcode_checkBeforeCalculate,robot-2019-qrcode_saveByJF,robot-2019-qrcode_getBusinessIn_alipay,robot-2019-qrcode_editFeeInfor,robot-2019-qrcode_editPayFeeByWeChat,robot-2019-qrcode_saveByWeChat,robot-2019-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2019', 'yangguang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-阳光-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2019-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2019-qrcode_query_editCheckFlag,robot-2019-qrcode_query_gotoJfcd,robot-2019-qrcode_query_prepareEditByJF" +                ",robot-2019-qrcode_query_editMainInfor,robot-2019-qrcode_query_getBusinessIn,robot-2019-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ" +            ",robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind,robot-2019-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2019', 'yangguang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-阳光-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-IdCarChekc" //申请验证码    else{        return"robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-editIDCardCheck,robot-2019-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2019', 'yangguang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-阳光-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectPolicyefc,robot-2019-selectPolicybiz,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ" +            ",robot-2019-showCitemCarQ,robot-2019-showCinsuredQ,robot-2019-showCitemKindCI,robot-2019-browseProposalS,robot-2019-showCitemCarS" +            ",robot-2019-showCinsuredS,robot-2019-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2019', 'yangguang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2019-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectProposalQ,robot-2019-selectProposalS,robot-2019-browseProposalQ,robot-2019-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2019', 'yangguang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-阳光-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-bj-editIDCardCheck,robot-2019-apply-bj-IdCarChekc";    } else {        s = "robot-2019-qrcode_login,robot-2019-qrcode_editCheckFlag,robot-2019-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2019', 'yangguang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi阳光报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2019_ask_charge,edi_2019_noMotor_quote"	} else {		return "edi_2019_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2019', 'yangguang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-阳光-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2019_ask_charge,edi_2019_noMotor_quote,edi_2019_askInsure,edi_2019_uploadImg,edi_2019_submitInsure,edi_2019_noMotor_submit" 	  	} else {		return "edi_2019_ask_chargeold,edi_2019_askInsure,edi_2019_uploadImg,edi_2019_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2019', 'yangguang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-阳光-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2019_ask_charge,edi_2019_noMotor_quote,edi_2019_askInsure,edi_2019_uploadImg" 	} else {		return "edi_2019_ask_chargeold,edi_2019_askInsure,edi_2019_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2019', 'yangguang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-阳光-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2019_efc_policyinfo,edi_2019_biz_policyinfo,edi_2019_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2019', 'yangguang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-阳光-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2019_efc_insurequery,edi_2019_biz_insurequery,edi_2019_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2019', 'yangguang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京阳光短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-bj-editIDCardCheck,robot-2019-apply-saveCheckCode,robot-2019-apply-selectIsNetProp";    } else {        s = "robot-2019-qrcode_login,robot-2019-apply-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2019', 'yangguang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-阳光-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2019-bj-qrcode_login,robot-2019-apply-bj-prepare,robot-2019-apply-select-jq,robot-2019-apply-select-sy,robot-2019-apply-bj-editIDCardCheck,robot-2019-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2019', 'yangguang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-阳光-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2019_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2019', 'yangguang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi阳光北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2019_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2019', 'yangguang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi阳光北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2019_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2019', 'yangguang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-阳光-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2019-login,robot-2019-prepareQueryCode,robot-2019-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2022', 'yongcheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-永诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-IdCarChekc" //申请验证码    else{        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2022', 'yongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-永诚-报价', 'def getTemplateGroup(dataSource) {    return "edi-2022-queryCar,edi-2022-xbQuery,edi-2022-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2022', 'yongcheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-永诚-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2022-queryCar,edi-2022-xbQuery,edi-2022-askCharge,edi-2022-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2022', 'yongcheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-永诚-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2022-queryCar,edi-2022-xbQuery,edi-2022-askCharge,edi-2022-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2022', 'yongcheng', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-永诚-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2022-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2022', 'yongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-永诚-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2022-login,robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2022-login,robot-2022-ObtainConfig,robot-2022-checkInsurePerson,robot-2022-changePerson,robot-2022-checkInsuredPerson,robot-2022-changePerson,robot-2022-prepareEdit," +                    "robot-2022-prepareQueryCode,robot-2022-selectProposalCar,robot-2022-browseProposalCar,robot-2022-browseProposalCarefc,robot-2022-selectRenewalPolicyNo"        }else{            s = "robot-2022-login,robot-2022-ObtainConfig,robot-2022-checkInsurePerson,robot-2022-changePerson,robot-2022-checkInsuredPerson,robot-2022-changePerson,robot-2022-prepareEdit," +                    "robot-2022-prepareQueryCode,robot-2022-browseProposalCar,robot-2022-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2022-VehicleModelList" //上海车型查询        }        s += ",robot-2022-queryPrepare,robot-2022-vehicleQuery,robot-2022-queryTaxAbateForPlat,robot-2022-calActualValue,robot-2022-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2022-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2022-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-nomotor-unitedSaleEdit,robot-2022-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2022-login,robot_2022_bj_initData,robot_2022_bj_queryModel,robot_2022_bj_getSaleTaxInfo,robot_2022_bj_getRealValue,robot_2022_bj_getPersonData,robot_2022_bj_addPersonData,robot_2022_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2022', 'yongcheng', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-永诚-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2022-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2022-ObtainConfig,robot-2022-selectRenewal,robot-2022-editCengage,robot-2022-editCitemCar,robot-2022-editCinsured,robot-2022-renewalPolicy,robot-2022-renewalPolicyCI,robot-2022-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2022-VehicleModelList" //上海车型查询        }        s += ",robot-2022-vehicleQueryXB,robot-2022-queryTaxAbateForPlat,robot-2022-calActualValue,robot-2022-editCitemKind,robot-2022-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2022-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-refreshPlanByTimes,robot-2022-insert"            }else{                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2022-getMaxCsellFee,robot-2022-getPrpCseller,robot-2022-getPrpCsellerCI,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            s += ",robot-2022-getMaxCsellFee,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"        }    }else{        s +=",robot-2022-ObtainConfig,robot-2022-checkInsurePerson,robot-2022-changePerson,robot-2022-checkInsuredPerson,robot-2022-changePerson,robot-2022-prepareEdit,robot-2022-selectRenewalPolicyNo,robot-2022-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2022-VehicleModelList" //上海车型查询        }        s += ",robot-2022-queryPrepare,robot-2022-vehicleQuery,robot-2022-queryTaxAbateForPlat,robot-2022-calActualValue,robot-2022-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2022-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2022-calAnciInfo,robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            }else{                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-nomotor-unitedSaleEdit,robot-2022-nomotor-saveUnitedSale,robot-2022-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2022-getMaxCsellFee,robot-2022-getPrpCseller,robot-2022-getPrpCsellerCI,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            s += ",robot-2022-getMaxCsellFee,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-nomotor-unitedSaleEdit,robot-2022-nomotor-saveUnitedSale,robot-2022-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2022-login,robot_2022_bj_initData,robot_2022_bj_queryModel,robot_2022_bj_getSaleTaxInfo,robot_2022_bj_getRealValue,robot_2022_bj_getPersonData,robot_2022_bj_addPersonData,robot_2022_bj_askCharge,robot_2022_bj_queryPayForXSFY,robot_2022_bj_getCagentCI,robot_2022_bj_getCagent,robot_2022_bj_queryPayForXSFY_req,robot_2022_bj_queryIlogEngage,robot_2022_bj_insureRefrenshPlan,robot_2022_bj_insure4S,robot-2022-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2022', 'yongcheng', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-永诚-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ" +            ",robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind,robot-2022-nomotor-query,robot-2022-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2022', 'yongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-永诚-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ" +            ",robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind,robot-2022-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2022', 'yongcheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-永诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ" +            ",robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind,robot-2022-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2022', 'yongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "永诚财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-永诚-电销', 'def getTemplateGroup(dataSource){    return "robot-2022-pureESale_Login,robot-2022-pureESale_Welcome,robot-2022-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2022', 'yongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永诚续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2022-login,robot-2022-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2022', 'yongcheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-永诚-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2022-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2022-ObtainConfig,robot-2022-selectRenewal,robot-2022-editCengage,robot-2022-editCitemCar,robot-2022-editCinsured,robot-2022-renewalPolicy,robot-2022-renewalPolicyCI,robot-2022-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2022-VehicleModelList" //上海车型查询        }        s += ",robot-2022-vehicleQueryXB,robot-2022-queryTaxAbateForPlat,robot-2022-calActualValue,robot-2022-editCitemKind,robot-2022-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2022-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-refreshPlanByTimes,robot-2022-insert"            }else{                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2022-calAnciInfo,robot-2022-getMaxCsellFee,robot-2022-getPrpCseller,robot-2022-getPrpCsellerCI,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            s += ",robot-2022-getMaxCsellFee,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"        }    }else{        s += ",robot-2022-ObtainConfig,robot-2022-checkInsurePerson,robot-2022-changePerson,robot-2022-checkInsuredPerson,robot-2022-changePerson,robot-2022-prepareEdit,robot-2022-editCengage,robot-2022-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2022-queryVehiclePMCheck,robot-2022-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2022-VehicleModelList" //上海车型查询        }        s += ",robot-2022-queryPrepare,robot-2022-vehicleQuery,robot-2022-queryTaxAbateForPlat,robot-2022-calActualValue,robot-2022-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2022-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2022-calAnciInfo,robot-2022-queryPayFor,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            }else{                s += ",robot-2022-calAnciInfo,robot-2022-checkAgentType,robot-2022-queryPayForSCMS,robot-2022-getCagent,robot-2022-getCagentCI,robot-2022-refreshPlanByTimes,robot-2022-nomotor-unitedSaleEdit,robot-2022-nomotor-saveUnitedSale,robot-2022-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2022-calAnciInfo,robot-2022-getMaxCsellFee,robot-2022-getPrpCseller,robot-2022-getPrpCsellerCI,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-insert"            s += ",robot-2022-getMaxCsellFee,robot-2022-queryPayForSCMS,robot-2022-refreshPlanByTimes,robot-2022-nomotor-unitedSaleEdit,robot-2022-nomotor-saveUnitedSale,robot-2022-insert"        }    }    s += ",robot-2022-checkRiskCode,robot-2022-editMainUwtFlag,robot-2022-editSubmitUndwrt,robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-showUndwrtMsgQ,robot-2022-showUndwrtMsgS"+            ",robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ,robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind,robot-2022-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2022-login,robot_2022_bj_initData,robot_2022_bj_queryModel,robot_2022_bj_getSaleTaxInfo,robot_2022_bj_getRealValue,robot_2022_bj_getPersonData,robot_2022_bj_addPersonData,robot_2022_bj_askCharge,robot_2022_bj_queryPayForXSFY,robot_2022_bj_getCagentCI,robot_2022_bj_getCagent,robot_2022_bj_queryPayForXSFY_req,robot_2022_bj_queryIlogEngage,robot_2022_bj_insureRefrenshPlan,robot_2022_bj_insure4S,robot-2022-uploadImage,robot_2022_bj_autoInsure,robot_2022_bj_showUndwrtMsgQ,robot_2022_bj_showUndwrtMsgS";       s += ",robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ,robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +                ",robot-2022-showCinsuredS,robot-2022-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2022', 'yongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永诚-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2022-qrcode_login,robot-2022-qrcode_printTwoBarCodeServlet,robot-2022-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2022-qrcode_login,robot-2022-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2022-qrcode_login,robot-2022-qrcode_editCheckFlag,robot-2022-qrcode_gotoJfcd,robot-2022-qrcode_prepareEditByJF,robot-2022-qrcode_getBusinessIn" +                ",robot-2022-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2022-qrcode_login,robot-2022-qrcode_editCheckFlag,robot-2022-qrcode_gotoJfcd,robot-2022-qrcode_prepareEditByJF,robot-2022-qrcode_getBusinessIn" +                ",robot-2022-qrcode_checkBeforeCalculate,robot-2022-qrcode_saveByJF,robot-2022-qrcode_getBusinessIn_alipay,robot-2022-qrcode_editFeeInfor,robot-2022-qrcode_editPayFeeByWeChat,robot-2022-qrcode_saveByWeChat,robot-2022-qrcode_save";		} else {					return  "robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-bj-editIDCardCheck,robot-2022-apply-selectIsNetProp,robot-2022-apply-saveCheckCode,robot-2022-qrcode_editCheckFlag,robot-2022-qrcode_gotoJfcd,robot-2022-qrcode_prepareEditByJF,robot-2022-qrcode_getBusinessIn" +",robot-2022-qrcode_checkBeforeCalculate,robot-2022-qrcode_saveByJF,robot-2022-qrcode_getBusinessIn_alipay,robot-2022-qrcode_editFeeInfor,robot-2022-qrcode_editPayFeeByWeChat,robot-2022-qrcode_saveByWeChat,robot-2022-qrcode_save";		}}    else {              return "robot-2022-qrcode_login,robot-2022-qrcode_editCheckFlag,robot-2022-qrcode_gotoJfcd,robot-2022-qrcode_prepareEditByJF,robot-2022-qrcode_getBusinessIn" +                ",robot-2022-qrcode_checkBeforeCalculate,robot-2022-qrcode_saveByJF,robot-2022-qrcode_getBusinessIn_alipay,robot-2022-qrcode_editFeeInfor,robot-2022-qrcode_editPayFeeByWeChat,robot-2022-qrcode_saveByWeChat,robot-2022-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2022', 'yongcheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-永诚-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2022-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2022-qrcode_query_editCheckFlag,robot-2022-qrcode_query_gotoJfcd,robot-2022-qrcode_query_prepareEditByJF" +                ",robot-2022-qrcode_query_editMainInfor,robot-2022-qrcode_query_getBusinessIn,robot-2022-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ" +            ",robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind,robot-2022-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2022', 'yongcheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-永诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-IdCarChekc" //申请验证码    else{        return"robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-editIDCardCheck,robot-2022-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2022', 'yongcheng', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-永诚-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectPolicyefc,robot-2022-selectPolicybiz,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ" +            ",robot-2022-showCitemCarQ,robot-2022-showCinsuredQ,robot-2022-showCitemKindCI,robot-2022-browseProposalS,robot-2022-showCitemCarS" +            ",robot-2022-showCinsuredS,robot-2022-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2022', 'yongcheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2022-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectProposalQ,robot-2022-selectProposalS,robot-2022-browseProposalQ,robot-2022-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2022', 'yongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永诚-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-bj-editIDCardCheck,robot-2022-apply-bj-IdCarChekc";    } else {        s = "robot-2022-qrcode_login,robot-2022-qrcode_editCheckFlag,robot-2022-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2022', 'yongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi永诚报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2022_ask_charge,edi_2022_noMotor_quote"	} else {		return "edi_2022_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2022', 'yongcheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-永诚-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2022_ask_charge,edi_2022_noMotor_quote,edi_2022_askInsure,edi_2022_uploadImg,edi_2022_submitInsure,edi_2022_noMotor_submit" 	  	} else {		return "edi_2022_ask_chargeold,edi_2022_askInsure,edi_2022_uploadImg,edi_2022_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2022', 'yongcheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-永诚-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2022_ask_charge,edi_2022_noMotor_quote,edi_2022_askInsure,edi_2022_uploadImg" 	} else {		return "edi_2022_ask_chargeold,edi_2022_askInsure,edi_2022_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2022', 'yongcheng', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-永诚-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2022_efc_policyinfo,edi_2022_biz_policyinfo,edi_2022_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2022', 'yongcheng', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-永诚-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2022_efc_insurequery,edi_2022_biz_insurequery,edi_2022_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2022', 'yongcheng', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京永诚短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-bj-editIDCardCheck,robot-2022-apply-saveCheckCode,robot-2022-apply-selectIsNetProp";    } else {        s = "robot-2022-qrcode_login,robot-2022-apply-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2022', 'yongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永诚-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2022-bj-qrcode_login,robot-2022-apply-bj-prepare,robot-2022-apply-select-jq,robot-2022-apply-select-sy,robot-2022-apply-bj-editIDCardCheck,robot-2022-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2022', 'yongcheng', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-永诚-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2022_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2022', 'yongcheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi永诚北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2022_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2022', 'yongcheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi永诚北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2022_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2022', 'yongcheng', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-永诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2022-login,robot-2022-prepareQueryCode,robot-2022-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2027', 'zhonghua', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中华-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-IdCarChekc" //申请验证码    else{        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2027', 'zhonghua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中华-报价', 'def getTemplateGroup(dataSource) {    return "edi-2027-queryCar,edi-2027-xbQuery,edi-2027-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2027', 'zhonghua', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中华-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2027-queryCar,edi-2027-xbQuery,edi-2027-askCharge,edi-2027-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2027', 'zhonghua', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中华-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2027-queryCar,edi-2027-xbQuery,edi-2027-askCharge,edi-2027-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2027', 'zhonghua', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-中华-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2027-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2027', 'zhonghua', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中华-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2027-login,robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2027-login,robot-2027-ObtainConfig,robot-2027-checkInsurePerson,robot-2027-changePerson,robot-2027-checkInsuredPerson,robot-2027-changePerson,robot-2027-prepareEdit," +                    "robot-2027-prepareQueryCode,robot-2027-selectProposalCar,robot-2027-browseProposalCar,robot-2027-browseProposalCarefc,robot-2027-selectRenewalPolicyNo"        }else{            s = "robot-2027-login,robot-2027-ObtainConfig,robot-2027-checkInsurePerson,robot-2027-changePerson,robot-2027-checkInsuredPerson,robot-2027-changePerson,robot-2027-prepareEdit," +                    "robot-2027-prepareQueryCode,robot-2027-browseProposalCar,robot-2027-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2027-VehicleModelList" //上海车型查询        }        s += ",robot-2027-queryPrepare,robot-2027-vehicleQuery,robot-2027-queryTaxAbateForPlat,robot-2027-calActualValue,robot-2027-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2027-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2027-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-nomotor-unitedSaleEdit,robot-2027-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2027-login,robot_2027_bj_initData,robot_2027_bj_queryModel,robot_2027_bj_getSaleTaxInfo,robot_2027_bj_getRealValue,robot_2027_bj_getPersonData,robot_2027_bj_addPersonData,robot_2027_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2027', 'zhonghua', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-中华-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2027-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2027-ObtainConfig,robot-2027-selectRenewal,robot-2027-editCengage,robot-2027-editCitemCar,robot-2027-editCinsured,robot-2027-renewalPolicy,robot-2027-renewalPolicyCI,robot-2027-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2027-VehicleModelList" //上海车型查询        }        s += ",robot-2027-vehicleQueryXB,robot-2027-queryTaxAbateForPlat,robot-2027-calActualValue,robot-2027-editCitemKind,robot-2027-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2027-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-refreshPlanByTimes,robot-2027-insert"            }else{                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2027-getMaxCsellFee,robot-2027-getPrpCseller,robot-2027-getPrpCsellerCI,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            s += ",robot-2027-getMaxCsellFee,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"        }    }else{        s +=",robot-2027-ObtainConfig,robot-2027-checkInsurePerson,robot-2027-changePerson,robot-2027-checkInsuredPerson,robot-2027-changePerson,robot-2027-prepareEdit,robot-2027-selectRenewalPolicyNo,robot-2027-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2027-VehicleModelList" //上海车型查询        }        s += ",robot-2027-queryPrepare,robot-2027-vehicleQuery,robot-2027-queryTaxAbateForPlat,robot-2027-calActualValue,robot-2027-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2027-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2027-calAnciInfo,robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            }else{                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-nomotor-unitedSaleEdit,robot-2027-nomotor-saveUnitedSale,robot-2027-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2027-getMaxCsellFee,robot-2027-getPrpCseller,robot-2027-getPrpCsellerCI,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            s += ",robot-2027-getMaxCsellFee,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-nomotor-unitedSaleEdit,robot-2027-nomotor-saveUnitedSale,robot-2027-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2027-login,robot_2027_bj_initData,robot_2027_bj_queryModel,robot_2027_bj_getSaleTaxInfo,robot_2027_bj_getRealValue,robot_2027_bj_getPersonData,robot_2027_bj_addPersonData,robot_2027_bj_askCharge,robot_2027_bj_queryPayForXSFY,robot_2027_bj_getCagentCI,robot_2027_bj_getCagent,robot_2027_bj_queryPayForXSFY_req,robot_2027_bj_queryIlogEngage,robot_2027_bj_insureRefrenshPlan,robot_2027_bj_insure4S,robot-2027-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2027', 'zhonghua', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-中华-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ" +            ",robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind,robot-2027-nomotor-query,robot-2027-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2027', 'zhonghua', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中华-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ" +            ",robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind,robot-2027-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2027', 'zhonghua', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中华-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ" +            ",robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind,robot-2027-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2027', 'zhonghua', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "中华财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-中华-电销', 'def getTemplateGroup(dataSource){    return "robot-2027-pureESale_Login,robot-2027-pureESale_Welcome,robot-2027-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2027', 'zhonghua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中华续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2027-login,robot-2027-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2027', 'zhonghua', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-中华-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2027-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2027-ObtainConfig,robot-2027-selectRenewal,robot-2027-editCengage,robot-2027-editCitemCar,robot-2027-editCinsured,robot-2027-renewalPolicy,robot-2027-renewalPolicyCI,robot-2027-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2027-VehicleModelList" //上海车型查询        }        s += ",robot-2027-vehicleQueryXB,robot-2027-queryTaxAbateForPlat,robot-2027-calActualValue,robot-2027-editCitemKind,robot-2027-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2027-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-refreshPlanByTimes,robot-2027-insert"            }else{                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2027-calAnciInfo,robot-2027-getMaxCsellFee,robot-2027-getPrpCseller,robot-2027-getPrpCsellerCI,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            s += ",robot-2027-getMaxCsellFee,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"        }    }else{        s += ",robot-2027-ObtainConfig,robot-2027-checkInsurePerson,robot-2027-changePerson,robot-2027-checkInsuredPerson,robot-2027-changePerson,robot-2027-prepareEdit,robot-2027-editCengage,robot-2027-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2027-queryVehiclePMCheck,robot-2027-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2027-VehicleModelList" //上海车型查询        }        s += ",robot-2027-queryPrepare,robot-2027-vehicleQuery,robot-2027-queryTaxAbateForPlat,robot-2027-calActualValue,robot-2027-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2027-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2027-calAnciInfo,robot-2027-queryPayFor,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            }else{                s += ",robot-2027-calAnciInfo,robot-2027-checkAgentType,robot-2027-queryPayForSCMS,robot-2027-getCagent,robot-2027-getCagentCI,robot-2027-refreshPlanByTimes,robot-2027-nomotor-unitedSaleEdit,robot-2027-nomotor-saveUnitedSale,robot-2027-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2027-calAnciInfo,robot-2027-getMaxCsellFee,robot-2027-getPrpCseller,robot-2027-getPrpCsellerCI,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-insert"            s += ",robot-2027-getMaxCsellFee,robot-2027-queryPayForSCMS,robot-2027-refreshPlanByTimes,robot-2027-nomotor-unitedSaleEdit,robot-2027-nomotor-saveUnitedSale,robot-2027-insert"        }    }    s += ",robot-2027-checkRiskCode,robot-2027-editMainUwtFlag,robot-2027-editSubmitUndwrt,robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-showUndwrtMsgQ,robot-2027-showUndwrtMsgS"+            ",robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ,robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind,robot-2027-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2027-login,robot_2027_bj_initData,robot_2027_bj_queryModel,robot_2027_bj_getSaleTaxInfo,robot_2027_bj_getRealValue,robot_2027_bj_getPersonData,robot_2027_bj_addPersonData,robot_2027_bj_askCharge,robot_2027_bj_queryPayForXSFY,robot_2027_bj_getCagentCI,robot_2027_bj_getCagent,robot_2027_bj_queryPayForXSFY_req,robot_2027_bj_queryIlogEngage,robot_2027_bj_insureRefrenshPlan,robot_2027_bj_insure4S,robot-2027-uploadImage,robot_2027_bj_autoInsure,robot_2027_bj_showUndwrtMsgQ,robot_2027_bj_showUndwrtMsgS";       s += ",robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ,robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +                ",robot-2027-showCinsuredS,robot-2027-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2027', 'zhonghua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中华-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2027-qrcode_login,robot-2027-qrcode_printTwoBarCodeServlet,robot-2027-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2027-qrcode_login,robot-2027-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2027-qrcode_login,robot-2027-qrcode_editCheckFlag,robot-2027-qrcode_gotoJfcd,robot-2027-qrcode_prepareEditByJF,robot-2027-qrcode_getBusinessIn" +                ",robot-2027-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2027-qrcode_login,robot-2027-qrcode_editCheckFlag,robot-2027-qrcode_gotoJfcd,robot-2027-qrcode_prepareEditByJF,robot-2027-qrcode_getBusinessIn" +                ",robot-2027-qrcode_checkBeforeCalculate,robot-2027-qrcode_saveByJF,robot-2027-qrcode_getBusinessIn_alipay,robot-2027-qrcode_editFeeInfor,robot-2027-qrcode_editPayFeeByWeChat,robot-2027-qrcode_saveByWeChat,robot-2027-qrcode_save";		} else {					return  "robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-bj-editIDCardCheck,robot-2027-apply-selectIsNetProp,robot-2027-apply-saveCheckCode,robot-2027-qrcode_editCheckFlag,robot-2027-qrcode_gotoJfcd,robot-2027-qrcode_prepareEditByJF,robot-2027-qrcode_getBusinessIn" +",robot-2027-qrcode_checkBeforeCalculate,robot-2027-qrcode_saveByJF,robot-2027-qrcode_getBusinessIn_alipay,robot-2027-qrcode_editFeeInfor,robot-2027-qrcode_editPayFeeByWeChat,robot-2027-qrcode_saveByWeChat,robot-2027-qrcode_save";		}}    else {              return "robot-2027-qrcode_login,robot-2027-qrcode_editCheckFlag,robot-2027-qrcode_gotoJfcd,robot-2027-qrcode_prepareEditByJF,robot-2027-qrcode_getBusinessIn" +                ",robot-2027-qrcode_checkBeforeCalculate,robot-2027-qrcode_saveByJF,robot-2027-qrcode_getBusinessIn_alipay,robot-2027-qrcode_editFeeInfor,robot-2027-qrcode_editPayFeeByWeChat,robot-2027-qrcode_saveByWeChat,robot-2027-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2027', 'zhonghua', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中华-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2027-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2027-qrcode_query_editCheckFlag,robot-2027-qrcode_query_gotoJfcd,robot-2027-qrcode_query_prepareEditByJF" +                ",robot-2027-qrcode_query_editMainInfor,robot-2027-qrcode_query_getBusinessIn,robot-2027-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ" +            ",robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind,robot-2027-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2027', 'zhonghua', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中华-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-IdCarChekc" //申请验证码    else{        return"robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-editIDCardCheck,robot-2027-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2027', 'zhonghua', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-中华-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectPolicyefc,robot-2027-selectPolicybiz,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ" +            ",robot-2027-showCitemCarQ,robot-2027-showCinsuredQ,robot-2027-showCitemKindCI,robot-2027-browseProposalS,robot-2027-showCitemCarS" +            ",robot-2027-showCinsuredS,robot-2027-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2027', 'zhonghua', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2027-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectProposalQ,robot-2027-selectProposalS,robot-2027-browseProposalQ,robot-2027-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2027', 'zhonghua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中华-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-bj-editIDCardCheck,robot-2027-apply-bj-IdCarChekc";    } else {        s = "robot-2027-qrcode_login,robot-2027-qrcode_editCheckFlag,robot-2027-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2027', 'zhonghua', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi中华报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2027_ask_charge,edi_2027_noMotor_quote"	} else {		return "edi_2027_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2027', 'zhonghua', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中华-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2027_ask_charge,edi_2027_noMotor_quote,edi_2027_askInsure,edi_2027_uploadImg,edi_2027_submitInsure,edi_2027_noMotor_submit" 	  	} else {		return "edi_2027_ask_chargeold,edi_2027_askInsure,edi_2027_uploadImg,edi_2027_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2027', 'zhonghua', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中华-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2027_ask_charge,edi_2027_noMotor_quote,edi_2027_askInsure,edi_2027_uploadImg" 	} else {		return "edi_2027_ask_chargeold,edi_2027_askInsure,edi_2027_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2027', 'zhonghua', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-中华-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2027_efc_policyinfo,edi_2027_biz_policyinfo,edi_2027_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2027', 'zhonghua', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-中华-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2027_efc_insurequery,edi_2027_biz_insurequery,edi_2027_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2027', 'zhonghua', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京中华短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-bj-editIDCardCheck,robot-2027-apply-saveCheckCode,robot-2027-apply-selectIsNetProp";    } else {        s = "robot-2027-qrcode_login,robot-2027-apply-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2027', 'zhonghua', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中华-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2027-bj-qrcode_login,robot-2027-apply-bj-prepare,robot-2027-apply-select-jq,robot-2027-apply-select-sy,robot-2027-apply-bj-editIDCardCheck,robot-2027-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2027', 'zhonghua', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-中华-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2027_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2027', 'zhonghua', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中华北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2027_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2027', 'zhonghua', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中华北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2027_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2027', 'zhonghua', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-中华-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2027-login,robot-2027-prepareQueryCode,robot-2027-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2095', 'zijin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-紫金-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-IdCarChekc" //申请验证码    else{        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2095', 'zijin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-紫金-报价', 'def getTemplateGroup(dataSource) {    return "edi-2095-queryCar,edi-2095-xbQuery,edi-2095-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2095', 'zijin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-紫金-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2095-queryCar,edi-2095-xbQuery,edi-2095-askCharge,edi-2095-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2095', 'zijin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-紫金-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2095-queryCar,edi-2095-xbQuery,edi-2095-askCharge,edi-2095-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2095', 'zijin', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-紫金-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2095-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2095', 'zijin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-紫金-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2095-login,robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2095-login,robot-2095-ObtainConfig,robot-2095-checkInsurePerson,robot-2095-changePerson,robot-2095-checkInsuredPerson,robot-2095-changePerson,robot-2095-prepareEdit," +                    "robot-2095-prepareQueryCode,robot-2095-selectProposalCar,robot-2095-browseProposalCar,robot-2095-browseProposalCarefc,robot-2095-selectRenewalPolicyNo"        }else{            s = "robot-2095-login,robot-2095-ObtainConfig,robot-2095-checkInsurePerson,robot-2095-changePerson,robot-2095-checkInsuredPerson,robot-2095-changePerson,robot-2095-prepareEdit," +                    "robot-2095-prepareQueryCode,robot-2095-browseProposalCar,robot-2095-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2095-VehicleModelList" //上海车型查询        }        s += ",robot-2095-queryPrepare,robot-2095-vehicleQuery,robot-2095-queryTaxAbateForPlat,robot-2095-calActualValue,robot-2095-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2095-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2095-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-nomotor-unitedSaleEdit,robot-2095-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2095-login,robot_2095_bj_initData,robot_2095_bj_queryModel,robot_2095_bj_getSaleTaxInfo,robot_2095_bj_getRealValue,robot_2095_bj_getPersonData,robot_2095_bj_addPersonData,robot_2095_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2095', 'zijin', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-紫金-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2095-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2095-ObtainConfig,robot-2095-selectRenewal,robot-2095-editCengage,robot-2095-editCitemCar,robot-2095-editCinsured,robot-2095-renewalPolicy,robot-2095-renewalPolicyCI,robot-2095-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2095-VehicleModelList" //上海车型查询        }        s += ",robot-2095-vehicleQueryXB,robot-2095-queryTaxAbateForPlat,robot-2095-calActualValue,robot-2095-editCitemKind,robot-2095-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2095-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-refreshPlanByTimes,robot-2095-insert"            }else{                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2095-getMaxCsellFee,robot-2095-getPrpCseller,robot-2095-getPrpCsellerCI,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            s += ",robot-2095-getMaxCsellFee,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"        }    }else{        s +=",robot-2095-ObtainConfig,robot-2095-checkInsurePerson,robot-2095-changePerson,robot-2095-checkInsuredPerson,robot-2095-changePerson,robot-2095-prepareEdit,robot-2095-selectRenewalPolicyNo,robot-2095-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2095-VehicleModelList" //上海车型查询        }        s += ",robot-2095-queryPrepare,robot-2095-vehicleQuery,robot-2095-queryTaxAbateForPlat,robot-2095-calActualValue,robot-2095-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2095-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2095-calAnciInfo,robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            }else{                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-nomotor-unitedSaleEdit,robot-2095-nomotor-saveUnitedSale,robot-2095-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2095-getMaxCsellFee,robot-2095-getPrpCseller,robot-2095-getPrpCsellerCI,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            s += ",robot-2095-getMaxCsellFee,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-nomotor-unitedSaleEdit,robot-2095-nomotor-saveUnitedSale,robot-2095-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2095-login,robot_2095_bj_initData,robot_2095_bj_queryModel,robot_2095_bj_getSaleTaxInfo,robot_2095_bj_getRealValue,robot_2095_bj_getPersonData,robot_2095_bj_addPersonData,robot_2095_bj_askCharge,robot_2095_bj_queryPayForXSFY,robot_2095_bj_getCagentCI,robot_2095_bj_getCagent,robot_2095_bj_queryPayForXSFY_req,robot_2095_bj_queryIlogEngage,robot_2095_bj_insureRefrenshPlan,robot_2095_bj_insure4S,robot-2095-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2095', 'zijin', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-紫金-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ" +            ",robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind,robot-2095-nomotor-query,robot-2095-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2095', 'zijin', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-紫金-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ" +            ",robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind,robot-2095-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2095', 'zijin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-紫金-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ" +            ",robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind,robot-2095-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2095', 'zijin', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "紫金财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-紫金-电销', 'def getTemplateGroup(dataSource){    return "robot-2095-pureESale_Login,robot-2095-pureESale_Welcome,robot-2095-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2095', 'zijin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-紫金续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2095-login,robot-2095-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2095', 'zijin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-紫金-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2095-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2095-ObtainConfig,robot-2095-selectRenewal,robot-2095-editCengage,robot-2095-editCitemCar,robot-2095-editCinsured,robot-2095-renewalPolicy,robot-2095-renewalPolicyCI,robot-2095-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2095-VehicleModelList" //上海车型查询        }        s += ",robot-2095-vehicleQueryXB,robot-2095-queryTaxAbateForPlat,robot-2095-calActualValue,robot-2095-editCitemKind,robot-2095-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2095-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-refreshPlanByTimes,robot-2095-insert"            }else{                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2095-calAnciInfo,robot-2095-getMaxCsellFee,robot-2095-getPrpCseller,robot-2095-getPrpCsellerCI,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            s += ",robot-2095-getMaxCsellFee,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"        }    }else{        s += ",robot-2095-ObtainConfig,robot-2095-checkInsurePerson,robot-2095-changePerson,robot-2095-checkInsuredPerson,robot-2095-changePerson,robot-2095-prepareEdit,robot-2095-editCengage,robot-2095-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2095-queryVehiclePMCheck,robot-2095-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2095-VehicleModelList" //上海车型查询        }        s += ",robot-2095-queryPrepare,robot-2095-vehicleQuery,robot-2095-queryTaxAbateForPlat,robot-2095-calActualValue,robot-2095-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2095-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2095-calAnciInfo,robot-2095-queryPayFor,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            }else{                s += ",robot-2095-calAnciInfo,robot-2095-checkAgentType,robot-2095-queryPayForSCMS,robot-2095-getCagent,robot-2095-getCagentCI,robot-2095-refreshPlanByTimes,robot-2095-nomotor-unitedSaleEdit,robot-2095-nomotor-saveUnitedSale,robot-2095-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2095-calAnciInfo,robot-2095-getMaxCsellFee,robot-2095-getPrpCseller,robot-2095-getPrpCsellerCI,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-insert"            s += ",robot-2095-getMaxCsellFee,robot-2095-queryPayForSCMS,robot-2095-refreshPlanByTimes,robot-2095-nomotor-unitedSaleEdit,robot-2095-nomotor-saveUnitedSale,robot-2095-insert"        }    }    s += ",robot-2095-checkRiskCode,robot-2095-editMainUwtFlag,robot-2095-editSubmitUndwrt,robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-showUndwrtMsgQ,robot-2095-showUndwrtMsgS"+            ",robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ,robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind,robot-2095-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2095-login,robot_2095_bj_initData,robot_2095_bj_queryModel,robot_2095_bj_getSaleTaxInfo,robot_2095_bj_getRealValue,robot_2095_bj_getPersonData,robot_2095_bj_addPersonData,robot_2095_bj_askCharge,robot_2095_bj_queryPayForXSFY,robot_2095_bj_getCagentCI,robot_2095_bj_getCagent,robot_2095_bj_queryPayForXSFY_req,robot_2095_bj_queryIlogEngage,robot_2095_bj_insureRefrenshPlan,robot_2095_bj_insure4S,robot-2095-uploadImage,robot_2095_bj_autoInsure,robot_2095_bj_showUndwrtMsgQ,robot_2095_bj_showUndwrtMsgS";       s += ",robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ,robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +                ",robot-2095-showCinsuredS,robot-2095-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2095', 'zijin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-紫金-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2095-qrcode_login,robot-2095-qrcode_printTwoBarCodeServlet,robot-2095-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2095-qrcode_login,robot-2095-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2095-qrcode_login,robot-2095-qrcode_editCheckFlag,robot-2095-qrcode_gotoJfcd,robot-2095-qrcode_prepareEditByJF,robot-2095-qrcode_getBusinessIn" +                ",robot-2095-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2095-qrcode_login,robot-2095-qrcode_editCheckFlag,robot-2095-qrcode_gotoJfcd,robot-2095-qrcode_prepareEditByJF,robot-2095-qrcode_getBusinessIn" +                ",robot-2095-qrcode_checkBeforeCalculate,robot-2095-qrcode_saveByJF,robot-2095-qrcode_getBusinessIn_alipay,robot-2095-qrcode_editFeeInfor,robot-2095-qrcode_editPayFeeByWeChat,robot-2095-qrcode_saveByWeChat,robot-2095-qrcode_save";		} else {					return  "robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-bj-editIDCardCheck,robot-2095-apply-selectIsNetProp,robot-2095-apply-saveCheckCode,robot-2095-qrcode_editCheckFlag,robot-2095-qrcode_gotoJfcd,robot-2095-qrcode_prepareEditByJF,robot-2095-qrcode_getBusinessIn" +",robot-2095-qrcode_checkBeforeCalculate,robot-2095-qrcode_saveByJF,robot-2095-qrcode_getBusinessIn_alipay,robot-2095-qrcode_editFeeInfor,robot-2095-qrcode_editPayFeeByWeChat,robot-2095-qrcode_saveByWeChat,robot-2095-qrcode_save";		}}    else {              return "robot-2095-qrcode_login,robot-2095-qrcode_editCheckFlag,robot-2095-qrcode_gotoJfcd,robot-2095-qrcode_prepareEditByJF,robot-2095-qrcode_getBusinessIn" +                ",robot-2095-qrcode_checkBeforeCalculate,robot-2095-qrcode_saveByJF,robot-2095-qrcode_getBusinessIn_alipay,robot-2095-qrcode_editFeeInfor,robot-2095-qrcode_editPayFeeByWeChat,robot-2095-qrcode_saveByWeChat,robot-2095-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2095', 'zijin', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-紫金-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2095-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2095-qrcode_query_editCheckFlag,robot-2095-qrcode_query_gotoJfcd,robot-2095-qrcode_query_prepareEditByJF" +                ",robot-2095-qrcode_query_editMainInfor,robot-2095-qrcode_query_getBusinessIn,robot-2095-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ" +            ",robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind,robot-2095-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2095', 'zijin', '15', '6', 'pro', 'other', b'1', '{}', '精灵-紫金-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-IdCarChekc" //申请验证码    else{        return"robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-editIDCardCheck,robot-2095-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2095', 'zijin', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-紫金-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectPolicyefc,robot-2095-selectPolicybiz,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ" +            ",robot-2095-showCitemCarQ,robot-2095-showCinsuredQ,robot-2095-showCitemKindCI,robot-2095-browseProposalS,robot-2095-showCitemCarS" +            ",robot-2095-showCinsuredS,robot-2095-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2095', 'zijin', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2095-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectProposalQ,robot-2095-selectProposalS,robot-2095-browseProposalQ,robot-2095-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2095', 'zijin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-紫金-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-bj-editIDCardCheck,robot-2095-apply-bj-IdCarChekc";    } else {        s = "robot-2095-qrcode_login,robot-2095-qrcode_editCheckFlag,robot-2095-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2095', 'zijin', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi紫金报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2095_ask_charge,edi_2095_noMotor_quote"	} else {		return "edi_2095_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2095', 'zijin', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-紫金-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2095_ask_charge,edi_2095_noMotor_quote,edi_2095_askInsure,edi_2095_uploadImg,edi_2095_submitInsure,edi_2095_noMotor_submit" 	  	} else {		return "edi_2095_ask_chargeold,edi_2095_askInsure,edi_2095_uploadImg,edi_2095_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2095', 'zijin', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-紫金-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2095_ask_charge,edi_2095_noMotor_quote,edi_2095_askInsure,edi_2095_uploadImg" 	} else {		return "edi_2095_ask_chargeold,edi_2095_askInsure,edi_2095_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2095', 'zijin', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-紫金-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2095_efc_policyinfo,edi_2095_biz_policyinfo,edi_2095_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2095', 'zijin', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-紫金-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2095_efc_insurequery,edi_2095_biz_insurequery,edi_2095_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2095', 'zijin', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京紫金短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-bj-editIDCardCheck,robot-2095-apply-saveCheckCode,robot-2095-apply-selectIsNetProp";    } else {        s = "robot-2095-qrcode_login,robot-2095-apply-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2095', 'zijin', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-紫金-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2095-bj-qrcode_login,robot-2095-apply-bj-prepare,robot-2095-apply-select-jq,robot-2095-apply-select-sy,robot-2095-apply-bj-editIDCardCheck,robot-2095-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2095', 'zijin', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-紫金-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2095_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2095', 'zijin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi紫金北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2095_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2095', 'zijin', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi紫金北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2095_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2095', 'zijin', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-紫金-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2095-login,robot-2095-prepareQueryCode,robot-2095-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2096', 'guoren', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国任-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-IdCarChekc" //申请验证码    else{        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2096', 'guoren', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国任-报价', 'def getTemplateGroup(dataSource) {    return "edi-2096-queryCar,edi-2096-xbQuery,edi-2096-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2096', 'guoren', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国任-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2096-queryCar,edi-2096-xbQuery,edi-2096-askCharge,edi-2096-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2096', 'guoren', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国任-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2096-queryCar,edi-2096-xbQuery,edi-2096-askCharge,edi-2096-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2096', 'guoren', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-国任-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2096-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2096', 'guoren', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国任-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2096-login,robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2096-login,robot-2096-ObtainConfig,robot-2096-checkInsurePerson,robot-2096-changePerson,robot-2096-checkInsuredPerson,robot-2096-changePerson,robot-2096-prepareEdit," +                    "robot-2096-prepareQueryCode,robot-2096-selectProposalCar,robot-2096-browseProposalCar,robot-2096-browseProposalCarefc,robot-2096-selectRenewalPolicyNo"        }else{            s = "robot-2096-login,robot-2096-ObtainConfig,robot-2096-checkInsurePerson,robot-2096-changePerson,robot-2096-checkInsuredPerson,robot-2096-changePerson,robot-2096-prepareEdit," +                    "robot-2096-prepareQueryCode,robot-2096-browseProposalCar,robot-2096-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2096-VehicleModelList" //上海车型查询        }        s += ",robot-2096-queryPrepare,robot-2096-vehicleQuery,robot-2096-queryTaxAbateForPlat,robot-2096-calActualValue,robot-2096-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2096-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2096-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-nomotor-unitedSaleEdit,robot-2096-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2096-login,robot_2096_bj_initData,robot_2096_bj_queryModel,robot_2096_bj_getSaleTaxInfo,robot_2096_bj_getRealValue,robot_2096_bj_getPersonData,robot_2096_bj_addPersonData,robot_2096_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2096', 'guoren', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-国任-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2096-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2096-ObtainConfig,robot-2096-selectRenewal,robot-2096-editCengage,robot-2096-editCitemCar,robot-2096-editCinsured,robot-2096-renewalPolicy,robot-2096-renewalPolicyCI,robot-2096-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2096-VehicleModelList" //上海车型查询        }        s += ",robot-2096-vehicleQueryXB,robot-2096-queryTaxAbateForPlat,robot-2096-calActualValue,robot-2096-editCitemKind,robot-2096-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2096-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-refreshPlanByTimes,robot-2096-insert"            }else{                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2096-getMaxCsellFee,robot-2096-getPrpCseller,robot-2096-getPrpCsellerCI,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            s += ",robot-2096-getMaxCsellFee,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"        }    }else{        s +=",robot-2096-ObtainConfig,robot-2096-checkInsurePerson,robot-2096-changePerson,robot-2096-checkInsuredPerson,robot-2096-changePerson,robot-2096-prepareEdit,robot-2096-selectRenewalPolicyNo,robot-2096-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2096-VehicleModelList" //上海车型查询        }        s += ",robot-2096-queryPrepare,robot-2096-vehicleQuery,robot-2096-queryTaxAbateForPlat,robot-2096-calActualValue,robot-2096-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2096-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2096-calAnciInfo,robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            }else{                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-nomotor-unitedSaleEdit,robot-2096-nomotor-saveUnitedSale,robot-2096-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2096-getMaxCsellFee,robot-2096-getPrpCseller,robot-2096-getPrpCsellerCI,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            s += ",robot-2096-getMaxCsellFee,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-nomotor-unitedSaleEdit,robot-2096-nomotor-saveUnitedSale,robot-2096-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2096-login,robot_2096_bj_initData,robot_2096_bj_queryModel,robot_2096_bj_getSaleTaxInfo,robot_2096_bj_getRealValue,robot_2096_bj_getPersonData,robot_2096_bj_addPersonData,robot_2096_bj_askCharge,robot_2096_bj_queryPayForXSFY,robot_2096_bj_getCagentCI,robot_2096_bj_getCagent,robot_2096_bj_queryPayForXSFY_req,robot_2096_bj_queryIlogEngage,robot_2096_bj_insureRefrenshPlan,robot_2096_bj_insure4S,robot-2096-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2096', 'guoren', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-国任-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ" +            ",robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind,robot-2096-nomotor-query,robot-2096-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2096', 'guoren', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国任-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ" +            ",robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind,robot-2096-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2096', 'guoren', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国任-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ" +            ",robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind,robot-2096-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2096', 'guoren', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "国任财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-国任-电销', 'def getTemplateGroup(dataSource){    return "robot-2096-pureESale_Login,robot-2096-pureESale_Welcome,robot-2096-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2096', 'guoren', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国任续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2096-login,robot-2096-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2096', 'guoren', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-国任-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2096-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2096-ObtainConfig,robot-2096-selectRenewal,robot-2096-editCengage,robot-2096-editCitemCar,robot-2096-editCinsured,robot-2096-renewalPolicy,robot-2096-renewalPolicyCI,robot-2096-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2096-VehicleModelList" //上海车型查询        }        s += ",robot-2096-vehicleQueryXB,robot-2096-queryTaxAbateForPlat,robot-2096-calActualValue,robot-2096-editCitemKind,robot-2096-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2096-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-refreshPlanByTimes,robot-2096-insert"            }else{                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2096-calAnciInfo,robot-2096-getMaxCsellFee,robot-2096-getPrpCseller,robot-2096-getPrpCsellerCI,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            s += ",robot-2096-getMaxCsellFee,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"        }    }else{        s += ",robot-2096-ObtainConfig,robot-2096-checkInsurePerson,robot-2096-changePerson,robot-2096-checkInsuredPerson,robot-2096-changePerson,robot-2096-prepareEdit,robot-2096-editCengage,robot-2096-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2096-queryVehiclePMCheck,robot-2096-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2096-VehicleModelList" //上海车型查询        }        s += ",robot-2096-queryPrepare,robot-2096-vehicleQuery,robot-2096-queryTaxAbateForPlat,robot-2096-calActualValue,robot-2096-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2096-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2096-calAnciInfo,robot-2096-queryPayFor,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            }else{                s += ",robot-2096-calAnciInfo,robot-2096-checkAgentType,robot-2096-queryPayForSCMS,robot-2096-getCagent,robot-2096-getCagentCI,robot-2096-refreshPlanByTimes,robot-2096-nomotor-unitedSaleEdit,robot-2096-nomotor-saveUnitedSale,robot-2096-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2096-calAnciInfo,robot-2096-getMaxCsellFee,robot-2096-getPrpCseller,robot-2096-getPrpCsellerCI,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-insert"            s += ",robot-2096-getMaxCsellFee,robot-2096-queryPayForSCMS,robot-2096-refreshPlanByTimes,robot-2096-nomotor-unitedSaleEdit,robot-2096-nomotor-saveUnitedSale,robot-2096-insert"        }    }    s += ",robot-2096-checkRiskCode,robot-2096-editMainUwtFlag,robot-2096-editSubmitUndwrt,robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-showUndwrtMsgQ,robot-2096-showUndwrtMsgS"+            ",robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ,robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind,robot-2096-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2096-login,robot_2096_bj_initData,robot_2096_bj_queryModel,robot_2096_bj_getSaleTaxInfo,robot_2096_bj_getRealValue,robot_2096_bj_getPersonData,robot_2096_bj_addPersonData,robot_2096_bj_askCharge,robot_2096_bj_queryPayForXSFY,robot_2096_bj_getCagentCI,robot_2096_bj_getCagent,robot_2096_bj_queryPayForXSFY_req,robot_2096_bj_queryIlogEngage,robot_2096_bj_insureRefrenshPlan,robot_2096_bj_insure4S,robot-2096-uploadImage,robot_2096_bj_autoInsure,robot_2096_bj_showUndwrtMsgQ,robot_2096_bj_showUndwrtMsgS";       s += ",robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ,robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +                ",robot-2096-showCinsuredS,robot-2096-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2096', 'guoren', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国任-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2096-qrcode_login,robot-2096-qrcode_printTwoBarCodeServlet,robot-2096-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2096-qrcode_login,robot-2096-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2096-qrcode_login,robot-2096-qrcode_editCheckFlag,robot-2096-qrcode_gotoJfcd,robot-2096-qrcode_prepareEditByJF,robot-2096-qrcode_getBusinessIn" +                ",robot-2096-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2096-qrcode_login,robot-2096-qrcode_editCheckFlag,robot-2096-qrcode_gotoJfcd,robot-2096-qrcode_prepareEditByJF,robot-2096-qrcode_getBusinessIn" +                ",robot-2096-qrcode_checkBeforeCalculate,robot-2096-qrcode_saveByJF,robot-2096-qrcode_getBusinessIn_alipay,robot-2096-qrcode_editFeeInfor,robot-2096-qrcode_editPayFeeByWeChat,robot-2096-qrcode_saveByWeChat,robot-2096-qrcode_save";		} else {					return  "robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-bj-editIDCardCheck,robot-2096-apply-selectIsNetProp,robot-2096-apply-saveCheckCode,robot-2096-qrcode_editCheckFlag,robot-2096-qrcode_gotoJfcd,robot-2096-qrcode_prepareEditByJF,robot-2096-qrcode_getBusinessIn" +",robot-2096-qrcode_checkBeforeCalculate,robot-2096-qrcode_saveByJF,robot-2096-qrcode_getBusinessIn_alipay,robot-2096-qrcode_editFeeInfor,robot-2096-qrcode_editPayFeeByWeChat,robot-2096-qrcode_saveByWeChat,robot-2096-qrcode_save";		}}    else {              return "robot-2096-qrcode_login,robot-2096-qrcode_editCheckFlag,robot-2096-qrcode_gotoJfcd,robot-2096-qrcode_prepareEditByJF,robot-2096-qrcode_getBusinessIn" +                ",robot-2096-qrcode_checkBeforeCalculate,robot-2096-qrcode_saveByJF,robot-2096-qrcode_getBusinessIn_alipay,robot-2096-qrcode_editFeeInfor,robot-2096-qrcode_editPayFeeByWeChat,robot-2096-qrcode_saveByWeChat,robot-2096-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2096', 'guoren', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国任-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2096-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2096-qrcode_query_editCheckFlag,robot-2096-qrcode_query_gotoJfcd,robot-2096-qrcode_query_prepareEditByJF" +                ",robot-2096-qrcode_query_editMainInfor,robot-2096-qrcode_query_getBusinessIn,robot-2096-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ" +            ",robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind,robot-2096-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2096', 'guoren', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国任-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-IdCarChekc" //申请验证码    else{        return"robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-editIDCardCheck,robot-2096-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2096', 'guoren', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-国任-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectPolicyefc,robot-2096-selectPolicybiz,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ" +            ",robot-2096-showCitemCarQ,robot-2096-showCinsuredQ,robot-2096-showCitemKindCI,robot-2096-browseProposalS,robot-2096-showCitemCarS" +            ",robot-2096-showCinsuredS,robot-2096-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2096', 'guoren', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2096-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectProposalQ,robot-2096-selectProposalS,robot-2096-browseProposalQ,robot-2096-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2096', 'guoren', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国任-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-bj-editIDCardCheck,robot-2096-apply-bj-IdCarChekc";    } else {        s = "robot-2096-qrcode_login,robot-2096-qrcode_editCheckFlag,robot-2096-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2096', 'guoren', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi国任报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2096_ask_charge,edi_2096_noMotor_quote"	} else {		return "edi_2096_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2096', 'guoren', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国任-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2096_ask_charge,edi_2096_noMotor_quote,edi_2096_askInsure,edi_2096_uploadImg,edi_2096_submitInsure,edi_2096_noMotor_submit" 	  	} else {		return "edi_2096_ask_chargeold,edi_2096_askInsure,edi_2096_uploadImg,edi_2096_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2096', 'guoren', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国任-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2096_ask_charge,edi_2096_noMotor_quote,edi_2096_askInsure,edi_2096_uploadImg" 	} else {		return "edi_2096_ask_chargeold,edi_2096_askInsure,edi_2096_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2096', 'guoren', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-国任-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2096_efc_policyinfo,edi_2096_biz_policyinfo,edi_2096_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2096', 'guoren', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-国任-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2096_efc_insurequery,edi_2096_biz_insurequery,edi_2096_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2096', 'guoren', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京国任短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-bj-editIDCardCheck,robot-2096-apply-saveCheckCode,robot-2096-apply-selectIsNetProp";    } else {        s = "robot-2096-qrcode_login,robot-2096-apply-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2096', 'guoren', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国任-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2096-bj-qrcode_login,robot-2096-apply-bj-prepare,robot-2096-apply-select-jq,robot-2096-apply-select-sy,robot-2096-apply-bj-editIDCardCheck,robot-2096-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2096', 'guoren', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-国任-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2096_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2096', 'guoren', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国任北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2096_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2096', 'guoren', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国任北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2096_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2096', 'guoren', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-国任-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2096-login,robot-2096-prepareQueryCode,robot-2096-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2002', 'guoshou', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国寿-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-IdCarChekc" //申请验证码    else{        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2002', 'guoshou', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国寿-报价', 'def getTemplateGroup(dataSource) {    return "edi-2002-queryCar,edi-2002-xbQuery,edi-2002-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2002', 'guoshou', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国寿-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2002-queryCar,edi-2002-xbQuery,edi-2002-askCharge,edi-2002-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2002', 'guoshou', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国寿-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2002-queryCar,edi-2002-xbQuery,edi-2002-askCharge,edi-2002-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2002', 'guoshou', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-国寿-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2002-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2002', 'guoshou', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国寿-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2002-login,robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2002-login,robot-2002-ObtainConfig,robot-2002-checkInsurePerson,robot-2002-changePerson,robot-2002-checkInsuredPerson,robot-2002-changePerson,robot-2002-prepareEdit," +                    "robot-2002-prepareQueryCode,robot-2002-selectProposalCar,robot-2002-browseProposalCar,robot-2002-browseProposalCarefc,robot-2002-selectRenewalPolicyNo"        }else{            s = "robot-2002-login,robot-2002-ObtainConfig,robot-2002-checkInsurePerson,robot-2002-changePerson,robot-2002-checkInsuredPerson,robot-2002-changePerson,robot-2002-prepareEdit," +                    "robot-2002-prepareQueryCode,robot-2002-browseProposalCar,robot-2002-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2002-VehicleModelList" //上海车型查询        }        s += ",robot-2002-queryPrepare,robot-2002-vehicleQuery,robot-2002-queryTaxAbateForPlat,robot-2002-calActualValue,robot-2002-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2002-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2002-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-nomotor-unitedSaleEdit,robot-2002-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2002-login,robot_2002_bj_initData,robot_2002_bj_queryModel,robot_2002_bj_getSaleTaxInfo,robot_2002_bj_getRealValue,robot_2002_bj_getPersonData,robot_2002_bj_addPersonData,robot_2002_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2002', 'guoshou', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-国寿-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2002-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2002-ObtainConfig,robot-2002-selectRenewal,robot-2002-editCengage,robot-2002-editCitemCar,robot-2002-editCinsured,robot-2002-renewalPolicy,robot-2002-renewalPolicyCI,robot-2002-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2002-VehicleModelList" //上海车型查询        }        s += ",robot-2002-vehicleQueryXB,robot-2002-queryTaxAbateForPlat,robot-2002-calActualValue,robot-2002-editCitemKind,robot-2002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2002-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-refreshPlanByTimes,robot-2002-insert"            }else{                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2002-getMaxCsellFee,robot-2002-getPrpCseller,robot-2002-getPrpCsellerCI,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            s += ",robot-2002-getMaxCsellFee,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"        }    }else{        s +=",robot-2002-ObtainConfig,robot-2002-checkInsurePerson,robot-2002-changePerson,robot-2002-checkInsuredPerson,robot-2002-changePerson,robot-2002-prepareEdit,robot-2002-selectRenewalPolicyNo,robot-2002-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2002-VehicleModelList" //上海车型查询        }        s += ",robot-2002-queryPrepare,robot-2002-vehicleQuery,robot-2002-queryTaxAbateForPlat,robot-2002-calActualValue,robot-2002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2002-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2002-calAnciInfo,robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            }else{                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-nomotor-unitedSaleEdit,robot-2002-nomotor-saveUnitedSale,robot-2002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2002-getMaxCsellFee,robot-2002-getPrpCseller,robot-2002-getPrpCsellerCI,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            s += ",robot-2002-getMaxCsellFee,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-nomotor-unitedSaleEdit,robot-2002-nomotor-saveUnitedSale,robot-2002-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2002-login,robot_2002_bj_initData,robot_2002_bj_queryModel,robot_2002_bj_getSaleTaxInfo,robot_2002_bj_getRealValue,robot_2002_bj_getPersonData,robot_2002_bj_addPersonData,robot_2002_bj_askCharge,robot_2002_bj_queryPayForXSFY,robot_2002_bj_getCagentCI,robot_2002_bj_getCagent,robot_2002_bj_queryPayForXSFY_req,robot_2002_bj_queryIlogEngage,robot_2002_bj_insureRefrenshPlan,robot_2002_bj_insure4S,robot-2002-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2002', 'guoshou', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-国寿-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ" +            ",robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind,robot-2002-nomotor-query,robot-2002-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2002', 'guoshou', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国寿-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ" +            ",robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind,robot-2002-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2002', 'guoshou', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国寿-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ" +            ",robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind,robot-2002-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2002', 'guoshou', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "国寿财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-国寿-电销', 'def getTemplateGroup(dataSource){    return "robot-2002-pureESale_Login,robot-2002-pureESale_Welcome,robot-2002-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2002', 'guoshou', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国寿续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2002-login,robot-2002-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2002', 'guoshou', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-国寿-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2002-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2002-ObtainConfig,robot-2002-selectRenewal,robot-2002-editCengage,robot-2002-editCitemCar,robot-2002-editCinsured,robot-2002-renewalPolicy,robot-2002-renewalPolicyCI,robot-2002-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2002-VehicleModelList" //上海车型查询        }        s += ",robot-2002-vehicleQueryXB,robot-2002-queryTaxAbateForPlat,robot-2002-calActualValue,robot-2002-editCitemKind,robot-2002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2002-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-refreshPlanByTimes,robot-2002-insert"            }else{                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2002-calAnciInfo,robot-2002-getMaxCsellFee,robot-2002-getPrpCseller,robot-2002-getPrpCsellerCI,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            s += ",robot-2002-getMaxCsellFee,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"        }    }else{        s += ",robot-2002-ObtainConfig,robot-2002-checkInsurePerson,robot-2002-changePerson,robot-2002-checkInsuredPerson,robot-2002-changePerson,robot-2002-prepareEdit,robot-2002-editCengage,robot-2002-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2002-queryVehiclePMCheck,robot-2002-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2002-VehicleModelList" //上海车型查询        }        s += ",robot-2002-queryPrepare,robot-2002-vehicleQuery,robot-2002-queryTaxAbateForPlat,robot-2002-calActualValue,robot-2002-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2002-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2002-calAnciInfo,robot-2002-queryPayFor,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            }else{                s += ",robot-2002-calAnciInfo,robot-2002-checkAgentType,robot-2002-queryPayForSCMS,robot-2002-getCagent,robot-2002-getCagentCI,robot-2002-refreshPlanByTimes,robot-2002-nomotor-unitedSaleEdit,robot-2002-nomotor-saveUnitedSale,robot-2002-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2002-calAnciInfo,robot-2002-getMaxCsellFee,robot-2002-getPrpCseller,robot-2002-getPrpCsellerCI,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-insert"            s += ",robot-2002-getMaxCsellFee,robot-2002-queryPayForSCMS,robot-2002-refreshPlanByTimes,robot-2002-nomotor-unitedSaleEdit,robot-2002-nomotor-saveUnitedSale,robot-2002-insert"        }    }    s += ",robot-2002-checkRiskCode,robot-2002-editMainUwtFlag,robot-2002-editSubmitUndwrt,robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-showUndwrtMsgQ,robot-2002-showUndwrtMsgS"+            ",robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ,robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind,robot-2002-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2002-login,robot_2002_bj_initData,robot_2002_bj_queryModel,robot_2002_bj_getSaleTaxInfo,robot_2002_bj_getRealValue,robot_2002_bj_getPersonData,robot_2002_bj_addPersonData,robot_2002_bj_askCharge,robot_2002_bj_queryPayForXSFY,robot_2002_bj_getCagentCI,robot_2002_bj_getCagent,robot_2002_bj_queryPayForXSFY_req,robot_2002_bj_queryIlogEngage,robot_2002_bj_insureRefrenshPlan,robot_2002_bj_insure4S,robot-2002-uploadImage,robot_2002_bj_autoInsure,robot_2002_bj_showUndwrtMsgQ,robot_2002_bj_showUndwrtMsgS";       s += ",robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ,robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +                ",robot-2002-showCinsuredS,robot-2002-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2002', 'guoshou', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国寿-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2002-qrcode_login,robot-2002-qrcode_printTwoBarCodeServlet,robot-2002-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2002-qrcode_login,robot-2002-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2002-qrcode_login,robot-2002-qrcode_editCheckFlag,robot-2002-qrcode_gotoJfcd,robot-2002-qrcode_prepareEditByJF,robot-2002-qrcode_getBusinessIn" +                ",robot-2002-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2002-qrcode_login,robot-2002-qrcode_editCheckFlag,robot-2002-qrcode_gotoJfcd,robot-2002-qrcode_prepareEditByJF,robot-2002-qrcode_getBusinessIn" +                ",robot-2002-qrcode_checkBeforeCalculate,robot-2002-qrcode_saveByJF,robot-2002-qrcode_getBusinessIn_alipay,robot-2002-qrcode_editFeeInfor,robot-2002-qrcode_editPayFeeByWeChat,robot-2002-qrcode_saveByWeChat,robot-2002-qrcode_save";		} else {					return  "robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-bj-editIDCardCheck,robot-2002-apply-selectIsNetProp,robot-2002-apply-saveCheckCode,robot-2002-qrcode_editCheckFlag,robot-2002-qrcode_gotoJfcd,robot-2002-qrcode_prepareEditByJF,robot-2002-qrcode_getBusinessIn" +",robot-2002-qrcode_checkBeforeCalculate,robot-2002-qrcode_saveByJF,robot-2002-qrcode_getBusinessIn_alipay,robot-2002-qrcode_editFeeInfor,robot-2002-qrcode_editPayFeeByWeChat,robot-2002-qrcode_saveByWeChat,robot-2002-qrcode_save";		}}    else {              return "robot-2002-qrcode_login,robot-2002-qrcode_editCheckFlag,robot-2002-qrcode_gotoJfcd,robot-2002-qrcode_prepareEditByJF,robot-2002-qrcode_getBusinessIn" +                ",robot-2002-qrcode_checkBeforeCalculate,robot-2002-qrcode_saveByJF,robot-2002-qrcode_getBusinessIn_alipay,robot-2002-qrcode_editFeeInfor,robot-2002-qrcode_editPayFeeByWeChat,robot-2002-qrcode_saveByWeChat,robot-2002-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2002', 'guoshou', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国寿-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2002-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2002-qrcode_query_editCheckFlag,robot-2002-qrcode_query_gotoJfcd,robot-2002-qrcode_query_prepareEditByJF" +                ",robot-2002-qrcode_query_editMainInfor,robot-2002-qrcode_query_getBusinessIn,robot-2002-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ" +            ",robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind,robot-2002-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2002', 'guoshou', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国寿-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-IdCarChekc" //申请验证码    else{        return"robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-editIDCardCheck,robot-2002-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2002', 'guoshou', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-国寿-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectPolicyefc,robot-2002-selectPolicybiz,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ" +            ",robot-2002-showCitemCarQ,robot-2002-showCinsuredQ,robot-2002-showCitemKindCI,robot-2002-browseProposalS,robot-2002-showCitemCarS" +            ",robot-2002-showCinsuredS,robot-2002-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2002', 'guoshou', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2002-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectProposalQ,robot-2002-selectProposalS,robot-2002-browseProposalQ,robot-2002-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2002', 'guoshou', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国寿-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-bj-editIDCardCheck,robot-2002-apply-bj-IdCarChekc";    } else {        s = "robot-2002-qrcode_login,robot-2002-qrcode_editCheckFlag,robot-2002-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2002', 'guoshou', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi国寿报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2002_ask_charge,edi_2002_noMotor_quote"	} else {		return "edi_2002_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2002', 'guoshou', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国寿-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2002_ask_charge,edi_2002_noMotor_quote,edi_2002_askInsure,edi_2002_uploadImg,edi_2002_submitInsure,edi_2002_noMotor_submit" 	  	} else {		return "edi_2002_ask_chargeold,edi_2002_askInsure,edi_2002_uploadImg,edi_2002_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2002', 'guoshou', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国寿-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2002_ask_charge,edi_2002_noMotor_quote,edi_2002_askInsure,edi_2002_uploadImg" 	} else {		return "edi_2002_ask_chargeold,edi_2002_askInsure,edi_2002_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2002', 'guoshou', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-国寿-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2002_efc_policyinfo,edi_2002_biz_policyinfo,edi_2002_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2002', 'guoshou', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-国寿-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2002_efc_insurequery,edi_2002_biz_insurequery,edi_2002_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2002', 'guoshou', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京国寿短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-bj-editIDCardCheck,robot-2002-apply-saveCheckCode,robot-2002-apply-selectIsNetProp";    } else {        s = "robot-2002-qrcode_login,robot-2002-apply-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2002', 'guoshou', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国寿-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2002-bj-qrcode_login,robot-2002-apply-bj-prepare,robot-2002-apply-select-jq,robot-2002-apply-select-sy,robot-2002-apply-bj-editIDCardCheck,robot-2002-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2002', 'guoshou', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-国寿-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2002_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2002', 'guoshou', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国寿北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2002_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2002', 'guoshou', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国寿北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2002_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2002', 'guoshou', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-国寿-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2002-login,robot-2002-prepareQueryCode,robot-2002-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2023', 'huatai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-IdCarChekc" //申请验证码    else{        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2023', 'huatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华泰-报价', 'def getTemplateGroup(dataSource) {    return "edi-2023-queryCar,edi-2023-xbQuery,edi-2023-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2023', 'huatai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华泰-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2023-queryCar,edi-2023-xbQuery,edi-2023-askCharge,edi-2023-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2023', 'huatai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华泰-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2023-queryCar,edi-2023-xbQuery,edi-2023-askCharge,edi-2023-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2023', 'huatai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-华泰-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2023-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2023', 'huatai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华泰-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2023-login,robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2023-login,robot-2023-ObtainConfig,robot-2023-checkInsurePerson,robot-2023-changePerson,robot-2023-checkInsuredPerson,robot-2023-changePerson,robot-2023-prepareEdit," +                    "robot-2023-prepareQueryCode,robot-2023-selectProposalCar,robot-2023-browseProposalCar,robot-2023-browseProposalCarefc,robot-2023-selectRenewalPolicyNo"        }else{            s = "robot-2023-login,robot-2023-ObtainConfig,robot-2023-checkInsurePerson,robot-2023-changePerson,robot-2023-checkInsuredPerson,robot-2023-changePerson,robot-2023-prepareEdit," +                    "robot-2023-prepareQueryCode,robot-2023-browseProposalCar,robot-2023-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2023-VehicleModelList" //上海车型查询        }        s += ",robot-2023-queryPrepare,robot-2023-vehicleQuery,robot-2023-queryTaxAbateForPlat,robot-2023-calActualValue,robot-2023-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2023-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2023-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-nomotor-unitedSaleEdit,robot-2023-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2023-login,robot_2023_bj_initData,robot_2023_bj_queryModel,robot_2023_bj_getSaleTaxInfo,robot_2023_bj_getRealValue,robot_2023_bj_getPersonData,robot_2023_bj_addPersonData,robot_2023_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2023', 'huatai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-华泰-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2023-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2023-ObtainConfig,robot-2023-selectRenewal,robot-2023-editCengage,robot-2023-editCitemCar,robot-2023-editCinsured,robot-2023-renewalPolicy,robot-2023-renewalPolicyCI,robot-2023-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2023-VehicleModelList" //上海车型查询        }        s += ",robot-2023-vehicleQueryXB,robot-2023-queryTaxAbateForPlat,robot-2023-calActualValue,robot-2023-editCitemKind,robot-2023-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2023-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-refreshPlanByTimes,robot-2023-insert"            }else{                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2023-getMaxCsellFee,robot-2023-getPrpCseller,robot-2023-getPrpCsellerCI,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            s += ",robot-2023-getMaxCsellFee,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"        }    }else{        s +=",robot-2023-ObtainConfig,robot-2023-checkInsurePerson,robot-2023-changePerson,robot-2023-checkInsuredPerson,robot-2023-changePerson,robot-2023-prepareEdit,robot-2023-selectRenewalPolicyNo,robot-2023-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2023-VehicleModelList" //上海车型查询        }        s += ",robot-2023-queryPrepare,robot-2023-vehicleQuery,robot-2023-queryTaxAbateForPlat,robot-2023-calActualValue,robot-2023-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2023-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2023-calAnciInfo,robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            }else{                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-nomotor-unitedSaleEdit,robot-2023-nomotor-saveUnitedSale,robot-2023-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2023-getMaxCsellFee,robot-2023-getPrpCseller,robot-2023-getPrpCsellerCI,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            s += ",robot-2023-getMaxCsellFee,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-nomotor-unitedSaleEdit,robot-2023-nomotor-saveUnitedSale,robot-2023-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2023-login,robot_2023_bj_initData,robot_2023_bj_queryModel,robot_2023_bj_getSaleTaxInfo,robot_2023_bj_getRealValue,robot_2023_bj_getPersonData,robot_2023_bj_addPersonData,robot_2023_bj_askCharge,robot_2023_bj_queryPayForXSFY,robot_2023_bj_getCagentCI,robot_2023_bj_getCagent,robot_2023_bj_queryPayForXSFY_req,robot_2023_bj_queryIlogEngage,robot_2023_bj_insureRefrenshPlan,robot_2023_bj_insure4S,robot-2023-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2023', 'huatai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-华泰-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ" +            ",robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind,robot-2023-nomotor-query,robot-2023-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2023', 'huatai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华泰-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ" +            ",robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind,robot-2023-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2023', 'huatai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ" +            ",robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind,robot-2023-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2023', 'huatai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "华泰财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-华泰-电销', 'def getTemplateGroup(dataSource){    return "robot-2023-pureESale_Login,robot-2023-pureESale_Welcome,robot-2023-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2023', 'huatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华泰续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2023-login,robot-2023-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2023', 'huatai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-华泰-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2023-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2023-ObtainConfig,robot-2023-selectRenewal,robot-2023-editCengage,robot-2023-editCitemCar,robot-2023-editCinsured,robot-2023-renewalPolicy,robot-2023-renewalPolicyCI,robot-2023-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2023-VehicleModelList" //上海车型查询        }        s += ",robot-2023-vehicleQueryXB,robot-2023-queryTaxAbateForPlat,robot-2023-calActualValue,robot-2023-editCitemKind,robot-2023-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2023-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-refreshPlanByTimes,robot-2023-insert"            }else{                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2023-calAnciInfo,robot-2023-getMaxCsellFee,robot-2023-getPrpCseller,robot-2023-getPrpCsellerCI,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            s += ",robot-2023-getMaxCsellFee,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"        }    }else{        s += ",robot-2023-ObtainConfig,robot-2023-checkInsurePerson,robot-2023-changePerson,robot-2023-checkInsuredPerson,robot-2023-changePerson,robot-2023-prepareEdit,robot-2023-editCengage,robot-2023-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2023-queryVehiclePMCheck,robot-2023-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2023-VehicleModelList" //上海车型查询        }        s += ",robot-2023-queryPrepare,robot-2023-vehicleQuery,robot-2023-queryTaxAbateForPlat,robot-2023-calActualValue,robot-2023-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2023-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2023-calAnciInfo,robot-2023-queryPayFor,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            }else{                s += ",robot-2023-calAnciInfo,robot-2023-checkAgentType,robot-2023-queryPayForSCMS,robot-2023-getCagent,robot-2023-getCagentCI,robot-2023-refreshPlanByTimes,robot-2023-nomotor-unitedSaleEdit,robot-2023-nomotor-saveUnitedSale,robot-2023-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2023-calAnciInfo,robot-2023-getMaxCsellFee,robot-2023-getPrpCseller,robot-2023-getPrpCsellerCI,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-insert"            s += ",robot-2023-getMaxCsellFee,robot-2023-queryPayForSCMS,robot-2023-refreshPlanByTimes,robot-2023-nomotor-unitedSaleEdit,robot-2023-nomotor-saveUnitedSale,robot-2023-insert"        }    }    s += ",robot-2023-checkRiskCode,robot-2023-editMainUwtFlag,robot-2023-editSubmitUndwrt,robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-showUndwrtMsgQ,robot-2023-showUndwrtMsgS"+            ",robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ,robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind,robot-2023-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2023-login,robot_2023_bj_initData,robot_2023_bj_queryModel,robot_2023_bj_getSaleTaxInfo,robot_2023_bj_getRealValue,robot_2023_bj_getPersonData,robot_2023_bj_addPersonData,robot_2023_bj_askCharge,robot_2023_bj_queryPayForXSFY,robot_2023_bj_getCagentCI,robot_2023_bj_getCagent,robot_2023_bj_queryPayForXSFY_req,robot_2023_bj_queryIlogEngage,robot_2023_bj_insureRefrenshPlan,robot_2023_bj_insure4S,robot-2023-uploadImage,robot_2023_bj_autoInsure,robot_2023_bj_showUndwrtMsgQ,robot_2023_bj_showUndwrtMsgS";       s += ",robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ,robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +                ",robot-2023-showCinsuredS,robot-2023-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2023', 'huatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华泰-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2023-qrcode_login,robot-2023-qrcode_printTwoBarCodeServlet,robot-2023-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2023-qrcode_login,robot-2023-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2023-qrcode_login,robot-2023-qrcode_editCheckFlag,robot-2023-qrcode_gotoJfcd,robot-2023-qrcode_prepareEditByJF,robot-2023-qrcode_getBusinessIn" +                ",robot-2023-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2023-qrcode_login,robot-2023-qrcode_editCheckFlag,robot-2023-qrcode_gotoJfcd,robot-2023-qrcode_prepareEditByJF,robot-2023-qrcode_getBusinessIn" +                ",robot-2023-qrcode_checkBeforeCalculate,robot-2023-qrcode_saveByJF,robot-2023-qrcode_getBusinessIn_alipay,robot-2023-qrcode_editFeeInfor,robot-2023-qrcode_editPayFeeByWeChat,robot-2023-qrcode_saveByWeChat,robot-2023-qrcode_save";		} else {					return  "robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-bj-editIDCardCheck,robot-2023-apply-selectIsNetProp,robot-2023-apply-saveCheckCode,robot-2023-qrcode_editCheckFlag,robot-2023-qrcode_gotoJfcd,robot-2023-qrcode_prepareEditByJF,robot-2023-qrcode_getBusinessIn" +",robot-2023-qrcode_checkBeforeCalculate,robot-2023-qrcode_saveByJF,robot-2023-qrcode_getBusinessIn_alipay,robot-2023-qrcode_editFeeInfor,robot-2023-qrcode_editPayFeeByWeChat,robot-2023-qrcode_saveByWeChat,robot-2023-qrcode_save";		}}    else {              return "robot-2023-qrcode_login,robot-2023-qrcode_editCheckFlag,robot-2023-qrcode_gotoJfcd,robot-2023-qrcode_prepareEditByJF,robot-2023-qrcode_getBusinessIn" +                ",robot-2023-qrcode_checkBeforeCalculate,robot-2023-qrcode_saveByJF,robot-2023-qrcode_getBusinessIn_alipay,robot-2023-qrcode_editFeeInfor,robot-2023-qrcode_editPayFeeByWeChat,robot-2023-qrcode_saveByWeChat,robot-2023-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2023', 'huatai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华泰-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2023-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2023-qrcode_query_editCheckFlag,robot-2023-qrcode_query_gotoJfcd,robot-2023-qrcode_query_prepareEditByJF" +                ",robot-2023-qrcode_query_editMainInfor,robot-2023-qrcode_query_getBusinessIn,robot-2023-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ" +            ",robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind,robot-2023-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2023', 'huatai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-IdCarChekc" //申请验证码    else{        return"robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-editIDCardCheck,robot-2023-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2023', 'huatai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-华泰-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectPolicyefc,robot-2023-selectPolicybiz,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ" +            ",robot-2023-showCitemCarQ,robot-2023-showCinsuredQ,robot-2023-showCitemKindCI,robot-2023-browseProposalS,robot-2023-showCitemCarS" +            ",robot-2023-showCinsuredS,robot-2023-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2023', 'huatai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2023-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectProposalQ,robot-2023-selectProposalS,robot-2023-browseProposalQ,robot-2023-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2023', 'huatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华泰-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-bj-editIDCardCheck,robot-2023-apply-bj-IdCarChekc";    } else {        s = "robot-2023-qrcode_login,robot-2023-qrcode_editCheckFlag,robot-2023-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2023', 'huatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi华泰报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2023_ask_charge,edi_2023_noMotor_quote"	} else {		return "edi_2023_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2023', 'huatai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华泰-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2023_ask_charge,edi_2023_noMotor_quote,edi_2023_askInsure,edi_2023_uploadImg,edi_2023_submitInsure,edi_2023_noMotor_submit" 	  	} else {		return "edi_2023_ask_chargeold,edi_2023_askInsure,edi_2023_uploadImg,edi_2023_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2023', 'huatai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华泰-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2023_ask_charge,edi_2023_noMotor_quote,edi_2023_askInsure,edi_2023_uploadImg" 	} else {		return "edi_2023_ask_chargeold,edi_2023_askInsure,edi_2023_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2023', 'huatai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-华泰-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2023_efc_policyinfo,edi_2023_biz_policyinfo,edi_2023_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2023', 'huatai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-华泰-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2023_efc_insurequery,edi_2023_biz_insurequery,edi_2023_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2023', 'huatai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京华泰短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-bj-editIDCardCheck,robot-2023-apply-saveCheckCode,robot-2023-apply-selectIsNetProp";    } else {        s = "robot-2023-qrcode_login,robot-2023-apply-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2023', 'huatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华泰-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2023-bj-qrcode_login,robot-2023-apply-bj-prepare,robot-2023-apply-select-jq,robot-2023-apply-select-sy,robot-2023-apply-bj-editIDCardCheck,robot-2023-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2023', 'huatai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-华泰-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2023_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2023', 'huatai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华泰北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2023_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2023', 'huatai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华泰北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2023_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2023', 'huatai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-华泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2023-login,robot-2023-prepareQueryCode,robot-2023-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2065', 'chengtai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-诚泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-IdCarChekc" //申请验证码    else{        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2065', 'chengtai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-诚泰-报价', 'def getTemplateGroup(dataSource) {    return "edi-2065-queryCar,edi-2065-xbQuery,edi-2065-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2065', 'chengtai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-诚泰-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2065-queryCar,edi-2065-xbQuery,edi-2065-askCharge,edi-2065-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2065', 'chengtai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-诚泰-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2065-queryCar,edi-2065-xbQuery,edi-2065-askCharge,edi-2065-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2065', 'chengtai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-诚泰-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2065-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2065', 'chengtai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-诚泰-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2065-login,robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2065-login,robot-2065-ObtainConfig,robot-2065-checkInsurePerson,robot-2065-changePerson,robot-2065-checkInsuredPerson,robot-2065-changePerson,robot-2065-prepareEdit," +                    "robot-2065-prepareQueryCode,robot-2065-selectProposalCar,robot-2065-browseProposalCar,robot-2065-browseProposalCarefc,robot-2065-selectRenewalPolicyNo"        }else{            s = "robot-2065-login,robot-2065-ObtainConfig,robot-2065-checkInsurePerson,robot-2065-changePerson,robot-2065-checkInsuredPerson,robot-2065-changePerson,robot-2065-prepareEdit," +                    "robot-2065-prepareQueryCode,robot-2065-browseProposalCar,robot-2065-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2065-VehicleModelList" //上海车型查询        }        s += ",robot-2065-queryPrepare,robot-2065-vehicleQuery,robot-2065-queryTaxAbateForPlat,robot-2065-calActualValue,robot-2065-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2065-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2065-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-nomotor-unitedSaleEdit,robot-2065-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2065-login,robot_2065_bj_initData,robot_2065_bj_queryModel,robot_2065_bj_getSaleTaxInfo,robot_2065_bj_getRealValue,robot_2065_bj_getPersonData,robot_2065_bj_addPersonData,robot_2065_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2065', 'chengtai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-诚泰-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2065-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2065-ObtainConfig,robot-2065-selectRenewal,robot-2065-editCengage,robot-2065-editCitemCar,robot-2065-editCinsured,robot-2065-renewalPolicy,robot-2065-renewalPolicyCI,robot-2065-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2065-VehicleModelList" //上海车型查询        }        s += ",robot-2065-vehicleQueryXB,robot-2065-queryTaxAbateForPlat,robot-2065-calActualValue,robot-2065-editCitemKind,robot-2065-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2065-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-refreshPlanByTimes,robot-2065-insert"            }else{                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2065-getMaxCsellFee,robot-2065-getPrpCseller,robot-2065-getPrpCsellerCI,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            s += ",robot-2065-getMaxCsellFee,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"        }    }else{        s +=",robot-2065-ObtainConfig,robot-2065-checkInsurePerson,robot-2065-changePerson,robot-2065-checkInsuredPerson,robot-2065-changePerson,robot-2065-prepareEdit,robot-2065-selectRenewalPolicyNo,robot-2065-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2065-VehicleModelList" //上海车型查询        }        s += ",robot-2065-queryPrepare,robot-2065-vehicleQuery,robot-2065-queryTaxAbateForPlat,robot-2065-calActualValue,robot-2065-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2065-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2065-calAnciInfo,robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            }else{                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-nomotor-unitedSaleEdit,robot-2065-nomotor-saveUnitedSale,robot-2065-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2065-getMaxCsellFee,robot-2065-getPrpCseller,robot-2065-getPrpCsellerCI,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            s += ",robot-2065-getMaxCsellFee,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-nomotor-unitedSaleEdit,robot-2065-nomotor-saveUnitedSale,robot-2065-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2065-login,robot_2065_bj_initData,robot_2065_bj_queryModel,robot_2065_bj_getSaleTaxInfo,robot_2065_bj_getRealValue,robot_2065_bj_getPersonData,robot_2065_bj_addPersonData,robot_2065_bj_askCharge,robot_2065_bj_queryPayForXSFY,robot_2065_bj_getCagentCI,robot_2065_bj_getCagent,robot_2065_bj_queryPayForXSFY_req,robot_2065_bj_queryIlogEngage,robot_2065_bj_insureRefrenshPlan,robot_2065_bj_insure4S,robot-2065-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2065', 'chengtai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-诚泰-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ" +            ",robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind,robot-2065-nomotor-query,robot-2065-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2065', 'chengtai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-诚泰-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ" +            ",robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind,robot-2065-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2065', 'chengtai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-诚泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ" +            ",robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind,robot-2065-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2065', 'chengtai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "诚泰财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-诚泰-电销', 'def getTemplateGroup(dataSource){    return "robot-2065-pureESale_Login,robot-2065-pureESale_Welcome,robot-2065-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2065', 'chengtai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-诚泰续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2065-login,robot-2065-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2065', 'chengtai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-诚泰-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2065-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2065-ObtainConfig,robot-2065-selectRenewal,robot-2065-editCengage,robot-2065-editCitemCar,robot-2065-editCinsured,robot-2065-renewalPolicy,robot-2065-renewalPolicyCI,robot-2065-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2065-VehicleModelList" //上海车型查询        }        s += ",robot-2065-vehicleQueryXB,robot-2065-queryTaxAbateForPlat,robot-2065-calActualValue,robot-2065-editCitemKind,robot-2065-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2065-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-refreshPlanByTimes,robot-2065-insert"            }else{                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2065-calAnciInfo,robot-2065-getMaxCsellFee,robot-2065-getPrpCseller,robot-2065-getPrpCsellerCI,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            s += ",robot-2065-getMaxCsellFee,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"        }    }else{        s += ",robot-2065-ObtainConfig,robot-2065-checkInsurePerson,robot-2065-changePerson,robot-2065-checkInsuredPerson,robot-2065-changePerson,robot-2065-prepareEdit,robot-2065-editCengage,robot-2065-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2065-queryVehiclePMCheck,robot-2065-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2065-VehicleModelList" //上海车型查询        }        s += ",robot-2065-queryPrepare,robot-2065-vehicleQuery,robot-2065-queryTaxAbateForPlat,robot-2065-calActualValue,robot-2065-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2065-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2065-calAnciInfo,robot-2065-queryPayFor,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            }else{                s += ",robot-2065-calAnciInfo,robot-2065-checkAgentType,robot-2065-queryPayForSCMS,robot-2065-getCagent,robot-2065-getCagentCI,robot-2065-refreshPlanByTimes,robot-2065-nomotor-unitedSaleEdit,robot-2065-nomotor-saveUnitedSale,robot-2065-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2065-calAnciInfo,robot-2065-getMaxCsellFee,robot-2065-getPrpCseller,robot-2065-getPrpCsellerCI,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-insert"            s += ",robot-2065-getMaxCsellFee,robot-2065-queryPayForSCMS,robot-2065-refreshPlanByTimes,robot-2065-nomotor-unitedSaleEdit,robot-2065-nomotor-saveUnitedSale,robot-2065-insert"        }    }    s += ",robot-2065-checkRiskCode,robot-2065-editMainUwtFlag,robot-2065-editSubmitUndwrt,robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-showUndwrtMsgQ,robot-2065-showUndwrtMsgS"+            ",robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ,robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind,robot-2065-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2065-login,robot_2065_bj_initData,robot_2065_bj_queryModel,robot_2065_bj_getSaleTaxInfo,robot_2065_bj_getRealValue,robot_2065_bj_getPersonData,robot_2065_bj_addPersonData,robot_2065_bj_askCharge,robot_2065_bj_queryPayForXSFY,robot_2065_bj_getCagentCI,robot_2065_bj_getCagent,robot_2065_bj_queryPayForXSFY_req,robot_2065_bj_queryIlogEngage,robot_2065_bj_insureRefrenshPlan,robot_2065_bj_insure4S,robot-2065-uploadImage,robot_2065_bj_autoInsure,robot_2065_bj_showUndwrtMsgQ,robot_2065_bj_showUndwrtMsgS";       s += ",robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ,robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +                ",robot-2065-showCinsuredS,robot-2065-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2065', 'chengtai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-诚泰-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2065-qrcode_login,robot-2065-qrcode_printTwoBarCodeServlet,robot-2065-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2065-qrcode_login,robot-2065-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2065-qrcode_login,robot-2065-qrcode_editCheckFlag,robot-2065-qrcode_gotoJfcd,robot-2065-qrcode_prepareEditByJF,robot-2065-qrcode_getBusinessIn" +                ",robot-2065-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2065-qrcode_login,robot-2065-qrcode_editCheckFlag,robot-2065-qrcode_gotoJfcd,robot-2065-qrcode_prepareEditByJF,robot-2065-qrcode_getBusinessIn" +                ",robot-2065-qrcode_checkBeforeCalculate,robot-2065-qrcode_saveByJF,robot-2065-qrcode_getBusinessIn_alipay,robot-2065-qrcode_editFeeInfor,robot-2065-qrcode_editPayFeeByWeChat,robot-2065-qrcode_saveByWeChat,robot-2065-qrcode_save";		} else {					return  "robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-bj-editIDCardCheck,robot-2065-apply-selectIsNetProp,robot-2065-apply-saveCheckCode,robot-2065-qrcode_editCheckFlag,robot-2065-qrcode_gotoJfcd,robot-2065-qrcode_prepareEditByJF,robot-2065-qrcode_getBusinessIn" +",robot-2065-qrcode_checkBeforeCalculate,robot-2065-qrcode_saveByJF,robot-2065-qrcode_getBusinessIn_alipay,robot-2065-qrcode_editFeeInfor,robot-2065-qrcode_editPayFeeByWeChat,robot-2065-qrcode_saveByWeChat,robot-2065-qrcode_save";		}}    else {              return "robot-2065-qrcode_login,robot-2065-qrcode_editCheckFlag,robot-2065-qrcode_gotoJfcd,robot-2065-qrcode_prepareEditByJF,robot-2065-qrcode_getBusinessIn" +                ",robot-2065-qrcode_checkBeforeCalculate,robot-2065-qrcode_saveByJF,robot-2065-qrcode_getBusinessIn_alipay,robot-2065-qrcode_editFeeInfor,robot-2065-qrcode_editPayFeeByWeChat,robot-2065-qrcode_saveByWeChat,robot-2065-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2065', 'chengtai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-诚泰-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2065-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2065-qrcode_query_editCheckFlag,robot-2065-qrcode_query_gotoJfcd,robot-2065-qrcode_query_prepareEditByJF" +                ",robot-2065-qrcode_query_editMainInfor,robot-2065-qrcode_query_getBusinessIn,robot-2065-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ" +            ",robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind,robot-2065-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2065', 'chengtai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-诚泰-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-IdCarChekc" //申请验证码    else{        return"robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-editIDCardCheck,robot-2065-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2065', 'chengtai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-诚泰-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectPolicyefc,robot-2065-selectPolicybiz,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ" +            ",robot-2065-showCitemCarQ,robot-2065-showCinsuredQ,robot-2065-showCitemKindCI,robot-2065-browseProposalS,robot-2065-showCitemCarS" +            ",robot-2065-showCinsuredS,robot-2065-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2065', 'chengtai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2065-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectProposalQ,robot-2065-selectProposalS,robot-2065-browseProposalQ,robot-2065-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2065', 'chengtai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-诚泰-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-bj-editIDCardCheck,robot-2065-apply-bj-IdCarChekc";    } else {        s = "robot-2065-qrcode_login,robot-2065-qrcode_editCheckFlag,robot-2065-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2065', 'chengtai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi诚泰报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2065_ask_charge,edi_2065_noMotor_quote"	} else {		return "edi_2065_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2065', 'chengtai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-诚泰-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2065_ask_charge,edi_2065_noMotor_quote,edi_2065_askInsure,edi_2065_uploadImg,edi_2065_submitInsure,edi_2065_noMotor_submit" 	  	} else {		return "edi_2065_ask_chargeold,edi_2065_askInsure,edi_2065_uploadImg,edi_2065_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2065', 'chengtai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-诚泰-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2065_ask_charge,edi_2065_noMotor_quote,edi_2065_askInsure,edi_2065_uploadImg" 	} else {		return "edi_2065_ask_chargeold,edi_2065_askInsure,edi_2065_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2065', 'chengtai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-诚泰-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2065_efc_policyinfo,edi_2065_biz_policyinfo,edi_2065_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2065', 'chengtai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-诚泰-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2065_efc_insurequery,edi_2065_biz_insurequery,edi_2065_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2065', 'chengtai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京诚泰短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-bj-editIDCardCheck,robot-2065-apply-saveCheckCode,robot-2065-apply-selectIsNetProp";    } else {        s = "robot-2065-qrcode_login,robot-2065-apply-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2065', 'chengtai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-诚泰-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2065-bj-qrcode_login,robot-2065-apply-bj-prepare,robot-2065-apply-select-jq,robot-2065-apply-select-sy,robot-2065-apply-bj-editIDCardCheck,robot-2065-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2065', 'chengtai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-诚泰-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2065_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2065', 'chengtai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi诚泰北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2065_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2065', 'chengtai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi诚泰北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2065_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2065', 'chengtai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-诚泰-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2065-login,robot-2065-prepareQueryCode,robot-2065-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2062', 'hengbang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-恒邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-IdCarChekc" //申请验证码    else{        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2062', 'hengbang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-恒邦-报价', 'def getTemplateGroup(dataSource) {    return "edi-2062-queryCar,edi-2062-xbQuery,edi-2062-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2062', 'hengbang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-恒邦-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2062-queryCar,edi-2062-xbQuery,edi-2062-askCharge,edi-2062-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2062', 'hengbang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-恒邦-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2062-queryCar,edi-2062-xbQuery,edi-2062-askCharge,edi-2062-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2062', 'hengbang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-恒邦-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2062-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2062', 'hengbang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-恒邦-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2062-login,robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2062-login,robot-2062-ObtainConfig,robot-2062-checkInsurePerson,robot-2062-changePerson,robot-2062-checkInsuredPerson,robot-2062-changePerson,robot-2062-prepareEdit," +                    "robot-2062-prepareQueryCode,robot-2062-selectProposalCar,robot-2062-browseProposalCar,robot-2062-browseProposalCarefc,robot-2062-selectRenewalPolicyNo"        }else{            s = "robot-2062-login,robot-2062-ObtainConfig,robot-2062-checkInsurePerson,robot-2062-changePerson,robot-2062-checkInsuredPerson,robot-2062-changePerson,robot-2062-prepareEdit," +                    "robot-2062-prepareQueryCode,robot-2062-browseProposalCar,robot-2062-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2062-VehicleModelList" //上海车型查询        }        s += ",robot-2062-queryPrepare,robot-2062-vehicleQuery,robot-2062-queryTaxAbateForPlat,robot-2062-calActualValue,robot-2062-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2062-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2062-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-nomotor-unitedSaleEdit,robot-2062-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2062-login,robot_2062_bj_initData,robot_2062_bj_queryModel,robot_2062_bj_getSaleTaxInfo,robot_2062_bj_getRealValue,robot_2062_bj_getPersonData,robot_2062_bj_addPersonData,robot_2062_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2062', 'hengbang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-恒邦-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2062-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2062-ObtainConfig,robot-2062-selectRenewal,robot-2062-editCengage,robot-2062-editCitemCar,robot-2062-editCinsured,robot-2062-renewalPolicy,robot-2062-renewalPolicyCI,robot-2062-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2062-VehicleModelList" //上海车型查询        }        s += ",robot-2062-vehicleQueryXB,robot-2062-queryTaxAbateForPlat,robot-2062-calActualValue,robot-2062-editCitemKind,robot-2062-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2062-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-refreshPlanByTimes,robot-2062-insert"            }else{                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2062-getMaxCsellFee,robot-2062-getPrpCseller,robot-2062-getPrpCsellerCI,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            s += ",robot-2062-getMaxCsellFee,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"        }    }else{        s +=",robot-2062-ObtainConfig,robot-2062-checkInsurePerson,robot-2062-changePerson,robot-2062-checkInsuredPerson,robot-2062-changePerson,robot-2062-prepareEdit,robot-2062-selectRenewalPolicyNo,robot-2062-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2062-VehicleModelList" //上海车型查询        }        s += ",robot-2062-queryPrepare,robot-2062-vehicleQuery,robot-2062-queryTaxAbateForPlat,robot-2062-calActualValue,robot-2062-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2062-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2062-calAnciInfo,robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            }else{                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-nomotor-unitedSaleEdit,robot-2062-nomotor-saveUnitedSale,robot-2062-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2062-getMaxCsellFee,robot-2062-getPrpCseller,robot-2062-getPrpCsellerCI,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            s += ",robot-2062-getMaxCsellFee,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-nomotor-unitedSaleEdit,robot-2062-nomotor-saveUnitedSale,robot-2062-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2062-login,robot_2062_bj_initData,robot_2062_bj_queryModel,robot_2062_bj_getSaleTaxInfo,robot_2062_bj_getRealValue,robot_2062_bj_getPersonData,robot_2062_bj_addPersonData,robot_2062_bj_askCharge,robot_2062_bj_queryPayForXSFY,robot_2062_bj_getCagentCI,robot_2062_bj_getCagent,robot_2062_bj_queryPayForXSFY_req,robot_2062_bj_queryIlogEngage,robot_2062_bj_insureRefrenshPlan,robot_2062_bj_insure4S,robot-2062-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2062', 'hengbang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-恒邦-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ" +            ",robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind,robot-2062-nomotor-query,robot-2062-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2062', 'hengbang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-恒邦-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ" +            ",robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind,robot-2062-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2062', 'hengbang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-恒邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ" +            ",robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind,robot-2062-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2062', 'hengbang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "恒邦财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-恒邦-电销', 'def getTemplateGroup(dataSource){    return "robot-2062-pureESale_Login,robot-2062-pureESale_Welcome,robot-2062-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2062', 'hengbang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-恒邦续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2062-login,robot-2062-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2062', 'hengbang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-恒邦-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2062-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2062-ObtainConfig,robot-2062-selectRenewal,robot-2062-editCengage,robot-2062-editCitemCar,robot-2062-editCinsured,robot-2062-renewalPolicy,robot-2062-renewalPolicyCI,robot-2062-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2062-VehicleModelList" //上海车型查询        }        s += ",robot-2062-vehicleQueryXB,robot-2062-queryTaxAbateForPlat,robot-2062-calActualValue,robot-2062-editCitemKind,robot-2062-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2062-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-refreshPlanByTimes,robot-2062-insert"            }else{                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2062-calAnciInfo,robot-2062-getMaxCsellFee,robot-2062-getPrpCseller,robot-2062-getPrpCsellerCI,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            s += ",robot-2062-getMaxCsellFee,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"        }    }else{        s += ",robot-2062-ObtainConfig,robot-2062-checkInsurePerson,robot-2062-changePerson,robot-2062-checkInsuredPerson,robot-2062-changePerson,robot-2062-prepareEdit,robot-2062-editCengage,robot-2062-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2062-queryVehiclePMCheck,robot-2062-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2062-VehicleModelList" //上海车型查询        }        s += ",robot-2062-queryPrepare,robot-2062-vehicleQuery,robot-2062-queryTaxAbateForPlat,robot-2062-calActualValue,robot-2062-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2062-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2062-calAnciInfo,robot-2062-queryPayFor,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            }else{                s += ",robot-2062-calAnciInfo,robot-2062-checkAgentType,robot-2062-queryPayForSCMS,robot-2062-getCagent,robot-2062-getCagentCI,robot-2062-refreshPlanByTimes,robot-2062-nomotor-unitedSaleEdit,robot-2062-nomotor-saveUnitedSale,robot-2062-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2062-calAnciInfo,robot-2062-getMaxCsellFee,robot-2062-getPrpCseller,robot-2062-getPrpCsellerCI,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-insert"            s += ",robot-2062-getMaxCsellFee,robot-2062-queryPayForSCMS,robot-2062-refreshPlanByTimes,robot-2062-nomotor-unitedSaleEdit,robot-2062-nomotor-saveUnitedSale,robot-2062-insert"        }    }    s += ",robot-2062-checkRiskCode,robot-2062-editMainUwtFlag,robot-2062-editSubmitUndwrt,robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-showUndwrtMsgQ,robot-2062-showUndwrtMsgS"+            ",robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ,robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind,robot-2062-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2062-login,robot_2062_bj_initData,robot_2062_bj_queryModel,robot_2062_bj_getSaleTaxInfo,robot_2062_bj_getRealValue,robot_2062_bj_getPersonData,robot_2062_bj_addPersonData,robot_2062_bj_askCharge,robot_2062_bj_queryPayForXSFY,robot_2062_bj_getCagentCI,robot_2062_bj_getCagent,robot_2062_bj_queryPayForXSFY_req,robot_2062_bj_queryIlogEngage,robot_2062_bj_insureRefrenshPlan,robot_2062_bj_insure4S,robot-2062-uploadImage,robot_2062_bj_autoInsure,robot_2062_bj_showUndwrtMsgQ,robot_2062_bj_showUndwrtMsgS";       s += ",robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ,robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +                ",robot-2062-showCinsuredS,robot-2062-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2062', 'hengbang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-恒邦-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2062-qrcode_login,robot-2062-qrcode_printTwoBarCodeServlet,robot-2062-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2062-qrcode_login,robot-2062-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2062-qrcode_login,robot-2062-qrcode_editCheckFlag,robot-2062-qrcode_gotoJfcd,robot-2062-qrcode_prepareEditByJF,robot-2062-qrcode_getBusinessIn" +                ",robot-2062-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2062-qrcode_login,robot-2062-qrcode_editCheckFlag,robot-2062-qrcode_gotoJfcd,robot-2062-qrcode_prepareEditByJF,robot-2062-qrcode_getBusinessIn" +                ",robot-2062-qrcode_checkBeforeCalculate,robot-2062-qrcode_saveByJF,robot-2062-qrcode_getBusinessIn_alipay,robot-2062-qrcode_editFeeInfor,robot-2062-qrcode_editPayFeeByWeChat,robot-2062-qrcode_saveByWeChat,robot-2062-qrcode_save";		} else {					return  "robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-bj-editIDCardCheck,robot-2062-apply-selectIsNetProp,robot-2062-apply-saveCheckCode,robot-2062-qrcode_editCheckFlag,robot-2062-qrcode_gotoJfcd,robot-2062-qrcode_prepareEditByJF,robot-2062-qrcode_getBusinessIn" +",robot-2062-qrcode_checkBeforeCalculate,robot-2062-qrcode_saveByJF,robot-2062-qrcode_getBusinessIn_alipay,robot-2062-qrcode_editFeeInfor,robot-2062-qrcode_editPayFeeByWeChat,robot-2062-qrcode_saveByWeChat,robot-2062-qrcode_save";		}}    else {              return "robot-2062-qrcode_login,robot-2062-qrcode_editCheckFlag,robot-2062-qrcode_gotoJfcd,robot-2062-qrcode_prepareEditByJF,robot-2062-qrcode_getBusinessIn" +                ",robot-2062-qrcode_checkBeforeCalculate,robot-2062-qrcode_saveByJF,robot-2062-qrcode_getBusinessIn_alipay,robot-2062-qrcode_editFeeInfor,robot-2062-qrcode_editPayFeeByWeChat,robot-2062-qrcode_saveByWeChat,robot-2062-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2062', 'hengbang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-恒邦-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2062-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2062-qrcode_query_editCheckFlag,robot-2062-qrcode_query_gotoJfcd,robot-2062-qrcode_query_prepareEditByJF" +                ",robot-2062-qrcode_query_editMainInfor,robot-2062-qrcode_query_getBusinessIn,robot-2062-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ" +            ",robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind,robot-2062-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2062', 'hengbang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-恒邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-IdCarChekc" //申请验证码    else{        return"robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-editIDCardCheck,robot-2062-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2062', 'hengbang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-恒邦-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectPolicyefc,robot-2062-selectPolicybiz,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ" +            ",robot-2062-showCitemCarQ,robot-2062-showCinsuredQ,robot-2062-showCitemKindCI,robot-2062-browseProposalS,robot-2062-showCitemCarS" +            ",robot-2062-showCinsuredS,robot-2062-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2062', 'hengbang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2062-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectProposalQ,robot-2062-selectProposalS,robot-2062-browseProposalQ,robot-2062-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2062', 'hengbang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-恒邦-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-bj-editIDCardCheck,robot-2062-apply-bj-IdCarChekc";    } else {        s = "robot-2062-qrcode_login,robot-2062-qrcode_editCheckFlag,robot-2062-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2062', 'hengbang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi恒邦报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2062_ask_charge,edi_2062_noMotor_quote"	} else {		return "edi_2062_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2062', 'hengbang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-恒邦-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2062_ask_charge,edi_2062_noMotor_quote,edi_2062_askInsure,edi_2062_uploadImg,edi_2062_submitInsure,edi_2062_noMotor_submit" 	  	} else {		return "edi_2062_ask_chargeold,edi_2062_askInsure,edi_2062_uploadImg,edi_2062_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2062', 'hengbang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-恒邦-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2062_ask_charge,edi_2062_noMotor_quote,edi_2062_askInsure,edi_2062_uploadImg" 	} else {		return "edi_2062_ask_chargeold,edi_2062_askInsure,edi_2062_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2062', 'hengbang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-恒邦-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2062_efc_policyinfo,edi_2062_biz_policyinfo,edi_2062_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2062', 'hengbang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-恒邦-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2062_efc_insurequery,edi_2062_biz_insurequery,edi_2062_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2062', 'hengbang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京恒邦短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-bj-editIDCardCheck,robot-2062-apply-saveCheckCode,robot-2062-apply-selectIsNetProp";    } else {        s = "robot-2062-qrcode_login,robot-2062-apply-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2062', 'hengbang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-恒邦-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2062-bj-qrcode_login,robot-2062-apply-bj-prepare,robot-2062-apply-select-jq,robot-2062-apply-select-sy,robot-2062-apply-bj-editIDCardCheck,robot-2062-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2062', 'hengbang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-恒邦-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2062_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2062', 'hengbang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi恒邦北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2062_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2062', 'hengbang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi恒邦北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2062_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2062', 'hengbang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-恒邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2062-login,robot-2062-prepareQueryCode,robot-2062-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
