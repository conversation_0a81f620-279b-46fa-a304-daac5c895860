INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3046', 'guoyuan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国元-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-IdCarChekc" //申请验证码    else{        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3046', 'guoyuan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-国元-报价', 'def getTemplateGroup(dataSource) {    return "edi-3046-queryCar,edi-3046-xbQuery,edi-3046-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3046', 'guoyuan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国元-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3046-queryCar,edi-3046-xbQuery,edi-3046-askCharge,edi-3046-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3046', 'guoyuan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国元-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3046-queryCar,edi-3046-xbQuery,edi-3046-askCharge,edi-3046-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3046', 'guoyuan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-国元-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3046-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3046', 'guoyuan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国元-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3046-login,robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3046-login,robot-3046-ObtainConfig,robot-3046-checkInsurePerson,robot-3046-changePerson,robot-3046-checkInsuredPerson,robot-3046-changePerson,robot-3046-prepareEdit," +                    "robot-3046-prepareQueryCode,robot-3046-selectProposalCar,robot-3046-browseProposalCar,robot-3046-browseProposalCarefc,robot-3046-selectRenewalPolicyNo"        }else{            s = "robot-3046-login,robot-3046-ObtainConfig,robot-3046-checkInsurePerson,robot-3046-changePerson,robot-3046-checkInsuredPerson,robot-3046-changePerson,robot-3046-prepareEdit," +                    "robot-3046-prepareQueryCode,robot-3046-browseProposalCar,robot-3046-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3046-VehicleModelList" //上海车型查询        }        s += ",robot-3046-queryPrepare,robot-3046-vehicleQuery,robot-3046-queryTaxAbateForPlat,robot-3046-calActualValue,robot-3046-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3046-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3046-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-nomotor-unitedSaleEdit,robot-3046-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3046-login,robot_3046_bj_initData,robot_3046_bj_queryModel,robot_3046_bj_getSaleTaxInfo,robot_3046_bj_getRealValue,robot_3046_bj_getPersonData,robot_3046_bj_addPersonData,robot_3046_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3046', 'guoyuan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-国元-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3046-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3046-ObtainConfig,robot-3046-selectRenewal,robot-3046-editCengage,robot-3046-editCitemCar,robot-3046-editCinsured,robot-3046-renewalPolicy,robot-3046-renewalPolicyCI,robot-3046-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3046-VehicleModelList" //上海车型查询        }        s += ",robot-3046-vehicleQueryXB,robot-3046-queryTaxAbateForPlat,robot-3046-calActualValue,robot-3046-editCitemKind,robot-3046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3046-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-refreshPlanByTimes,robot-3046-insert"            }else{                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3046-getMaxCsellFee,robot-3046-getPrpCseller,robot-3046-getPrpCsellerCI,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            s += ",robot-3046-getMaxCsellFee,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"        }    }else{        s +=",robot-3046-ObtainConfig,robot-3046-checkInsurePerson,robot-3046-changePerson,robot-3046-checkInsuredPerson,robot-3046-changePerson,robot-3046-prepareEdit,robot-3046-selectRenewalPolicyNo,robot-3046-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3046-VehicleModelList" //上海车型查询        }        s += ",robot-3046-queryPrepare,robot-3046-vehicleQuery,robot-3046-queryTaxAbateForPlat,robot-3046-calActualValue,robot-3046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3046-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3046-calAnciInfo,robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            }else{                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-nomotor-unitedSaleEdit,robot-3046-nomotor-saveUnitedSale,robot-3046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3046-getMaxCsellFee,robot-3046-getPrpCseller,robot-3046-getPrpCsellerCI,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            s += ",robot-3046-getMaxCsellFee,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-nomotor-unitedSaleEdit,robot-3046-nomotor-saveUnitedSale,robot-3046-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3046-login,robot_3046_bj_initData,robot_3046_bj_queryModel,robot_3046_bj_getSaleTaxInfo,robot_3046_bj_getRealValue,robot_3046_bj_getPersonData,robot_3046_bj_addPersonData,robot_3046_bj_askCharge,robot_3046_bj_queryPayForXSFY,robot_3046_bj_getCagentCI,robot_3046_bj_getCagent,robot_3046_bj_queryPayForXSFY_req,robot_3046_bj_queryIlogEngage,robot_3046_bj_insureRefrenshPlan,robot_3046_bj_insure4S,robot-3046-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3046', 'guoyuan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-国元-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ" +            ",robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind,robot-3046-nomotor-query,robot-3046-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3046', 'guoyuan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-国元-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ" +            ",robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind,robot-3046-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3046', 'guoyuan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国元-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ" +            ",robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind,robot-3046-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3046', 'guoyuan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "国元农业江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-国元-电销', 'def getTemplateGroup(dataSource){    return "robot-3046-pureESale_Login,robot-3046-pureESale_Welcome,robot-3046-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3046', 'guoyuan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国元续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3046-login,robot-3046-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3046', 'guoyuan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-国元-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3046-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3046-ObtainConfig,robot-3046-selectRenewal,robot-3046-editCengage,robot-3046-editCitemCar,robot-3046-editCinsured,robot-3046-renewalPolicy,robot-3046-renewalPolicyCI,robot-3046-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3046-VehicleModelList" //上海车型查询        }        s += ",robot-3046-vehicleQueryXB,robot-3046-queryTaxAbateForPlat,robot-3046-calActualValue,robot-3046-editCitemKind,robot-3046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3046-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-refreshPlanByTimes,robot-3046-insert"            }else{                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3046-calAnciInfo,robot-3046-getMaxCsellFee,robot-3046-getPrpCseller,robot-3046-getPrpCsellerCI,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            s += ",robot-3046-getMaxCsellFee,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"        }    }else{        s += ",robot-3046-ObtainConfig,robot-3046-checkInsurePerson,robot-3046-changePerson,robot-3046-checkInsuredPerson,robot-3046-changePerson,robot-3046-prepareEdit,robot-3046-editCengage,robot-3046-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3046-queryVehiclePMCheck,robot-3046-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3046-VehicleModelList" //上海车型查询        }        s += ",robot-3046-queryPrepare,robot-3046-vehicleQuery,robot-3046-queryTaxAbateForPlat,robot-3046-calActualValue,robot-3046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3046-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3046-calAnciInfo,robot-3046-queryPayFor,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            }else{                s += ",robot-3046-calAnciInfo,robot-3046-checkAgentType,robot-3046-queryPayForSCMS,robot-3046-getCagent,robot-3046-getCagentCI,robot-3046-refreshPlanByTimes,robot-3046-nomotor-unitedSaleEdit,robot-3046-nomotor-saveUnitedSale,robot-3046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3046-calAnciInfo,robot-3046-getMaxCsellFee,robot-3046-getPrpCseller,robot-3046-getPrpCsellerCI,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-insert"            s += ",robot-3046-getMaxCsellFee,robot-3046-queryPayForSCMS,robot-3046-refreshPlanByTimes,robot-3046-nomotor-unitedSaleEdit,robot-3046-nomotor-saveUnitedSale,robot-3046-insert"        }    }    s += ",robot-3046-checkRiskCode,robot-3046-editMainUwtFlag,robot-3046-editSubmitUndwrt,robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-showUndwrtMsgQ,robot-3046-showUndwrtMsgS"+            ",robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ,robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind,robot-3046-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3046-login,robot_3046_bj_initData,robot_3046_bj_queryModel,robot_3046_bj_getSaleTaxInfo,robot_3046_bj_getRealValue,robot_3046_bj_getPersonData,robot_3046_bj_addPersonData,robot_3046_bj_askCharge,robot_3046_bj_queryPayForXSFY,robot_3046_bj_getCagentCI,robot_3046_bj_getCagent,robot_3046_bj_queryPayForXSFY_req,robot_3046_bj_queryIlogEngage,robot_3046_bj_insureRefrenshPlan,robot_3046_bj_insure4S,robot-3046-uploadImage,robot_3046_bj_autoInsure,robot_3046_bj_showUndwrtMsgQ,robot_3046_bj_showUndwrtMsgS";       s += ",robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ,robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +                ",robot-3046-showCinsuredS,robot-3046-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3046', 'guoyuan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国元-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3046-qrcode_login,robot-3046-qrcode_printTwoBarCodeServlet,robot-3046-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3046-qrcode_login,robot-3046-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3046-qrcode_login,robot-3046-qrcode_editCheckFlag,robot-3046-qrcode_gotoJfcd,robot-3046-qrcode_prepareEditByJF,robot-3046-qrcode_getBusinessIn" +                ",robot-3046-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3046-qrcode_login,robot-3046-qrcode_editCheckFlag,robot-3046-qrcode_gotoJfcd,robot-3046-qrcode_prepareEditByJF,robot-3046-qrcode_getBusinessIn" +                ",robot-3046-qrcode_checkBeforeCalculate,robot-3046-qrcode_saveByJF,robot-3046-qrcode_getBusinessIn_alipay,robot-3046-qrcode_editFeeInfor,robot-3046-qrcode_editPayFeeByWeChat,robot-3046-qrcode_saveByWeChat,robot-3046-qrcode_save";		} else {					return  "robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-bj-editIDCardCheck,robot-3046-apply-selectIsNetProp,robot-3046-apply-saveCheckCode,robot-3046-qrcode_editCheckFlag,robot-3046-qrcode_gotoJfcd,robot-3046-qrcode_prepareEditByJF,robot-3046-qrcode_getBusinessIn" +",robot-3046-qrcode_checkBeforeCalculate,robot-3046-qrcode_saveByJF,robot-3046-qrcode_getBusinessIn_alipay,robot-3046-qrcode_editFeeInfor,robot-3046-qrcode_editPayFeeByWeChat,robot-3046-qrcode_saveByWeChat,robot-3046-qrcode_save";		}}    else {              return "robot-3046-qrcode_login,robot-3046-qrcode_editCheckFlag,robot-3046-qrcode_gotoJfcd,robot-3046-qrcode_prepareEditByJF,robot-3046-qrcode_getBusinessIn" +                ",robot-3046-qrcode_checkBeforeCalculate,robot-3046-qrcode_saveByJF,robot-3046-qrcode_getBusinessIn_alipay,robot-3046-qrcode_editFeeInfor,robot-3046-qrcode_editPayFeeByWeChat,robot-3046-qrcode_saveByWeChat,robot-3046-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3046', 'guoyuan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-国元-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3046-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3046-qrcode_query_editCheckFlag,robot-3046-qrcode_query_gotoJfcd,robot-3046-qrcode_query_prepareEditByJF" +                ",robot-3046-qrcode_query_editMainInfor,robot-3046-qrcode_query_getBusinessIn,robot-3046-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ" +            ",robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind,robot-3046-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3046', 'guoyuan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-国元-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-IdCarChekc" //申请验证码    else{        return"robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-editIDCardCheck,robot-3046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3046', 'guoyuan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-国元-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectPolicyefc,robot-3046-selectPolicybiz,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ" +            ",robot-3046-showCitemCarQ,robot-3046-showCinsuredQ,robot-3046-showCitemKindCI,robot-3046-browseProposalS,robot-3046-showCitemCarS" +            ",robot-3046-showCinsuredS,robot-3046-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3046', 'guoyuan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3046-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectProposalQ,robot-3046-selectProposalS,robot-3046-browseProposalQ,robot-3046-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3046', 'guoyuan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国元-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-bj-editIDCardCheck,robot-3046-apply-bj-IdCarChekc";    } else {        s = "robot-3046-qrcode_login,robot-3046-qrcode_editCheckFlag,robot-3046-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3046', 'guoyuan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi国元报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3046_ask_charge,edi_3046_noMotor_quote"	} else {		return "edi_3046_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3046', 'guoyuan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-国元-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3046_ask_charge,edi_3046_noMotor_quote,edi_3046_askInsure,edi_3046_uploadImg,edi_3046_submitInsure,edi_3046_noMotor_submit" 	  	} else {		return "edi_3046_ask_chargeold,edi_3046_askInsure,edi_3046_uploadImg,edi_3046_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3046', 'guoyuan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-国元-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3046_ask_charge,edi_3046_noMotor_quote,edi_3046_askInsure,edi_3046_uploadImg" 	} else {		return "edi_3046_ask_chargeold,edi_3046_askInsure,edi_3046_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3046', 'guoyuan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-国元-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3046_efc_policyinfo,edi_3046_biz_policyinfo,edi_3046_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3046', 'guoyuan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-国元-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3046_efc_insurequery,edi_3046_biz_insurequery,edi_3046_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3046', 'guoyuan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京国元短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-bj-editIDCardCheck,robot-3046-apply-saveCheckCode,robot-3046-apply-selectIsNetProp";    } else {        s = "robot-3046-qrcode_login,robot-3046-apply-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3046', 'guoyuan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-国元-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3046-bj-qrcode_login,robot-3046-apply-bj-prepare,robot-3046-apply-select-jq,robot-3046-apply-select-sy,robot-3046-apply-bj-editIDCardCheck,robot-3046-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3046', 'guoyuan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-国元-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3046_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3046', 'guoyuan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国元北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3046_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3046', 'guoyuan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi国元北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3046_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3046', 'guoyuan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-国元-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3046-login,robot-3046-prepareQueryCode,robot-3046-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2076', 'zhongmei', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中煤-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-IdCarChekc" //申请验证码    else{        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2076', 'zhongmei', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-中煤-报价', 'def getTemplateGroup(dataSource) {    return "edi-2076-queryCar,edi-2076-xbQuery,edi-2076-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2076', 'zhongmei', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中煤-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2076-queryCar,edi-2076-xbQuery,edi-2076-askCharge,edi-2076-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2076', 'zhongmei', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中煤-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2076-queryCar,edi-2076-xbQuery,edi-2076-askCharge,edi-2076-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2076', 'zhongmei', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-中煤-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2076-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2076', 'zhongmei', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中煤-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2076-login,robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2076-login,robot-2076-ObtainConfig,robot-2076-checkInsurePerson,robot-2076-changePerson,robot-2076-checkInsuredPerson,robot-2076-changePerson,robot-2076-prepareEdit," +                    "robot-2076-prepareQueryCode,robot-2076-selectProposalCar,robot-2076-browseProposalCar,robot-2076-browseProposalCarefc,robot-2076-selectRenewalPolicyNo"        }else{            s = "robot-2076-login,robot-2076-ObtainConfig,robot-2076-checkInsurePerson,robot-2076-changePerson,robot-2076-checkInsuredPerson,robot-2076-changePerson,robot-2076-prepareEdit," +                    "robot-2076-prepareQueryCode,robot-2076-browseProposalCar,robot-2076-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2076-VehicleModelList" //上海车型查询        }        s += ",robot-2076-queryPrepare,robot-2076-vehicleQuery,robot-2076-queryTaxAbateForPlat,robot-2076-calActualValue,robot-2076-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2076-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2076-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-nomotor-unitedSaleEdit,robot-2076-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2076-login,robot_2076_bj_initData,robot_2076_bj_queryModel,robot_2076_bj_getSaleTaxInfo,robot_2076_bj_getRealValue,robot_2076_bj_getPersonData,robot_2076_bj_addPersonData,robot_2076_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2076', 'zhongmei', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-中煤-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2076-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2076-ObtainConfig,robot-2076-selectRenewal,robot-2076-editCengage,robot-2076-editCitemCar,robot-2076-editCinsured,robot-2076-renewalPolicy,robot-2076-renewalPolicyCI,robot-2076-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2076-VehicleModelList" //上海车型查询        }        s += ",robot-2076-vehicleQueryXB,robot-2076-queryTaxAbateForPlat,robot-2076-calActualValue,robot-2076-editCitemKind,robot-2076-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2076-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-refreshPlanByTimes,robot-2076-insert"            }else{                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2076-getMaxCsellFee,robot-2076-getPrpCseller,robot-2076-getPrpCsellerCI,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            s += ",robot-2076-getMaxCsellFee,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"        }    }else{        s +=",robot-2076-ObtainConfig,robot-2076-checkInsurePerson,robot-2076-changePerson,robot-2076-checkInsuredPerson,robot-2076-changePerson,robot-2076-prepareEdit,robot-2076-selectRenewalPolicyNo,robot-2076-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2076-VehicleModelList" //上海车型查询        }        s += ",robot-2076-queryPrepare,robot-2076-vehicleQuery,robot-2076-queryTaxAbateForPlat,robot-2076-calActualValue,robot-2076-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2076-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2076-calAnciInfo,robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            }else{                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-nomotor-unitedSaleEdit,robot-2076-nomotor-saveUnitedSale,robot-2076-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2076-getMaxCsellFee,robot-2076-getPrpCseller,robot-2076-getPrpCsellerCI,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            s += ",robot-2076-getMaxCsellFee,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-nomotor-unitedSaleEdit,robot-2076-nomotor-saveUnitedSale,robot-2076-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2076-login,robot_2076_bj_initData,robot_2076_bj_queryModel,robot_2076_bj_getSaleTaxInfo,robot_2076_bj_getRealValue,robot_2076_bj_getPersonData,robot_2076_bj_addPersonData,robot_2076_bj_askCharge,robot_2076_bj_queryPayForXSFY,robot_2076_bj_getCagentCI,robot_2076_bj_getCagent,robot_2076_bj_queryPayForXSFY_req,robot_2076_bj_queryIlogEngage,robot_2076_bj_insureRefrenshPlan,robot_2076_bj_insure4S,robot-2076-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2076', 'zhongmei', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-中煤-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ" +            ",robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind,robot-2076-nomotor-query,robot-2076-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2076', 'zhongmei', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-中煤-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ" +            ",robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind,robot-2076-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2076', 'zhongmei', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中煤-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ" +            ",robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind,robot-2076-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2076', 'zhongmei', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "中煤财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-中煤-电销', 'def getTemplateGroup(dataSource){    return "robot-2076-pureESale_Login,robot-2076-pureESale_Welcome,robot-2076-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2076', 'zhongmei', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中煤续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2076-login,robot-2076-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2076', 'zhongmei', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-中煤-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2076-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2076-ObtainConfig,robot-2076-selectRenewal,robot-2076-editCengage,robot-2076-editCitemCar,robot-2076-editCinsured,robot-2076-renewalPolicy,robot-2076-renewalPolicyCI,robot-2076-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2076-VehicleModelList" //上海车型查询        }        s += ",robot-2076-vehicleQueryXB,robot-2076-queryTaxAbateForPlat,robot-2076-calActualValue,robot-2076-editCitemKind,robot-2076-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2076-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-refreshPlanByTimes,robot-2076-insert"            }else{                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2076-calAnciInfo,robot-2076-getMaxCsellFee,robot-2076-getPrpCseller,robot-2076-getPrpCsellerCI,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            s += ",robot-2076-getMaxCsellFee,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"        }    }else{        s += ",robot-2076-ObtainConfig,robot-2076-checkInsurePerson,robot-2076-changePerson,robot-2076-checkInsuredPerson,robot-2076-changePerson,robot-2076-prepareEdit,robot-2076-editCengage,robot-2076-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2076-queryVehiclePMCheck,robot-2076-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2076-VehicleModelList" //上海车型查询        }        s += ",robot-2076-queryPrepare,robot-2076-vehicleQuery,robot-2076-queryTaxAbateForPlat,robot-2076-calActualValue,robot-2076-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2076-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2076-calAnciInfo,robot-2076-queryPayFor,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            }else{                s += ",robot-2076-calAnciInfo,robot-2076-checkAgentType,robot-2076-queryPayForSCMS,robot-2076-getCagent,robot-2076-getCagentCI,robot-2076-refreshPlanByTimes,robot-2076-nomotor-unitedSaleEdit,robot-2076-nomotor-saveUnitedSale,robot-2076-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2076-calAnciInfo,robot-2076-getMaxCsellFee,robot-2076-getPrpCseller,robot-2076-getPrpCsellerCI,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-insert"            s += ",robot-2076-getMaxCsellFee,robot-2076-queryPayForSCMS,robot-2076-refreshPlanByTimes,robot-2076-nomotor-unitedSaleEdit,robot-2076-nomotor-saveUnitedSale,robot-2076-insert"        }    }    s += ",robot-2076-checkRiskCode,robot-2076-editMainUwtFlag,robot-2076-editSubmitUndwrt,robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-showUndwrtMsgQ,robot-2076-showUndwrtMsgS"+            ",robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ,robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind,robot-2076-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2076-login,robot_2076_bj_initData,robot_2076_bj_queryModel,robot_2076_bj_getSaleTaxInfo,robot_2076_bj_getRealValue,robot_2076_bj_getPersonData,robot_2076_bj_addPersonData,robot_2076_bj_askCharge,robot_2076_bj_queryPayForXSFY,robot_2076_bj_getCagentCI,robot_2076_bj_getCagent,robot_2076_bj_queryPayForXSFY_req,robot_2076_bj_queryIlogEngage,robot_2076_bj_insureRefrenshPlan,robot_2076_bj_insure4S,robot-2076-uploadImage,robot_2076_bj_autoInsure,robot_2076_bj_showUndwrtMsgQ,robot_2076_bj_showUndwrtMsgS";       s += ",robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ,robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +                ",robot-2076-showCinsuredS,robot-2076-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2076', 'zhongmei', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中煤-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2076-qrcode_login,robot-2076-qrcode_printTwoBarCodeServlet,robot-2076-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2076-qrcode_login,robot-2076-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2076-qrcode_login,robot-2076-qrcode_editCheckFlag,robot-2076-qrcode_gotoJfcd,robot-2076-qrcode_prepareEditByJF,robot-2076-qrcode_getBusinessIn" +                ",robot-2076-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2076-qrcode_login,robot-2076-qrcode_editCheckFlag,robot-2076-qrcode_gotoJfcd,robot-2076-qrcode_prepareEditByJF,robot-2076-qrcode_getBusinessIn" +                ",robot-2076-qrcode_checkBeforeCalculate,robot-2076-qrcode_saveByJF,robot-2076-qrcode_getBusinessIn_alipay,robot-2076-qrcode_editFeeInfor,robot-2076-qrcode_editPayFeeByWeChat,robot-2076-qrcode_saveByWeChat,robot-2076-qrcode_save";		} else {					return  "robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-bj-editIDCardCheck,robot-2076-apply-selectIsNetProp,robot-2076-apply-saveCheckCode,robot-2076-qrcode_editCheckFlag,robot-2076-qrcode_gotoJfcd,robot-2076-qrcode_prepareEditByJF,robot-2076-qrcode_getBusinessIn" +",robot-2076-qrcode_checkBeforeCalculate,robot-2076-qrcode_saveByJF,robot-2076-qrcode_getBusinessIn_alipay,robot-2076-qrcode_editFeeInfor,robot-2076-qrcode_editPayFeeByWeChat,robot-2076-qrcode_saveByWeChat,robot-2076-qrcode_save";		}}    else {              return "robot-2076-qrcode_login,robot-2076-qrcode_editCheckFlag,robot-2076-qrcode_gotoJfcd,robot-2076-qrcode_prepareEditByJF,robot-2076-qrcode_getBusinessIn" +                ",robot-2076-qrcode_checkBeforeCalculate,robot-2076-qrcode_saveByJF,robot-2076-qrcode_getBusinessIn_alipay,robot-2076-qrcode_editFeeInfor,robot-2076-qrcode_editPayFeeByWeChat,robot-2076-qrcode_saveByWeChat,robot-2076-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2076', 'zhongmei', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-中煤-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2076-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2076-qrcode_query_editCheckFlag,robot-2076-qrcode_query_gotoJfcd,robot-2076-qrcode_query_prepareEditByJF" +                ",robot-2076-qrcode_query_editMainInfor,robot-2076-qrcode_query_getBusinessIn,robot-2076-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ" +            ",robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind,robot-2076-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2076', 'zhongmei', '15', '6', 'pro', 'other', b'1', '{}', '精灵-中煤-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-IdCarChekc" //申请验证码    else{        return"robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-editIDCardCheck,robot-2076-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2076', 'zhongmei', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-中煤-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectPolicyefc,robot-2076-selectPolicybiz,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ" +            ",robot-2076-showCitemCarQ,robot-2076-showCinsuredQ,robot-2076-showCitemKindCI,robot-2076-browseProposalS,robot-2076-showCitemCarS" +            ",robot-2076-showCinsuredS,robot-2076-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2076', 'zhongmei', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2076-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectProposalQ,robot-2076-selectProposalS,robot-2076-browseProposalQ,robot-2076-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2076', 'zhongmei', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中煤-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-bj-editIDCardCheck,robot-2076-apply-bj-IdCarChekc";    } else {        s = "robot-2076-qrcode_login,robot-2076-qrcode_editCheckFlag,robot-2076-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2076', 'zhongmei', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi中煤报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2076_ask_charge,edi_2076_noMotor_quote"	} else {		return "edi_2076_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2076', 'zhongmei', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-中煤-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2076_ask_charge,edi_2076_noMotor_quote,edi_2076_askInsure,edi_2076_uploadImg,edi_2076_submitInsure,edi_2076_noMotor_submit" 	  	} else {		return "edi_2076_ask_chargeold,edi_2076_askInsure,edi_2076_uploadImg,edi_2076_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2076', 'zhongmei', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-中煤-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2076_ask_charge,edi_2076_noMotor_quote,edi_2076_askInsure,edi_2076_uploadImg" 	} else {		return "edi_2076_ask_chargeold,edi_2076_askInsure,edi_2076_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2076', 'zhongmei', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-中煤-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2076_efc_policyinfo,edi_2076_biz_policyinfo,edi_2076_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2076', 'zhongmei', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-中煤-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2076_efc_insurequery,edi_2076_biz_insurequery,edi_2076_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2076', 'zhongmei', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京中煤短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-bj-editIDCardCheck,robot-2076-apply-saveCheckCode,robot-2076-apply-selectIsNetProp";    } else {        s = "robot-2076-qrcode_login,robot-2076-apply-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2076', 'zhongmei', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-中煤-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2076-bj-qrcode_login,robot-2076-apply-bj-prepare,robot-2076-apply-select-jq,robot-2076-apply-select-sy,robot-2076-apply-bj-editIDCardCheck,robot-2076-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2076', 'zhongmei', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-中煤-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2076_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2076', 'zhongmei', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中煤北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2076_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2076', 'zhongmei', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi中煤北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2076_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2076', 'zhongmei', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-中煤-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2076-login,robot-2076-prepareQueryCode,robot-2076-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3028', 'bohai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-渤海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-IdCarChekc" //申请验证码    else{        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3028', 'bohai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-渤海-报价', 'def getTemplateGroup(dataSource) {    return "edi-3028-queryCar,edi-3028-xbQuery,edi-3028-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3028', 'bohai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-渤海-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3028-queryCar,edi-3028-xbQuery,edi-3028-askCharge,edi-3028-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3028', 'bohai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-渤海-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3028-queryCar,edi-3028-xbQuery,edi-3028-askCharge,edi-3028-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3028', 'bohai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-渤海-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3028-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3028', 'bohai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-渤海-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3028-login,robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3028-login,robot-3028-ObtainConfig,robot-3028-checkInsurePerson,robot-3028-changePerson,robot-3028-checkInsuredPerson,robot-3028-changePerson,robot-3028-prepareEdit," +                    "robot-3028-prepareQueryCode,robot-3028-selectProposalCar,robot-3028-browseProposalCar,robot-3028-browseProposalCarefc,robot-3028-selectRenewalPolicyNo"        }else{            s = "robot-3028-login,robot-3028-ObtainConfig,robot-3028-checkInsurePerson,robot-3028-changePerson,robot-3028-checkInsuredPerson,robot-3028-changePerson,robot-3028-prepareEdit," +                    "robot-3028-prepareQueryCode,robot-3028-browseProposalCar,robot-3028-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3028-VehicleModelList" //上海车型查询        }        s += ",robot-3028-queryPrepare,robot-3028-vehicleQuery,robot-3028-queryTaxAbateForPlat,robot-3028-calActualValue,robot-3028-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3028-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3028-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-nomotor-unitedSaleEdit,robot-3028-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3028-login,robot_3028_bj_initData,robot_3028_bj_queryModel,robot_3028_bj_getSaleTaxInfo,robot_3028_bj_getRealValue,robot_3028_bj_getPersonData,robot_3028_bj_addPersonData,robot_3028_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3028', 'bohai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-渤海-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3028-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3028-ObtainConfig,robot-3028-selectRenewal,robot-3028-editCengage,robot-3028-editCitemCar,robot-3028-editCinsured,robot-3028-renewalPolicy,robot-3028-renewalPolicyCI,robot-3028-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3028-VehicleModelList" //上海车型查询        }        s += ",robot-3028-vehicleQueryXB,robot-3028-queryTaxAbateForPlat,robot-3028-calActualValue,robot-3028-editCitemKind,robot-3028-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3028-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-refreshPlanByTimes,robot-3028-insert"            }else{                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3028-getMaxCsellFee,robot-3028-getPrpCseller,robot-3028-getPrpCsellerCI,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            s += ",robot-3028-getMaxCsellFee,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"        }    }else{        s +=",robot-3028-ObtainConfig,robot-3028-checkInsurePerson,robot-3028-changePerson,robot-3028-checkInsuredPerson,robot-3028-changePerson,robot-3028-prepareEdit,robot-3028-selectRenewalPolicyNo,robot-3028-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3028-VehicleModelList" //上海车型查询        }        s += ",robot-3028-queryPrepare,robot-3028-vehicleQuery,robot-3028-queryTaxAbateForPlat,robot-3028-calActualValue,robot-3028-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3028-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3028-calAnciInfo,robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            }else{                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-nomotor-unitedSaleEdit,robot-3028-nomotor-saveUnitedSale,robot-3028-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3028-getMaxCsellFee,robot-3028-getPrpCseller,robot-3028-getPrpCsellerCI,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            s += ",robot-3028-getMaxCsellFee,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-nomotor-unitedSaleEdit,robot-3028-nomotor-saveUnitedSale,robot-3028-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3028-login,robot_3028_bj_initData,robot_3028_bj_queryModel,robot_3028_bj_getSaleTaxInfo,robot_3028_bj_getRealValue,robot_3028_bj_getPersonData,robot_3028_bj_addPersonData,robot_3028_bj_askCharge,robot_3028_bj_queryPayForXSFY,robot_3028_bj_getCagentCI,robot_3028_bj_getCagent,robot_3028_bj_queryPayForXSFY_req,robot_3028_bj_queryIlogEngage,robot_3028_bj_insureRefrenshPlan,robot_3028_bj_insure4S,robot-3028-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3028', 'bohai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-渤海-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ" +            ",robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind,robot-3028-nomotor-query,robot-3028-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3028', 'bohai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-渤海-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ" +            ",robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind,robot-3028-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3028', 'bohai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-渤海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ" +            ",robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind,robot-3028-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3028', 'bohai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "渤海财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-渤海-电销', 'def getTemplateGroup(dataSource){    return "robot-3028-pureESale_Login,robot-3028-pureESale_Welcome,robot-3028-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3028', 'bohai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-渤海续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3028-login,robot-3028-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3028', 'bohai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-渤海-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3028-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3028-ObtainConfig,robot-3028-selectRenewal,robot-3028-editCengage,robot-3028-editCitemCar,robot-3028-editCinsured,robot-3028-renewalPolicy,robot-3028-renewalPolicyCI,robot-3028-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3028-VehicleModelList" //上海车型查询        }        s += ",robot-3028-vehicleQueryXB,robot-3028-queryTaxAbateForPlat,robot-3028-calActualValue,robot-3028-editCitemKind,robot-3028-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3028-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-refreshPlanByTimes,robot-3028-insert"            }else{                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3028-calAnciInfo,robot-3028-getMaxCsellFee,robot-3028-getPrpCseller,robot-3028-getPrpCsellerCI,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            s += ",robot-3028-getMaxCsellFee,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"        }    }else{        s += ",robot-3028-ObtainConfig,robot-3028-checkInsurePerson,robot-3028-changePerson,robot-3028-checkInsuredPerson,robot-3028-changePerson,robot-3028-prepareEdit,robot-3028-editCengage,robot-3028-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3028-queryVehiclePMCheck,robot-3028-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3028-VehicleModelList" //上海车型查询        }        s += ",robot-3028-queryPrepare,robot-3028-vehicleQuery,robot-3028-queryTaxAbateForPlat,robot-3028-calActualValue,robot-3028-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3028-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3028-calAnciInfo,robot-3028-queryPayFor,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            }else{                s += ",robot-3028-calAnciInfo,robot-3028-checkAgentType,robot-3028-queryPayForSCMS,robot-3028-getCagent,robot-3028-getCagentCI,robot-3028-refreshPlanByTimes,robot-3028-nomotor-unitedSaleEdit,robot-3028-nomotor-saveUnitedSale,robot-3028-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3028-calAnciInfo,robot-3028-getMaxCsellFee,robot-3028-getPrpCseller,robot-3028-getPrpCsellerCI,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-insert"            s += ",robot-3028-getMaxCsellFee,robot-3028-queryPayForSCMS,robot-3028-refreshPlanByTimes,robot-3028-nomotor-unitedSaleEdit,robot-3028-nomotor-saveUnitedSale,robot-3028-insert"        }    }    s += ",robot-3028-checkRiskCode,robot-3028-editMainUwtFlag,robot-3028-editSubmitUndwrt,robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-showUndwrtMsgQ,robot-3028-showUndwrtMsgS"+            ",robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ,robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind,robot-3028-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3028-login,robot_3028_bj_initData,robot_3028_bj_queryModel,robot_3028_bj_getSaleTaxInfo,robot_3028_bj_getRealValue,robot_3028_bj_getPersonData,robot_3028_bj_addPersonData,robot_3028_bj_askCharge,robot_3028_bj_queryPayForXSFY,robot_3028_bj_getCagentCI,robot_3028_bj_getCagent,robot_3028_bj_queryPayForXSFY_req,robot_3028_bj_queryIlogEngage,robot_3028_bj_insureRefrenshPlan,robot_3028_bj_insure4S,robot-3028-uploadImage,robot_3028_bj_autoInsure,robot_3028_bj_showUndwrtMsgQ,robot_3028_bj_showUndwrtMsgS";       s += ",robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ,robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +                ",robot-3028-showCinsuredS,robot-3028-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3028', 'bohai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-渤海-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3028-qrcode_login,robot-3028-qrcode_printTwoBarCodeServlet,robot-3028-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3028-qrcode_login,robot-3028-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3028-qrcode_login,robot-3028-qrcode_editCheckFlag,robot-3028-qrcode_gotoJfcd,robot-3028-qrcode_prepareEditByJF,robot-3028-qrcode_getBusinessIn" +                ",robot-3028-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3028-qrcode_login,robot-3028-qrcode_editCheckFlag,robot-3028-qrcode_gotoJfcd,robot-3028-qrcode_prepareEditByJF,robot-3028-qrcode_getBusinessIn" +                ",robot-3028-qrcode_checkBeforeCalculate,robot-3028-qrcode_saveByJF,robot-3028-qrcode_getBusinessIn_alipay,robot-3028-qrcode_editFeeInfor,robot-3028-qrcode_editPayFeeByWeChat,robot-3028-qrcode_saveByWeChat,robot-3028-qrcode_save";		} else {					return  "robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-bj-editIDCardCheck,robot-3028-apply-selectIsNetProp,robot-3028-apply-saveCheckCode,robot-3028-qrcode_editCheckFlag,robot-3028-qrcode_gotoJfcd,robot-3028-qrcode_prepareEditByJF,robot-3028-qrcode_getBusinessIn" +",robot-3028-qrcode_checkBeforeCalculate,robot-3028-qrcode_saveByJF,robot-3028-qrcode_getBusinessIn_alipay,robot-3028-qrcode_editFeeInfor,robot-3028-qrcode_editPayFeeByWeChat,robot-3028-qrcode_saveByWeChat,robot-3028-qrcode_save";		}}    else {              return "robot-3028-qrcode_login,robot-3028-qrcode_editCheckFlag,robot-3028-qrcode_gotoJfcd,robot-3028-qrcode_prepareEditByJF,robot-3028-qrcode_getBusinessIn" +                ",robot-3028-qrcode_checkBeforeCalculate,robot-3028-qrcode_saveByJF,robot-3028-qrcode_getBusinessIn_alipay,robot-3028-qrcode_editFeeInfor,robot-3028-qrcode_editPayFeeByWeChat,robot-3028-qrcode_saveByWeChat,robot-3028-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3028', 'bohai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-渤海-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3028-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3028-qrcode_query_editCheckFlag,robot-3028-qrcode_query_gotoJfcd,robot-3028-qrcode_query_prepareEditByJF" +                ",robot-3028-qrcode_query_editMainInfor,robot-3028-qrcode_query_getBusinessIn,robot-3028-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ" +            ",robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind,robot-3028-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3028', 'bohai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-渤海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-IdCarChekc" //申请验证码    else{        return"robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-editIDCardCheck,robot-3028-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3028', 'bohai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-渤海-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectPolicyefc,robot-3028-selectPolicybiz,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ" +            ",robot-3028-showCitemCarQ,robot-3028-showCinsuredQ,robot-3028-showCitemKindCI,robot-3028-browseProposalS,robot-3028-showCitemCarS" +            ",robot-3028-showCinsuredS,robot-3028-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3028', 'bohai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3028-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectProposalQ,robot-3028-selectProposalS,robot-3028-browseProposalQ,robot-3028-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3028', 'bohai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-渤海-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-bj-editIDCardCheck,robot-3028-apply-bj-IdCarChekc";    } else {        s = "robot-3028-qrcode_login,robot-3028-qrcode_editCheckFlag,robot-3028-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3028', 'bohai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi渤海报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3028_ask_charge,edi_3028_noMotor_quote"	} else {		return "edi_3028_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3028', 'bohai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-渤海-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3028_ask_charge,edi_3028_noMotor_quote,edi_3028_askInsure,edi_3028_uploadImg,edi_3028_submitInsure,edi_3028_noMotor_submit" 	  	} else {		return "edi_3028_ask_chargeold,edi_3028_askInsure,edi_3028_uploadImg,edi_3028_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3028', 'bohai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-渤海-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3028_ask_charge,edi_3028_noMotor_quote,edi_3028_askInsure,edi_3028_uploadImg" 	} else {		return "edi_3028_ask_chargeold,edi_3028_askInsure,edi_3028_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3028', 'bohai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-渤海-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3028_efc_policyinfo,edi_3028_biz_policyinfo,edi_3028_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3028', 'bohai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-渤海-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3028_efc_insurequery,edi_3028_biz_insurequery,edi_3028_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3028', 'bohai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京渤海短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-bj-editIDCardCheck,robot-3028-apply-saveCheckCode,robot-3028-apply-selectIsNetProp";    } else {        s = "robot-3028-qrcode_login,robot-3028-apply-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3028', 'bohai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-渤海-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3028-bj-qrcode_login,robot-3028-apply-bj-prepare,robot-3028-apply-select-jq,robot-3028-apply-select-sy,robot-3028-apply-bj-editIDCardCheck,robot-3028-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3028', 'bohai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-渤海-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3028_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3028', 'bohai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi渤海北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3028_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3028', 'bohai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi渤海北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3028_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3028', 'bohai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-渤海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3028-login,robot-3028-prepareQueryCode,robot-3028-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2024', 'dajia', '15', '6', 'pro', 'other', b'1', '{}', '精灵-大家-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-IdCarChekc" //申请验证码    else{        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2024', 'dajia', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-大家-报价', 'def getTemplateGroup(dataSource) {    return "edi-2024-queryCar,edi-2024-xbQuery,edi-2024-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2024', 'dajia', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-大家-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2024-queryCar,edi-2024-xbQuery,edi-2024-askCharge,edi-2024-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2024', 'dajia', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-大家-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2024-queryCar,edi-2024-xbQuery,edi-2024-askCharge,edi-2024-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2024', 'dajia', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-大家-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2024-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2024', 'dajia', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-大家-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2024-login,robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-2024-login,robot-2024-ObtainConfig,robot-2024-checkInsurePerson,robot-2024-changePerson,robot-2024-checkInsuredPerson,robot-2024-changePerson,robot-2024-prepareEdit," +                    "robot-2024-prepareQueryCode,robot-2024-selectProposalCar,robot-2024-browseProposalCar,robot-2024-browseProposalCarefc,robot-2024-selectRenewalPolicyNo"        }else{            s = "robot-2024-login,robot-2024-ObtainConfig,robot-2024-checkInsurePerson,robot-2024-changePerson,robot-2024-checkInsuredPerson,robot-2024-changePerson,robot-2024-prepareEdit," +                    "robot-2024-prepareQueryCode,robot-2024-browseProposalCar,robot-2024-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2024-VehicleModelList" //上海车型查询        }        s += ",robot-2024-queryPrepare,robot-2024-vehicleQuery,robot-2024-queryTaxAbateForPlat,robot-2024-calActualValue,robot-2024-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2024-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-2024-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-nomotor-unitedSaleEdit,robot-2024-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-2024-login,robot_2024_bj_initData,robot_2024_bj_queryModel,robot_2024_bj_getSaleTaxInfo,robot_2024_bj_getRealValue,robot_2024_bj_getPersonData,robot_2024_bj_addPersonData,robot_2024_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2024', 'dajia', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-大家-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2024-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2024-ObtainConfig,robot-2024-selectRenewal,robot-2024-editCengage,robot-2024-editCitemCar,robot-2024-editCinsured,robot-2024-renewalPolicy,robot-2024-renewalPolicyCI,robot-2024-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2024-VehicleModelList" //上海车型查询        }        s += ",robot-2024-vehicleQueryXB,robot-2024-queryTaxAbateForPlat,robot-2024-calActualValue,robot-2024-editCitemKind,robot-2024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2024-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-refreshPlanByTimes,robot-2024-insert"            }else{                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2024-getMaxCsellFee,robot-2024-getPrpCseller,robot-2024-getPrpCsellerCI,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            s += ",robot-2024-getMaxCsellFee,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"        }    }else{        s +=",robot-2024-ObtainConfig,robot-2024-checkInsurePerson,robot-2024-changePerson,robot-2024-checkInsuredPerson,robot-2024-changePerson,robot-2024-prepareEdit,robot-2024-selectRenewalPolicyNo,robot-2024-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2024-VehicleModelList" //上海车型查询        }        s += ",robot-2024-queryPrepare,robot-2024-vehicleQuery,robot-2024-queryTaxAbateForPlat,robot-2024-calActualValue,robot-2024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2024-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-2024-calAnciInfo,robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            }else{                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-nomotor-unitedSaleEdit,robot-2024-nomotor-saveUnitedSale,robot-2024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2024-getMaxCsellFee,robot-2024-getPrpCseller,robot-2024-getPrpCsellerCI,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            s += ",robot-2024-getMaxCsellFee,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-nomotor-unitedSaleEdit,robot-2024-nomotor-saveUnitedSale,robot-2024-insert"        }         if("1211000000".equals(orgId)){            s = "robot-2024-login,robot_2024_bj_initData,robot_2024_bj_queryModel,robot_2024_bj_getSaleTaxInfo,robot_2024_bj_getRealValue,robot_2024_bj_getPersonData,robot_2024_bj_addPersonData,robot_2024_bj_askCharge,robot_2024_bj_queryPayForXSFY,robot_2024_bj_getCagentCI,robot_2024_bj_getCagent,robot_2024_bj_queryPayForXSFY_req,robot_2024_bj_queryIlogEngage,robot_2024_bj_insureRefrenshPlan,robot_2024_bj_insure4S,robot-2024-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2024', 'dajia', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-大家-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ" +            ",robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind,robot-2024-nomotor-query,robot-2024-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2024', 'dajia', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-大家-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ" +            ",robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind,robot-2024-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2024', 'dajia', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-大家-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ" +            ",robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind,robot-2024-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2024', 'dajia', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "大家财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-大家-电销', 'def getTemplateGroup(dataSource){    return "robot-2024-pureESale_Login,robot-2024-pureESale_Welcome,robot-2024-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2024', 'dajia', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大家续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-2024-login,robot-2024-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2024', 'dajia', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-大家-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2024-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2024-ObtainConfig,robot-2024-selectRenewal,robot-2024-editCengage,robot-2024-editCitemCar,robot-2024-editCinsured,robot-2024-renewalPolicy,robot-2024-renewalPolicyCI,robot-2024-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2024-VehicleModelList" //上海车型查询        }        s += ",robot-2024-vehicleQueryXB,robot-2024-queryTaxAbateForPlat,robot-2024-calActualValue,robot-2024-editCitemKind,robot-2024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-2024-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-refreshPlanByTimes,robot-2024-insert"            }else{                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2024-calAnciInfo,robot-2024-getMaxCsellFee,robot-2024-getPrpCseller,robot-2024-getPrpCsellerCI,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            s += ",robot-2024-getMaxCsellFee,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"        }    }else{        s += ",robot-2024-ObtainConfig,robot-2024-checkInsurePerson,robot-2024-changePerson,robot-2024-checkInsuredPerson,robot-2024-changePerson,robot-2024-prepareEdit,robot-2024-editCengage,robot-2024-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-2024-queryVehiclePMCheck,robot-2024-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-2024-VehicleModelList" //上海车型查询        }        s += ",robot-2024-queryPrepare,robot-2024-vehicleQuery,robot-2024-queryTaxAbateForPlat,robot-2024-calActualValue,robot-2024-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2024-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-2024-calAnciInfo,robot-2024-queryPayFor,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            }else{                s += ",robot-2024-calAnciInfo,robot-2024-checkAgentType,robot-2024-queryPayForSCMS,robot-2024-getCagent,robot-2024-getCagentCI,robot-2024-refreshPlanByTimes,robot-2024-nomotor-unitedSaleEdit,robot-2024-nomotor-saveUnitedSale,robot-2024-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2024-calAnciInfo,robot-2024-getMaxCsellFee,robot-2024-getPrpCseller,robot-2024-getPrpCsellerCI,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-insert"            s += ",robot-2024-getMaxCsellFee,robot-2024-queryPayForSCMS,robot-2024-refreshPlanByTimes,robot-2024-nomotor-unitedSaleEdit,robot-2024-nomotor-saveUnitedSale,robot-2024-insert"        }    }    s += ",robot-2024-checkRiskCode,robot-2024-editMainUwtFlag,robot-2024-editSubmitUndwrt,robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-showUndwrtMsgQ,robot-2024-showUndwrtMsgS"+            ",robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ,robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind,robot-2024-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-2024-login,robot_2024_bj_initData,robot_2024_bj_queryModel,robot_2024_bj_getSaleTaxInfo,robot_2024_bj_getRealValue,robot_2024_bj_getPersonData,robot_2024_bj_addPersonData,robot_2024_bj_askCharge,robot_2024_bj_queryPayForXSFY,robot_2024_bj_getCagentCI,robot_2024_bj_getCagent,robot_2024_bj_queryPayForXSFY_req,robot_2024_bj_queryIlogEngage,robot_2024_bj_insureRefrenshPlan,robot_2024_bj_insure4S,robot-2024-uploadImage,robot_2024_bj_autoInsure,robot_2024_bj_showUndwrtMsgQ,robot_2024_bj_showUndwrtMsgS";       s += ",robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ,robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +                ",robot-2024-showCinsuredS,robot-2024-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2024', 'dajia', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大家-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2024-qrcode_login,robot-2024-qrcode_printTwoBarCodeServlet,robot-2024-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2024-qrcode_login,robot-2024-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-2024-qrcode_login,robot-2024-qrcode_editCheckFlag,robot-2024-qrcode_gotoJfcd,robot-2024-qrcode_prepareEditByJF,robot-2024-qrcode_getBusinessIn" +                ",robot-2024-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2024-qrcode_login,robot-2024-qrcode_editCheckFlag,robot-2024-qrcode_gotoJfcd,robot-2024-qrcode_prepareEditByJF,robot-2024-qrcode_getBusinessIn" +                ",robot-2024-qrcode_checkBeforeCalculate,robot-2024-qrcode_saveByJF,robot-2024-qrcode_getBusinessIn_alipay,robot-2024-qrcode_editFeeInfor,robot-2024-qrcode_editPayFeeByWeChat,robot-2024-qrcode_saveByWeChat,robot-2024-qrcode_save";		} else {					return  "robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-bj-editIDCardCheck,robot-2024-apply-selectIsNetProp,robot-2024-apply-saveCheckCode,robot-2024-qrcode_editCheckFlag,robot-2024-qrcode_gotoJfcd,robot-2024-qrcode_prepareEditByJF,robot-2024-qrcode_getBusinessIn" +",robot-2024-qrcode_checkBeforeCalculate,robot-2024-qrcode_saveByJF,robot-2024-qrcode_getBusinessIn_alipay,robot-2024-qrcode_editFeeInfor,robot-2024-qrcode_editPayFeeByWeChat,robot-2024-qrcode_saveByWeChat,robot-2024-qrcode_save";		}}    else {              return "robot-2024-qrcode_login,robot-2024-qrcode_editCheckFlag,robot-2024-qrcode_gotoJfcd,robot-2024-qrcode_prepareEditByJF,robot-2024-qrcode_getBusinessIn" +                ",robot-2024-qrcode_checkBeforeCalculate,robot-2024-qrcode_saveByJF,robot-2024-qrcode_getBusinessIn_alipay,robot-2024-qrcode_editFeeInfor,robot-2024-qrcode_editPayFeeByWeChat,robot-2024-qrcode_saveByWeChat,robot-2024-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2024', 'dajia', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-大家-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2024-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2024-qrcode_query_editCheckFlag,robot-2024-qrcode_query_gotoJfcd,robot-2024-qrcode_query_prepareEditByJF" +                ",robot-2024-qrcode_query_editMainInfor,robot-2024-qrcode_query_getBusinessIn,robot-2024-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ" +            ",robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind,robot-2024-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2024', 'dajia', '15', '6', 'pro', 'other', b'1', '{}', '精灵-大家-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-IdCarChekc" //申请验证码    else{        return"robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-editIDCardCheck,robot-2024-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2024', 'dajia', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-大家-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectPolicyefc,robot-2024-selectPolicybiz,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ" +            ",robot-2024-showCitemCarQ,robot-2024-showCinsuredQ,robot-2024-showCitemKindCI,robot-2024-browseProposalS,robot-2024-showCitemCarS" +            ",robot-2024-showCinsuredS,robot-2024-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2024', 'dajia', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2024-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectProposalQ,robot-2024-selectProposalS,robot-2024-browseProposalQ,robot-2024-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2024', 'dajia', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大家-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-bj-editIDCardCheck,robot-2024-apply-bj-IdCarChekc";    } else {        s = "robot-2024-qrcode_login,robot-2024-qrcode_editCheckFlag,robot-2024-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2024', 'dajia', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi大家报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2024_ask_charge,edi_2024_noMotor_quote"	} else {		return "edi_2024_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2024', 'dajia', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-大家-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2024_ask_charge,edi_2024_noMotor_quote,edi_2024_askInsure,edi_2024_uploadImg,edi_2024_submitInsure,edi_2024_noMotor_submit" 	  	} else {		return "edi_2024_ask_chargeold,edi_2024_askInsure,edi_2024_uploadImg,edi_2024_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2024', 'dajia', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-大家-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2024_ask_charge,edi_2024_noMotor_quote,edi_2024_askInsure,edi_2024_uploadImg" 	} else {		return "edi_2024_ask_chargeold,edi_2024_askInsure,edi_2024_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2024', 'dajia', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-大家-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2024_efc_policyinfo,edi_2024_biz_policyinfo,edi_2024_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2024', 'dajia', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-大家-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2024_efc_insurequery,edi_2024_biz_insurequery,edi_2024_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2024', 'dajia', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京大家短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-bj-editIDCardCheck,robot-2024-apply-saveCheckCode,robot-2024-apply-selectIsNetProp";    } else {        s = "robot-2024-qrcode_login,robot-2024-apply-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2024', 'dajia', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大家-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-2024-bj-qrcode_login,robot-2024-apply-bj-prepare,robot-2024-apply-select-jq,robot-2024-apply-select-sy,robot-2024-apply-bj-editIDCardCheck,robot-2024-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2024', 'dajia', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-大家-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2024_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2024', 'dajia', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi大家北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2024_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2024', 'dajia', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi大家北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2024_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2024', 'dajia', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-大家-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2024-login,robot-2024-prepareQueryCode,robot-2024-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3033', 'fubang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-富邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-IdCarChekc" //申请验证码    else{        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3033', 'fubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-富邦-报价', 'def getTemplateGroup(dataSource) {    return "edi-3033-queryCar,edi-3033-xbQuery,edi-3033-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3033', 'fubang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-富邦-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3033-queryCar,edi-3033-xbQuery,edi-3033-askCharge,edi-3033-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3033', 'fubang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-富邦-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3033-queryCar,edi-3033-xbQuery,edi-3033-askCharge,edi-3033-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3033', 'fubang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-富邦-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3033-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3033', 'fubang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-富邦-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3033-login,robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3033-login,robot-3033-ObtainConfig,robot-3033-checkInsurePerson,robot-3033-changePerson,robot-3033-checkInsuredPerson,robot-3033-changePerson,robot-3033-prepareEdit," +                    "robot-3033-prepareQueryCode,robot-3033-selectProposalCar,robot-3033-browseProposalCar,robot-3033-browseProposalCarefc,robot-3033-selectRenewalPolicyNo"        }else{            s = "robot-3033-login,robot-3033-ObtainConfig,robot-3033-checkInsurePerson,robot-3033-changePerson,robot-3033-checkInsuredPerson,robot-3033-changePerson,robot-3033-prepareEdit," +                    "robot-3033-prepareQueryCode,robot-3033-browseProposalCar,robot-3033-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3033-VehicleModelList" //上海车型查询        }        s += ",robot-3033-queryPrepare,robot-3033-vehicleQuery,robot-3033-queryTaxAbateForPlat,robot-3033-calActualValue,robot-3033-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3033-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3033-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-nomotor-unitedSaleEdit,robot-3033-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3033-login,robot_3033_bj_initData,robot_3033_bj_queryModel,robot_3033_bj_getSaleTaxInfo,robot_3033_bj_getRealValue,robot_3033_bj_getPersonData,robot_3033_bj_addPersonData,robot_3033_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3033', 'fubang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-富邦-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3033-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3033-ObtainConfig,robot-3033-selectRenewal,robot-3033-editCengage,robot-3033-editCitemCar,robot-3033-editCinsured,robot-3033-renewalPolicy,robot-3033-renewalPolicyCI,robot-3033-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3033-VehicleModelList" //上海车型查询        }        s += ",robot-3033-vehicleQueryXB,robot-3033-queryTaxAbateForPlat,robot-3033-calActualValue,robot-3033-editCitemKind,robot-3033-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3033-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-refreshPlanByTimes,robot-3033-insert"            }else{                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3033-getMaxCsellFee,robot-3033-getPrpCseller,robot-3033-getPrpCsellerCI,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            s += ",robot-3033-getMaxCsellFee,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"        }    }else{        s +=",robot-3033-ObtainConfig,robot-3033-checkInsurePerson,robot-3033-changePerson,robot-3033-checkInsuredPerson,robot-3033-changePerson,robot-3033-prepareEdit,robot-3033-selectRenewalPolicyNo,robot-3033-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3033-VehicleModelList" //上海车型查询        }        s += ",robot-3033-queryPrepare,robot-3033-vehicleQuery,robot-3033-queryTaxAbateForPlat,robot-3033-calActualValue,robot-3033-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3033-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3033-calAnciInfo,robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            }else{                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-nomotor-unitedSaleEdit,robot-3033-nomotor-saveUnitedSale,robot-3033-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3033-getMaxCsellFee,robot-3033-getPrpCseller,robot-3033-getPrpCsellerCI,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            s += ",robot-3033-getMaxCsellFee,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-nomotor-unitedSaleEdit,robot-3033-nomotor-saveUnitedSale,robot-3033-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3033-login,robot_3033_bj_initData,robot_3033_bj_queryModel,robot_3033_bj_getSaleTaxInfo,robot_3033_bj_getRealValue,robot_3033_bj_getPersonData,robot_3033_bj_addPersonData,robot_3033_bj_askCharge,robot_3033_bj_queryPayForXSFY,robot_3033_bj_getCagentCI,robot_3033_bj_getCagent,robot_3033_bj_queryPayForXSFY_req,robot_3033_bj_queryIlogEngage,robot_3033_bj_insureRefrenshPlan,robot_3033_bj_insure4S,robot-3033-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3033', 'fubang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-富邦-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ" +            ",robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind,robot-3033-nomotor-query,robot-3033-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3033', 'fubang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-富邦-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ" +            ",robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind,robot-3033-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3033', 'fubang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-富邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ" +            ",robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind,robot-3033-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3033', 'fubang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "富邦财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-富邦-电销', 'def getTemplateGroup(dataSource){    return "robot-3033-pureESale_Login,robot-3033-pureESale_Welcome,robot-3033-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3033', 'fubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3033-login,robot-3033-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3033', 'fubang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-富邦-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3033-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3033-ObtainConfig,robot-3033-selectRenewal,robot-3033-editCengage,robot-3033-editCitemCar,robot-3033-editCinsured,robot-3033-renewalPolicy,robot-3033-renewalPolicyCI,robot-3033-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3033-VehicleModelList" //上海车型查询        }        s += ",robot-3033-vehicleQueryXB,robot-3033-queryTaxAbateForPlat,robot-3033-calActualValue,robot-3033-editCitemKind,robot-3033-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3033-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-refreshPlanByTimes,robot-3033-insert"            }else{                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3033-calAnciInfo,robot-3033-getMaxCsellFee,robot-3033-getPrpCseller,robot-3033-getPrpCsellerCI,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            s += ",robot-3033-getMaxCsellFee,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"        }    }else{        s += ",robot-3033-ObtainConfig,robot-3033-checkInsurePerson,robot-3033-changePerson,robot-3033-checkInsuredPerson,robot-3033-changePerson,robot-3033-prepareEdit,robot-3033-editCengage,robot-3033-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3033-queryVehiclePMCheck,robot-3033-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3033-VehicleModelList" //上海车型查询        }        s += ",robot-3033-queryPrepare,robot-3033-vehicleQuery,robot-3033-queryTaxAbateForPlat,robot-3033-calActualValue,robot-3033-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3033-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3033-calAnciInfo,robot-3033-queryPayFor,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            }else{                s += ",robot-3033-calAnciInfo,robot-3033-checkAgentType,robot-3033-queryPayForSCMS,robot-3033-getCagent,robot-3033-getCagentCI,robot-3033-refreshPlanByTimes,robot-3033-nomotor-unitedSaleEdit,robot-3033-nomotor-saveUnitedSale,robot-3033-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3033-calAnciInfo,robot-3033-getMaxCsellFee,robot-3033-getPrpCseller,robot-3033-getPrpCsellerCI,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-insert"            s += ",robot-3033-getMaxCsellFee,robot-3033-queryPayForSCMS,robot-3033-refreshPlanByTimes,robot-3033-nomotor-unitedSaleEdit,robot-3033-nomotor-saveUnitedSale,robot-3033-insert"        }    }    s += ",robot-3033-checkRiskCode,robot-3033-editMainUwtFlag,robot-3033-editSubmitUndwrt,robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-showUndwrtMsgQ,robot-3033-showUndwrtMsgS"+            ",robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ,robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind,robot-3033-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3033-login,robot_3033_bj_initData,robot_3033_bj_queryModel,robot_3033_bj_getSaleTaxInfo,robot_3033_bj_getRealValue,robot_3033_bj_getPersonData,robot_3033_bj_addPersonData,robot_3033_bj_askCharge,robot_3033_bj_queryPayForXSFY,robot_3033_bj_getCagentCI,robot_3033_bj_getCagent,robot_3033_bj_queryPayForXSFY_req,robot_3033_bj_queryIlogEngage,robot_3033_bj_insureRefrenshPlan,robot_3033_bj_insure4S,robot-3033-uploadImage,robot_3033_bj_autoInsure,robot_3033_bj_showUndwrtMsgQ,robot_3033_bj_showUndwrtMsgS";       s += ",robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ,robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +                ",robot-3033-showCinsuredS,robot-3033-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3033', 'fubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3033-qrcode_login,robot-3033-qrcode_printTwoBarCodeServlet,robot-3033-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3033-qrcode_login,robot-3033-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3033-qrcode_login,robot-3033-qrcode_editCheckFlag,robot-3033-qrcode_gotoJfcd,robot-3033-qrcode_prepareEditByJF,robot-3033-qrcode_getBusinessIn" +                ",robot-3033-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3033-qrcode_login,robot-3033-qrcode_editCheckFlag,robot-3033-qrcode_gotoJfcd,robot-3033-qrcode_prepareEditByJF,robot-3033-qrcode_getBusinessIn" +                ",robot-3033-qrcode_checkBeforeCalculate,robot-3033-qrcode_saveByJF,robot-3033-qrcode_getBusinessIn_alipay,robot-3033-qrcode_editFeeInfor,robot-3033-qrcode_editPayFeeByWeChat,robot-3033-qrcode_saveByWeChat,robot-3033-qrcode_save";		} else {					return  "robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-bj-editIDCardCheck,robot-3033-apply-selectIsNetProp,robot-3033-apply-saveCheckCode,robot-3033-qrcode_editCheckFlag,robot-3033-qrcode_gotoJfcd,robot-3033-qrcode_prepareEditByJF,robot-3033-qrcode_getBusinessIn" +",robot-3033-qrcode_checkBeforeCalculate,robot-3033-qrcode_saveByJF,robot-3033-qrcode_getBusinessIn_alipay,robot-3033-qrcode_editFeeInfor,robot-3033-qrcode_editPayFeeByWeChat,robot-3033-qrcode_saveByWeChat,robot-3033-qrcode_save";		}}    else {              return "robot-3033-qrcode_login,robot-3033-qrcode_editCheckFlag,robot-3033-qrcode_gotoJfcd,robot-3033-qrcode_prepareEditByJF,robot-3033-qrcode_getBusinessIn" +                ",robot-3033-qrcode_checkBeforeCalculate,robot-3033-qrcode_saveByJF,robot-3033-qrcode_getBusinessIn_alipay,robot-3033-qrcode_editFeeInfor,robot-3033-qrcode_editPayFeeByWeChat,robot-3033-qrcode_saveByWeChat,robot-3033-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3033', 'fubang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-富邦-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3033-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3033-qrcode_query_editCheckFlag,robot-3033-qrcode_query_gotoJfcd,robot-3033-qrcode_query_prepareEditByJF" +                ",robot-3033-qrcode_query_editMainInfor,robot-3033-qrcode_query_getBusinessIn,robot-3033-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ" +            ",robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind,robot-3033-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3033', 'fubang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-富邦-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-IdCarChekc" //申请验证码    else{        return"robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-editIDCardCheck,robot-3033-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3033', 'fubang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-富邦-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectPolicyefc,robot-3033-selectPolicybiz,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ" +            ",robot-3033-showCitemCarQ,robot-3033-showCinsuredQ,robot-3033-showCitemKindCI,robot-3033-browseProposalS,robot-3033-showCitemCarS" +            ",robot-3033-showCinsuredS,robot-3033-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3033', 'fubang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3033-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectProposalQ,robot-3033-selectProposalS,robot-3033-browseProposalQ,robot-3033-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3033', 'fubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-bj-editIDCardCheck,robot-3033-apply-bj-IdCarChekc";    } else {        s = "robot-3033-qrcode_login,robot-3033-qrcode_editCheckFlag,robot-3033-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3033', 'fubang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi富邦报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3033_ask_charge,edi_3033_noMotor_quote"	} else {		return "edi_3033_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3033', 'fubang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-富邦-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3033_ask_charge,edi_3033_noMotor_quote,edi_3033_askInsure,edi_3033_uploadImg,edi_3033_submitInsure,edi_3033_noMotor_submit" 	  	} else {		return "edi_3033_ask_chargeold,edi_3033_askInsure,edi_3033_uploadImg,edi_3033_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3033', 'fubang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-富邦-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3033_ask_charge,edi_3033_noMotor_quote,edi_3033_askInsure,edi_3033_uploadImg" 	} else {		return "edi_3033_ask_chargeold,edi_3033_askInsure,edi_3033_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3033', 'fubang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-富邦-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3033_efc_policyinfo,edi_3033_biz_policyinfo,edi_3033_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3033', 'fubang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-富邦-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3033_efc_insurequery,edi_3033_biz_insurequery,edi_3033_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3033', 'fubang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京富邦短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-bj-editIDCardCheck,robot-3033-apply-saveCheckCode,robot-3033-apply-selectIsNetProp";    } else {        s = "robot-3033-qrcode_login,robot-3033-apply-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3033', 'fubang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-富邦-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3033-bj-qrcode_login,robot-3033-apply-bj-prepare,robot-3033-apply-select-jq,robot-3033-apply-select-sy,robot-3033-apply-bj-editIDCardCheck,robot-3033-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3033', 'fubang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-富邦-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3033_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3033', 'fubang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi富邦北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3033_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3033', 'fubang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi富邦北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3033_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3033', 'fubang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-富邦-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3033-login,robot-3033-prepareQueryCode,robot-3033-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3069', 'zheshang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-浙商-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-IdCarChekc" //申请验证码    else{        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3069', 'zheshang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-浙商-报价', 'def getTemplateGroup(dataSource) {    return "edi-3069-queryCar,edi-3069-xbQuery,edi-3069-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3069', 'zheshang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-浙商-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3069-queryCar,edi-3069-xbQuery,edi-3069-askCharge,edi-3069-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3069', 'zheshang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-浙商-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3069-queryCar,edi-3069-xbQuery,edi-3069-askCharge,edi-3069-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3069', 'zheshang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-浙商-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3069-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3069', 'zheshang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-浙商-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3069-login,robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3069-login,robot-3069-ObtainConfig,robot-3069-checkInsurePerson,robot-3069-changePerson,robot-3069-checkInsuredPerson,robot-3069-changePerson,robot-3069-prepareEdit," +                    "robot-3069-prepareQueryCode,robot-3069-selectProposalCar,robot-3069-browseProposalCar,robot-3069-browseProposalCarefc,robot-3069-selectRenewalPolicyNo"        }else{            s = "robot-3069-login,robot-3069-ObtainConfig,robot-3069-checkInsurePerson,robot-3069-changePerson,robot-3069-checkInsuredPerson,robot-3069-changePerson,robot-3069-prepareEdit," +                    "robot-3069-prepareQueryCode,robot-3069-browseProposalCar,robot-3069-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3069-VehicleModelList" //上海车型查询        }        s += ",robot-3069-queryPrepare,robot-3069-vehicleQuery,robot-3069-queryTaxAbateForPlat,robot-3069-calActualValue,robot-3069-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3069-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3069-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-nomotor-unitedSaleEdit,robot-3069-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3069-login,robot_3069_bj_initData,robot_3069_bj_queryModel,robot_3069_bj_getSaleTaxInfo,robot_3069_bj_getRealValue,robot_3069_bj_getPersonData,robot_3069_bj_addPersonData,robot_3069_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3069', 'zheshang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-浙商-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3069-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3069-ObtainConfig,robot-3069-selectRenewal,robot-3069-editCengage,robot-3069-editCitemCar,robot-3069-editCinsured,robot-3069-renewalPolicy,robot-3069-renewalPolicyCI,robot-3069-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3069-VehicleModelList" //上海车型查询        }        s += ",robot-3069-vehicleQueryXB,robot-3069-queryTaxAbateForPlat,robot-3069-calActualValue,robot-3069-editCitemKind,robot-3069-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3069-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-refreshPlanByTimes,robot-3069-insert"            }else{                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3069-getMaxCsellFee,robot-3069-getPrpCseller,robot-3069-getPrpCsellerCI,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            s += ",robot-3069-getMaxCsellFee,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"        }    }else{        s +=",robot-3069-ObtainConfig,robot-3069-checkInsurePerson,robot-3069-changePerson,robot-3069-checkInsuredPerson,robot-3069-changePerson,robot-3069-prepareEdit,robot-3069-selectRenewalPolicyNo,robot-3069-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3069-VehicleModelList" //上海车型查询        }        s += ",robot-3069-queryPrepare,robot-3069-vehicleQuery,robot-3069-queryTaxAbateForPlat,robot-3069-calActualValue,robot-3069-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3069-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3069-calAnciInfo,robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            }else{                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-nomotor-unitedSaleEdit,robot-3069-nomotor-saveUnitedSale,robot-3069-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3069-getMaxCsellFee,robot-3069-getPrpCseller,robot-3069-getPrpCsellerCI,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            s += ",robot-3069-getMaxCsellFee,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-nomotor-unitedSaleEdit,robot-3069-nomotor-saveUnitedSale,robot-3069-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3069-login,robot_3069_bj_initData,robot_3069_bj_queryModel,robot_3069_bj_getSaleTaxInfo,robot_3069_bj_getRealValue,robot_3069_bj_getPersonData,robot_3069_bj_addPersonData,robot_3069_bj_askCharge,robot_3069_bj_queryPayForXSFY,robot_3069_bj_getCagentCI,robot_3069_bj_getCagent,robot_3069_bj_queryPayForXSFY_req,robot_3069_bj_queryIlogEngage,robot_3069_bj_insureRefrenshPlan,robot_3069_bj_insure4S,robot-3069-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3069', 'zheshang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-浙商-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ" +            ",robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind,robot-3069-nomotor-query,robot-3069-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3069', 'zheshang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-浙商-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ" +            ",robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind,robot-3069-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3069', 'zheshang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-浙商-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ" +            ",robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind,robot-3069-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3069', 'zheshang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "浙商财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-浙商-电销', 'def getTemplateGroup(dataSource){    return "robot-3069-pureESale_Login,robot-3069-pureESale_Welcome,robot-3069-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3069', 'zheshang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-浙商续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3069-login,robot-3069-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3069', 'zheshang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-浙商-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3069-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3069-ObtainConfig,robot-3069-selectRenewal,robot-3069-editCengage,robot-3069-editCitemCar,robot-3069-editCinsured,robot-3069-renewalPolicy,robot-3069-renewalPolicyCI,robot-3069-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3069-VehicleModelList" //上海车型查询        }        s += ",robot-3069-vehicleQueryXB,robot-3069-queryTaxAbateForPlat,robot-3069-calActualValue,robot-3069-editCitemKind,robot-3069-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3069-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-refreshPlanByTimes,robot-3069-insert"            }else{                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3069-calAnciInfo,robot-3069-getMaxCsellFee,robot-3069-getPrpCseller,robot-3069-getPrpCsellerCI,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            s += ",robot-3069-getMaxCsellFee,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"        }    }else{        s += ",robot-3069-ObtainConfig,robot-3069-checkInsurePerson,robot-3069-changePerson,robot-3069-checkInsuredPerson,robot-3069-changePerson,robot-3069-prepareEdit,robot-3069-editCengage,robot-3069-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3069-queryVehiclePMCheck,robot-3069-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3069-VehicleModelList" //上海车型查询        }        s += ",robot-3069-queryPrepare,robot-3069-vehicleQuery,robot-3069-queryTaxAbateForPlat,robot-3069-calActualValue,robot-3069-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3069-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3069-calAnciInfo,robot-3069-queryPayFor,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            }else{                s += ",robot-3069-calAnciInfo,robot-3069-checkAgentType,robot-3069-queryPayForSCMS,robot-3069-getCagent,robot-3069-getCagentCI,robot-3069-refreshPlanByTimes,robot-3069-nomotor-unitedSaleEdit,robot-3069-nomotor-saveUnitedSale,robot-3069-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3069-calAnciInfo,robot-3069-getMaxCsellFee,robot-3069-getPrpCseller,robot-3069-getPrpCsellerCI,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-insert"            s += ",robot-3069-getMaxCsellFee,robot-3069-queryPayForSCMS,robot-3069-refreshPlanByTimes,robot-3069-nomotor-unitedSaleEdit,robot-3069-nomotor-saveUnitedSale,robot-3069-insert"        }    }    s += ",robot-3069-checkRiskCode,robot-3069-editMainUwtFlag,robot-3069-editSubmitUndwrt,robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-showUndwrtMsgQ,robot-3069-showUndwrtMsgS"+            ",robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ,robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind,robot-3069-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3069-login,robot_3069_bj_initData,robot_3069_bj_queryModel,robot_3069_bj_getSaleTaxInfo,robot_3069_bj_getRealValue,robot_3069_bj_getPersonData,robot_3069_bj_addPersonData,robot_3069_bj_askCharge,robot_3069_bj_queryPayForXSFY,robot_3069_bj_getCagentCI,robot_3069_bj_getCagent,robot_3069_bj_queryPayForXSFY_req,robot_3069_bj_queryIlogEngage,robot_3069_bj_insureRefrenshPlan,robot_3069_bj_insure4S,robot-3069-uploadImage,robot_3069_bj_autoInsure,robot_3069_bj_showUndwrtMsgQ,robot_3069_bj_showUndwrtMsgS";       s += ",robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ,robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +                ",robot-3069-showCinsuredS,robot-3069-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3069', 'zheshang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-浙商-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3069-qrcode_login,robot-3069-qrcode_printTwoBarCodeServlet,robot-3069-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3069-qrcode_login,robot-3069-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3069-qrcode_login,robot-3069-qrcode_editCheckFlag,robot-3069-qrcode_gotoJfcd,robot-3069-qrcode_prepareEditByJF,robot-3069-qrcode_getBusinessIn" +                ",robot-3069-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3069-qrcode_login,robot-3069-qrcode_editCheckFlag,robot-3069-qrcode_gotoJfcd,robot-3069-qrcode_prepareEditByJF,robot-3069-qrcode_getBusinessIn" +                ",robot-3069-qrcode_checkBeforeCalculate,robot-3069-qrcode_saveByJF,robot-3069-qrcode_getBusinessIn_alipay,robot-3069-qrcode_editFeeInfor,robot-3069-qrcode_editPayFeeByWeChat,robot-3069-qrcode_saveByWeChat,robot-3069-qrcode_save";		} else {					return  "robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-bj-editIDCardCheck,robot-3069-apply-selectIsNetProp,robot-3069-apply-saveCheckCode,robot-3069-qrcode_editCheckFlag,robot-3069-qrcode_gotoJfcd,robot-3069-qrcode_prepareEditByJF,robot-3069-qrcode_getBusinessIn" +",robot-3069-qrcode_checkBeforeCalculate,robot-3069-qrcode_saveByJF,robot-3069-qrcode_getBusinessIn_alipay,robot-3069-qrcode_editFeeInfor,robot-3069-qrcode_editPayFeeByWeChat,robot-3069-qrcode_saveByWeChat,robot-3069-qrcode_save";		}}    else {              return "robot-3069-qrcode_login,robot-3069-qrcode_editCheckFlag,robot-3069-qrcode_gotoJfcd,robot-3069-qrcode_prepareEditByJF,robot-3069-qrcode_getBusinessIn" +                ",robot-3069-qrcode_checkBeforeCalculate,robot-3069-qrcode_saveByJF,robot-3069-qrcode_getBusinessIn_alipay,robot-3069-qrcode_editFeeInfor,robot-3069-qrcode_editPayFeeByWeChat,robot-3069-qrcode_saveByWeChat,robot-3069-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3069', 'zheshang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-浙商-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3069-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3069-qrcode_query_editCheckFlag,robot-3069-qrcode_query_gotoJfcd,robot-3069-qrcode_query_prepareEditByJF" +                ",robot-3069-qrcode_query_editMainInfor,robot-3069-qrcode_query_getBusinessIn,robot-3069-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ" +            ",robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind,robot-3069-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3069', 'zheshang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-浙商-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-IdCarChekc" //申请验证码    else{        return"robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-editIDCardCheck,robot-3069-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3069', 'zheshang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-浙商-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectPolicyefc,robot-3069-selectPolicybiz,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ" +            ",robot-3069-showCitemCarQ,robot-3069-showCinsuredQ,robot-3069-showCitemKindCI,robot-3069-browseProposalS,robot-3069-showCitemCarS" +            ",robot-3069-showCinsuredS,robot-3069-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3069', 'zheshang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3069-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectProposalQ,robot-3069-selectProposalS,robot-3069-browseProposalQ,robot-3069-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3069', 'zheshang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-浙商-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-bj-editIDCardCheck,robot-3069-apply-bj-IdCarChekc";    } else {        s = "robot-3069-qrcode_login,robot-3069-qrcode_editCheckFlag,robot-3069-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3069', 'zheshang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi浙商报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3069_ask_charge,edi_3069_noMotor_quote"	} else {		return "edi_3069_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3069', 'zheshang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-浙商-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3069_ask_charge,edi_3069_noMotor_quote,edi_3069_askInsure,edi_3069_uploadImg,edi_3069_submitInsure,edi_3069_noMotor_submit" 	  	} else {		return "edi_3069_ask_chargeold,edi_3069_askInsure,edi_3069_uploadImg,edi_3069_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3069', 'zheshang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-浙商-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3069_ask_charge,edi_3069_noMotor_quote,edi_3069_askInsure,edi_3069_uploadImg" 	} else {		return "edi_3069_ask_chargeold,edi_3069_askInsure,edi_3069_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3069', 'zheshang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-浙商-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3069_efc_policyinfo,edi_3069_biz_policyinfo,edi_3069_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3069', 'zheshang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-浙商-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3069_efc_insurequery,edi_3069_biz_insurequery,edi_3069_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3069', 'zheshang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京浙商短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-bj-editIDCardCheck,robot-3069-apply-saveCheckCode,robot-3069-apply-selectIsNetProp";    } else {        s = "robot-3069-qrcode_login,robot-3069-apply-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3069', 'zheshang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-浙商-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3069-bj-qrcode_login,robot-3069-apply-bj-prepare,robot-3069-apply-select-jq,robot-3069-apply-select-sy,robot-3069-apply-bj-editIDCardCheck,robot-3069-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3069', 'zheshang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-浙商-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3069_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3069', 'zheshang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi浙商北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3069_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3069', 'zheshang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi浙商北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3069_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3069', 'zheshang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-浙商-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3069-login,robot-3069-prepareQueryCode,robot-3069-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3050', 'huahai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-IdCarChekc" //申请验证码    else{        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3050', 'huahai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-华海-报价', 'def getTemplateGroup(dataSource) {    return "edi-3050-queryCar,edi-3050-xbQuery,edi-3050-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3050', 'huahai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华海-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3050-queryCar,edi-3050-xbQuery,edi-3050-askCharge,edi-3050-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3050', 'huahai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华海-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3050-queryCar,edi-3050-xbQuery,edi-3050-askCharge,edi-3050-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3050', 'huahai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-华海-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3050-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3050', 'huahai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华海-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3050-login,robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3050-login,robot-3050-ObtainConfig,robot-3050-checkInsurePerson,robot-3050-changePerson,robot-3050-checkInsuredPerson,robot-3050-changePerson,robot-3050-prepareEdit," +                    "robot-3050-prepareQueryCode,robot-3050-selectProposalCar,robot-3050-browseProposalCar,robot-3050-browseProposalCarefc,robot-3050-selectRenewalPolicyNo"        }else{            s = "robot-3050-login,robot-3050-ObtainConfig,robot-3050-checkInsurePerson,robot-3050-changePerson,robot-3050-checkInsuredPerson,robot-3050-changePerson,robot-3050-prepareEdit," +                    "robot-3050-prepareQueryCode,robot-3050-browseProposalCar,robot-3050-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3050-VehicleModelList" //上海车型查询        }        s += ",robot-3050-queryPrepare,robot-3050-vehicleQuery,robot-3050-queryTaxAbateForPlat,robot-3050-calActualValue,robot-3050-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3050-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3050-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-nomotor-unitedSaleEdit,robot-3050-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3050-login,robot_3050_bj_initData,robot_3050_bj_queryModel,robot_3050_bj_getSaleTaxInfo,robot_3050_bj_getRealValue,robot_3050_bj_getPersonData,robot_3050_bj_addPersonData,robot_3050_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3050', 'huahai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-华海-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3050-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3050-ObtainConfig,robot-3050-selectRenewal,robot-3050-editCengage,robot-3050-editCitemCar,robot-3050-editCinsured,robot-3050-renewalPolicy,robot-3050-renewalPolicyCI,robot-3050-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3050-VehicleModelList" //上海车型查询        }        s += ",robot-3050-vehicleQueryXB,robot-3050-queryTaxAbateForPlat,robot-3050-calActualValue,robot-3050-editCitemKind,robot-3050-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3050-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-refreshPlanByTimes,robot-3050-insert"            }else{                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3050-getMaxCsellFee,robot-3050-getPrpCseller,robot-3050-getPrpCsellerCI,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            s += ",robot-3050-getMaxCsellFee,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"        }    }else{        s +=",robot-3050-ObtainConfig,robot-3050-checkInsurePerson,robot-3050-changePerson,robot-3050-checkInsuredPerson,robot-3050-changePerson,robot-3050-prepareEdit,robot-3050-selectRenewalPolicyNo,robot-3050-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3050-VehicleModelList" //上海车型查询        }        s += ",robot-3050-queryPrepare,robot-3050-vehicleQuery,robot-3050-queryTaxAbateForPlat,robot-3050-calActualValue,robot-3050-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3050-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3050-calAnciInfo,robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            }else{                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-nomotor-unitedSaleEdit,robot-3050-nomotor-saveUnitedSale,robot-3050-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3050-getMaxCsellFee,robot-3050-getPrpCseller,robot-3050-getPrpCsellerCI,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            s += ",robot-3050-getMaxCsellFee,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-nomotor-unitedSaleEdit,robot-3050-nomotor-saveUnitedSale,robot-3050-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3050-login,robot_3050_bj_initData,robot_3050_bj_queryModel,robot_3050_bj_getSaleTaxInfo,robot_3050_bj_getRealValue,robot_3050_bj_getPersonData,robot_3050_bj_addPersonData,robot_3050_bj_askCharge,robot_3050_bj_queryPayForXSFY,robot_3050_bj_getCagentCI,robot_3050_bj_getCagent,robot_3050_bj_queryPayForXSFY_req,robot_3050_bj_queryIlogEngage,robot_3050_bj_insureRefrenshPlan,robot_3050_bj_insure4S,robot-3050-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3050', 'huahai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-华海-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ" +            ",robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind,robot-3050-nomotor-query,robot-3050-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3050', 'huahai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-华海-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ" +            ",robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind,robot-3050-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3050', 'huahai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ" +            ",robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind,robot-3050-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3050', 'huahai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "华海财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-华海-电销', 'def getTemplateGroup(dataSource){    return "robot-3050-pureESale_Login,robot-3050-pureESale_Welcome,robot-3050-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3050', 'huahai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华海续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3050-login,robot-3050-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3050', 'huahai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-华海-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3050-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3050-ObtainConfig,robot-3050-selectRenewal,robot-3050-editCengage,robot-3050-editCitemCar,robot-3050-editCinsured,robot-3050-renewalPolicy,robot-3050-renewalPolicyCI,robot-3050-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3050-VehicleModelList" //上海车型查询        }        s += ",robot-3050-vehicleQueryXB,robot-3050-queryTaxAbateForPlat,robot-3050-calActualValue,robot-3050-editCitemKind,robot-3050-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3050-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-refreshPlanByTimes,robot-3050-insert"            }else{                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3050-calAnciInfo,robot-3050-getMaxCsellFee,robot-3050-getPrpCseller,robot-3050-getPrpCsellerCI,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            s += ",robot-3050-getMaxCsellFee,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"        }    }else{        s += ",robot-3050-ObtainConfig,robot-3050-checkInsurePerson,robot-3050-changePerson,robot-3050-checkInsuredPerson,robot-3050-changePerson,robot-3050-prepareEdit,robot-3050-editCengage,robot-3050-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3050-queryVehiclePMCheck,robot-3050-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3050-VehicleModelList" //上海车型查询        }        s += ",robot-3050-queryPrepare,robot-3050-vehicleQuery,robot-3050-queryTaxAbateForPlat,robot-3050-calActualValue,robot-3050-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3050-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3050-calAnciInfo,robot-3050-queryPayFor,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            }else{                s += ",robot-3050-calAnciInfo,robot-3050-checkAgentType,robot-3050-queryPayForSCMS,robot-3050-getCagent,robot-3050-getCagentCI,robot-3050-refreshPlanByTimes,robot-3050-nomotor-unitedSaleEdit,robot-3050-nomotor-saveUnitedSale,robot-3050-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3050-calAnciInfo,robot-3050-getMaxCsellFee,robot-3050-getPrpCseller,robot-3050-getPrpCsellerCI,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-insert"            s += ",robot-3050-getMaxCsellFee,robot-3050-queryPayForSCMS,robot-3050-refreshPlanByTimes,robot-3050-nomotor-unitedSaleEdit,robot-3050-nomotor-saveUnitedSale,robot-3050-insert"        }    }    s += ",robot-3050-checkRiskCode,robot-3050-editMainUwtFlag,robot-3050-editSubmitUndwrt,robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-showUndwrtMsgQ,robot-3050-showUndwrtMsgS"+            ",robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ,robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind,robot-3050-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3050-login,robot_3050_bj_initData,robot_3050_bj_queryModel,robot_3050_bj_getSaleTaxInfo,robot_3050_bj_getRealValue,robot_3050_bj_getPersonData,robot_3050_bj_addPersonData,robot_3050_bj_askCharge,robot_3050_bj_queryPayForXSFY,robot_3050_bj_getCagentCI,robot_3050_bj_getCagent,robot_3050_bj_queryPayForXSFY_req,robot_3050_bj_queryIlogEngage,robot_3050_bj_insureRefrenshPlan,robot_3050_bj_insure4S,robot-3050-uploadImage,robot_3050_bj_autoInsure,robot_3050_bj_showUndwrtMsgQ,robot_3050_bj_showUndwrtMsgS";       s += ",robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ,robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +                ",robot-3050-showCinsuredS,robot-3050-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3050', 'huahai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华海-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3050-qrcode_login,robot-3050-qrcode_printTwoBarCodeServlet,robot-3050-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3050-qrcode_login,robot-3050-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3050-qrcode_login,robot-3050-qrcode_editCheckFlag,robot-3050-qrcode_gotoJfcd,robot-3050-qrcode_prepareEditByJF,robot-3050-qrcode_getBusinessIn" +                ",robot-3050-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3050-qrcode_login,robot-3050-qrcode_editCheckFlag,robot-3050-qrcode_gotoJfcd,robot-3050-qrcode_prepareEditByJF,robot-3050-qrcode_getBusinessIn" +                ",robot-3050-qrcode_checkBeforeCalculate,robot-3050-qrcode_saveByJF,robot-3050-qrcode_getBusinessIn_alipay,robot-3050-qrcode_editFeeInfor,robot-3050-qrcode_editPayFeeByWeChat,robot-3050-qrcode_saveByWeChat,robot-3050-qrcode_save";		} else {					return  "robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-bj-editIDCardCheck,robot-3050-apply-selectIsNetProp,robot-3050-apply-saveCheckCode,robot-3050-qrcode_editCheckFlag,robot-3050-qrcode_gotoJfcd,robot-3050-qrcode_prepareEditByJF,robot-3050-qrcode_getBusinessIn" +",robot-3050-qrcode_checkBeforeCalculate,robot-3050-qrcode_saveByJF,robot-3050-qrcode_getBusinessIn_alipay,robot-3050-qrcode_editFeeInfor,robot-3050-qrcode_editPayFeeByWeChat,robot-3050-qrcode_saveByWeChat,robot-3050-qrcode_save";		}}    else {              return "robot-3050-qrcode_login,robot-3050-qrcode_editCheckFlag,robot-3050-qrcode_gotoJfcd,robot-3050-qrcode_prepareEditByJF,robot-3050-qrcode_getBusinessIn" +                ",robot-3050-qrcode_checkBeforeCalculate,robot-3050-qrcode_saveByJF,robot-3050-qrcode_getBusinessIn_alipay,robot-3050-qrcode_editFeeInfor,robot-3050-qrcode_editPayFeeByWeChat,robot-3050-qrcode_saveByWeChat,robot-3050-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3050', 'huahai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-华海-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3050-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3050-qrcode_query_editCheckFlag,robot-3050-qrcode_query_gotoJfcd,robot-3050-qrcode_query_prepareEditByJF" +                ",robot-3050-qrcode_query_editMainInfor,robot-3050-qrcode_query_getBusinessIn,robot-3050-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ" +            ",robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind,robot-3050-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3050', 'huahai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-华海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-IdCarChekc" //申请验证码    else{        return"robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-editIDCardCheck,robot-3050-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3050', 'huahai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-华海-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectPolicyefc,robot-3050-selectPolicybiz,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ" +            ",robot-3050-showCitemCarQ,robot-3050-showCinsuredQ,robot-3050-showCitemKindCI,robot-3050-browseProposalS,robot-3050-showCitemCarS" +            ",robot-3050-showCinsuredS,robot-3050-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3050', 'huahai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3050-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectProposalQ,robot-3050-selectProposalS,robot-3050-browseProposalQ,robot-3050-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3050', 'huahai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华海-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-bj-editIDCardCheck,robot-3050-apply-bj-IdCarChekc";    } else {        s = "robot-3050-qrcode_login,robot-3050-qrcode_editCheckFlag,robot-3050-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3050', 'huahai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi华海报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3050_ask_charge,edi_3050_noMotor_quote"	} else {		return "edi_3050_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3050', 'huahai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-华海-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3050_ask_charge,edi_3050_noMotor_quote,edi_3050_askInsure,edi_3050_uploadImg,edi_3050_submitInsure,edi_3050_noMotor_submit" 	  	} else {		return "edi_3050_ask_chargeold,edi_3050_askInsure,edi_3050_uploadImg,edi_3050_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3050', 'huahai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-华海-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3050_ask_charge,edi_3050_noMotor_quote,edi_3050_askInsure,edi_3050_uploadImg" 	} else {		return "edi_3050_ask_chargeold,edi_3050_askInsure,edi_3050_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3050', 'huahai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-华海-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3050_efc_policyinfo,edi_3050_biz_policyinfo,edi_3050_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3050', 'huahai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-华海-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3050_efc_insurequery,edi_3050_biz_insurequery,edi_3050_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3050', 'huahai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京华海短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-bj-editIDCardCheck,robot-3050-apply-saveCheckCode,robot-3050-apply-selectIsNetProp";    } else {        s = "robot-3050-qrcode_login,robot-3050-apply-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3050', 'huahai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-华海-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3050-bj-qrcode_login,robot-3050-apply-bj-prepare,robot-3050-apply-select-jq,robot-3050-apply-select-sy,robot-3050-apply-bj-editIDCardCheck,robot-3050-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3050', 'huahai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-华海-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3050_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3050', 'huahai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华海北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3050_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3050', 'huahai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi华海北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3050_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3050', 'huahai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-华海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3050-login,robot-3050-prepareQueryCode,robot-3050-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3061', 'qianhai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-前海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-IdCarChekc" //申请验证码    else{        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3061', 'qianhai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-前海-报价', 'def getTemplateGroup(dataSource) {    return "edi-3061-queryCar,edi-3061-xbQuery,edi-3061-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3061', 'qianhai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-前海-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3061-queryCar,edi-3061-xbQuery,edi-3061-askCharge,edi-3061-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3061', 'qianhai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-前海-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3061-queryCar,edi-3061-xbQuery,edi-3061-askCharge,edi-3061-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3061', 'qianhai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-前海-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3061-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3061', 'qianhai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-前海-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3061-login,robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3061-login,robot-3061-ObtainConfig,robot-3061-checkInsurePerson,robot-3061-changePerson,robot-3061-checkInsuredPerson,robot-3061-changePerson,robot-3061-prepareEdit," +                    "robot-3061-prepareQueryCode,robot-3061-selectProposalCar,robot-3061-browseProposalCar,robot-3061-browseProposalCarefc,robot-3061-selectRenewalPolicyNo"        }else{            s = "robot-3061-login,robot-3061-ObtainConfig,robot-3061-checkInsurePerson,robot-3061-changePerson,robot-3061-checkInsuredPerson,robot-3061-changePerson,robot-3061-prepareEdit," +                    "robot-3061-prepareQueryCode,robot-3061-browseProposalCar,robot-3061-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3061-VehicleModelList" //上海车型查询        }        s += ",robot-3061-queryPrepare,robot-3061-vehicleQuery,robot-3061-queryTaxAbateForPlat,robot-3061-calActualValue,robot-3061-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3061-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3061-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-nomotor-unitedSaleEdit,robot-3061-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3061-login,robot_3061_bj_initData,robot_3061_bj_queryModel,robot_3061_bj_getSaleTaxInfo,robot_3061_bj_getRealValue,robot_3061_bj_getPersonData,robot_3061_bj_addPersonData,robot_3061_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3061', 'qianhai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-前海-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3061-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3061-ObtainConfig,robot-3061-selectRenewal,robot-3061-editCengage,robot-3061-editCitemCar,robot-3061-editCinsured,robot-3061-renewalPolicy,robot-3061-renewalPolicyCI,robot-3061-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3061-VehicleModelList" //上海车型查询        }        s += ",robot-3061-vehicleQueryXB,robot-3061-queryTaxAbateForPlat,robot-3061-calActualValue,robot-3061-editCitemKind,robot-3061-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3061-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-refreshPlanByTimes,robot-3061-insert"            }else{                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3061-getMaxCsellFee,robot-3061-getPrpCseller,robot-3061-getPrpCsellerCI,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            s += ",robot-3061-getMaxCsellFee,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"        }    }else{        s +=",robot-3061-ObtainConfig,robot-3061-checkInsurePerson,robot-3061-changePerson,robot-3061-checkInsuredPerson,robot-3061-changePerson,robot-3061-prepareEdit,robot-3061-selectRenewalPolicyNo,robot-3061-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3061-VehicleModelList" //上海车型查询        }        s += ",robot-3061-queryPrepare,robot-3061-vehicleQuery,robot-3061-queryTaxAbateForPlat,robot-3061-calActualValue,robot-3061-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3061-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3061-calAnciInfo,robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            }else{                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-nomotor-unitedSaleEdit,robot-3061-nomotor-saveUnitedSale,robot-3061-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3061-getMaxCsellFee,robot-3061-getPrpCseller,robot-3061-getPrpCsellerCI,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            s += ",robot-3061-getMaxCsellFee,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-nomotor-unitedSaleEdit,robot-3061-nomotor-saveUnitedSale,robot-3061-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3061-login,robot_3061_bj_initData,robot_3061_bj_queryModel,robot_3061_bj_getSaleTaxInfo,robot_3061_bj_getRealValue,robot_3061_bj_getPersonData,robot_3061_bj_addPersonData,robot_3061_bj_askCharge,robot_3061_bj_queryPayForXSFY,robot_3061_bj_getCagentCI,robot_3061_bj_getCagent,robot_3061_bj_queryPayForXSFY_req,robot_3061_bj_queryIlogEngage,robot_3061_bj_insureRefrenshPlan,robot_3061_bj_insure4S,robot-3061-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3061', 'qianhai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-前海-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ" +            ",robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind,robot-3061-nomotor-query,robot-3061-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3061', 'qianhai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-前海-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ" +            ",robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind,robot-3061-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3061', 'qianhai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-前海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ" +            ",robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind,robot-3061-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3061', 'qianhai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "前海财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-前海-电销', 'def getTemplateGroup(dataSource){    return "robot-3061-pureESale_Login,robot-3061-pureESale_Welcome,robot-3061-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3061', 'qianhai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-前海续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3061-login,robot-3061-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3061', 'qianhai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-前海-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3061-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3061-ObtainConfig,robot-3061-selectRenewal,robot-3061-editCengage,robot-3061-editCitemCar,robot-3061-editCinsured,robot-3061-renewalPolicy,robot-3061-renewalPolicyCI,robot-3061-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3061-VehicleModelList" //上海车型查询        }        s += ",robot-3061-vehicleQueryXB,robot-3061-queryTaxAbateForPlat,robot-3061-calActualValue,robot-3061-editCitemKind,robot-3061-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3061-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-refreshPlanByTimes,robot-3061-insert"            }else{                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3061-calAnciInfo,robot-3061-getMaxCsellFee,robot-3061-getPrpCseller,robot-3061-getPrpCsellerCI,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            s += ",robot-3061-getMaxCsellFee,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"        }    }else{        s += ",robot-3061-ObtainConfig,robot-3061-checkInsurePerson,robot-3061-changePerson,robot-3061-checkInsuredPerson,robot-3061-changePerson,robot-3061-prepareEdit,robot-3061-editCengage,robot-3061-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3061-queryVehiclePMCheck,robot-3061-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3061-VehicleModelList" //上海车型查询        }        s += ",robot-3061-queryPrepare,robot-3061-vehicleQuery,robot-3061-queryTaxAbateForPlat,robot-3061-calActualValue,robot-3061-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3061-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3061-calAnciInfo,robot-3061-queryPayFor,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            }else{                s += ",robot-3061-calAnciInfo,robot-3061-checkAgentType,robot-3061-queryPayForSCMS,robot-3061-getCagent,robot-3061-getCagentCI,robot-3061-refreshPlanByTimes,robot-3061-nomotor-unitedSaleEdit,robot-3061-nomotor-saveUnitedSale,robot-3061-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3061-calAnciInfo,robot-3061-getMaxCsellFee,robot-3061-getPrpCseller,robot-3061-getPrpCsellerCI,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-insert"            s += ",robot-3061-getMaxCsellFee,robot-3061-queryPayForSCMS,robot-3061-refreshPlanByTimes,robot-3061-nomotor-unitedSaleEdit,robot-3061-nomotor-saveUnitedSale,robot-3061-insert"        }    }    s += ",robot-3061-checkRiskCode,robot-3061-editMainUwtFlag,robot-3061-editSubmitUndwrt,robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-showUndwrtMsgQ,robot-3061-showUndwrtMsgS"+            ",robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ,robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind,robot-3061-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3061-login,robot_3061_bj_initData,robot_3061_bj_queryModel,robot_3061_bj_getSaleTaxInfo,robot_3061_bj_getRealValue,robot_3061_bj_getPersonData,robot_3061_bj_addPersonData,robot_3061_bj_askCharge,robot_3061_bj_queryPayForXSFY,robot_3061_bj_getCagentCI,robot_3061_bj_getCagent,robot_3061_bj_queryPayForXSFY_req,robot_3061_bj_queryIlogEngage,robot_3061_bj_insureRefrenshPlan,robot_3061_bj_insure4S,robot-3061-uploadImage,robot_3061_bj_autoInsure,robot_3061_bj_showUndwrtMsgQ,robot_3061_bj_showUndwrtMsgS";       s += ",robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ,robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +                ",robot-3061-showCinsuredS,robot-3061-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3061', 'qianhai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-前海-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3061-qrcode_login,robot-3061-qrcode_printTwoBarCodeServlet,robot-3061-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3061-qrcode_login,robot-3061-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3061-qrcode_login,robot-3061-qrcode_editCheckFlag,robot-3061-qrcode_gotoJfcd,robot-3061-qrcode_prepareEditByJF,robot-3061-qrcode_getBusinessIn" +                ",robot-3061-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3061-qrcode_login,robot-3061-qrcode_editCheckFlag,robot-3061-qrcode_gotoJfcd,robot-3061-qrcode_prepareEditByJF,robot-3061-qrcode_getBusinessIn" +                ",robot-3061-qrcode_checkBeforeCalculate,robot-3061-qrcode_saveByJF,robot-3061-qrcode_getBusinessIn_alipay,robot-3061-qrcode_editFeeInfor,robot-3061-qrcode_editPayFeeByWeChat,robot-3061-qrcode_saveByWeChat,robot-3061-qrcode_save";		} else {					return  "robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-bj-editIDCardCheck,robot-3061-apply-selectIsNetProp,robot-3061-apply-saveCheckCode,robot-3061-qrcode_editCheckFlag,robot-3061-qrcode_gotoJfcd,robot-3061-qrcode_prepareEditByJF,robot-3061-qrcode_getBusinessIn" +",robot-3061-qrcode_checkBeforeCalculate,robot-3061-qrcode_saveByJF,robot-3061-qrcode_getBusinessIn_alipay,robot-3061-qrcode_editFeeInfor,robot-3061-qrcode_editPayFeeByWeChat,robot-3061-qrcode_saveByWeChat,robot-3061-qrcode_save";		}}    else {              return "robot-3061-qrcode_login,robot-3061-qrcode_editCheckFlag,robot-3061-qrcode_gotoJfcd,robot-3061-qrcode_prepareEditByJF,robot-3061-qrcode_getBusinessIn" +                ",robot-3061-qrcode_checkBeforeCalculate,robot-3061-qrcode_saveByJF,robot-3061-qrcode_getBusinessIn_alipay,robot-3061-qrcode_editFeeInfor,robot-3061-qrcode_editPayFeeByWeChat,robot-3061-qrcode_saveByWeChat,robot-3061-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3061', 'qianhai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-前海-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3061-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3061-qrcode_query_editCheckFlag,robot-3061-qrcode_query_gotoJfcd,robot-3061-qrcode_query_prepareEditByJF" +                ",robot-3061-qrcode_query_editMainInfor,robot-3061-qrcode_query_getBusinessIn,robot-3061-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ" +            ",robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind,robot-3061-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3061', 'qianhai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-前海-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-IdCarChekc" //申请验证码    else{        return"robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-editIDCardCheck,robot-3061-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3061', 'qianhai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-前海-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectPolicyefc,robot-3061-selectPolicybiz,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ" +            ",robot-3061-showCitemCarQ,robot-3061-showCinsuredQ,robot-3061-showCitemKindCI,robot-3061-browseProposalS,robot-3061-showCitemCarS" +            ",robot-3061-showCinsuredS,robot-3061-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3061', 'qianhai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3061-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectProposalQ,robot-3061-selectProposalS,robot-3061-browseProposalQ,robot-3061-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3061', 'qianhai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-前海-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-bj-editIDCardCheck,robot-3061-apply-bj-IdCarChekc";    } else {        s = "robot-3061-qrcode_login,robot-3061-qrcode_editCheckFlag,robot-3061-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3061', 'qianhai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi前海报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3061_ask_charge,edi_3061_noMotor_quote"	} else {		return "edi_3061_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3061', 'qianhai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-前海-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3061_ask_charge,edi_3061_noMotor_quote,edi_3061_askInsure,edi_3061_uploadImg,edi_3061_submitInsure,edi_3061_noMotor_submit" 	  	} else {		return "edi_3061_ask_chargeold,edi_3061_askInsure,edi_3061_uploadImg,edi_3061_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3061', 'qianhai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-前海-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3061_ask_charge,edi_3061_noMotor_quote,edi_3061_askInsure,edi_3061_uploadImg" 	} else {		return "edi_3061_ask_chargeold,edi_3061_askInsure,edi_3061_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3061', 'qianhai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-前海-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3061_efc_policyinfo,edi_3061_biz_policyinfo,edi_3061_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3061', 'qianhai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-前海-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3061_efc_insurequery,edi_3061_biz_insurequery,edi_3061_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3061', 'qianhai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京前海短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-bj-editIDCardCheck,robot-3061-apply-saveCheckCode,robot-3061-apply-selectIsNetProp";    } else {        s = "robot-3061-qrcode_login,robot-3061-apply-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3061', 'qianhai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-前海-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3061-bj-qrcode_login,robot-3061-apply-bj-prepare,robot-3061-apply-select-jq,robot-3061-apply-select-sy,robot-3061-apply-bj-editIDCardCheck,robot-3061-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3061', 'qianhai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-前海-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3061_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3061', 'qianhai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi前海北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3061_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3061', 'qianhai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi前海北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3061_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3061', 'qianhai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-前海-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3061-login,robot-3061-prepareQueryCode,robot-3061-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3063', 'yanzhao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-燕赵-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-IdCarChekc" //申请验证码    else{        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '3063', 'yanzhao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-燕赵-报价', 'def getTemplateGroup(dataSource) {    return "edi-3063-queryCar,edi-3063-xbQuery,edi-3063-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '3063', 'yanzhao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-燕赵-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-3063-queryCar,edi-3063-xbQuery,edi-3063-askCharge,edi-3063-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '3063', 'yanzhao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-燕赵-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-3063-queryCar,edi-3063-xbQuery,edi-3063-askCharge,edi-3063-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '3063', 'yanzhao', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-燕赵-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-3063-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3063', 'yanzhao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-燕赵-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-3063-login,robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("1237000000".equals(orgId)){            s = "robot-3063-login,robot-3063-ObtainConfig,robot-3063-checkInsurePerson,robot-3063-changePerson,robot-3063-checkInsuredPerson,robot-3063-changePerson,robot-3063-prepareEdit," +                    "robot-3063-prepareQueryCode,robot-3063-selectProposalCar,robot-3063-browseProposalCar,robot-3063-browseProposalCarefc,robot-3063-selectRenewalPolicyNo"        }else{            s = "robot-3063-login,robot-3063-ObtainConfig,robot-3063-checkInsurePerson,robot-3063-changePerson,robot-3063-checkInsuredPerson,robot-3063-changePerson,robot-3063-prepareEdit," +                    "robot-3063-prepareQueryCode,robot-3063-browseProposalCar,robot-3063-selectRenewalPolicyNo"        }        if("1232000000".equals(orgId)){            s += ",robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3063-VehicleModelList" //上海车型查询        }        s += ",robot-3063-queryPrepare,robot-3063-vehicleQuery,robot-3063-queryTaxAbateForPlat,robot-3063-calActualValue,robot-3063-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3063-editCalculateCarShipTax" //车船税单独计算        }        if(!"1232000000".equals(orgId)){            s += ",robot-3063-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["1251000000", "1244200000"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-nomotor-unitedSaleEdit,robot-3063-nomotor-saveUnitedSale"		}         if("1211000000".equals(orgId)){            s = "robot-3063-login,robot_3063_bj_initData,robot_3063_bj_queryModel,robot_3063_bj_getSaleTaxInfo,robot_3063_bj_getRealValue,robot_3063_bj_getPersonData,robot_3063_bj_addPersonData,robot_3063_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3063', 'yanzhao', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-燕赵-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-3063-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3063-ObtainConfig,robot-3063-selectRenewal,robot-3063-editCengage,robot-3063-editCitemCar,robot-3063-editCinsured,robot-3063-renewalPolicy,robot-3063-renewalPolicyCI,robot-3063-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3063-VehicleModelList" //上海车型查询        }        s += ",robot-3063-vehicleQueryXB,robot-3063-queryTaxAbateForPlat,robot-3063-calActualValue,robot-3063-editCitemKind,robot-3063-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3063-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-refreshPlanByTimes,robot-3063-insert"            }else{                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3063-getMaxCsellFee,robot-3063-getPrpCseller,robot-3063-getPrpCsellerCI,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            s += ",robot-3063-getMaxCsellFee,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"        }    }else{        s +=",robot-3063-ObtainConfig,robot-3063-checkInsurePerson,robot-3063-changePerson,robot-3063-checkInsuredPerson,robot-3063-changePerson,robot-3063-prepareEdit,robot-3063-selectRenewalPolicyNo,robot-3063-editCengage"        String orgId = autoTask.configs.orgId;        if("1232000000".equals(orgId)){            s += ",robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3063-VehicleModelList" //上海车型查询        }        s += ",robot-3063-queryPrepare,robot-3063-vehicleQuery,robot-3063-queryTaxAbateForPlat,robot-3063-calActualValue,robot-3063-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3063-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("1237000000".equals(orgId)){                s += ",robot-3063-calAnciInfo,robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            }else{                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-nomotor-unitedSaleEdit,robot-3063-nomotor-saveUnitedSale,robot-3063-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3063-getMaxCsellFee,robot-3063-getPrpCseller,robot-3063-getPrpCsellerCI,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            s += ",robot-3063-getMaxCsellFee,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-nomotor-unitedSaleEdit,robot-3063-nomotor-saveUnitedSale,robot-3063-insert"        }         if("1211000000".equals(orgId)){            s = "robot-3063-login,robot_3063_bj_initData,robot_3063_bj_queryModel,robot_3063_bj_getSaleTaxInfo,robot_3063_bj_getRealValue,robot_3063_bj_getPersonData,robot_3063_bj_addPersonData,robot_3063_bj_askCharge,robot_3063_bj_queryPayForXSFY,robot_3063_bj_getCagentCI,robot_3063_bj_getCagent,robot_3063_bj_queryPayForXSFY_req,robot_3063_bj_queryIlogEngage,robot_3063_bj_insureRefrenshPlan,robot_3063_bj_insure4S,robot-3063-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3063', 'yanzhao', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-燕赵-核保查询', 'def getTemplateGroup(dataSource){    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ" +            ",robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind,robot-3063-nomotor-query,robot-3063-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '3063', 'yanzhao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-燕赵-报价查询', 'def getTemplateGroup(dataSource){    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ" +            ",robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind,robot-3063-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3063', 'yanzhao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-燕赵-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ" +            ",robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind,robot-3063-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '3063', 'yanzhao', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "燕赵财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "1232000000",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-燕赵-电销', 'def getTemplateGroup(dataSource){    return "robot-3063-pureESale_Login,robot-3063-pureESale_Welcome,robot-3063-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '3063', 'yanzhao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-燕赵续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"1244000000".equals(orgId)){      return "robot-3063-login,robot-3063-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3063', 'yanzhao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-燕赵-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-3063-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-3063-ObtainConfig,robot-3063-selectRenewal,robot-3063-editCengage,robot-3063-editCitemCar,robot-3063-editCinsured,robot-3063-renewalPolicy,robot-3063-renewalPolicyCI,robot-3063-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("1232000000".equals(orgId)){            s += ",robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3063-VehicleModelList" //上海车型查询        }        s += ",robot-3063-vehicleQueryXB,robot-3063-queryTaxAbateForPlat,robot-3063-calActualValue,robot-3063-editCitemKind,robot-3063-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)){            s += ",robot-3063-editCalculateCarShipTax" //车船税单独计算        }        if("1251000000".equals(orgId)){//四川            s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-refreshPlanByTimes,robot-3063-insert"            }else{                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3063-calAnciInfo,robot-3063-getMaxCsellFee,robot-3063-getPrpCseller,robot-3063-getPrpCsellerCI,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            s += ",robot-3063-getMaxCsellFee,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"        }    }else{        s += ",robot-3063-ObtainConfig,robot-3063-checkInsurePerson,robot-3063-changePerson,robot-3063-checkInsuredPerson,robot-3063-changePerson,robot-3063-prepareEdit,robot-3063-editCengage,robot-3063-selectRenewalPolicyNo"        if("1232000000".equals(orgId)){            s += ",robot-3063-queryVehiclePMCheck,robot-3063-queryVehiclePMConfirm" //江苏流程        }else if("1231000000".equals(orgId)){            s += ",robot-3063-VehicleModelList" //上海车型查询        }        s += ",robot-3063-queryPrepare,robot-3063-vehicleQuery,robot-3063-queryTaxAbateForPlat,robot-3063-calActualValue,robot-3063-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "1237000000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-3063-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("1237000000".equals(orgId)){                s += ",robot-3063-calAnciInfo,robot-3063-queryPayFor,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            }else{                s += ",robot-3063-calAnciInfo,robot-3063-checkAgentType,robot-3063-queryPayForSCMS,robot-3063-getCagent,robot-3063-getCagentCI,robot-3063-refreshPlanByTimes,robot-3063-nomotor-unitedSaleEdit,robot-3063-nomotor-saveUnitedSale,robot-3063-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-3063-calAnciInfo,robot-3063-getMaxCsellFee,robot-3063-getPrpCseller,robot-3063-getPrpCsellerCI,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-insert"            s += ",robot-3063-getMaxCsellFee,robot-3063-queryPayForSCMS,robot-3063-refreshPlanByTimes,robot-3063-nomotor-unitedSaleEdit,robot-3063-nomotor-saveUnitedSale,robot-3063-insert"        }    }    s += ",robot-3063-checkRiskCode,robot-3063-editMainUwtFlag,robot-3063-editSubmitUndwrt,robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-showUndwrtMsgQ,robot-3063-showUndwrtMsgS"+            ",robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ,robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind,robot-3063-nomotor-query";   if("1211000000".equals(orgId)){       s = "robot-3063-login,robot_3063_bj_initData,robot_3063_bj_queryModel,robot_3063_bj_getSaleTaxInfo,robot_3063_bj_getRealValue,robot_3063_bj_getPersonData,robot_3063_bj_addPersonData,robot_3063_bj_askCharge,robot_3063_bj_queryPayForXSFY,robot_3063_bj_getCagentCI,robot_3063_bj_getCagent,robot_3063_bj_queryPayForXSFY_req,robot_3063_bj_queryIlogEngage,robot_3063_bj_insureRefrenshPlan,robot_3063_bj_insure4S,robot-3063-uploadImage,robot_3063_bj_autoInsure,robot_3063_bj_showUndwrtMsgQ,robot_3063_bj_showUndwrtMsgS";       s += ",robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ,robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +                ",robot-3063-showCinsuredS,robot-3063-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '3063', 'yanzhao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-燕赵-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-3063-qrcode_login,robot-3063-qrcode_printTwoBarCodeServlet,robot-3063-qrcode_pay_getReferrer";    }    else if (["1243000000","1251000000","1245000000","010002134"].contains(orgId) || (["1244200000","1244000000"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-3063-qrcode_login,robot-3063-qrcode_printTwoBarCodeServlet";    } else if ("1242000000".equals(orgId) || "1213000000".equals(orgId)){                return "robot-3063-qrcode_login,robot-3063-qrcode_editCheckFlag,robot-3063-qrcode_gotoJfcd,robot-3063-qrcode_prepareEditByJF,robot-3063-qrcode_getBusinessIn" +                ",robot-3063-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("1211000000".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-3063-qrcode_login,robot-3063-qrcode_editCheckFlag,robot-3063-qrcode_gotoJfcd,robot-3063-qrcode_prepareEditByJF,robot-3063-qrcode_getBusinessIn" +                ",robot-3063-qrcode_checkBeforeCalculate,robot-3063-qrcode_saveByJF,robot-3063-qrcode_getBusinessIn_alipay,robot-3063-qrcode_editFeeInfor,robot-3063-qrcode_editPayFeeByWeChat,robot-3063-qrcode_saveByWeChat,robot-3063-qrcode_save";		} else {					return  "robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-bj-editIDCardCheck,robot-3063-apply-selectIsNetProp,robot-3063-apply-saveCheckCode,robot-3063-qrcode_editCheckFlag,robot-3063-qrcode_gotoJfcd,robot-3063-qrcode_prepareEditByJF,robot-3063-qrcode_getBusinessIn" +",robot-3063-qrcode_checkBeforeCalculate,robot-3063-qrcode_saveByJF,robot-3063-qrcode_getBusinessIn_alipay,robot-3063-qrcode_editFeeInfor,robot-3063-qrcode_editPayFeeByWeChat,robot-3063-qrcode_saveByWeChat,robot-3063-qrcode_save";		}}    else {              return "robot-3063-qrcode_login,robot-3063-qrcode_editCheckFlag,robot-3063-qrcode_gotoJfcd,robot-3063-qrcode_prepareEditByJF,robot-3063-qrcode_getBusinessIn" +                ",robot-3063-qrcode_checkBeforeCalculate,robot-3063-qrcode_saveByJF,robot-3063-qrcode_getBusinessIn_alipay,robot-3063-qrcode_editFeeInfor,robot-3063-qrcode_editPayFeeByWeChat,robot-3063-qrcode_saveByWeChat,robot-3063-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '3063', 'yanzhao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-燕赵-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-3063-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-3063-qrcode_query_editCheckFlag,robot-3063-qrcode_query_gotoJfcd,robot-3063-qrcode_query_prepareEditByJF" +                ",robot-3063-qrcode_query_editMainInfor,robot-3063-qrcode_query_getBusinessIn,robot-3063-qrcode_query_proposalToPolicy";    }    s +=  ",robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ" +            ",robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind,robot-3063-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '3063', 'yanzhao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-燕赵-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-IdCarChekc" //申请验证码    else{        return"robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-editIDCardCheck,robot-3063-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '3063', 'yanzhao', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-燕赵-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectPolicyefc,robot-3063-selectPolicybiz,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ" +            ",robot-3063-showCitemCarQ,robot-3063-showCinsuredQ,robot-3063-showCitemKindCI,robot-3063-browseProposalS,robot-3063-showCitemCarS" +            ",robot-3063-showCinsuredS,robot-3063-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '3063', 'yanzhao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-3063-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectProposalQ,robot-3063-selectProposalS,robot-3063-browseProposalQ,robot-3063-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3063', 'yanzhao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-燕赵-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-bj-editIDCardCheck,robot-3063-apply-bj-IdCarChekc";    } else {        s = "robot-3063-qrcode_login,robot-3063-qrcode_editCheckFlag,robot-3063-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '3063', 'yanzhao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi燕赵报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3063_ask_charge,edi_3063_noMotor_quote"	} else {		return "edi_3063_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '3063', 'yanzhao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-燕赵-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_3063_ask_charge,edi_3063_noMotor_quote,edi_3063_askInsure,edi_3063_uploadImg,edi_3063_submitInsure,edi_3063_noMotor_submit" 	  	} else {		return "edi_3063_ask_chargeold,edi_3063_askInsure,edi_3063_uploadImg,edi_3063_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '3063', 'yanzhao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-燕赵-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_3063_ask_charge,edi_3063_noMotor_quote,edi_3063_askInsure,edi_3063_uploadImg" 	} else {		return "edi_3063_ask_chargeold,edi_3063_askInsure,edi_3063_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '3063', 'yanzhao', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-燕赵-承保查询', 'def getTemplateGroup(dataSource){  return "edi_3063_efc_policyinfo,edi_3063_biz_policyinfo,edi_3063_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '3063', 'yanzhao', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-燕赵-核保查询', 'def getTemplateGroup(dataSource){    return "edi_3063_efc_insurequery,edi_3063_biz_insurequery,edi_3063_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3063', 'yanzhao', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京燕赵短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-bj-editIDCardCheck,robot-3063-apply-saveCheckCode,robot-3063-apply-selectIsNetProp";    } else {        s = "robot-3063-qrcode_login,robot-3063-apply-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '3063', 'yanzhao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-燕赵-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("1211000000".equals(orgId)) {        s = "robot-3063-bj-qrcode_login,robot-3063-apply-bj-prepare,robot-3063-apply-select-jq,robot-3063-apply-select-sy,robot-3063-apply-bj-editIDCardCheck,robot-3063-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '3063', 'yanzhao', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-燕赵-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_3063_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '3063', 'yanzhao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi燕赵北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_3063_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '3063', 'yanzhao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi燕赵北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_3063_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '3063', 'yanzhao', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-燕赵-承保查询', 'def getTemplateGroup(dataSource){    return "robot-3063-login,robot-3063-prepareQueryCode,robot-3063-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
