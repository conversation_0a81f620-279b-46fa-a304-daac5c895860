DROP TABLE IF EXISTS `tb_api_new`;
CREATE TABLE `tb_api_new` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action` varchar(255) DEFAULT NULL,
  `comId` varchar(255) DEFAULT NULL,
  `comCode` varchar(32) DEFAULT NULL,
  `defaultFailedStatus` varchar(255) DEFAULT NULL,
  `defaultSuccessStatus` varchar(255) DEFAULT NULL,
  `env` varchar(255) DEFAULT NULL,
  `intType` varchar(255) DEFAULT NULL,
  `keepSession` bit(1) NOT NULL,
  `proConfig` longtext,
  `remark` varchar(255) DEFAULT NULL,
  `term` longtext,
  `testConfig` longtext,
  `uatConfig` longtext,
  `useTaskConfig` bit(1) NOT NULL,
  `version` int(11) NOT NULL,
  `dataStructureType` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='API表';
