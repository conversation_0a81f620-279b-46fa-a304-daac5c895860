INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2026', 'tianping', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安盛天平-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-IdCarChekc" //申请验证码    else{        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2026', 'tianping', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-报价', 'def getTemplateGroup(dataSource) {    return "edi-2026-queryCar,edi-2026-xbQuery,edi-2026-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2026', 'tianping', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2026-queryCar,edi-2026-xbQuery,edi-2026-askCharge,edi-2026-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2026', 'tianping', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2026-queryCar,edi-2026-xbQuery,edi-2026-askCharge,edi-2026-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2026', 'tianping', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2026-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2026', 'tianping', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2026-login,robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2026-login,robot-2026-ObtainConfig,robot-2026-checkInsurePerson,robot-2026-changePerson,robot-2026-checkInsuredPerson,robot-2026-changePerson,robot-2026-prepareEdit," +                    "robot-2026-prepareQueryCode,robot-2026-selectProposalCar,robot-2026-browseProposalCar,robot-2026-browseProposalCarefc,robot-2026-selectRenewalPolicyNo"        }else{            s = "robot-2026-login,robot-2026-ObtainConfig,robot-2026-checkInsurePerson,robot-2026-changePerson,robot-2026-checkInsuredPerson,robot-2026-changePerson,robot-2026-prepareEdit," +                    "robot-2026-prepareQueryCode,robot-2026-browseProposalCar,robot-2026-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2026-VehicleModelList" //上海车型查询        }        s += ",robot-2026-queryPrepare,robot-2026-vehicleQuery,robot-2026-queryTaxAbateForPlat,robot-2026-calActualValue,robot-2026-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2026-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2026-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-nomotor-unitedSaleEdit,robot-2026-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2026-login,robot_2026_bj_initData,robot_2026_bj_queryModel,robot_2026_bj_getSaleTaxInfo,robot_2026_bj_getRealValue,robot_2026_bj_getPersonData,robot_2026_bj_addPersonData,robot_2026_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2026', 'tianping', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2026-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2026-ObtainConfig,robot-2026-selectRenewal,robot-2026-editCengage,robot-2026-editCitemCar,robot-2026-editCinsured,robot-2026-renewalPolicy,robot-2026-renewalPolicyCI,robot-2026-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2026-VehicleModelList" //上海车型查询        }        s += ",robot-2026-vehicleQueryXB,robot-2026-queryTaxAbateForPlat,robot-2026-calActualValue,robot-2026-editCitemKind,robot-2026-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2026-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-refreshPlanByTimes,robot-2026-insert"            }else{                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2026-getMaxCsellFee,robot-2026-getPrpCseller,robot-2026-getPrpCsellerCI,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            s += ",robot-2026-getMaxCsellFee,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"        }    }else{        s +=",robot-2026-ObtainConfig,robot-2026-checkInsurePerson,robot-2026-changePerson,robot-2026-checkInsuredPerson,robot-2026-changePerson,robot-2026-prepareEdit,robot-2026-selectRenewalPolicyNo,robot-2026-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2026-VehicleModelList" //上海车型查询        }        s += ",robot-2026-queryPrepare,robot-2026-vehicleQuery,robot-2026-queryTaxAbateForPlat,robot-2026-calActualValue,robot-2026-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2026-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2026-calAnciInfo,robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            }else{                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-nomotor-unitedSaleEdit,robot-2026-nomotor-saveUnitedSale,robot-2026-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2026-getMaxCsellFee,robot-2026-getPrpCseller,robot-2026-getPrpCsellerCI,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            s += ",robot-2026-getMaxCsellFee,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-nomotor-unitedSaleEdit,robot-2026-nomotor-saveUnitedSale,robot-2026-insert"        }         if("**********".equals(orgId)){            s = "robot-2026-login,robot_2026_bj_initData,robot_2026_bj_queryModel,robot_2026_bj_getSaleTaxInfo,robot_2026_bj_getRealValue,robot_2026_bj_getPersonData,robot_2026_bj_addPersonData,robot_2026_bj_askCharge,robot_2026_bj_queryPayForXSFY,robot_2026_bj_getCagentCI,robot_2026_bj_getCagent,robot_2026_bj_queryPayForXSFY_req,robot_2026_bj_queryIlogEngage,robot_2026_bj_insureRefrenshPlan,robot_2026_bj_insure4S,robot-2026-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2026', 'tianping', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ" +            ",robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind,robot-2026-nomotor-query,robot-2026-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2026', 'tianping', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ" +            ",robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind,robot-2026-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2026', 'tianping', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ" +            ",robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind,robot-2026-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2026', 'tianping', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "安盛天平财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-安盛天平-电销', 'def getTemplateGroup(dataSource){    return "robot-2026-pureESale_Login,robot-2026-pureESale_Welcome,robot-2026-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2026', 'tianping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安盛天平续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2026-login,robot-2026-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2026', 'tianping', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2026-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2026-ObtainConfig,robot-2026-selectRenewal,robot-2026-editCengage,robot-2026-editCitemCar,robot-2026-editCinsured,robot-2026-renewalPolicy,robot-2026-renewalPolicyCI,robot-2026-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2026-VehicleModelList" //上海车型查询        }        s += ",robot-2026-vehicleQueryXB,robot-2026-queryTaxAbateForPlat,robot-2026-calActualValue,robot-2026-editCitemKind,robot-2026-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2026-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-refreshPlanByTimes,robot-2026-insert"            }else{                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2026-calAnciInfo,robot-2026-getMaxCsellFee,robot-2026-getPrpCseller,robot-2026-getPrpCsellerCI,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            s += ",robot-2026-getMaxCsellFee,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"        }    }else{        s += ",robot-2026-ObtainConfig,robot-2026-checkInsurePerson,robot-2026-changePerson,robot-2026-checkInsuredPerson,robot-2026-changePerson,robot-2026-prepareEdit,robot-2026-editCengage,robot-2026-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2026-queryVehiclePMCheck,robot-2026-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2026-VehicleModelList" //上海车型查询        }        s += ",robot-2026-queryPrepare,robot-2026-vehicleQuery,robot-2026-queryTaxAbateForPlat,robot-2026-calActualValue,robot-2026-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2026-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2026-calAnciInfo,robot-2026-queryPayFor,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            }else{                s += ",robot-2026-calAnciInfo,robot-2026-checkAgentType,robot-2026-queryPayForSCMS,robot-2026-getCagent,robot-2026-getCagentCI,robot-2026-refreshPlanByTimes,robot-2026-nomotor-unitedSaleEdit,robot-2026-nomotor-saveUnitedSale,robot-2026-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2026-calAnciInfo,robot-2026-getMaxCsellFee,robot-2026-getPrpCseller,robot-2026-getPrpCsellerCI,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-insert"            s += ",robot-2026-getMaxCsellFee,robot-2026-queryPayForSCMS,robot-2026-refreshPlanByTimes,robot-2026-nomotor-unitedSaleEdit,robot-2026-nomotor-saveUnitedSale,robot-2026-insert"        }    }    s += ",robot-2026-checkRiskCode,robot-2026-editMainUwtFlag,robot-2026-editSubmitUndwrt,robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-showUndwrtMsgQ,robot-2026-showUndwrtMsgS"+            ",robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ,robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind,robot-2026-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2026-login,robot_2026_bj_initData,robot_2026_bj_queryModel,robot_2026_bj_getSaleTaxInfo,robot_2026_bj_getRealValue,robot_2026_bj_getPersonData,robot_2026_bj_addPersonData,robot_2026_bj_askCharge,robot_2026_bj_queryPayForXSFY,robot_2026_bj_getCagentCI,robot_2026_bj_getCagent,robot_2026_bj_queryPayForXSFY_req,robot_2026_bj_queryIlogEngage,robot_2026_bj_insureRefrenshPlan,robot_2026_bj_insure4S,robot-2026-uploadImage,robot_2026_bj_autoInsure,robot_2026_bj_showUndwrtMsgQ,robot_2026_bj_showUndwrtMsgS";       s += ",robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ,robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +                ",robot-2026-showCinsuredS,robot-2026-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2026', 'tianping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2026-qrcode_login,robot-2026-qrcode_printTwoBarCodeServlet,robot-2026-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2026-qrcode_login,robot-2026-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2026-qrcode_login,robot-2026-qrcode_editCheckFlag,robot-2026-qrcode_gotoJfcd,robot-2026-qrcode_prepareEditByJF,robot-2026-qrcode_getBusinessIn" +                ",robot-2026-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2026-qrcode_login,robot-2026-qrcode_editCheckFlag,robot-2026-qrcode_gotoJfcd,robot-2026-qrcode_prepareEditByJF,robot-2026-qrcode_getBusinessIn" +                ",robot-2026-qrcode_checkBeforeCalculate,robot-2026-qrcode_saveByJF,robot-2026-qrcode_getBusinessIn_alipay,robot-2026-qrcode_editFeeInfor,robot-2026-qrcode_editPayFeeByWeChat,robot-2026-qrcode_saveByWeChat,robot-2026-qrcode_save";		} else {					return  "robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-bj-editIDCardCheck,robot-2026-apply-selectIsNetProp,robot-2026-apply-saveCheckCode,robot-2026-qrcode_editCheckFlag,robot-2026-qrcode_gotoJfcd,robot-2026-qrcode_prepareEditByJF,robot-2026-qrcode_getBusinessIn" +",robot-2026-qrcode_checkBeforeCalculate,robot-2026-qrcode_saveByJF,robot-2026-qrcode_getBusinessIn_alipay,robot-2026-qrcode_editFeeInfor,robot-2026-qrcode_editPayFeeByWeChat,robot-2026-qrcode_saveByWeChat,robot-2026-qrcode_save";		}}    else {              return "robot-2026-qrcode_login,robot-2026-qrcode_editCheckFlag,robot-2026-qrcode_gotoJfcd,robot-2026-qrcode_prepareEditByJF,robot-2026-qrcode_getBusinessIn" +                ",robot-2026-qrcode_checkBeforeCalculate,robot-2026-qrcode_saveByJF,robot-2026-qrcode_getBusinessIn_alipay,robot-2026-qrcode_editFeeInfor,robot-2026-qrcode_editPayFeeByWeChat,robot-2026-qrcode_saveByWeChat,robot-2026-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2026', 'tianping', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2026-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2026-qrcode_query_editCheckFlag,robot-2026-qrcode_query_gotoJfcd,robot-2026-qrcode_query_prepareEditByJF" +                ",robot-2026-qrcode_query_editMainInfor,robot-2026-qrcode_query_getBusinessIn,robot-2026-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ" +            ",robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind,robot-2026-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2026', 'tianping', '15', '6', 'pro', 'other', b'1', '{}', '精灵-安盛天平-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-IdCarChekc" //申请验证码    else{        return"robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-editIDCardCheck,robot-2026-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2026', 'tianping', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectPolicyefc,robot-2026-selectPolicybiz,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ" +            ",robot-2026-showCitemCarQ,robot-2026-showCinsuredQ,robot-2026-showCitemKindCI,robot-2026-browseProposalS,robot-2026-showCitemCarS" +            ",robot-2026-showCinsuredS,robot-2026-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2026', 'tianping', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2026-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectProposalQ,robot-2026-selectProposalS,robot-2026-browseProposalQ,robot-2026-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2026', 'tianping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-bj-editIDCardCheck,robot-2026-apply-bj-IdCarChekc";    } else {        s = "robot-2026-qrcode_login,robot-2026-qrcode_editCheckFlag,robot-2026-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2026', 'tianping', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi安盛天平报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2026_ask_charge,edi_2026_noMotor_quote"	} else {		return "edi_2026_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2026', 'tianping', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2026_ask_charge,edi_2026_noMotor_quote,edi_2026_askInsure,edi_2026_uploadImg,edi_2026_submitInsure,edi_2026_noMotor_submit" 	  	} else {		return "edi_2026_ask_chargeold,edi_2026_askInsure,edi_2026_uploadImg,edi_2026_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2026', 'tianping', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2026_ask_charge,edi_2026_noMotor_quote,edi_2026_askInsure,edi_2026_uploadImg" 	} else {		return "edi_2026_ask_chargeold,edi_2026_askInsure,edi_2026_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2026', 'tianping', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2026_efc_policyinfo,edi_2026_biz_policyinfo,edi_2026_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2026', 'tianping', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2026_efc_insurequery,edi_2026_biz_insurequery,edi_2026_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2026', 'tianping', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京安盛天平短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-bj-editIDCardCheck,robot-2026-apply-saveCheckCode,robot-2026-apply-selectIsNetProp";    } else {        s = "robot-2026-qrcode_login,robot-2026-apply-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2026', 'tianping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-安盛天平-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2026-bj-qrcode_login,robot-2026-apply-bj-prepare,robot-2026-apply-select-jq,robot-2026-apply-select-sy,robot-2026-apply-bj-editIDCardCheck,robot-2026-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2026', 'tianping', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-安盛天平-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2026_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2026', 'tianping', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安盛天平北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2026_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2026', 'tianping', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi安盛天平北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2026_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2026', 'tianping', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-安盛天平-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2026-login,robot-2026-prepareQueryCode,robot-2026-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2021', 'dadi', '15', '6', 'pro', 'other', b'1', '{}', '精灵-大地-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-IdCarChekc" //申请验证码    else{        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2021', 'dadi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-大地-报价', 'def getTemplateGroup(dataSource) {    return "edi-2021-queryCar,edi-2021-xbQuery,edi-2021-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2021', 'dadi', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-大地-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2021-queryCar,edi-2021-xbQuery,edi-2021-askCharge,edi-2021-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2021', 'dadi', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-大地-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2021-queryCar,edi-2021-xbQuery,edi-2021-askCharge,edi-2021-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2021', 'dadi', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-大地-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2021-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2021', 'dadi', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-大地-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2021-login,robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2021-login,robot-2021-ObtainConfig,robot-2021-checkInsurePerson,robot-2021-changePerson,robot-2021-checkInsuredPerson,robot-2021-changePerson,robot-2021-prepareEdit," +                    "robot-2021-prepareQueryCode,robot-2021-selectProposalCar,robot-2021-browseProposalCar,robot-2021-browseProposalCarefc,robot-2021-selectRenewalPolicyNo"        }else{            s = "robot-2021-login,robot-2021-ObtainConfig,robot-2021-checkInsurePerson,robot-2021-changePerson,robot-2021-checkInsuredPerson,robot-2021-changePerson,robot-2021-prepareEdit," +                    "robot-2021-prepareQueryCode,robot-2021-browseProposalCar,robot-2021-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2021-VehicleModelList" //上海车型查询        }        s += ",robot-2021-queryPrepare,robot-2021-vehicleQuery,robot-2021-queryTaxAbateForPlat,robot-2021-calActualValue,robot-2021-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2021-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2021-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-nomotor-unitedSaleEdit,robot-2021-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2021-login,robot_2021_bj_initData,robot_2021_bj_queryModel,robot_2021_bj_getSaleTaxInfo,robot_2021_bj_getRealValue,robot_2021_bj_getPersonData,robot_2021_bj_addPersonData,robot_2021_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2021', 'dadi', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-大地-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2021-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2021-ObtainConfig,robot-2021-selectRenewal,robot-2021-editCengage,robot-2021-editCitemCar,robot-2021-editCinsured,robot-2021-renewalPolicy,robot-2021-renewalPolicyCI,robot-2021-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2021-VehicleModelList" //上海车型查询        }        s += ",robot-2021-vehicleQueryXB,robot-2021-queryTaxAbateForPlat,robot-2021-calActualValue,robot-2021-editCitemKind,robot-2021-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2021-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-refreshPlanByTimes,robot-2021-insert"            }else{                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2021-getMaxCsellFee,robot-2021-getPrpCseller,robot-2021-getPrpCsellerCI,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            s += ",robot-2021-getMaxCsellFee,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"        }    }else{        s +=",robot-2021-ObtainConfig,robot-2021-checkInsurePerson,robot-2021-changePerson,robot-2021-checkInsuredPerson,robot-2021-changePerson,robot-2021-prepareEdit,robot-2021-selectRenewalPolicyNo,robot-2021-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2021-VehicleModelList" //上海车型查询        }        s += ",robot-2021-queryPrepare,robot-2021-vehicleQuery,robot-2021-queryTaxAbateForPlat,robot-2021-calActualValue,robot-2021-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2021-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2021-calAnciInfo,robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            }else{                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-nomotor-unitedSaleEdit,robot-2021-nomotor-saveUnitedSale,robot-2021-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2021-getMaxCsellFee,robot-2021-getPrpCseller,robot-2021-getPrpCsellerCI,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            s += ",robot-2021-getMaxCsellFee,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-nomotor-unitedSaleEdit,robot-2021-nomotor-saveUnitedSale,robot-2021-insert"        }         if("**********".equals(orgId)){            s = "robot-2021-login,robot_2021_bj_initData,robot_2021_bj_queryModel,robot_2021_bj_getSaleTaxInfo,robot_2021_bj_getRealValue,robot_2021_bj_getPersonData,robot_2021_bj_addPersonData,robot_2021_bj_askCharge,robot_2021_bj_queryPayForXSFY,robot_2021_bj_getCagentCI,robot_2021_bj_getCagent,robot_2021_bj_queryPayForXSFY_req,robot_2021_bj_queryIlogEngage,robot_2021_bj_insureRefrenshPlan,robot_2021_bj_insure4S,robot-2021-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2021', 'dadi', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-大地-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ" +            ",robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind,robot-2021-nomotor-query,robot-2021-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2021', 'dadi', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-大地-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ" +            ",robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind,robot-2021-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2021', 'dadi', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-大地-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ" +            ",robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind,robot-2021-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2021', 'dadi', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "大地财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-大地-电销', 'def getTemplateGroup(dataSource){    return "robot-2021-pureESale_Login,robot-2021-pureESale_Welcome,robot-2021-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2021', 'dadi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大地续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2021-login,robot-2021-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2021', 'dadi', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-大地-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2021-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2021-ObtainConfig,robot-2021-selectRenewal,robot-2021-editCengage,robot-2021-editCitemCar,robot-2021-editCinsured,robot-2021-renewalPolicy,robot-2021-renewalPolicyCI,robot-2021-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2021-VehicleModelList" //上海车型查询        }        s += ",robot-2021-vehicleQueryXB,robot-2021-queryTaxAbateForPlat,robot-2021-calActualValue,robot-2021-editCitemKind,robot-2021-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2021-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-refreshPlanByTimes,robot-2021-insert"            }else{                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2021-calAnciInfo,robot-2021-getMaxCsellFee,robot-2021-getPrpCseller,robot-2021-getPrpCsellerCI,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            s += ",robot-2021-getMaxCsellFee,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"        }    }else{        s += ",robot-2021-ObtainConfig,robot-2021-checkInsurePerson,robot-2021-changePerson,robot-2021-checkInsuredPerson,robot-2021-changePerson,robot-2021-prepareEdit,robot-2021-editCengage,robot-2021-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2021-queryVehiclePMCheck,robot-2021-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2021-VehicleModelList" //上海车型查询        }        s += ",robot-2021-queryPrepare,robot-2021-vehicleQuery,robot-2021-queryTaxAbateForPlat,robot-2021-calActualValue,robot-2021-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2021-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2021-calAnciInfo,robot-2021-queryPayFor,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            }else{                s += ",robot-2021-calAnciInfo,robot-2021-checkAgentType,robot-2021-queryPayForSCMS,robot-2021-getCagent,robot-2021-getCagentCI,robot-2021-refreshPlanByTimes,robot-2021-nomotor-unitedSaleEdit,robot-2021-nomotor-saveUnitedSale,robot-2021-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2021-calAnciInfo,robot-2021-getMaxCsellFee,robot-2021-getPrpCseller,robot-2021-getPrpCsellerCI,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-insert"            s += ",robot-2021-getMaxCsellFee,robot-2021-queryPayForSCMS,robot-2021-refreshPlanByTimes,robot-2021-nomotor-unitedSaleEdit,robot-2021-nomotor-saveUnitedSale,robot-2021-insert"        }    }    s += ",robot-2021-checkRiskCode,robot-2021-editMainUwtFlag,robot-2021-editSubmitUndwrt,robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-showUndwrtMsgQ,robot-2021-showUndwrtMsgS"+            ",robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ,robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind,robot-2021-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2021-login,robot_2021_bj_initData,robot_2021_bj_queryModel,robot_2021_bj_getSaleTaxInfo,robot_2021_bj_getRealValue,robot_2021_bj_getPersonData,robot_2021_bj_addPersonData,robot_2021_bj_askCharge,robot_2021_bj_queryPayForXSFY,robot_2021_bj_getCagentCI,robot_2021_bj_getCagent,robot_2021_bj_queryPayForXSFY_req,robot_2021_bj_queryIlogEngage,robot_2021_bj_insureRefrenshPlan,robot_2021_bj_insure4S,robot-2021-uploadImage,robot_2021_bj_autoInsure,robot_2021_bj_showUndwrtMsgQ,robot_2021_bj_showUndwrtMsgS";       s += ",robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ,robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +                ",robot-2021-showCinsuredS,robot-2021-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2021', 'dadi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大地-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2021-qrcode_login,robot-2021-qrcode_printTwoBarCodeServlet,robot-2021-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2021-qrcode_login,robot-2021-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2021-qrcode_login,robot-2021-qrcode_editCheckFlag,robot-2021-qrcode_gotoJfcd,robot-2021-qrcode_prepareEditByJF,robot-2021-qrcode_getBusinessIn" +                ",robot-2021-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2021-qrcode_login,robot-2021-qrcode_editCheckFlag,robot-2021-qrcode_gotoJfcd,robot-2021-qrcode_prepareEditByJF,robot-2021-qrcode_getBusinessIn" +                ",robot-2021-qrcode_checkBeforeCalculate,robot-2021-qrcode_saveByJF,robot-2021-qrcode_getBusinessIn_alipay,robot-2021-qrcode_editFeeInfor,robot-2021-qrcode_editPayFeeByWeChat,robot-2021-qrcode_saveByWeChat,robot-2021-qrcode_save";		} else {					return  "robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-bj-editIDCardCheck,robot-2021-apply-selectIsNetProp,robot-2021-apply-saveCheckCode,robot-2021-qrcode_editCheckFlag,robot-2021-qrcode_gotoJfcd,robot-2021-qrcode_prepareEditByJF,robot-2021-qrcode_getBusinessIn" +",robot-2021-qrcode_checkBeforeCalculate,robot-2021-qrcode_saveByJF,robot-2021-qrcode_getBusinessIn_alipay,robot-2021-qrcode_editFeeInfor,robot-2021-qrcode_editPayFeeByWeChat,robot-2021-qrcode_saveByWeChat,robot-2021-qrcode_save";		}}    else {              return "robot-2021-qrcode_login,robot-2021-qrcode_editCheckFlag,robot-2021-qrcode_gotoJfcd,robot-2021-qrcode_prepareEditByJF,robot-2021-qrcode_getBusinessIn" +                ",robot-2021-qrcode_checkBeforeCalculate,robot-2021-qrcode_saveByJF,robot-2021-qrcode_getBusinessIn_alipay,robot-2021-qrcode_editFeeInfor,robot-2021-qrcode_editPayFeeByWeChat,robot-2021-qrcode_saveByWeChat,robot-2021-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2021', 'dadi', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-大地-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2021-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2021-qrcode_query_editCheckFlag,robot-2021-qrcode_query_gotoJfcd,robot-2021-qrcode_query_prepareEditByJF" +                ",robot-2021-qrcode_query_editMainInfor,robot-2021-qrcode_query_getBusinessIn,robot-2021-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ" +            ",robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind,robot-2021-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2021', 'dadi', '15', '6', 'pro', 'other', b'1', '{}', '精灵-大地-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-IdCarChekc" //申请验证码    else{        return"robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-editIDCardCheck,robot-2021-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2021', 'dadi', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-大地-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectPolicyefc,robot-2021-selectPolicybiz,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ" +            ",robot-2021-showCitemCarQ,robot-2021-showCinsuredQ,robot-2021-showCitemKindCI,robot-2021-browseProposalS,robot-2021-showCitemCarS" +            ",robot-2021-showCinsuredS,robot-2021-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2021', 'dadi', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2021-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectProposalQ,robot-2021-selectProposalS,robot-2021-browseProposalQ,robot-2021-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2021', 'dadi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大地-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-bj-editIDCardCheck,robot-2021-apply-bj-IdCarChekc";    } else {        s = "robot-2021-qrcode_login,robot-2021-qrcode_editCheckFlag,robot-2021-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2021', 'dadi', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi大地报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2021_ask_charge,edi_2021_noMotor_quote"	} else {		return "edi_2021_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2021', 'dadi', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-大地-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2021_ask_charge,edi_2021_noMotor_quote,edi_2021_askInsure,edi_2021_uploadImg,edi_2021_submitInsure,edi_2021_noMotor_submit" 	  	} else {		return "edi_2021_ask_chargeold,edi_2021_askInsure,edi_2021_uploadImg,edi_2021_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2021', 'dadi', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-大地-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2021_ask_charge,edi_2021_noMotor_quote,edi_2021_askInsure,edi_2021_uploadImg" 	} else {		return "edi_2021_ask_chargeold,edi_2021_askInsure,edi_2021_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2021', 'dadi', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-大地-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2021_efc_policyinfo,edi_2021_biz_policyinfo,edi_2021_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2021', 'dadi', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-大地-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2021_efc_insurequery,edi_2021_biz_insurequery,edi_2021_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2021', 'dadi', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京大地短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-bj-editIDCardCheck,robot-2021-apply-saveCheckCode,robot-2021-apply-selectIsNetProp";    } else {        s = "robot-2021-qrcode_login,robot-2021-apply-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2021', 'dadi', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-大地-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2021-bj-qrcode_login,robot-2021-apply-bj-prepare,robot-2021-apply-select-jq,robot-2021-apply-select-sy,robot-2021-apply-bj-editIDCardCheck,robot-2021-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2021', 'dadi', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-大地-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2021_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2021', 'dadi', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi大地北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2021_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2021', 'dadi', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi大地北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2021_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2021', 'dadi', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-大地-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2021-login,robot-2021-prepareQueryCode,robot-2021-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2088', 'dinghe', '15', '6', 'pro', 'other', b'1', '{}', '精灵-鼎和-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-IdCarChekc" //申请验证码    else{        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2088', 'dinghe', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-鼎和-报价', 'def getTemplateGroup(dataSource) {    return "edi-2088-queryCar,edi-2088-xbQuery,edi-2088-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2088', 'dinghe', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-鼎和-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2088-queryCar,edi-2088-xbQuery,edi-2088-askCharge,edi-2088-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2088', 'dinghe', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-鼎和-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2088-queryCar,edi-2088-xbQuery,edi-2088-askCharge,edi-2088-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2088', 'dinghe', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-鼎和-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2088-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2088', 'dinghe', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-鼎和-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2088-login,robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2088-login,robot-2088-ObtainConfig,robot-2088-checkInsurePerson,robot-2088-changePerson,robot-2088-checkInsuredPerson,robot-2088-changePerson,robot-2088-prepareEdit," +                    "robot-2088-prepareQueryCode,robot-2088-selectProposalCar,robot-2088-browseProposalCar,robot-2088-browseProposalCarefc,robot-2088-selectRenewalPolicyNo"        }else{            s = "robot-2088-login,robot-2088-ObtainConfig,robot-2088-checkInsurePerson,robot-2088-changePerson,robot-2088-checkInsuredPerson,robot-2088-changePerson,robot-2088-prepareEdit," +                    "robot-2088-prepareQueryCode,robot-2088-browseProposalCar,robot-2088-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2088-VehicleModelList" //上海车型查询        }        s += ",robot-2088-queryPrepare,robot-2088-vehicleQuery,robot-2088-queryTaxAbateForPlat,robot-2088-calActualValue,robot-2088-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2088-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2088-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-nomotor-unitedSaleEdit,robot-2088-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2088-login,robot_2088_bj_initData,robot_2088_bj_queryModel,robot_2088_bj_getSaleTaxInfo,robot_2088_bj_getRealValue,robot_2088_bj_getPersonData,robot_2088_bj_addPersonData,robot_2088_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2088', 'dinghe', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-鼎和-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2088-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2088-ObtainConfig,robot-2088-selectRenewal,robot-2088-editCengage,robot-2088-editCitemCar,robot-2088-editCinsured,robot-2088-renewalPolicy,robot-2088-renewalPolicyCI,robot-2088-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2088-VehicleModelList" //上海车型查询        }        s += ",robot-2088-vehicleQueryXB,robot-2088-queryTaxAbateForPlat,robot-2088-calActualValue,robot-2088-editCitemKind,robot-2088-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2088-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-refreshPlanByTimes,robot-2088-insert"            }else{                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2088-getMaxCsellFee,robot-2088-getPrpCseller,robot-2088-getPrpCsellerCI,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            s += ",robot-2088-getMaxCsellFee,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"        }    }else{        s +=",robot-2088-ObtainConfig,robot-2088-checkInsurePerson,robot-2088-changePerson,robot-2088-checkInsuredPerson,robot-2088-changePerson,robot-2088-prepareEdit,robot-2088-selectRenewalPolicyNo,robot-2088-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2088-VehicleModelList" //上海车型查询        }        s += ",robot-2088-queryPrepare,robot-2088-vehicleQuery,robot-2088-queryTaxAbateForPlat,robot-2088-calActualValue,robot-2088-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2088-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2088-calAnciInfo,robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            }else{                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-nomotor-unitedSaleEdit,robot-2088-nomotor-saveUnitedSale,robot-2088-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2088-getMaxCsellFee,robot-2088-getPrpCseller,robot-2088-getPrpCsellerCI,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            s += ",robot-2088-getMaxCsellFee,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-nomotor-unitedSaleEdit,robot-2088-nomotor-saveUnitedSale,robot-2088-insert"        }         if("**********".equals(orgId)){            s = "robot-2088-login,robot_2088_bj_initData,robot_2088_bj_queryModel,robot_2088_bj_getSaleTaxInfo,robot_2088_bj_getRealValue,robot_2088_bj_getPersonData,robot_2088_bj_addPersonData,robot_2088_bj_askCharge,robot_2088_bj_queryPayForXSFY,robot_2088_bj_getCagentCI,robot_2088_bj_getCagent,robot_2088_bj_queryPayForXSFY_req,robot_2088_bj_queryIlogEngage,robot_2088_bj_insureRefrenshPlan,robot_2088_bj_insure4S,robot-2088-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2088', 'dinghe', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-鼎和-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ" +            ",robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind,robot-2088-nomotor-query,robot-2088-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2088', 'dinghe', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-鼎和-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ" +            ",robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind,robot-2088-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2088', 'dinghe', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-鼎和-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ" +            ",robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind,robot-2088-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2088', 'dinghe', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "鼎和财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-鼎和-电销', 'def getTemplateGroup(dataSource){    return "robot-2088-pureESale_Login,robot-2088-pureESale_Welcome,robot-2088-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2088', 'dinghe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-鼎和续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2088-login,robot-2088-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2088', 'dinghe', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-鼎和-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2088-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2088-ObtainConfig,robot-2088-selectRenewal,robot-2088-editCengage,robot-2088-editCitemCar,robot-2088-editCinsured,robot-2088-renewalPolicy,robot-2088-renewalPolicyCI,robot-2088-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2088-VehicleModelList" //上海车型查询        }        s += ",robot-2088-vehicleQueryXB,robot-2088-queryTaxAbateForPlat,robot-2088-calActualValue,robot-2088-editCitemKind,robot-2088-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2088-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-refreshPlanByTimes,robot-2088-insert"            }else{                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2088-calAnciInfo,robot-2088-getMaxCsellFee,robot-2088-getPrpCseller,robot-2088-getPrpCsellerCI,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            s += ",robot-2088-getMaxCsellFee,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"        }    }else{        s += ",robot-2088-ObtainConfig,robot-2088-checkInsurePerson,robot-2088-changePerson,robot-2088-checkInsuredPerson,robot-2088-changePerson,robot-2088-prepareEdit,robot-2088-editCengage,robot-2088-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2088-queryVehiclePMCheck,robot-2088-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2088-VehicleModelList" //上海车型查询        }        s += ",robot-2088-queryPrepare,robot-2088-vehicleQuery,robot-2088-queryTaxAbateForPlat,robot-2088-calActualValue,robot-2088-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2088-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2088-calAnciInfo,robot-2088-queryPayFor,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            }else{                s += ",robot-2088-calAnciInfo,robot-2088-checkAgentType,robot-2088-queryPayForSCMS,robot-2088-getCagent,robot-2088-getCagentCI,robot-2088-refreshPlanByTimes,robot-2088-nomotor-unitedSaleEdit,robot-2088-nomotor-saveUnitedSale,robot-2088-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2088-calAnciInfo,robot-2088-getMaxCsellFee,robot-2088-getPrpCseller,robot-2088-getPrpCsellerCI,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-insert"            s += ",robot-2088-getMaxCsellFee,robot-2088-queryPayForSCMS,robot-2088-refreshPlanByTimes,robot-2088-nomotor-unitedSaleEdit,robot-2088-nomotor-saveUnitedSale,robot-2088-insert"        }    }    s += ",robot-2088-checkRiskCode,robot-2088-editMainUwtFlag,robot-2088-editSubmitUndwrt,robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-showUndwrtMsgQ,robot-2088-showUndwrtMsgS"+            ",robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ,robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind,robot-2088-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2088-login,robot_2088_bj_initData,robot_2088_bj_queryModel,robot_2088_bj_getSaleTaxInfo,robot_2088_bj_getRealValue,robot_2088_bj_getPersonData,robot_2088_bj_addPersonData,robot_2088_bj_askCharge,robot_2088_bj_queryPayForXSFY,robot_2088_bj_getCagentCI,robot_2088_bj_getCagent,robot_2088_bj_queryPayForXSFY_req,robot_2088_bj_queryIlogEngage,robot_2088_bj_insureRefrenshPlan,robot_2088_bj_insure4S,robot-2088-uploadImage,robot_2088_bj_autoInsure,robot_2088_bj_showUndwrtMsgQ,robot_2088_bj_showUndwrtMsgS";       s += ",robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ,robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +                ",robot-2088-showCinsuredS,robot-2088-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2088', 'dinghe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-鼎和-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2088-qrcode_login,robot-2088-qrcode_printTwoBarCodeServlet,robot-2088-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2088-qrcode_login,robot-2088-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2088-qrcode_login,robot-2088-qrcode_editCheckFlag,robot-2088-qrcode_gotoJfcd,robot-2088-qrcode_prepareEditByJF,robot-2088-qrcode_getBusinessIn" +                ",robot-2088-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2088-qrcode_login,robot-2088-qrcode_editCheckFlag,robot-2088-qrcode_gotoJfcd,robot-2088-qrcode_prepareEditByJF,robot-2088-qrcode_getBusinessIn" +                ",robot-2088-qrcode_checkBeforeCalculate,robot-2088-qrcode_saveByJF,robot-2088-qrcode_getBusinessIn_alipay,robot-2088-qrcode_editFeeInfor,robot-2088-qrcode_editPayFeeByWeChat,robot-2088-qrcode_saveByWeChat,robot-2088-qrcode_save";		} else {					return  "robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-bj-editIDCardCheck,robot-2088-apply-selectIsNetProp,robot-2088-apply-saveCheckCode,robot-2088-qrcode_editCheckFlag,robot-2088-qrcode_gotoJfcd,robot-2088-qrcode_prepareEditByJF,robot-2088-qrcode_getBusinessIn" +",robot-2088-qrcode_checkBeforeCalculate,robot-2088-qrcode_saveByJF,robot-2088-qrcode_getBusinessIn_alipay,robot-2088-qrcode_editFeeInfor,robot-2088-qrcode_editPayFeeByWeChat,robot-2088-qrcode_saveByWeChat,robot-2088-qrcode_save";		}}    else {              return "robot-2088-qrcode_login,robot-2088-qrcode_editCheckFlag,robot-2088-qrcode_gotoJfcd,robot-2088-qrcode_prepareEditByJF,robot-2088-qrcode_getBusinessIn" +                ",robot-2088-qrcode_checkBeforeCalculate,robot-2088-qrcode_saveByJF,robot-2088-qrcode_getBusinessIn_alipay,robot-2088-qrcode_editFeeInfor,robot-2088-qrcode_editPayFeeByWeChat,robot-2088-qrcode_saveByWeChat,robot-2088-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2088', 'dinghe', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-鼎和-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2088-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2088-qrcode_query_editCheckFlag,robot-2088-qrcode_query_gotoJfcd,robot-2088-qrcode_query_prepareEditByJF" +                ",robot-2088-qrcode_query_editMainInfor,robot-2088-qrcode_query_getBusinessIn,robot-2088-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ" +            ",robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind,robot-2088-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2088', 'dinghe', '15', '6', 'pro', 'other', b'1', '{}', '精灵-鼎和-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-IdCarChekc" //申请验证码    else{        return"robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-editIDCardCheck,robot-2088-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2088', 'dinghe', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-鼎和-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectPolicyefc,robot-2088-selectPolicybiz,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ" +            ",robot-2088-showCitemCarQ,robot-2088-showCinsuredQ,robot-2088-showCitemKindCI,robot-2088-browseProposalS,robot-2088-showCitemCarS" +            ",robot-2088-showCinsuredS,robot-2088-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2088', 'dinghe', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2088-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectProposalQ,robot-2088-selectProposalS,robot-2088-browseProposalQ,robot-2088-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2088', 'dinghe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-鼎和-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-bj-editIDCardCheck,robot-2088-apply-bj-IdCarChekc";    } else {        s = "robot-2088-qrcode_login,robot-2088-qrcode_editCheckFlag,robot-2088-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2088', 'dinghe', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi鼎和报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2088_ask_charge,edi_2088_noMotor_quote"	} else {		return "edi_2088_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2088', 'dinghe', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-鼎和-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2088_ask_charge,edi_2088_noMotor_quote,edi_2088_askInsure,edi_2088_uploadImg,edi_2088_submitInsure,edi_2088_noMotor_submit" 	  	} else {		return "edi_2088_ask_chargeold,edi_2088_askInsure,edi_2088_uploadImg,edi_2088_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2088', 'dinghe', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-鼎和-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2088_ask_charge,edi_2088_noMotor_quote,edi_2088_askInsure,edi_2088_uploadImg" 	} else {		return "edi_2088_ask_chargeold,edi_2088_askInsure,edi_2088_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2088', 'dinghe', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-鼎和-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2088_efc_policyinfo,edi_2088_biz_policyinfo,edi_2088_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2088', 'dinghe', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-鼎和-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2088_efc_insurequery,edi_2088_biz_insurequery,edi_2088_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2088', 'dinghe', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京鼎和短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-bj-editIDCardCheck,robot-2088-apply-saveCheckCode,robot-2088-apply-selectIsNetProp";    } else {        s = "robot-2088-qrcode_login,robot-2088-apply-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2088', 'dinghe', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-鼎和-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2088-bj-qrcode_login,robot-2088-apply-bj-prepare,robot-2088-apply-select-jq,robot-2088-apply-select-sy,robot-2088-apply-bj-editIDCardCheck,robot-2088-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2088', 'dinghe', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-鼎和-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2088_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2088', 'dinghe', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi鼎和北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2088_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2088', 'dinghe', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi鼎和北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2088_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2088', 'dinghe', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-鼎和-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2088-login,robot-2088-prepareQueryCode,robot-2088-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2085', 'libao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-利宝-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-IdCarChekc" //申请验证码    else{        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2085', 'libao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-利宝-报价', 'def getTemplateGroup(dataSource) {    return "edi-2085-queryCar,edi-2085-xbQuery,edi-2085-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2085', 'libao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-利宝-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2085-queryCar,edi-2085-xbQuery,edi-2085-askCharge,edi-2085-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2085', 'libao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-利宝-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2085-queryCar,edi-2085-xbQuery,edi-2085-askCharge,edi-2085-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2085', 'libao', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-利宝-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2085-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2085', 'libao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-利宝-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2085-login,robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2085-login,robot-2085-ObtainConfig,robot-2085-checkInsurePerson,robot-2085-changePerson,robot-2085-checkInsuredPerson,robot-2085-changePerson,robot-2085-prepareEdit," +                    "robot-2085-prepareQueryCode,robot-2085-selectProposalCar,robot-2085-browseProposalCar,robot-2085-browseProposalCarefc,robot-2085-selectRenewalPolicyNo"        }else{            s = "robot-2085-login,robot-2085-ObtainConfig,robot-2085-checkInsurePerson,robot-2085-changePerson,robot-2085-checkInsuredPerson,robot-2085-changePerson,robot-2085-prepareEdit," +                    "robot-2085-prepareQueryCode,robot-2085-browseProposalCar,robot-2085-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2085-VehicleModelList" //上海车型查询        }        s += ",robot-2085-queryPrepare,robot-2085-vehicleQuery,robot-2085-queryTaxAbateForPlat,robot-2085-calActualValue,robot-2085-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2085-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2085-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-nomotor-unitedSaleEdit,robot-2085-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2085-login,robot_2085_bj_initData,robot_2085_bj_queryModel,robot_2085_bj_getSaleTaxInfo,robot_2085_bj_getRealValue,robot_2085_bj_getPersonData,robot_2085_bj_addPersonData,robot_2085_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2085', 'libao', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-利宝-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2085-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2085-ObtainConfig,robot-2085-selectRenewal,robot-2085-editCengage,robot-2085-editCitemCar,robot-2085-editCinsured,robot-2085-renewalPolicy,robot-2085-renewalPolicyCI,robot-2085-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2085-VehicleModelList" //上海车型查询        }        s += ",robot-2085-vehicleQueryXB,robot-2085-queryTaxAbateForPlat,robot-2085-calActualValue,robot-2085-editCitemKind,robot-2085-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2085-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-refreshPlanByTimes,robot-2085-insert"            }else{                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2085-getMaxCsellFee,robot-2085-getPrpCseller,robot-2085-getPrpCsellerCI,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            s += ",robot-2085-getMaxCsellFee,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"        }    }else{        s +=",robot-2085-ObtainConfig,robot-2085-checkInsurePerson,robot-2085-changePerson,robot-2085-checkInsuredPerson,robot-2085-changePerson,robot-2085-prepareEdit,robot-2085-selectRenewalPolicyNo,robot-2085-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2085-VehicleModelList" //上海车型查询        }        s += ",robot-2085-queryPrepare,robot-2085-vehicleQuery,robot-2085-queryTaxAbateForPlat,robot-2085-calActualValue,robot-2085-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2085-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2085-calAnciInfo,robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            }else{                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-nomotor-unitedSaleEdit,robot-2085-nomotor-saveUnitedSale,robot-2085-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2085-getMaxCsellFee,robot-2085-getPrpCseller,robot-2085-getPrpCsellerCI,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            s += ",robot-2085-getMaxCsellFee,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-nomotor-unitedSaleEdit,robot-2085-nomotor-saveUnitedSale,robot-2085-insert"        }         if("**********".equals(orgId)){            s = "robot-2085-login,robot_2085_bj_initData,robot_2085_bj_queryModel,robot_2085_bj_getSaleTaxInfo,robot_2085_bj_getRealValue,robot_2085_bj_getPersonData,robot_2085_bj_addPersonData,robot_2085_bj_askCharge,robot_2085_bj_queryPayForXSFY,robot_2085_bj_getCagentCI,robot_2085_bj_getCagent,robot_2085_bj_queryPayForXSFY_req,robot_2085_bj_queryIlogEngage,robot_2085_bj_insureRefrenshPlan,robot_2085_bj_insure4S,robot-2085-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2085', 'libao', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-利宝-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ" +            ",robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind,robot-2085-nomotor-query,robot-2085-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2085', 'libao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-利宝-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ" +            ",robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind,robot-2085-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2085', 'libao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-利宝-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ" +            ",robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind,robot-2085-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2085', 'libao', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "利宝财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-利宝-电销', 'def getTemplateGroup(dataSource){    return "robot-2085-pureESale_Login,robot-2085-pureESale_Welcome,robot-2085-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2085', 'libao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-利宝续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2085-login,robot-2085-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2085', 'libao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-利宝-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2085-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2085-ObtainConfig,robot-2085-selectRenewal,robot-2085-editCengage,robot-2085-editCitemCar,robot-2085-editCinsured,robot-2085-renewalPolicy,robot-2085-renewalPolicyCI,robot-2085-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2085-VehicleModelList" //上海车型查询        }        s += ",robot-2085-vehicleQueryXB,robot-2085-queryTaxAbateForPlat,robot-2085-calActualValue,robot-2085-editCitemKind,robot-2085-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2085-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-refreshPlanByTimes,robot-2085-insert"            }else{                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2085-calAnciInfo,robot-2085-getMaxCsellFee,robot-2085-getPrpCseller,robot-2085-getPrpCsellerCI,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            s += ",robot-2085-getMaxCsellFee,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"        }    }else{        s += ",robot-2085-ObtainConfig,robot-2085-checkInsurePerson,robot-2085-changePerson,robot-2085-checkInsuredPerson,robot-2085-changePerson,robot-2085-prepareEdit,robot-2085-editCengage,robot-2085-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2085-queryVehiclePMCheck,robot-2085-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2085-VehicleModelList" //上海车型查询        }        s += ",robot-2085-queryPrepare,robot-2085-vehicleQuery,robot-2085-queryTaxAbateForPlat,robot-2085-calActualValue,robot-2085-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2085-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2085-calAnciInfo,robot-2085-queryPayFor,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            }else{                s += ",robot-2085-calAnciInfo,robot-2085-checkAgentType,robot-2085-queryPayForSCMS,robot-2085-getCagent,robot-2085-getCagentCI,robot-2085-refreshPlanByTimes,robot-2085-nomotor-unitedSaleEdit,robot-2085-nomotor-saveUnitedSale,robot-2085-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2085-calAnciInfo,robot-2085-getMaxCsellFee,robot-2085-getPrpCseller,robot-2085-getPrpCsellerCI,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-insert"            s += ",robot-2085-getMaxCsellFee,robot-2085-queryPayForSCMS,robot-2085-refreshPlanByTimes,robot-2085-nomotor-unitedSaleEdit,robot-2085-nomotor-saveUnitedSale,robot-2085-insert"        }    }    s += ",robot-2085-checkRiskCode,robot-2085-editMainUwtFlag,robot-2085-editSubmitUndwrt,robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-showUndwrtMsgQ,robot-2085-showUndwrtMsgS"+            ",robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ,robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind,robot-2085-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2085-login,robot_2085_bj_initData,robot_2085_bj_queryModel,robot_2085_bj_getSaleTaxInfo,robot_2085_bj_getRealValue,robot_2085_bj_getPersonData,robot_2085_bj_addPersonData,robot_2085_bj_askCharge,robot_2085_bj_queryPayForXSFY,robot_2085_bj_getCagentCI,robot_2085_bj_getCagent,robot_2085_bj_queryPayForXSFY_req,robot_2085_bj_queryIlogEngage,robot_2085_bj_insureRefrenshPlan,robot_2085_bj_insure4S,robot-2085-uploadImage,robot_2085_bj_autoInsure,robot_2085_bj_showUndwrtMsgQ,robot_2085_bj_showUndwrtMsgS";       s += ",robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ,robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +                ",robot-2085-showCinsuredS,robot-2085-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2085', 'libao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-利宝-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2085-qrcode_login,robot-2085-qrcode_printTwoBarCodeServlet,robot-2085-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2085-qrcode_login,robot-2085-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2085-qrcode_login,robot-2085-qrcode_editCheckFlag,robot-2085-qrcode_gotoJfcd,robot-2085-qrcode_prepareEditByJF,robot-2085-qrcode_getBusinessIn" +                ",robot-2085-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2085-qrcode_login,robot-2085-qrcode_editCheckFlag,robot-2085-qrcode_gotoJfcd,robot-2085-qrcode_prepareEditByJF,robot-2085-qrcode_getBusinessIn" +                ",robot-2085-qrcode_checkBeforeCalculate,robot-2085-qrcode_saveByJF,robot-2085-qrcode_getBusinessIn_alipay,robot-2085-qrcode_editFeeInfor,robot-2085-qrcode_editPayFeeByWeChat,robot-2085-qrcode_saveByWeChat,robot-2085-qrcode_save";		} else {					return  "robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-bj-editIDCardCheck,robot-2085-apply-selectIsNetProp,robot-2085-apply-saveCheckCode,robot-2085-qrcode_editCheckFlag,robot-2085-qrcode_gotoJfcd,robot-2085-qrcode_prepareEditByJF,robot-2085-qrcode_getBusinessIn" +",robot-2085-qrcode_checkBeforeCalculate,robot-2085-qrcode_saveByJF,robot-2085-qrcode_getBusinessIn_alipay,robot-2085-qrcode_editFeeInfor,robot-2085-qrcode_editPayFeeByWeChat,robot-2085-qrcode_saveByWeChat,robot-2085-qrcode_save";		}}    else {              return "robot-2085-qrcode_login,robot-2085-qrcode_editCheckFlag,robot-2085-qrcode_gotoJfcd,robot-2085-qrcode_prepareEditByJF,robot-2085-qrcode_getBusinessIn" +                ",robot-2085-qrcode_checkBeforeCalculate,robot-2085-qrcode_saveByJF,robot-2085-qrcode_getBusinessIn_alipay,robot-2085-qrcode_editFeeInfor,robot-2085-qrcode_editPayFeeByWeChat,robot-2085-qrcode_saveByWeChat,robot-2085-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2085', 'libao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-利宝-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2085-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2085-qrcode_query_editCheckFlag,robot-2085-qrcode_query_gotoJfcd,robot-2085-qrcode_query_prepareEditByJF" +                ",robot-2085-qrcode_query_editMainInfor,robot-2085-qrcode_query_getBusinessIn,robot-2085-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ" +            ",robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind,robot-2085-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2085', 'libao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-利宝-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-IdCarChekc" //申请验证码    else{        return"robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-editIDCardCheck,robot-2085-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2085', 'libao', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-利宝-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectPolicyefc,robot-2085-selectPolicybiz,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ" +            ",robot-2085-showCitemCarQ,robot-2085-showCinsuredQ,robot-2085-showCitemKindCI,robot-2085-browseProposalS,robot-2085-showCitemCarS" +            ",robot-2085-showCinsuredS,robot-2085-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2085', 'libao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2085-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectProposalQ,robot-2085-selectProposalS,robot-2085-browseProposalQ,robot-2085-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2085', 'libao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-利宝-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-bj-editIDCardCheck,robot-2085-apply-bj-IdCarChekc";    } else {        s = "robot-2085-qrcode_login,robot-2085-qrcode_editCheckFlag,robot-2085-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2085', 'libao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi利宝报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2085_ask_charge,edi_2085_noMotor_quote"	} else {		return "edi_2085_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2085', 'libao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-利宝-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2085_ask_charge,edi_2085_noMotor_quote,edi_2085_askInsure,edi_2085_uploadImg,edi_2085_submitInsure,edi_2085_noMotor_submit" 	  	} else {		return "edi_2085_ask_chargeold,edi_2085_askInsure,edi_2085_uploadImg,edi_2085_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2085', 'libao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-利宝-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2085_ask_charge,edi_2085_noMotor_quote,edi_2085_askInsure,edi_2085_uploadImg" 	} else {		return "edi_2085_ask_chargeold,edi_2085_askInsure,edi_2085_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2085', 'libao', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-利宝-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2085_efc_policyinfo,edi_2085_biz_policyinfo,edi_2085_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2085', 'libao', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-利宝-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2085_efc_insurequery,edi_2085_biz_insurequery,edi_2085_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2085', 'libao', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京利宝短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-bj-editIDCardCheck,robot-2085-apply-saveCheckCode,robot-2085-apply-selectIsNetProp";    } else {        s = "robot-2085-qrcode_login,robot-2085-apply-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2085', 'libao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-利宝-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2085-bj-qrcode_login,robot-2085-apply-bj-prepare,robot-2085-apply-select-jq,robot-2085-apply-select-sy,robot-2085-apply-bj-editIDCardCheck,robot-2085-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2085', 'libao', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-利宝-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2085_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2085', 'libao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi利宝北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2085_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2085', 'libao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi利宝北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2085_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2085', 'libao', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-利宝-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2085-login,robot-2085-prepareQueryCode,robot-2085-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2005', 'renbao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-人保-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-IdCarChekc" //申请验证码    else{        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2005', 'renbao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-人保-报价', 'def getTemplateGroup(dataSource) {    return "edi-2005-queryCar,edi-2005-xbQuery,edi-2005-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2005', 'renbao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-人保-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2005-queryCar,edi-2005-xbQuery,edi-2005-askCharge,edi-2005-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2005', 'renbao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-人保-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2005-queryCar,edi-2005-xbQuery,edi-2005-askCharge,edi-2005-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2005', 'renbao', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-人保-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2005-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2005', 'renbao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-人保-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2005-login,robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2005-login,robot-2005-ObtainConfig,robot-2005-checkInsurePerson,robot-2005-changePerson,robot-2005-checkInsuredPerson,robot-2005-changePerson,robot-2005-prepareEdit," +                    "robot-2005-prepareQueryCode,robot-2005-selectProposalCar,robot-2005-browseProposalCar,robot-2005-browseProposalCarefc,robot-2005-selectRenewalPolicyNo"        }else{            s = "robot-2005-login,robot-2005-ObtainConfig,robot-2005-checkInsurePerson,robot-2005-changePerson,robot-2005-checkInsuredPerson,robot-2005-changePerson,robot-2005-prepareEdit," +                    "robot-2005-prepareQueryCode,robot-2005-browseProposalCar,robot-2005-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2005-VehicleModelList" //上海车型查询        }        s += ",robot-2005-queryPrepare,robot-2005-vehicleQuery,robot-2005-queryTaxAbateForPlat,robot-2005-calActualValue,robot-2005-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2005-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2005-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-nomotor-unitedSaleEdit,robot-2005-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2005-login,robot_2005_bj_initData,robot_2005_bj_queryModel,robot_2005_bj_getSaleTaxInfo,robot_2005_bj_getRealValue,robot_2005_bj_getPersonData,robot_2005_bj_addPersonData,robot_2005_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2005', 'renbao', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-人保-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2005-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2005-ObtainConfig,robot-2005-selectRenewal,robot-2005-editCengage,robot-2005-editCitemCar,robot-2005-editCinsured,robot-2005-renewalPolicy,robot-2005-renewalPolicyCI,robot-2005-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2005-VehicleModelList" //上海车型查询        }        s += ",robot-2005-vehicleQueryXB,robot-2005-queryTaxAbateForPlat,robot-2005-calActualValue,robot-2005-editCitemKind,robot-2005-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2005-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-refreshPlanByTimes,robot-2005-insert"            }else{                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2005-getMaxCsellFee,robot-2005-getPrpCseller,robot-2005-getPrpCsellerCI,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            s += ",robot-2005-getMaxCsellFee,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"        }    }else{        s +=",robot-2005-ObtainConfig,robot-2005-checkInsurePerson,robot-2005-changePerson,robot-2005-checkInsuredPerson,robot-2005-changePerson,robot-2005-prepareEdit,robot-2005-selectRenewalPolicyNo,robot-2005-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2005-VehicleModelList" //上海车型查询        }        s += ",robot-2005-queryPrepare,robot-2005-vehicleQuery,robot-2005-queryTaxAbateForPlat,robot-2005-calActualValue,robot-2005-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2005-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2005-calAnciInfo,robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            }else{                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-nomotor-unitedSaleEdit,robot-2005-nomotor-saveUnitedSale,robot-2005-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2005-getMaxCsellFee,robot-2005-getPrpCseller,robot-2005-getPrpCsellerCI,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            s += ",robot-2005-getMaxCsellFee,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-nomotor-unitedSaleEdit,robot-2005-nomotor-saveUnitedSale,robot-2005-insert"        }         if("**********".equals(orgId)){            s = "robot-2005-login,robot_2005_bj_initData,robot_2005_bj_queryModel,robot_2005_bj_getSaleTaxInfo,robot_2005_bj_getRealValue,robot_2005_bj_getPersonData,robot_2005_bj_addPersonData,robot_2005_bj_askCharge,robot_2005_bj_queryPayForXSFY,robot_2005_bj_getCagentCI,robot_2005_bj_getCagent,robot_2005_bj_queryPayForXSFY_req,robot_2005_bj_queryIlogEngage,robot_2005_bj_insureRefrenshPlan,robot_2005_bj_insure4S,robot-2005-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2005', 'renbao', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-人保-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ" +            ",robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind,robot-2005-nomotor-query,robot-2005-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2005', 'renbao', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-人保-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ" +            ",robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind,robot-2005-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2005', 'renbao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-人保-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ" +            ",robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind,robot-2005-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2005', 'renbao', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "人保财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-人保-电销', 'def getTemplateGroup(dataSource){    return "robot-2005-pureESale_Login,robot-2005-pureESale_Welcome,robot-2005-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2005', 'renbao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-人保续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2005-login,robot-2005-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2005', 'renbao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-人保-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2005-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2005-ObtainConfig,robot-2005-selectRenewal,robot-2005-editCengage,robot-2005-editCitemCar,robot-2005-editCinsured,robot-2005-renewalPolicy,robot-2005-renewalPolicyCI,robot-2005-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2005-VehicleModelList" //上海车型查询        }        s += ",robot-2005-vehicleQueryXB,robot-2005-queryTaxAbateForPlat,robot-2005-calActualValue,robot-2005-editCitemKind,robot-2005-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2005-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-refreshPlanByTimes,robot-2005-insert"            }else{                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2005-calAnciInfo,robot-2005-getMaxCsellFee,robot-2005-getPrpCseller,robot-2005-getPrpCsellerCI,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            s += ",robot-2005-getMaxCsellFee,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"        }    }else{        s += ",robot-2005-ObtainConfig,robot-2005-checkInsurePerson,robot-2005-changePerson,robot-2005-checkInsuredPerson,robot-2005-changePerson,robot-2005-prepareEdit,robot-2005-editCengage,robot-2005-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2005-queryVehiclePMCheck,robot-2005-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2005-VehicleModelList" //上海车型查询        }        s += ",robot-2005-queryPrepare,robot-2005-vehicleQuery,robot-2005-queryTaxAbateForPlat,robot-2005-calActualValue,robot-2005-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2005-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2005-calAnciInfo,robot-2005-queryPayFor,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            }else{                s += ",robot-2005-calAnciInfo,robot-2005-checkAgentType,robot-2005-queryPayForSCMS,robot-2005-getCagent,robot-2005-getCagentCI,robot-2005-refreshPlanByTimes,robot-2005-nomotor-unitedSaleEdit,robot-2005-nomotor-saveUnitedSale,robot-2005-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2005-calAnciInfo,robot-2005-getMaxCsellFee,robot-2005-getPrpCseller,robot-2005-getPrpCsellerCI,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-insert"            s += ",robot-2005-getMaxCsellFee,robot-2005-queryPayForSCMS,robot-2005-refreshPlanByTimes,robot-2005-nomotor-unitedSaleEdit,robot-2005-nomotor-saveUnitedSale,robot-2005-insert"        }    }    s += ",robot-2005-checkRiskCode,robot-2005-editMainUwtFlag,robot-2005-editSubmitUndwrt,robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-showUndwrtMsgQ,robot-2005-showUndwrtMsgS"+            ",robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ,robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind,robot-2005-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2005-login,robot_2005_bj_initData,robot_2005_bj_queryModel,robot_2005_bj_getSaleTaxInfo,robot_2005_bj_getRealValue,robot_2005_bj_getPersonData,robot_2005_bj_addPersonData,robot_2005_bj_askCharge,robot_2005_bj_queryPayForXSFY,robot_2005_bj_getCagentCI,robot_2005_bj_getCagent,robot_2005_bj_queryPayForXSFY_req,robot_2005_bj_queryIlogEngage,robot_2005_bj_insureRefrenshPlan,robot_2005_bj_insure4S,robot-2005-uploadImage,robot_2005_bj_autoInsure,robot_2005_bj_showUndwrtMsgQ,robot_2005_bj_showUndwrtMsgS";       s += ",robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ,robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +                ",robot-2005-showCinsuredS,robot-2005-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2005', 'renbao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-人保-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2005-qrcode_login,robot-2005-qrcode_printTwoBarCodeServlet,robot-2005-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2005-qrcode_login,robot-2005-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2005-qrcode_login,robot-2005-qrcode_editCheckFlag,robot-2005-qrcode_gotoJfcd,robot-2005-qrcode_prepareEditByJF,robot-2005-qrcode_getBusinessIn" +                ",robot-2005-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2005-qrcode_login,robot-2005-qrcode_editCheckFlag,robot-2005-qrcode_gotoJfcd,robot-2005-qrcode_prepareEditByJF,robot-2005-qrcode_getBusinessIn" +                ",robot-2005-qrcode_checkBeforeCalculate,robot-2005-qrcode_saveByJF,robot-2005-qrcode_getBusinessIn_alipay,robot-2005-qrcode_editFeeInfor,robot-2005-qrcode_editPayFeeByWeChat,robot-2005-qrcode_saveByWeChat,robot-2005-qrcode_save";		} else {					return  "robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-bj-editIDCardCheck,robot-2005-apply-selectIsNetProp,robot-2005-apply-saveCheckCode,robot-2005-qrcode_editCheckFlag,robot-2005-qrcode_gotoJfcd,robot-2005-qrcode_prepareEditByJF,robot-2005-qrcode_getBusinessIn" +",robot-2005-qrcode_checkBeforeCalculate,robot-2005-qrcode_saveByJF,robot-2005-qrcode_getBusinessIn_alipay,robot-2005-qrcode_editFeeInfor,robot-2005-qrcode_editPayFeeByWeChat,robot-2005-qrcode_saveByWeChat,robot-2005-qrcode_save";		}}    else {              return "robot-2005-qrcode_login,robot-2005-qrcode_editCheckFlag,robot-2005-qrcode_gotoJfcd,robot-2005-qrcode_prepareEditByJF,robot-2005-qrcode_getBusinessIn" +                ",robot-2005-qrcode_checkBeforeCalculate,robot-2005-qrcode_saveByJF,robot-2005-qrcode_getBusinessIn_alipay,robot-2005-qrcode_editFeeInfor,robot-2005-qrcode_editPayFeeByWeChat,robot-2005-qrcode_saveByWeChat,robot-2005-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2005', 'renbao', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-人保-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2005-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2005-qrcode_query_editCheckFlag,robot-2005-qrcode_query_gotoJfcd,robot-2005-qrcode_query_prepareEditByJF" +                ",robot-2005-qrcode_query_editMainInfor,robot-2005-qrcode_query_getBusinessIn,robot-2005-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ" +            ",robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind,robot-2005-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2005', 'renbao', '15', '6', 'pro', 'other', b'1', '{}', '精灵-人保-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-IdCarChekc" //申请验证码    else{        return"robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-editIDCardCheck,robot-2005-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2005', 'renbao', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-人保-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectPolicyefc,robot-2005-selectPolicybiz,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ" +            ",robot-2005-showCitemCarQ,robot-2005-showCinsuredQ,robot-2005-showCitemKindCI,robot-2005-browseProposalS,robot-2005-showCitemCarS" +            ",robot-2005-showCinsuredS,robot-2005-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2005', 'renbao', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2005-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectProposalQ,robot-2005-selectProposalS,robot-2005-browseProposalQ,robot-2005-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2005', 'renbao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-人保-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-bj-editIDCardCheck,robot-2005-apply-bj-IdCarChekc";    } else {        s = "robot-2005-qrcode_login,robot-2005-qrcode_editCheckFlag,robot-2005-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2005', 'renbao', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi人保报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2005_ask_charge,edi_2005_noMotor_quote"	} else {		return "edi_2005_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2005', 'renbao', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-人保-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2005_ask_charge,edi_2005_noMotor_quote,edi_2005_askInsure,edi_2005_uploadImg,edi_2005_submitInsure,edi_2005_noMotor_submit" 	  	} else {		return "edi_2005_ask_chargeold,edi_2005_askInsure,edi_2005_uploadImg,edi_2005_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2005', 'renbao', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-人保-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2005_ask_charge,edi_2005_noMotor_quote,edi_2005_askInsure,edi_2005_uploadImg" 	} else {		return "edi_2005_ask_chargeold,edi_2005_askInsure,edi_2005_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2005', 'renbao', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-人保-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2005_efc_policyinfo,edi_2005_biz_policyinfo,edi_2005_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2005', 'renbao', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-人保-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2005_efc_insurequery,edi_2005_biz_insurequery,edi_2005_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2005', 'renbao', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京人保短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-bj-editIDCardCheck,robot-2005-apply-saveCheckCode,robot-2005-apply-selectIsNetProp";    } else {        s = "robot-2005-qrcode_login,robot-2005-apply-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2005', 'renbao', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-人保-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2005-bj-qrcode_login,robot-2005-apply-bj-prepare,robot-2005-apply-select-jq,robot-2005-apply-select-sy,robot-2005-apply-bj-editIDCardCheck,robot-2005-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2005', 'renbao', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-人保-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2005_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2005', 'renbao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi人保北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2005_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2005', 'renbao', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi人保北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2005_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2005', 'renbao', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-人保-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2005-login,robot-2005-prepareQueryCode,robot-2005-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2016', 'taiping', '15', '6', 'pro', 'other', b'1', '{}', '精灵-太平-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-IdCarChekc" //申请验证码    else{        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2016', 'taiping', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-太平-报价', 'def getTemplateGroup(dataSource) {    return "edi-2016-queryCar,edi-2016-xbQuery,edi-2016-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2016', 'taiping', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-太平-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2016-queryCar,edi-2016-xbQuery,edi-2016-askCharge,edi-2016-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2016', 'taiping', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-太平-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2016-queryCar,edi-2016-xbQuery,edi-2016-askCharge,edi-2016-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2016', 'taiping', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-太平-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2016-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2016', 'taiping', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-太平-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2016-login,robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2016-login,robot-2016-ObtainConfig,robot-2016-checkInsurePerson,robot-2016-changePerson,robot-2016-checkInsuredPerson,robot-2016-changePerson,robot-2016-prepareEdit," +                    "robot-2016-prepareQueryCode,robot-2016-selectProposalCar,robot-2016-browseProposalCar,robot-2016-browseProposalCarefc,robot-2016-selectRenewalPolicyNo"        }else{            s = "robot-2016-login,robot-2016-ObtainConfig,robot-2016-checkInsurePerson,robot-2016-changePerson,robot-2016-checkInsuredPerson,robot-2016-changePerson,robot-2016-prepareEdit," +                    "robot-2016-prepareQueryCode,robot-2016-browseProposalCar,robot-2016-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2016-VehicleModelList" //上海车型查询        }        s += ",robot-2016-queryPrepare,robot-2016-vehicleQuery,robot-2016-queryTaxAbateForPlat,robot-2016-calActualValue,robot-2016-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2016-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2016-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-nomotor-unitedSaleEdit,robot-2016-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2016-login,robot_2016_bj_initData,robot_2016_bj_queryModel,robot_2016_bj_getSaleTaxInfo,robot_2016_bj_getRealValue,robot_2016_bj_getPersonData,robot_2016_bj_addPersonData,robot_2016_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2016', 'taiping', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-太平-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2016-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2016-ObtainConfig,robot-2016-selectRenewal,robot-2016-editCengage,robot-2016-editCitemCar,robot-2016-editCinsured,robot-2016-renewalPolicy,robot-2016-renewalPolicyCI,robot-2016-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2016-VehicleModelList" //上海车型查询        }        s += ",robot-2016-vehicleQueryXB,robot-2016-queryTaxAbateForPlat,robot-2016-calActualValue,robot-2016-editCitemKind,robot-2016-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2016-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-refreshPlanByTimes,robot-2016-insert"            }else{                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2016-getMaxCsellFee,robot-2016-getPrpCseller,robot-2016-getPrpCsellerCI,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            s += ",robot-2016-getMaxCsellFee,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"        }    }else{        s +=",robot-2016-ObtainConfig,robot-2016-checkInsurePerson,robot-2016-changePerson,robot-2016-checkInsuredPerson,robot-2016-changePerson,robot-2016-prepareEdit,robot-2016-selectRenewalPolicyNo,robot-2016-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2016-VehicleModelList" //上海车型查询        }        s += ",robot-2016-queryPrepare,robot-2016-vehicleQuery,robot-2016-queryTaxAbateForPlat,robot-2016-calActualValue,robot-2016-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2016-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2016-calAnciInfo,robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            }else{                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-nomotor-unitedSaleEdit,robot-2016-nomotor-saveUnitedSale,robot-2016-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2016-getMaxCsellFee,robot-2016-getPrpCseller,robot-2016-getPrpCsellerCI,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            s += ",robot-2016-getMaxCsellFee,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-nomotor-unitedSaleEdit,robot-2016-nomotor-saveUnitedSale,robot-2016-insert"        }         if("**********".equals(orgId)){            s = "robot-2016-login,robot_2016_bj_initData,robot_2016_bj_queryModel,robot_2016_bj_getSaleTaxInfo,robot_2016_bj_getRealValue,robot_2016_bj_getPersonData,robot_2016_bj_addPersonData,robot_2016_bj_askCharge,robot_2016_bj_queryPayForXSFY,robot_2016_bj_getCagentCI,robot_2016_bj_getCagent,robot_2016_bj_queryPayForXSFY_req,robot_2016_bj_queryIlogEngage,robot_2016_bj_insureRefrenshPlan,robot_2016_bj_insure4S,robot-2016-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2016', 'taiping', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-太平-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ" +            ",robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind,robot-2016-nomotor-query,robot-2016-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2016', 'taiping', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-太平-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ" +            ",robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind,robot-2016-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2016', 'taiping', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-太平-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ" +            ",robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind,robot-2016-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2016', 'taiping', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "太平财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-太平-电销', 'def getTemplateGroup(dataSource){    return "robot-2016-pureESale_Login,robot-2016-pureESale_Welcome,robot-2016-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2016', 'taiping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2016-login,robot-2016-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2016', 'taiping', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-太平-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2016-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2016-ObtainConfig,robot-2016-selectRenewal,robot-2016-editCengage,robot-2016-editCitemCar,robot-2016-editCinsured,robot-2016-renewalPolicy,robot-2016-renewalPolicyCI,robot-2016-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2016-VehicleModelList" //上海车型查询        }        s += ",robot-2016-vehicleQueryXB,robot-2016-queryTaxAbateForPlat,robot-2016-calActualValue,robot-2016-editCitemKind,robot-2016-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2016-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-refreshPlanByTimes,robot-2016-insert"            }else{                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2016-calAnciInfo,robot-2016-getMaxCsellFee,robot-2016-getPrpCseller,robot-2016-getPrpCsellerCI,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            s += ",robot-2016-getMaxCsellFee,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"        }    }else{        s += ",robot-2016-ObtainConfig,robot-2016-checkInsurePerson,robot-2016-changePerson,robot-2016-checkInsuredPerson,robot-2016-changePerson,robot-2016-prepareEdit,robot-2016-editCengage,robot-2016-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2016-queryVehiclePMCheck,robot-2016-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2016-VehicleModelList" //上海车型查询        }        s += ",robot-2016-queryPrepare,robot-2016-vehicleQuery,robot-2016-queryTaxAbateForPlat,robot-2016-calActualValue,robot-2016-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2016-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2016-calAnciInfo,robot-2016-queryPayFor,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            }else{                s += ",robot-2016-calAnciInfo,robot-2016-checkAgentType,robot-2016-queryPayForSCMS,robot-2016-getCagent,robot-2016-getCagentCI,robot-2016-refreshPlanByTimes,robot-2016-nomotor-unitedSaleEdit,robot-2016-nomotor-saveUnitedSale,robot-2016-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2016-calAnciInfo,robot-2016-getMaxCsellFee,robot-2016-getPrpCseller,robot-2016-getPrpCsellerCI,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-insert"            s += ",robot-2016-getMaxCsellFee,robot-2016-queryPayForSCMS,robot-2016-refreshPlanByTimes,robot-2016-nomotor-unitedSaleEdit,robot-2016-nomotor-saveUnitedSale,robot-2016-insert"        }    }    s += ",robot-2016-checkRiskCode,robot-2016-editMainUwtFlag,robot-2016-editSubmitUndwrt,robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-showUndwrtMsgQ,robot-2016-showUndwrtMsgS"+            ",robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ,robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind,robot-2016-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2016-login,robot_2016_bj_initData,robot_2016_bj_queryModel,robot_2016_bj_getSaleTaxInfo,robot_2016_bj_getRealValue,robot_2016_bj_getPersonData,robot_2016_bj_addPersonData,robot_2016_bj_askCharge,robot_2016_bj_queryPayForXSFY,robot_2016_bj_getCagentCI,robot_2016_bj_getCagent,robot_2016_bj_queryPayForXSFY_req,robot_2016_bj_queryIlogEngage,robot_2016_bj_insureRefrenshPlan,robot_2016_bj_insure4S,robot-2016-uploadImage,robot_2016_bj_autoInsure,robot_2016_bj_showUndwrtMsgQ,robot_2016_bj_showUndwrtMsgS";       s += ",robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ,robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +                ",robot-2016-showCinsuredS,robot-2016-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2016', 'taiping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2016-qrcode_login,robot-2016-qrcode_printTwoBarCodeServlet,robot-2016-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2016-qrcode_login,robot-2016-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2016-qrcode_login,robot-2016-qrcode_editCheckFlag,robot-2016-qrcode_gotoJfcd,robot-2016-qrcode_prepareEditByJF,robot-2016-qrcode_getBusinessIn" +                ",robot-2016-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2016-qrcode_login,robot-2016-qrcode_editCheckFlag,robot-2016-qrcode_gotoJfcd,robot-2016-qrcode_prepareEditByJF,robot-2016-qrcode_getBusinessIn" +                ",robot-2016-qrcode_checkBeforeCalculate,robot-2016-qrcode_saveByJF,robot-2016-qrcode_getBusinessIn_alipay,robot-2016-qrcode_editFeeInfor,robot-2016-qrcode_editPayFeeByWeChat,robot-2016-qrcode_saveByWeChat,robot-2016-qrcode_save";		} else {					return  "robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-bj-editIDCardCheck,robot-2016-apply-selectIsNetProp,robot-2016-apply-saveCheckCode,robot-2016-qrcode_editCheckFlag,robot-2016-qrcode_gotoJfcd,robot-2016-qrcode_prepareEditByJF,robot-2016-qrcode_getBusinessIn" +",robot-2016-qrcode_checkBeforeCalculate,robot-2016-qrcode_saveByJF,robot-2016-qrcode_getBusinessIn_alipay,robot-2016-qrcode_editFeeInfor,robot-2016-qrcode_editPayFeeByWeChat,robot-2016-qrcode_saveByWeChat,robot-2016-qrcode_save";		}}    else {              return "robot-2016-qrcode_login,robot-2016-qrcode_editCheckFlag,robot-2016-qrcode_gotoJfcd,robot-2016-qrcode_prepareEditByJF,robot-2016-qrcode_getBusinessIn" +                ",robot-2016-qrcode_checkBeforeCalculate,robot-2016-qrcode_saveByJF,robot-2016-qrcode_getBusinessIn_alipay,robot-2016-qrcode_editFeeInfor,robot-2016-qrcode_editPayFeeByWeChat,robot-2016-qrcode_saveByWeChat,robot-2016-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2016', 'taiping', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-太平-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2016-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2016-qrcode_query_editCheckFlag,robot-2016-qrcode_query_gotoJfcd,robot-2016-qrcode_query_prepareEditByJF" +                ",robot-2016-qrcode_query_editMainInfor,robot-2016-qrcode_query_getBusinessIn,robot-2016-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ" +            ",robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind,robot-2016-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2016', 'taiping', '15', '6', 'pro', 'other', b'1', '{}', '精灵-太平-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-IdCarChekc" //申请验证码    else{        return"robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-editIDCardCheck,robot-2016-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2016', 'taiping', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-太平-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectPolicyefc,robot-2016-selectPolicybiz,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ" +            ",robot-2016-showCitemCarQ,robot-2016-showCinsuredQ,robot-2016-showCitemKindCI,robot-2016-browseProposalS,robot-2016-showCitemCarS" +            ",robot-2016-showCinsuredS,robot-2016-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2016', 'taiping', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2016-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectProposalQ,robot-2016-selectProposalS,robot-2016-browseProposalQ,robot-2016-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2016', 'taiping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-bj-editIDCardCheck,robot-2016-apply-bj-IdCarChekc";    } else {        s = "robot-2016-qrcode_login,robot-2016-qrcode_editCheckFlag,robot-2016-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2016', 'taiping', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi太平报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2016_ask_charge,edi_2016_noMotor_quote"	} else {		return "edi_2016_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2016', 'taiping', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-太平-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2016_ask_charge,edi_2016_noMotor_quote,edi_2016_askInsure,edi_2016_uploadImg,edi_2016_submitInsure,edi_2016_noMotor_submit" 	  	} else {		return "edi_2016_ask_chargeold,edi_2016_askInsure,edi_2016_uploadImg,edi_2016_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2016', 'taiping', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-太平-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2016_ask_charge,edi_2016_noMotor_quote,edi_2016_askInsure,edi_2016_uploadImg" 	} else {		return "edi_2016_ask_chargeold,edi_2016_askInsure,edi_2016_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2016', 'taiping', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-太平-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2016_efc_policyinfo,edi_2016_biz_policyinfo,edi_2016_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2016', 'taiping', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-太平-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2016_efc_insurequery,edi_2016_biz_insurequery,edi_2016_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2016', 'taiping', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京太平短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-bj-editIDCardCheck,robot-2016-apply-saveCheckCode,robot-2016-apply-selectIsNetProp";    } else {        s = "robot-2016-qrcode_login,robot-2016-apply-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2016', 'taiping', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2016-bj-qrcode_login,robot-2016-apply-bj-prepare,robot-2016-apply-select-jq,robot-2016-apply-select-sy,robot-2016-apply-bj-editIDCardCheck,robot-2016-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2016', 'taiping', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-太平-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2016_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2016', 'taiping', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi太平北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2016_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2016', 'taiping', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi太平北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2016_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2016', 'taiping', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-太平-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2016-login,robot-2016-prepareQueryCode,robot-2016-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2011', 'taipingyang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-太平洋-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-IdCarChekc" //申请验证码    else{        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2011', 'taipingyang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-太平洋-报价', 'def getTemplateGroup(dataSource) {    return "edi-2011-queryCar,edi-2011-xbQuery,edi-2011-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2011', 'taipingyang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-太平洋-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2011-queryCar,edi-2011-xbQuery,edi-2011-askCharge,edi-2011-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2011', 'taipingyang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-太平洋-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2011-queryCar,edi-2011-xbQuery,edi-2011-askCharge,edi-2011-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2011', 'taipingyang', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-太平洋-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2011-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2011', 'taipingyang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-太平洋-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2011-login,robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2011-login,robot-2011-ObtainConfig,robot-2011-checkInsurePerson,robot-2011-changePerson,robot-2011-checkInsuredPerson,robot-2011-changePerson,robot-2011-prepareEdit," +                    "robot-2011-prepareQueryCode,robot-2011-selectProposalCar,robot-2011-browseProposalCar,robot-2011-browseProposalCarefc,robot-2011-selectRenewalPolicyNo"        }else{            s = "robot-2011-login,robot-2011-ObtainConfig,robot-2011-checkInsurePerson,robot-2011-changePerson,robot-2011-checkInsuredPerson,robot-2011-changePerson,robot-2011-prepareEdit," +                    "robot-2011-prepareQueryCode,robot-2011-browseProposalCar,robot-2011-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2011-VehicleModelList" //上海车型查询        }        s += ",robot-2011-queryPrepare,robot-2011-vehicleQuery,robot-2011-queryTaxAbateForPlat,robot-2011-calActualValue,robot-2011-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2011-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2011-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-nomotor-unitedSaleEdit,robot-2011-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2011-login,robot_2011_bj_initData,robot_2011_bj_queryModel,robot_2011_bj_getSaleTaxInfo,robot_2011_bj_getRealValue,robot_2011_bj_getPersonData,robot_2011_bj_addPersonData,robot_2011_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2011', 'taipingyang', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-太平洋-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2011-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2011-ObtainConfig,robot-2011-selectRenewal,robot-2011-editCengage,robot-2011-editCitemCar,robot-2011-editCinsured,robot-2011-renewalPolicy,robot-2011-renewalPolicyCI,robot-2011-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2011-VehicleModelList" //上海车型查询        }        s += ",robot-2011-vehicleQueryXB,robot-2011-queryTaxAbateForPlat,robot-2011-calActualValue,robot-2011-editCitemKind,robot-2011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2011-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-refreshPlanByTimes,robot-2011-insert"            }else{                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2011-getMaxCsellFee,robot-2011-getPrpCseller,robot-2011-getPrpCsellerCI,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            s += ",robot-2011-getMaxCsellFee,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"        }    }else{        s +=",robot-2011-ObtainConfig,robot-2011-checkInsurePerson,robot-2011-changePerson,robot-2011-checkInsuredPerson,robot-2011-changePerson,robot-2011-prepareEdit,robot-2011-selectRenewalPolicyNo,robot-2011-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2011-VehicleModelList" //上海车型查询        }        s += ",robot-2011-queryPrepare,robot-2011-vehicleQuery,robot-2011-queryTaxAbateForPlat,robot-2011-calActualValue,robot-2011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2011-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2011-calAnciInfo,robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            }else{                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-nomotor-unitedSaleEdit,robot-2011-nomotor-saveUnitedSale,robot-2011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2011-getMaxCsellFee,robot-2011-getPrpCseller,robot-2011-getPrpCsellerCI,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            s += ",robot-2011-getMaxCsellFee,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-nomotor-unitedSaleEdit,robot-2011-nomotor-saveUnitedSale,robot-2011-insert"        }         if("**********".equals(orgId)){            s = "robot-2011-login,robot_2011_bj_initData,robot_2011_bj_queryModel,robot_2011_bj_getSaleTaxInfo,robot_2011_bj_getRealValue,robot_2011_bj_getPersonData,robot_2011_bj_addPersonData,robot_2011_bj_askCharge,robot_2011_bj_queryPayForXSFY,robot_2011_bj_getCagentCI,robot_2011_bj_getCagent,robot_2011_bj_queryPayForXSFY_req,robot_2011_bj_queryIlogEngage,robot_2011_bj_insureRefrenshPlan,robot_2011_bj_insure4S,robot-2011-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2011', 'taipingyang', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-太平洋-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ" +            ",robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind,robot-2011-nomotor-query,robot-2011-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2011', 'taipingyang', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-太平洋-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ" +            ",robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind,robot-2011-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2011', 'taipingyang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-太平洋-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ" +            ",robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind,robot-2011-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2011', 'taipingyang', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "太平洋财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-太平洋-电销', 'def getTemplateGroup(dataSource){    return "robot-2011-pureESale_Login,robot-2011-pureESale_Welcome,robot-2011-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2011', 'taipingyang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平洋续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2011-login,robot-2011-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2011', 'taipingyang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-太平洋-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2011-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2011-ObtainConfig,robot-2011-selectRenewal,robot-2011-editCengage,robot-2011-editCitemCar,robot-2011-editCinsured,robot-2011-renewalPolicy,robot-2011-renewalPolicyCI,robot-2011-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2011-VehicleModelList" //上海车型查询        }        s += ",robot-2011-vehicleQueryXB,robot-2011-queryTaxAbateForPlat,robot-2011-calActualValue,robot-2011-editCitemKind,robot-2011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2011-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-refreshPlanByTimes,robot-2011-insert"            }else{                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2011-calAnciInfo,robot-2011-getMaxCsellFee,robot-2011-getPrpCseller,robot-2011-getPrpCsellerCI,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            s += ",robot-2011-getMaxCsellFee,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"        }    }else{        s += ",robot-2011-ObtainConfig,robot-2011-checkInsurePerson,robot-2011-changePerson,robot-2011-checkInsuredPerson,robot-2011-changePerson,robot-2011-prepareEdit,robot-2011-editCengage,robot-2011-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2011-queryVehiclePMCheck,robot-2011-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2011-VehicleModelList" //上海车型查询        }        s += ",robot-2011-queryPrepare,robot-2011-vehicleQuery,robot-2011-queryTaxAbateForPlat,robot-2011-calActualValue,robot-2011-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2011-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2011-calAnciInfo,robot-2011-queryPayFor,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            }else{                s += ",robot-2011-calAnciInfo,robot-2011-checkAgentType,robot-2011-queryPayForSCMS,robot-2011-getCagent,robot-2011-getCagentCI,robot-2011-refreshPlanByTimes,robot-2011-nomotor-unitedSaleEdit,robot-2011-nomotor-saveUnitedSale,robot-2011-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2011-calAnciInfo,robot-2011-getMaxCsellFee,robot-2011-getPrpCseller,robot-2011-getPrpCsellerCI,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-insert"            s += ",robot-2011-getMaxCsellFee,robot-2011-queryPayForSCMS,robot-2011-refreshPlanByTimes,robot-2011-nomotor-unitedSaleEdit,robot-2011-nomotor-saveUnitedSale,robot-2011-insert"        }    }    s += ",robot-2011-checkRiskCode,robot-2011-editMainUwtFlag,robot-2011-editSubmitUndwrt,robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-showUndwrtMsgQ,robot-2011-showUndwrtMsgS"+            ",robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ,robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind,robot-2011-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2011-login,robot_2011_bj_initData,robot_2011_bj_queryModel,robot_2011_bj_getSaleTaxInfo,robot_2011_bj_getRealValue,robot_2011_bj_getPersonData,robot_2011_bj_addPersonData,robot_2011_bj_askCharge,robot_2011_bj_queryPayForXSFY,robot_2011_bj_getCagentCI,robot_2011_bj_getCagent,robot_2011_bj_queryPayForXSFY_req,robot_2011_bj_queryIlogEngage,robot_2011_bj_insureRefrenshPlan,robot_2011_bj_insure4S,robot-2011-uploadImage,robot_2011_bj_autoInsure,robot_2011_bj_showUndwrtMsgQ,robot_2011_bj_showUndwrtMsgS";       s += ",robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ,robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +                ",robot-2011-showCinsuredS,robot-2011-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2011', 'taipingyang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平洋-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2011-qrcode_login,robot-2011-qrcode_printTwoBarCodeServlet,robot-2011-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2011-qrcode_login,robot-2011-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2011-qrcode_login,robot-2011-qrcode_editCheckFlag,robot-2011-qrcode_gotoJfcd,robot-2011-qrcode_prepareEditByJF,robot-2011-qrcode_getBusinessIn" +                ",robot-2011-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2011-qrcode_login,robot-2011-qrcode_editCheckFlag,robot-2011-qrcode_gotoJfcd,robot-2011-qrcode_prepareEditByJF,robot-2011-qrcode_getBusinessIn" +                ",robot-2011-qrcode_checkBeforeCalculate,robot-2011-qrcode_saveByJF,robot-2011-qrcode_getBusinessIn_alipay,robot-2011-qrcode_editFeeInfor,robot-2011-qrcode_editPayFeeByWeChat,robot-2011-qrcode_saveByWeChat,robot-2011-qrcode_save";		} else {					return  "robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-bj-editIDCardCheck,robot-2011-apply-selectIsNetProp,robot-2011-apply-saveCheckCode,robot-2011-qrcode_editCheckFlag,robot-2011-qrcode_gotoJfcd,robot-2011-qrcode_prepareEditByJF,robot-2011-qrcode_getBusinessIn" +",robot-2011-qrcode_checkBeforeCalculate,robot-2011-qrcode_saveByJF,robot-2011-qrcode_getBusinessIn_alipay,robot-2011-qrcode_editFeeInfor,robot-2011-qrcode_editPayFeeByWeChat,robot-2011-qrcode_saveByWeChat,robot-2011-qrcode_save";		}}    else {              return "robot-2011-qrcode_login,robot-2011-qrcode_editCheckFlag,robot-2011-qrcode_gotoJfcd,robot-2011-qrcode_prepareEditByJF,robot-2011-qrcode_getBusinessIn" +                ",robot-2011-qrcode_checkBeforeCalculate,robot-2011-qrcode_saveByJF,robot-2011-qrcode_getBusinessIn_alipay,robot-2011-qrcode_editFeeInfor,robot-2011-qrcode_editPayFeeByWeChat,robot-2011-qrcode_saveByWeChat,robot-2011-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2011', 'taipingyang', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-太平洋-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2011-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2011-qrcode_query_editCheckFlag,robot-2011-qrcode_query_gotoJfcd,robot-2011-qrcode_query_prepareEditByJF" +                ",robot-2011-qrcode_query_editMainInfor,robot-2011-qrcode_query_getBusinessIn,robot-2011-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ" +            ",robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind,robot-2011-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2011', 'taipingyang', '15', '6', 'pro', 'other', b'1', '{}', '精灵-太平洋-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-IdCarChekc" //申请验证码    else{        return"robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-editIDCardCheck,robot-2011-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2011', 'taipingyang', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-太平洋-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectPolicyefc,robot-2011-selectPolicybiz,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ" +            ",robot-2011-showCitemCarQ,robot-2011-showCinsuredQ,robot-2011-showCitemKindCI,robot-2011-browseProposalS,robot-2011-showCitemCarS" +            ",robot-2011-showCinsuredS,robot-2011-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2011', 'taipingyang', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2011-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectProposalQ,robot-2011-selectProposalS,robot-2011-browseProposalQ,robot-2011-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2011', 'taipingyang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平洋-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-bj-editIDCardCheck,robot-2011-apply-bj-IdCarChekc";    } else {        s = "robot-2011-qrcode_login,robot-2011-qrcode_editCheckFlag,robot-2011-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2011', 'taipingyang', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi太平洋报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2011_ask_charge,edi_2011_noMotor_quote"	} else {		return "edi_2011_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2011', 'taipingyang', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-太平洋-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2011_ask_charge,edi_2011_noMotor_quote,edi_2011_askInsure,edi_2011_uploadImg,edi_2011_submitInsure,edi_2011_noMotor_submit" 	  	} else {		return "edi_2011_ask_chargeold,edi_2011_askInsure,edi_2011_uploadImg,edi_2011_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2011', 'taipingyang', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-太平洋-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2011_ask_charge,edi_2011_noMotor_quote,edi_2011_askInsure,edi_2011_uploadImg" 	} else {		return "edi_2011_ask_chargeold,edi_2011_askInsure,edi_2011_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2011', 'taipingyang', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-太平洋-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2011_efc_policyinfo,edi_2011_biz_policyinfo,edi_2011_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2011', 'taipingyang', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-太平洋-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2011_efc_insurequery,edi_2011_biz_insurequery,edi_2011_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2011', 'taipingyang', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京太平洋短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-bj-editIDCardCheck,robot-2011-apply-saveCheckCode,robot-2011-apply-selectIsNetProp";    } else {        s = "robot-2011-qrcode_login,robot-2011-apply-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2011', 'taipingyang', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-太平洋-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2011-bj-qrcode_login,robot-2011-apply-bj-prepare,robot-2011-apply-select-jq,robot-2011-apply-select-sy,robot-2011-apply-bj-editIDCardCheck,robot-2011-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2011', 'taipingyang', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-太平洋-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2011_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2011', 'taipingyang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi太平洋北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2011_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2011', 'taipingyang', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi太平洋北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2011_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2011', 'taipingyang', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-太平洋-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2011-login,robot-2011-prepareQueryCode,robot-2011-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2066', 'yatai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-亚太-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-IdCarChekc" //申请验证码    else{        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2066', 'yatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-亚太-报价', 'def getTemplateGroup(dataSource) {    return "edi-2066-queryCar,edi-2066-xbQuery,edi-2066-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2066', 'yatai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-亚太-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2066-queryCar,edi-2066-xbQuery,edi-2066-askCharge,edi-2066-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2066', 'yatai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-亚太-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2066-queryCar,edi-2066-xbQuery,edi-2066-askCharge,edi-2066-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2066', 'yatai', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-亚太-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2066-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2066', 'yatai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-亚太-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2066-login,robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2066-login,robot-2066-ObtainConfig,robot-2066-checkInsurePerson,robot-2066-changePerson,robot-2066-checkInsuredPerson,robot-2066-changePerson,robot-2066-prepareEdit," +                    "robot-2066-prepareQueryCode,robot-2066-selectProposalCar,robot-2066-browseProposalCar,robot-2066-browseProposalCarefc,robot-2066-selectRenewalPolicyNo"        }else{            s = "robot-2066-login,robot-2066-ObtainConfig,robot-2066-checkInsurePerson,robot-2066-changePerson,robot-2066-checkInsuredPerson,robot-2066-changePerson,robot-2066-prepareEdit," +                    "robot-2066-prepareQueryCode,robot-2066-browseProposalCar,robot-2066-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2066-VehicleModelList" //上海车型查询        }        s += ",robot-2066-queryPrepare,robot-2066-vehicleQuery,robot-2066-queryTaxAbateForPlat,robot-2066-calActualValue,robot-2066-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2066-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2066-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-nomotor-unitedSaleEdit,robot-2066-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2066-login,robot_2066_bj_initData,robot_2066_bj_queryModel,robot_2066_bj_getSaleTaxInfo,robot_2066_bj_getRealValue,robot_2066_bj_getPersonData,robot_2066_bj_addPersonData,robot_2066_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2066', 'yatai', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-亚太-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2066-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2066-ObtainConfig,robot-2066-selectRenewal,robot-2066-editCengage,robot-2066-editCitemCar,robot-2066-editCinsured,robot-2066-renewalPolicy,robot-2066-renewalPolicyCI,robot-2066-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2066-VehicleModelList" //上海车型查询        }        s += ",robot-2066-vehicleQueryXB,robot-2066-queryTaxAbateForPlat,robot-2066-calActualValue,robot-2066-editCitemKind,robot-2066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2066-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-refreshPlanByTimes,robot-2066-insert"            }else{                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2066-getMaxCsellFee,robot-2066-getPrpCseller,robot-2066-getPrpCsellerCI,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            s += ",robot-2066-getMaxCsellFee,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"        }    }else{        s +=",robot-2066-ObtainConfig,robot-2066-checkInsurePerson,robot-2066-changePerson,robot-2066-checkInsuredPerson,robot-2066-changePerson,robot-2066-prepareEdit,robot-2066-selectRenewalPolicyNo,robot-2066-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2066-VehicleModelList" //上海车型查询        }        s += ",robot-2066-queryPrepare,robot-2066-vehicleQuery,robot-2066-queryTaxAbateForPlat,robot-2066-calActualValue,robot-2066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2066-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2066-calAnciInfo,robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            }else{                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-nomotor-unitedSaleEdit,robot-2066-nomotor-saveUnitedSale,robot-2066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2066-getMaxCsellFee,robot-2066-getPrpCseller,robot-2066-getPrpCsellerCI,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            s += ",robot-2066-getMaxCsellFee,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-nomotor-unitedSaleEdit,robot-2066-nomotor-saveUnitedSale,robot-2066-insert"        }         if("**********".equals(orgId)){            s = "robot-2066-login,robot_2066_bj_initData,robot_2066_bj_queryModel,robot_2066_bj_getSaleTaxInfo,robot_2066_bj_getRealValue,robot_2066_bj_getPersonData,robot_2066_bj_addPersonData,robot_2066_bj_askCharge,robot_2066_bj_queryPayForXSFY,robot_2066_bj_getCagentCI,robot_2066_bj_getCagent,robot_2066_bj_queryPayForXSFY_req,robot_2066_bj_queryIlogEngage,robot_2066_bj_insureRefrenshPlan,robot_2066_bj_insure4S,robot-2066-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2066', 'yatai', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-亚太-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ" +            ",robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind,robot-2066-nomotor-query,robot-2066-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2066', 'yatai', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-亚太-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ" +            ",robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind,robot-2066-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2066', 'yatai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-亚太-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ" +            ",robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind,robot-2066-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2066', 'yatai', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "亚太财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-亚太-电销', 'def getTemplateGroup(dataSource){    return "robot-2066-pureESale_Login,robot-2066-pureESale_Welcome,robot-2066-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2066', 'yatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-亚太续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2066-login,robot-2066-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2066', 'yatai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-亚太-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2066-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2066-ObtainConfig,robot-2066-selectRenewal,robot-2066-editCengage,robot-2066-editCitemCar,robot-2066-editCinsured,robot-2066-renewalPolicy,robot-2066-renewalPolicyCI,robot-2066-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2066-VehicleModelList" //上海车型查询        }        s += ",robot-2066-vehicleQueryXB,robot-2066-queryTaxAbateForPlat,robot-2066-calActualValue,robot-2066-editCitemKind,robot-2066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2066-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-refreshPlanByTimes,robot-2066-insert"            }else{                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2066-calAnciInfo,robot-2066-getMaxCsellFee,robot-2066-getPrpCseller,robot-2066-getPrpCsellerCI,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            s += ",robot-2066-getMaxCsellFee,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"        }    }else{        s += ",robot-2066-ObtainConfig,robot-2066-checkInsurePerson,robot-2066-changePerson,robot-2066-checkInsuredPerson,robot-2066-changePerson,robot-2066-prepareEdit,robot-2066-editCengage,robot-2066-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2066-queryVehiclePMCheck,robot-2066-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2066-VehicleModelList" //上海车型查询        }        s += ",robot-2066-queryPrepare,robot-2066-vehicleQuery,robot-2066-queryTaxAbateForPlat,robot-2066-calActualValue,robot-2066-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2066-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2066-calAnciInfo,robot-2066-queryPayFor,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            }else{                s += ",robot-2066-calAnciInfo,robot-2066-checkAgentType,robot-2066-queryPayForSCMS,robot-2066-getCagent,robot-2066-getCagentCI,robot-2066-refreshPlanByTimes,robot-2066-nomotor-unitedSaleEdit,robot-2066-nomotor-saveUnitedSale,robot-2066-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2066-calAnciInfo,robot-2066-getMaxCsellFee,robot-2066-getPrpCseller,robot-2066-getPrpCsellerCI,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-insert"            s += ",robot-2066-getMaxCsellFee,robot-2066-queryPayForSCMS,robot-2066-refreshPlanByTimes,robot-2066-nomotor-unitedSaleEdit,robot-2066-nomotor-saveUnitedSale,robot-2066-insert"        }    }    s += ",robot-2066-checkRiskCode,robot-2066-editMainUwtFlag,robot-2066-editSubmitUndwrt,robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-showUndwrtMsgQ,robot-2066-showUndwrtMsgS"+            ",robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ,robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind,robot-2066-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2066-login,robot_2066_bj_initData,robot_2066_bj_queryModel,robot_2066_bj_getSaleTaxInfo,robot_2066_bj_getRealValue,robot_2066_bj_getPersonData,robot_2066_bj_addPersonData,robot_2066_bj_askCharge,robot_2066_bj_queryPayForXSFY,robot_2066_bj_getCagentCI,robot_2066_bj_getCagent,robot_2066_bj_queryPayForXSFY_req,robot_2066_bj_queryIlogEngage,robot_2066_bj_insureRefrenshPlan,robot_2066_bj_insure4S,robot-2066-uploadImage,robot_2066_bj_autoInsure,robot_2066_bj_showUndwrtMsgQ,robot_2066_bj_showUndwrtMsgS";       s += ",robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ,robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +                ",robot-2066-showCinsuredS,robot-2066-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2066', 'yatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-亚太-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2066-qrcode_login,robot-2066-qrcode_printTwoBarCodeServlet,robot-2066-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2066-qrcode_login,robot-2066-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2066-qrcode_login,robot-2066-qrcode_editCheckFlag,robot-2066-qrcode_gotoJfcd,robot-2066-qrcode_prepareEditByJF,robot-2066-qrcode_getBusinessIn" +                ",robot-2066-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2066-qrcode_login,robot-2066-qrcode_editCheckFlag,robot-2066-qrcode_gotoJfcd,robot-2066-qrcode_prepareEditByJF,robot-2066-qrcode_getBusinessIn" +                ",robot-2066-qrcode_checkBeforeCalculate,robot-2066-qrcode_saveByJF,robot-2066-qrcode_getBusinessIn_alipay,robot-2066-qrcode_editFeeInfor,robot-2066-qrcode_editPayFeeByWeChat,robot-2066-qrcode_saveByWeChat,robot-2066-qrcode_save";		} else {					return  "robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-bj-editIDCardCheck,robot-2066-apply-selectIsNetProp,robot-2066-apply-saveCheckCode,robot-2066-qrcode_editCheckFlag,robot-2066-qrcode_gotoJfcd,robot-2066-qrcode_prepareEditByJF,robot-2066-qrcode_getBusinessIn" +",robot-2066-qrcode_checkBeforeCalculate,robot-2066-qrcode_saveByJF,robot-2066-qrcode_getBusinessIn_alipay,robot-2066-qrcode_editFeeInfor,robot-2066-qrcode_editPayFeeByWeChat,robot-2066-qrcode_saveByWeChat,robot-2066-qrcode_save";		}}    else {              return "robot-2066-qrcode_login,robot-2066-qrcode_editCheckFlag,robot-2066-qrcode_gotoJfcd,robot-2066-qrcode_prepareEditByJF,robot-2066-qrcode_getBusinessIn" +                ",robot-2066-qrcode_checkBeforeCalculate,robot-2066-qrcode_saveByJF,robot-2066-qrcode_getBusinessIn_alipay,robot-2066-qrcode_editFeeInfor,robot-2066-qrcode_editPayFeeByWeChat,robot-2066-qrcode_saveByWeChat,robot-2066-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2066', 'yatai', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-亚太-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2066-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2066-qrcode_query_editCheckFlag,robot-2066-qrcode_query_gotoJfcd,robot-2066-qrcode_query_prepareEditByJF" +                ",robot-2066-qrcode_query_editMainInfor,robot-2066-qrcode_query_getBusinessIn,robot-2066-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ" +            ",robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind,robot-2066-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2066', 'yatai', '15', '6', 'pro', 'other', b'1', '{}', '精灵-亚太-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-IdCarChekc" //申请验证码    else{        return"robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-editIDCardCheck,robot-2066-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2066', 'yatai', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-亚太-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectPolicyefc,robot-2066-selectPolicybiz,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ" +            ",robot-2066-showCitemCarQ,robot-2066-showCinsuredQ,robot-2066-showCitemKindCI,robot-2066-browseProposalS,robot-2066-showCitemCarS" +            ",robot-2066-showCinsuredS,robot-2066-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2066', 'yatai', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2066-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectProposalQ,robot-2066-selectProposalS,robot-2066-browseProposalQ,robot-2066-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2066', 'yatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-亚太-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-bj-editIDCardCheck,robot-2066-apply-bj-IdCarChekc";    } else {        s = "robot-2066-qrcode_login,robot-2066-qrcode_editCheckFlag,robot-2066-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2066', 'yatai', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi亚太报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2066_ask_charge,edi_2066_noMotor_quote"	} else {		return "edi_2066_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2066', 'yatai', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-亚太-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2066_ask_charge,edi_2066_noMotor_quote,edi_2066_askInsure,edi_2066_uploadImg,edi_2066_submitInsure,edi_2066_noMotor_submit" 	  	} else {		return "edi_2066_ask_chargeold,edi_2066_askInsure,edi_2066_uploadImg,edi_2066_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2066', 'yatai', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-亚太-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2066_ask_charge,edi_2066_noMotor_quote,edi_2066_askInsure,edi_2066_uploadImg" 	} else {		return "edi_2066_ask_chargeold,edi_2066_askInsure,edi_2066_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2066', 'yatai', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-亚太-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2066_efc_policyinfo,edi_2066_biz_policyinfo,edi_2066_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2066', 'yatai', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-亚太-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2066_efc_insurequery,edi_2066_biz_insurequery,edi_2066_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2066', 'yatai', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京亚太短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-bj-editIDCardCheck,robot-2066-apply-saveCheckCode,robot-2066-apply-selectIsNetProp";    } else {        s = "robot-2066-qrcode_login,robot-2066-apply-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2066', 'yatai', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-亚太-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2066-bj-qrcode_login,robot-2066-apply-bj-prepare,robot-2066-apply-select-jq,robot-2066-apply-select-sy,robot-2066-apply-bj-editIDCardCheck,robot-2066-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2066', 'yatai', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-亚太-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2066_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2066', 'yatai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi亚太北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2066_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2066', 'yatai', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi亚太北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2066_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2066', 'yatai', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-亚太-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2066-login,robot-2066-prepareQueryCode,robot-2066-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2044', 'zhongcheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-众诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-IdCarChekc" //申请验证码    else{        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-众诚-报价', 'def getTemplateGroup(dataSource) {    return "edi-2044-queryCar,edi-2044-xbQuery,edi-2044-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2044', 'zhongcheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-众诚-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2044-queryCar,edi-2044-xbQuery,edi-2044-askCharge,edi-2044-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2044', 'zhongcheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-众诚-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2044-queryCar,edi-2044-xbQuery,edi-2044-askCharge,edi-2044-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2044', 'zhongcheng', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-众诚-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2044-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-众诚-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2044-login,robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2044-login,robot-2044-ObtainConfig,robot-2044-checkInsurePerson,robot-2044-changePerson,robot-2044-checkInsuredPerson,robot-2044-changePerson,robot-2044-prepareEdit," +                    "robot-2044-prepareQueryCode,robot-2044-selectProposalCar,robot-2044-browseProposalCar,robot-2044-browseProposalCarefc,robot-2044-selectRenewalPolicyNo"        }else{            s = "robot-2044-login,robot-2044-ObtainConfig,robot-2044-checkInsurePerson,robot-2044-changePerson,robot-2044-checkInsuredPerson,robot-2044-changePerson,robot-2044-prepareEdit," +                    "robot-2044-prepareQueryCode,robot-2044-browseProposalCar,robot-2044-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2044-VehicleModelList" //上海车型查询        }        s += ",robot-2044-queryPrepare,robot-2044-vehicleQuery,robot-2044-queryTaxAbateForPlat,robot-2044-calActualValue,robot-2044-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2044-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2044-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-nomotor-unitedSaleEdit,robot-2044-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2044-login,robot_2044_bj_initData,robot_2044_bj_queryModel,robot_2044_bj_getSaleTaxInfo,robot_2044_bj_getRealValue,robot_2044_bj_getPersonData,robot_2044_bj_addPersonData,robot_2044_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2044', 'zhongcheng', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-众诚-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2044-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2044-ObtainConfig,robot-2044-selectRenewal,robot-2044-editCengage,robot-2044-editCitemCar,robot-2044-editCinsured,robot-2044-renewalPolicy,robot-2044-renewalPolicyCI,robot-2044-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2044-VehicleModelList" //上海车型查询        }        s += ",robot-2044-vehicleQueryXB,robot-2044-queryTaxAbateForPlat,robot-2044-calActualValue,robot-2044-editCitemKind,robot-2044-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2044-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-refreshPlanByTimes,robot-2044-insert"            }else{                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2044-getMaxCsellFee,robot-2044-getPrpCseller,robot-2044-getPrpCsellerCI,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            s += ",robot-2044-getMaxCsellFee,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"        }    }else{        s +=",robot-2044-ObtainConfig,robot-2044-checkInsurePerson,robot-2044-changePerson,robot-2044-checkInsuredPerson,robot-2044-changePerson,robot-2044-prepareEdit,robot-2044-selectRenewalPolicyNo,robot-2044-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2044-VehicleModelList" //上海车型查询        }        s += ",robot-2044-queryPrepare,robot-2044-vehicleQuery,robot-2044-queryTaxAbateForPlat,robot-2044-calActualValue,robot-2044-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2044-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2044-calAnciInfo,robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            }else{                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-nomotor-unitedSaleEdit,robot-2044-nomotor-saveUnitedSale,robot-2044-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2044-getMaxCsellFee,robot-2044-getPrpCseller,robot-2044-getPrpCsellerCI,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            s += ",robot-2044-getMaxCsellFee,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-nomotor-unitedSaleEdit,robot-2044-nomotor-saveUnitedSale,robot-2044-insert"        }         if("**********".equals(orgId)){            s = "robot-2044-login,robot_2044_bj_initData,robot_2044_bj_queryModel,robot_2044_bj_getSaleTaxInfo,robot_2044_bj_getRealValue,robot_2044_bj_getPersonData,robot_2044_bj_addPersonData,robot_2044_bj_askCharge,robot_2044_bj_queryPayForXSFY,robot_2044_bj_getCagentCI,robot_2044_bj_getCagent,robot_2044_bj_queryPayForXSFY_req,robot_2044_bj_queryIlogEngage,robot_2044_bj_insureRefrenshPlan,robot_2044_bj_insure4S,robot-2044-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2044', 'zhongcheng', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-众诚-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ" +            ",robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind,robot-2044-nomotor-query,robot-2044-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-众诚-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ" +            ",robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind,robot-2044-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2044', 'zhongcheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-众诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ" +            ",robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind,robot-2044-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "众诚财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-众诚-电销', 'def getTemplateGroup(dataSource){    return "robot-2044-pureESale_Login,robot-2044-pureESale_Welcome,robot-2044-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2044', 'zhongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众诚续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2044-login,robot-2044-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2044', 'zhongcheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-众诚-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2044-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2044-ObtainConfig,robot-2044-selectRenewal,robot-2044-editCengage,robot-2044-editCitemCar,robot-2044-editCinsured,robot-2044-renewalPolicy,robot-2044-renewalPolicyCI,robot-2044-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2044-VehicleModelList" //上海车型查询        }        s += ",robot-2044-vehicleQueryXB,robot-2044-queryTaxAbateForPlat,robot-2044-calActualValue,robot-2044-editCitemKind,robot-2044-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2044-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-refreshPlanByTimes,robot-2044-insert"            }else{                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2044-calAnciInfo,robot-2044-getMaxCsellFee,robot-2044-getPrpCseller,robot-2044-getPrpCsellerCI,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            s += ",robot-2044-getMaxCsellFee,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"        }    }else{        s += ",robot-2044-ObtainConfig,robot-2044-checkInsurePerson,robot-2044-changePerson,robot-2044-checkInsuredPerson,robot-2044-changePerson,robot-2044-prepareEdit,robot-2044-editCengage,robot-2044-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2044-queryVehiclePMCheck,robot-2044-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2044-VehicleModelList" //上海车型查询        }        s += ",robot-2044-queryPrepare,robot-2044-vehicleQuery,robot-2044-queryTaxAbateForPlat,robot-2044-calActualValue,robot-2044-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2044-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2044-calAnciInfo,robot-2044-queryPayFor,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            }else{                s += ",robot-2044-calAnciInfo,robot-2044-checkAgentType,robot-2044-queryPayForSCMS,robot-2044-getCagent,robot-2044-getCagentCI,robot-2044-refreshPlanByTimes,robot-2044-nomotor-unitedSaleEdit,robot-2044-nomotor-saveUnitedSale,robot-2044-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2044-calAnciInfo,robot-2044-getMaxCsellFee,robot-2044-getPrpCseller,robot-2044-getPrpCsellerCI,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-insert"            s += ",robot-2044-getMaxCsellFee,robot-2044-queryPayForSCMS,robot-2044-refreshPlanByTimes,robot-2044-nomotor-unitedSaleEdit,robot-2044-nomotor-saveUnitedSale,robot-2044-insert"        }    }    s += ",robot-2044-checkRiskCode,robot-2044-editMainUwtFlag,robot-2044-editSubmitUndwrt,robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-showUndwrtMsgQ,robot-2044-showUndwrtMsgS"+            ",robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ,robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind,robot-2044-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2044-login,robot_2044_bj_initData,robot_2044_bj_queryModel,robot_2044_bj_getSaleTaxInfo,robot_2044_bj_getRealValue,robot_2044_bj_getPersonData,robot_2044_bj_addPersonData,robot_2044_bj_askCharge,robot_2044_bj_queryPayForXSFY,robot_2044_bj_getCagentCI,robot_2044_bj_getCagent,robot_2044_bj_queryPayForXSFY_req,robot_2044_bj_queryIlogEngage,robot_2044_bj_insureRefrenshPlan,robot_2044_bj_insure4S,robot-2044-uploadImage,robot_2044_bj_autoInsure,robot_2044_bj_showUndwrtMsgQ,robot_2044_bj_showUndwrtMsgS";       s += ",robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ,robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +                ",robot-2044-showCinsuredS,robot-2044-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2044', 'zhongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众诚-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2044-qrcode_login,robot-2044-qrcode_printTwoBarCodeServlet,robot-2044-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2044-qrcode_login,robot-2044-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2044-qrcode_login,robot-2044-qrcode_editCheckFlag,robot-2044-qrcode_gotoJfcd,robot-2044-qrcode_prepareEditByJF,robot-2044-qrcode_getBusinessIn" +                ",robot-2044-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2044-qrcode_login,robot-2044-qrcode_editCheckFlag,robot-2044-qrcode_gotoJfcd,robot-2044-qrcode_prepareEditByJF,robot-2044-qrcode_getBusinessIn" +                ",robot-2044-qrcode_checkBeforeCalculate,robot-2044-qrcode_saveByJF,robot-2044-qrcode_getBusinessIn_alipay,robot-2044-qrcode_editFeeInfor,robot-2044-qrcode_editPayFeeByWeChat,robot-2044-qrcode_saveByWeChat,robot-2044-qrcode_save";		} else {					return  "robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-bj-editIDCardCheck,robot-2044-apply-selectIsNetProp,robot-2044-apply-saveCheckCode,robot-2044-qrcode_editCheckFlag,robot-2044-qrcode_gotoJfcd,robot-2044-qrcode_prepareEditByJF,robot-2044-qrcode_getBusinessIn" +",robot-2044-qrcode_checkBeforeCalculate,robot-2044-qrcode_saveByJF,robot-2044-qrcode_getBusinessIn_alipay,robot-2044-qrcode_editFeeInfor,robot-2044-qrcode_editPayFeeByWeChat,robot-2044-qrcode_saveByWeChat,robot-2044-qrcode_save";		}}    else {              return "robot-2044-qrcode_login,robot-2044-qrcode_editCheckFlag,robot-2044-qrcode_gotoJfcd,robot-2044-qrcode_prepareEditByJF,robot-2044-qrcode_getBusinessIn" +                ",robot-2044-qrcode_checkBeforeCalculate,robot-2044-qrcode_saveByJF,robot-2044-qrcode_getBusinessIn_alipay,robot-2044-qrcode_editFeeInfor,robot-2044-qrcode_editPayFeeByWeChat,robot-2044-qrcode_saveByWeChat,robot-2044-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2044', 'zhongcheng', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-众诚-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2044-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2044-qrcode_query_editCheckFlag,robot-2044-qrcode_query_gotoJfcd,robot-2044-qrcode_query_prepareEditByJF" +                ",robot-2044-qrcode_query_editMainInfor,robot-2044-qrcode_query_getBusinessIn,robot-2044-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ" +            ",robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind,robot-2044-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2044', 'zhongcheng', '15', '6', 'pro', 'other', b'1', '{}', '精灵-众诚-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-IdCarChekc" //申请验证码    else{        return"robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-editIDCardCheck,robot-2044-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2044', 'zhongcheng', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-众诚-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectPolicyefc,robot-2044-selectPolicybiz,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ" +            ",robot-2044-showCitemCarQ,robot-2044-showCinsuredQ,robot-2044-showCitemKindCI,robot-2044-browseProposalS,robot-2044-showCitemCarS" +            ",robot-2044-showCinsuredS,robot-2044-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2044', 'zhongcheng', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2044-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectProposalQ,robot-2044-selectProposalS,robot-2044-browseProposalQ,robot-2044-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2044', 'zhongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众诚-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-bj-editIDCardCheck,robot-2044-apply-bj-IdCarChekc";    } else {        s = "robot-2044-qrcode_login,robot-2044-qrcode_editCheckFlag,robot-2044-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2044', 'zhongcheng', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi众诚报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2044_ask_charge,edi_2044_noMotor_quote"	} else {		return "edi_2044_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2044', 'zhongcheng', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-众诚-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2044_ask_charge,edi_2044_noMotor_quote,edi_2044_askInsure,edi_2044_uploadImg,edi_2044_submitInsure,edi_2044_noMotor_submit" 	  	} else {		return "edi_2044_ask_chargeold,edi_2044_askInsure,edi_2044_uploadImg,edi_2044_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2044', 'zhongcheng', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-众诚-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2044_ask_charge,edi_2044_noMotor_quote,edi_2044_askInsure,edi_2044_uploadImg" 	} else {		return "edi_2044_ask_chargeold,edi_2044_askInsure,edi_2044_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2044', 'zhongcheng', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-众诚-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2044_efc_policyinfo,edi_2044_biz_policyinfo,edi_2044_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2044', 'zhongcheng', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-众诚-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2044_efc_insurequery,edi_2044_biz_insurequery,edi_2044_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2044', 'zhongcheng', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京众诚短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-bj-editIDCardCheck,robot-2044-apply-saveCheckCode,robot-2044-apply-selectIsNetProp";    } else {        s = "robot-2044-qrcode_login,robot-2044-apply-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2044', 'zhongcheng', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-众诚-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2044-bj-qrcode_login,robot-2044-apply-bj-prepare,robot-2044-apply-select-jq,robot-2044-apply-select-sy,robot-2044-apply-bj-editIDCardCheck,robot-2044-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2044', 'zhongcheng', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-众诚-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2044_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2044', 'zhongcheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi众诚北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2044_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2044', 'zhongcheng', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi众诚北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2044_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2044', 'zhongcheng', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-众诚-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2044-login,robot-2044-prepareQueryCode,robot-2044-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2007', 'pingan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-平安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-IdCarChekc" //申请验证码    else{        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2007', 'pingan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-平安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2007-queryCar,edi-2007-xbQuery,edi-2007-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2007', 'pingan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-平安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2007-queryCar,edi-2007-xbQuery,edi-2007-askCharge,edi-2007-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2007', 'pingan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-平安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2007-queryCar,edi-2007-xbQuery,edi-2007-askCharge,edi-2007-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2007', 'pingan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-平安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2007-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2007', 'pingan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-平安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2007-login,robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2007-login,robot-2007-ObtainConfig,robot-2007-checkInsurePerson,robot-2007-changePerson,robot-2007-checkInsuredPerson,robot-2007-changePerson,robot-2007-prepareEdit," +                    "robot-2007-prepareQueryCode,robot-2007-selectProposalCar,robot-2007-browseProposalCar,robot-2007-browseProposalCarefc,robot-2007-selectRenewalPolicyNo"        }else{            s = "robot-2007-login,robot-2007-ObtainConfig,robot-2007-checkInsurePerson,robot-2007-changePerson,robot-2007-checkInsuredPerson,robot-2007-changePerson,robot-2007-prepareEdit," +                    "robot-2007-prepareQueryCode,robot-2007-browseProposalCar,robot-2007-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2007-VehicleModelList" //上海车型查询        }        s += ",robot-2007-queryPrepare,robot-2007-vehicleQuery,robot-2007-queryTaxAbateForPlat,robot-2007-calActualValue,robot-2007-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2007-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2007-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-nomotor-unitedSaleEdit,robot-2007-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2007-login,robot_2007_bj_initData,robot_2007_bj_queryModel,robot_2007_bj_getSaleTaxInfo,robot_2007_bj_getRealValue,robot_2007_bj_getPersonData,robot_2007_bj_addPersonData,robot_2007_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2007', 'pingan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-平安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2007-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2007-ObtainConfig,robot-2007-selectRenewal,robot-2007-editCengage,robot-2007-editCitemCar,robot-2007-editCinsured,robot-2007-renewalPolicy,robot-2007-renewalPolicyCI,robot-2007-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2007-VehicleModelList" //上海车型查询        }        s += ",robot-2007-vehicleQueryXB,robot-2007-queryTaxAbateForPlat,robot-2007-calActualValue,robot-2007-editCitemKind,robot-2007-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2007-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-refreshPlanByTimes,robot-2007-insert"            }else{                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2007-getMaxCsellFee,robot-2007-getPrpCseller,robot-2007-getPrpCsellerCI,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            s += ",robot-2007-getMaxCsellFee,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"        }    }else{        s +=",robot-2007-ObtainConfig,robot-2007-checkInsurePerson,robot-2007-changePerson,robot-2007-checkInsuredPerson,robot-2007-changePerson,robot-2007-prepareEdit,robot-2007-selectRenewalPolicyNo,robot-2007-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2007-VehicleModelList" //上海车型查询        }        s += ",robot-2007-queryPrepare,robot-2007-vehicleQuery,robot-2007-queryTaxAbateForPlat,robot-2007-calActualValue,robot-2007-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2007-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2007-calAnciInfo,robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            }else{                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-nomotor-unitedSaleEdit,robot-2007-nomotor-saveUnitedSale,robot-2007-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2007-getMaxCsellFee,robot-2007-getPrpCseller,robot-2007-getPrpCsellerCI,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            s += ",robot-2007-getMaxCsellFee,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-nomotor-unitedSaleEdit,robot-2007-nomotor-saveUnitedSale,robot-2007-insert"        }         if("**********".equals(orgId)){            s = "robot-2007-login,robot_2007_bj_initData,robot_2007_bj_queryModel,robot_2007_bj_getSaleTaxInfo,robot_2007_bj_getRealValue,robot_2007_bj_getPersonData,robot_2007_bj_addPersonData,robot_2007_bj_askCharge,robot_2007_bj_queryPayForXSFY,robot_2007_bj_getCagentCI,robot_2007_bj_getCagent,robot_2007_bj_queryPayForXSFY_req,robot_2007_bj_queryIlogEngage,robot_2007_bj_insureRefrenshPlan,robot_2007_bj_insure4S,robot-2007-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2007', 'pingan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-平安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ" +            ",robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind,robot-2007-nomotor-query,robot-2007-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2007', 'pingan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-平安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ" +            ",robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind,robot-2007-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2007', 'pingan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-平安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ" +            ",robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind,robot-2007-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2007', 'pingan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "平安财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-平安-电销', 'def getTemplateGroup(dataSource){    return "robot-2007-pureESale_Login,robot-2007-pureESale_Welcome,robot-2007-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2007', 'pingan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-平安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2007-login,robot-2007-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2007', 'pingan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-平安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2007-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2007-ObtainConfig,robot-2007-selectRenewal,robot-2007-editCengage,robot-2007-editCitemCar,robot-2007-editCinsured,robot-2007-renewalPolicy,robot-2007-renewalPolicyCI,robot-2007-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2007-VehicleModelList" //上海车型查询        }        s += ",robot-2007-vehicleQueryXB,robot-2007-queryTaxAbateForPlat,robot-2007-calActualValue,robot-2007-editCitemKind,robot-2007-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2007-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-refreshPlanByTimes,robot-2007-insert"            }else{                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2007-calAnciInfo,robot-2007-getMaxCsellFee,robot-2007-getPrpCseller,robot-2007-getPrpCsellerCI,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            s += ",robot-2007-getMaxCsellFee,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"        }    }else{        s += ",robot-2007-ObtainConfig,robot-2007-checkInsurePerson,robot-2007-changePerson,robot-2007-checkInsuredPerson,robot-2007-changePerson,robot-2007-prepareEdit,robot-2007-editCengage,robot-2007-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2007-queryVehiclePMCheck,robot-2007-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2007-VehicleModelList" //上海车型查询        }        s += ",robot-2007-queryPrepare,robot-2007-vehicleQuery,robot-2007-queryTaxAbateForPlat,robot-2007-calActualValue,robot-2007-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2007-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2007-calAnciInfo,robot-2007-queryPayFor,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            }else{                s += ",robot-2007-calAnciInfo,robot-2007-checkAgentType,robot-2007-queryPayForSCMS,robot-2007-getCagent,robot-2007-getCagentCI,robot-2007-refreshPlanByTimes,robot-2007-nomotor-unitedSaleEdit,robot-2007-nomotor-saveUnitedSale,robot-2007-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2007-calAnciInfo,robot-2007-getMaxCsellFee,robot-2007-getPrpCseller,robot-2007-getPrpCsellerCI,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-insert"            s += ",robot-2007-getMaxCsellFee,robot-2007-queryPayForSCMS,robot-2007-refreshPlanByTimes,robot-2007-nomotor-unitedSaleEdit,robot-2007-nomotor-saveUnitedSale,robot-2007-insert"        }    }    s += ",robot-2007-checkRiskCode,robot-2007-editMainUwtFlag,robot-2007-editSubmitUndwrt,robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-showUndwrtMsgQ,robot-2007-showUndwrtMsgS"+            ",robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ,robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind,robot-2007-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2007-login,robot_2007_bj_initData,robot_2007_bj_queryModel,robot_2007_bj_getSaleTaxInfo,robot_2007_bj_getRealValue,robot_2007_bj_getPersonData,robot_2007_bj_addPersonData,robot_2007_bj_askCharge,robot_2007_bj_queryPayForXSFY,robot_2007_bj_getCagentCI,robot_2007_bj_getCagent,robot_2007_bj_queryPayForXSFY_req,robot_2007_bj_queryIlogEngage,robot_2007_bj_insureRefrenshPlan,robot_2007_bj_insure4S,robot-2007-uploadImage,robot_2007_bj_autoInsure,robot_2007_bj_showUndwrtMsgQ,robot_2007_bj_showUndwrtMsgS";       s += ",robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ,robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +                ",robot-2007-showCinsuredS,robot-2007-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2007', 'pingan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-平安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2007-qrcode_login,robot-2007-qrcode_printTwoBarCodeServlet,robot-2007-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2007-qrcode_login,robot-2007-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2007-qrcode_login,robot-2007-qrcode_editCheckFlag,robot-2007-qrcode_gotoJfcd,robot-2007-qrcode_prepareEditByJF,robot-2007-qrcode_getBusinessIn" +                ",robot-2007-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2007-qrcode_login,robot-2007-qrcode_editCheckFlag,robot-2007-qrcode_gotoJfcd,robot-2007-qrcode_prepareEditByJF,robot-2007-qrcode_getBusinessIn" +                ",robot-2007-qrcode_checkBeforeCalculate,robot-2007-qrcode_saveByJF,robot-2007-qrcode_getBusinessIn_alipay,robot-2007-qrcode_editFeeInfor,robot-2007-qrcode_editPayFeeByWeChat,robot-2007-qrcode_saveByWeChat,robot-2007-qrcode_save";		} else {					return  "robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-bj-editIDCardCheck,robot-2007-apply-selectIsNetProp,robot-2007-apply-saveCheckCode,robot-2007-qrcode_editCheckFlag,robot-2007-qrcode_gotoJfcd,robot-2007-qrcode_prepareEditByJF,robot-2007-qrcode_getBusinessIn" +",robot-2007-qrcode_checkBeforeCalculate,robot-2007-qrcode_saveByJF,robot-2007-qrcode_getBusinessIn_alipay,robot-2007-qrcode_editFeeInfor,robot-2007-qrcode_editPayFeeByWeChat,robot-2007-qrcode_saveByWeChat,robot-2007-qrcode_save";		}}    else {              return "robot-2007-qrcode_login,robot-2007-qrcode_editCheckFlag,robot-2007-qrcode_gotoJfcd,robot-2007-qrcode_prepareEditByJF,robot-2007-qrcode_getBusinessIn" +                ",robot-2007-qrcode_checkBeforeCalculate,robot-2007-qrcode_saveByJF,robot-2007-qrcode_getBusinessIn_alipay,robot-2007-qrcode_editFeeInfor,robot-2007-qrcode_editPayFeeByWeChat,robot-2007-qrcode_saveByWeChat,robot-2007-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2007', 'pingan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-平安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2007-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2007-qrcode_query_editCheckFlag,robot-2007-qrcode_query_gotoJfcd,robot-2007-qrcode_query_prepareEditByJF" +                ",robot-2007-qrcode_query_editMainInfor,robot-2007-qrcode_query_getBusinessIn,robot-2007-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ" +            ",robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind,robot-2007-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2007', 'pingan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-平安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-IdCarChekc" //申请验证码    else{        return"robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-editIDCardCheck,robot-2007-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2007', 'pingan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-平安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectPolicyefc,robot-2007-selectPolicybiz,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ" +            ",robot-2007-showCitemCarQ,robot-2007-showCinsuredQ,robot-2007-showCitemKindCI,robot-2007-browseProposalS,robot-2007-showCitemCarS" +            ",robot-2007-showCinsuredS,robot-2007-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2007', 'pingan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2007-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectProposalQ,robot-2007-selectProposalS,robot-2007-browseProposalQ,robot-2007-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2007', 'pingan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-平安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-bj-editIDCardCheck,robot-2007-apply-bj-IdCarChekc";    } else {        s = "robot-2007-qrcode_login,robot-2007-qrcode_editCheckFlag,robot-2007-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2007', 'pingan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi平安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2007_ask_charge,edi_2007_noMotor_quote"	} else {		return "edi_2007_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2007', 'pingan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-平安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2007_ask_charge,edi_2007_noMotor_quote,edi_2007_askInsure,edi_2007_uploadImg,edi_2007_submitInsure,edi_2007_noMotor_submit" 	  	} else {		return "edi_2007_ask_chargeold,edi_2007_askInsure,edi_2007_uploadImg,edi_2007_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2007', 'pingan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-平安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2007_ask_charge,edi_2007_noMotor_quote,edi_2007_askInsure,edi_2007_uploadImg" 	} else {		return "edi_2007_ask_chargeold,edi_2007_askInsure,edi_2007_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2007', 'pingan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-平安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2007_efc_policyinfo,edi_2007_biz_policyinfo,edi_2007_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2007', 'pingan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-平安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2007_efc_insurequery,edi_2007_biz_insurequery,edi_2007_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2007', 'pingan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京平安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-bj-editIDCardCheck,robot-2007-apply-saveCheckCode,robot-2007-apply-selectIsNetProp";    } else {        s = "robot-2007-qrcode_login,robot-2007-apply-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2007', 'pingan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-平安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2007-bj-qrcode_login,robot-2007-apply-bj-prepare,robot-2007-apply-select-jq,robot-2007-apply-select-sy,robot-2007-apply-bj-editIDCardCheck,robot-2007-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2007', 'pingan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-平安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2007_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2007', 'pingan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi平安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2007_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2007', 'pingan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi平安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2007_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2007', 'pingan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-平安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2007-login,robot-2007-prepareQueryCode,robot-2007-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2046', 'yongan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-永安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-IdCarChekc" //申请验证码    else{        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-quote', '2046', 'yongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi-永安-报价', 'def getTemplateGroup(dataSource) {    return "edi-2046-queryCar,edi-2046-xbQuery,edi-2046-askCharge"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-autoinsure', '2046', 'yongan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-永安-自核', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="autoinsure";    return "edi-2046-queryCar,edi-2046-xbQuery,edi-2046-askCharge,edi-2046-askQuote"}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insure', '2046', 'yongan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-永安-核保暂存', 'def getTemplateGroup(dataSource) {        dataSource?.enquiry?.taskType="insure";    return "edi-2046-queryCar,edi-2046-xbQuery,edi-2046-askCharge,edi-2046-askQuote"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('Deprecated-insurequery', '2046', 'yongan', 'B1', 'B', 'pro', 'edi', b'0', '{}', 'edi-永安-核保查询', 'def getTemplateGroup(dataSource){    dataSource?.enquiry?.taskType = "insurequery";    return "edi-2046-queryInsure";}', '{}', '{}', b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2046', 'yongan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-永安-报价', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    AutoTask autoTask = dataSource    if(dataSource.applyJson.contains("trafficQuery"))        return "robot-2046-login,robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm";    else{        String orgId = autoTask.configs.orgId;        String s = "";        if("**********".equals(orgId)){            s = "robot-2046-login,robot-2046-ObtainConfig,robot-2046-checkInsurePerson,robot-2046-changePerson,robot-2046-checkInsuredPerson,robot-2046-changePerson,robot-2046-prepareEdit," +                    "robot-2046-prepareQueryCode,robot-2046-selectProposalCar,robot-2046-browseProposalCar,robot-2046-browseProposalCarefc,robot-2046-selectRenewalPolicyNo"        }else{            s = "robot-2046-login,robot-2046-ObtainConfig,robot-2046-checkInsurePerson,robot-2046-changePerson,robot-2046-checkInsuredPerson,robot-2046-changePerson,robot-2046-prepareEdit," +                    "robot-2046-prepareQueryCode,robot-2046-browseProposalCar,robot-2046-selectRenewalPolicyNo"        }        if("**********".equals(orgId)){            s += ",robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2046-VehicleModelList" //上海车型查询        }        s += ",robot-2046-queryPrepare,robot-2046-vehicleQuery,robot-2046-queryTaxAbateForPlat,robot-2046-calActualValue,robot-2046-caculatePremiunForFG"        if("1233400000".equals(orgId) || "1233200000".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2046-editCalculateCarShipTax" //车船税单独计算        }        if(!"**********".equals(orgId)){            s += ",robot-2046-calAnciInfo" //辅助核保 报价抓取 业务类型        }if (["**********", "**********"].contains(orgId) && autoTask.taskEntity.misc && autoTask.taskEntity.misc.nonMotor) {			 s += ",robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-nomotor-unitedSaleEdit,robot-2046-nomotor-saveUnitedSale"		}         if("**********".equals(orgId)){            s = "robot-2046-login,robot_2046_bj_initData,robot_2046_bj_queryModel,robot_2046_bj_getSaleTaxInfo,robot_2046_bj_getRealValue,robot_2046_bj_getPersonData,robot_2046_bj_addPersonData,robot_2046_bj_askCharge";;        }        return s    }}', '{}', '{}', b'1', 35, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2046', 'yongan', '12', '11', 'pro', 'robot', b'1', '{}', '精灵-永安-核保暂存', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskimport com.cheche365.bc.model.car.Enquirydef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource      String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String s = "robot-2046-login";    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2046-ObtainConfig,robot-2046-selectRenewal,robot-2046-editCengage,robot-2046-editCitemCar,robot-2046-editCinsured,robot-2046-renewalPolicy,robot-2046-renewalPolicyCI,robot-2046-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2046-VehicleModelList" //上海车型查询        }        s += ",robot-2046-vehicleQueryXB,robot-2046-queryTaxAbateForPlat,robot-2046-calActualValue,robot-2046-editCitemKind,robot-2046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2046-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-refreshPlanByTimes,robot-2046-insert"            }else{                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2046-getMaxCsellFee,robot-2046-getPrpCseller,robot-2046-getPrpCsellerCI,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            s += ",robot-2046-getMaxCsellFee,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"        }    }else{        s +=",robot-2046-ObtainConfig,robot-2046-checkInsurePerson,robot-2046-changePerson,robot-2046-checkInsuredPerson,robot-2046-changePerson,robot-2046-prepareEdit,robot-2046-selectRenewalPolicyNo,robot-2046-editCengage"        String orgId = autoTask.configs.orgId;        if("**********".equals(orgId)){            s += ",robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2046-VehicleModelList" //上海车型查询        }        s += ",robot-2046-queryPrepare,robot-2046-vehicleQuery,robot-2046-queryTaxAbateForPlat,robot-2046-calActualValue,robot-2046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2046-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北             if("**********".equals(orgId)){                s += ",robot-2046-calAnciInfo,robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            }else{                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-nomotor-unitedSaleEdit,robot-2046-nomotor-saveUnitedSale,robot-2046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2046-getMaxCsellFee,robot-2046-getPrpCseller,robot-2046-getPrpCsellerCI,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            s += ",robot-2046-getMaxCsellFee,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-nomotor-unitedSaleEdit,robot-2046-nomotor-saveUnitedSale,robot-2046-insert"        }         if("**********".equals(orgId)){            s = "robot-2046-login,robot_2046_bj_initData,robot_2046_bj_queryModel,robot_2046_bj_getSaleTaxInfo,robot_2046_bj_getRealValue,robot_2046_bj_getPersonData,robot_2046_bj_addPersonData,robot_2046_bj_askCharge,robot_2046_bj_queryPayForXSFY,robot_2046_bj_getCagentCI,robot_2046_bj_getCagent,robot_2046_bj_queryPayForXSFY_req,robot_2046_bj_queryIlogEngage,robot_2046_bj_insureRefrenshPlan,robot_2046_bj_insure4S,robot-2046-uploadImage";        }    }    return s}', '{}', '{}', b'1', 65, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2046', 'yongan', 'B1', 'B', 'pro', 'robot', b'1', '{}', '精灵-永安-核保查询', 'def getTemplateGroup(dataSource){    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ" +            ",robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind,robot-2046-nomotor-query,robot-2046-showAnciInfo";}', '{}', '{}', b'1', 12, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quotequery', '2046', 'yongan', 'A1', 'A', 'pro', 'robot', b'1', '{}', '精灵-永安-报价查询', 'def getTemplateGroup(dataSource){    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ" +            ",robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind,robot-2046-nomotor-query";}', '{}', '{}', b'1', 9, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2046', 'yongan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-永安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ" +            ",robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind,robot-2046-nomotor-query";}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('pureESale', '2046', 'yongan', 'A1', 'A', 'pro', 'robot', b'1', '{  "default": {    "taxRegistryNumber": "9132010083490580XH",    "dutyPaidProofNo": "232221",    "agentCode": "110041100036",    "password": "JIANG2017",    "makeCom": "32019502",    "taxComName": "南京市地方税务局玄武税务分局",    "biSellerno": "",    "makeComDes": "南京市分公司直属第二营业部",    "comCode": "32019501",    "repeatDayState": "0",    "carCheckStatus": "1",    "carCheckerTranslate": "黄成",    "biSellername": "",    "homePhone": "",    "buyerProvinceDes": "永安财险江苏省分公司",    "businessNature": "4",    "handlerCode": "32926354",    "expectSuccessFlag": "浏览器不支持框架",    "keepSessionUrl": "https://**************:8888/casserver/login?service=http://**************:80/portal/index.jsp",    "ciSellerno": "",    "handlerCodeDes": "黄成",    "prpCmainagentName": "北京天道保险经纪有限责任公司",    "moblie": "",    "agentType": "2110BR",    "Telephone": "***********",    "CarState": "1",    "businessNatureTranslation": "经纪业务",    "login": "A320101056",    "operatorCode": "A320101056",    "operatorName": "王洋",    "RemoteCard": "苏",    "orgId": "**********",    "carChecker": "1232010079",    "DomesticCar": "10",    "queryAreaCode": "320000",    "ciSellername": "",    "ImportedCar": "20",    "buyerProvince": "32000000",    "CarNumber": "10",    "URLIP": "**************",    "address": "江苏南京",    "handler1code_uni": "1232010934",    "comCodeDes": "南京市分公司直属第二营业部经理室",    "TeleState": "1",    "taxComCode": "***********",    "repeatDay": "40",    "queryAreaName": "江苏省",    "handler1Code": "32926354",    "handlercode_uni": "1232010934",    "handler1CodeDes": "黄成",    "username": "A320101056"  },  "headers": {}}', '精灵-永安-电销', 'def getTemplateGroup(dataSource){    return "robot-2046-pureESale_Login,robot-2046-pureESale_Welcome,robot-2046-pureESale_Query";}', '{}', '{}', b'0', 11, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('XB', '2046', 'yongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永安续保查询', 'def getTemplateGroup(dataSource){   //AutoTask autoTask = dataSource   //String orgId = autoTask.configs.orgId;   //if(!"**********".equals(orgId)){      return "robot-2046-login,robot-2046-Renew";   //}}', '{}', '{}', b'1', 10, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2046', 'yongan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵-永安-自动提交核保', 'import com.cheche365.bc.utils.DataUtilimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String  dotCode = "";       if(autoTask.configs.orgId?.startsWith("1244")){           dotCode = "3";        } else {            dotCode = "2";         }    String orgId = autoTask.configs.orgId;    String s = "robot-2046-login"    if(dataSource.taskEntity.isRenewal){        s +=  ",robot-2046-ObtainConfig,robot-2046-selectRenewal,robot-2046-editCengage,robot-2046-editCitemCar,robot-2046-editCinsured,robot-2046-renewalPolicy,robot-2046-renewalPolicyCI,robot-2046-editRenewalCopy"        autoTask.tempValues.put("isRenewal","1");        if("**********".equals(orgId)){            s += ",robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2046-VehicleModelList" //上海车型查询        }        s += ",robot-2046-vehicleQueryXB,robot-2046-queryTaxAbateForPlat,robot-2046-calActualValue,robot-2046-editCitemKind,robot-2046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)){            s += ",robot-2046-editCalculateCarShipTax" //车船税单独计算        }        if("**********".equals(orgId)){//四川            s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("1".equals(dotCode)){//江苏            s += ",robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if(autoTask.configs.orgIdDetail != null && ("1213192082".equals(autoTask.configs.orgIdDetail) || "1251191001".equals(autoTask.configs.orgIdDetail))){                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-refreshPlanByTimes,robot-2046-insert"            }else{                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2046-calAnciInfo,robot-2046-getMaxCsellFee,robot-2046-getPrpCseller,robot-2046-getPrpCsellerCI,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            s += ",robot-2046-getMaxCsellFee,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"        }    }else{        s += ",robot-2046-ObtainConfig,robot-2046-checkInsurePerson,robot-2046-changePerson,robot-2046-checkInsuredPerson,robot-2046-changePerson,robot-2046-prepareEdit,robot-2046-editCengage,robot-2046-selectRenewalPolicyNo"        if("**********".equals(orgId)){            s += ",robot-2046-queryVehiclePMCheck,robot-2046-queryVehiclePMConfirm" //江苏流程        }else if("**********".equals(orgId)){            s += ",robot-2046-VehicleModelList" //上海车型查询        }        s += ",robot-2046-queryPrepare,robot-2046-vehicleQuery,robot-2046-queryTaxAbateForPlat,robot-2046-calActualValue,robot-2046-caculatePremiunForFG"        if("1233400000".equals(orgId) ||"1233200000".equals(orgId) || "**********".equals(orgId)||"1237192501".equals(orgId)){            s += ",robot-2046-editCalculateCarShipTax" //车船税单独计算        }        if("1".equals(dotCode)){//江苏            s += ",robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-insert"        }else if("2".equals(dotCode)){ //四川  陕西 广西 河北            if("**********".equals(orgId)){                s += ",robot-2046-calAnciInfo,robot-2046-queryPayFor,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            }else{                s += ",robot-2046-calAnciInfo,robot-2046-checkAgentType,robot-2046-queryPayForSCMS,robot-2046-getCagent,robot-2046-getCagentCI,robot-2046-refreshPlanByTimes,robot-2046-nomotor-unitedSaleEdit,robot-2046-nomotor-saveUnitedSale,robot-2046-insert"            }        }else if("3".equals(dotCode)){//东莞 广州 佛山            //s += ",robot-2046-calAnciInfo,robot-2046-getMaxCsellFee,robot-2046-getPrpCseller,robot-2046-getPrpCsellerCI,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-insert"            s += ",robot-2046-getMaxCsellFee,robot-2046-queryPayForSCMS,robot-2046-refreshPlanByTimes,robot-2046-nomotor-unitedSaleEdit,robot-2046-nomotor-saveUnitedSale,robot-2046-insert"        }    }    s += ",robot-2046-checkRiskCode,robot-2046-editMainUwtFlag,robot-2046-editSubmitUndwrt,robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-showUndwrtMsgQ,robot-2046-showUndwrtMsgS"+            ",robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ,robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind,robot-2046-nomotor-query";   if("**********".equals(orgId)){       s = "robot-2046-login,robot_2046_bj_initData,robot_2046_bj_queryModel,robot_2046_bj_getSaleTaxInfo,robot_2046_bj_getRealValue,robot_2046_bj_getPersonData,robot_2046_bj_addPersonData,robot_2046_bj_askCharge,robot_2046_bj_queryPayForXSFY,robot_2046_bj_getCagentCI,robot_2046_bj_getCagent,robot_2046_bj_queryPayForXSFY_req,robot_2046_bj_queryIlogEngage,robot_2046_bj_insureRefrenshPlan,robot_2046_bj_insure4S,robot-2046-uploadImage,robot_2046_bj_autoInsure,robot_2046_bj_showUndwrtMsgQ,robot_2046_bj_showUndwrtMsgS";       s += ",robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ,robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +                ",robot-2046-showCinsuredS,robot-2046-showCitemKind";    }    return s}', '{}', '{}', b'1', 48, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_insurequery', '2046', 'yongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永安-二维码支付查询', 'def getTemplateGroup(dataSource) {    String orgId = dataSource?.enquiry?.configInfo?.configMap?.orgId;    if ("1".equals(dataSource?.enquiry?.configInfo?.configMap?.getReferrer) && dataSource?.enquiry?.carInfo?.useProps == 1){        return "robot-2046-qrcode_login,robot-2046-qrcode_printTwoBarCodeServlet,robot-2046-qrcode_pay_getReferrer";    }    else if (["**********","**********","**********","010002134"].contains(orgId) || (["**********","**********"].contains(orgId) && [0,5,12].contains(dataSource?.enquiry?.insurePerson?.idCardType))) {        return "robot-2046-qrcode_login,robot-2046-qrcode_printTwoBarCodeServlet";    } else if ("**********".equals(orgId) || "**********".equals(orgId)){                return "robot-2046-qrcode_login,robot-2046-qrcode_editCheckFlag,robot-2046-qrcode_gotoJfcd,robot-2046-qrcode_prepareEditByJF,robot-2046-qrcode_getBusinessIn" +                ",robot-2046-qrcode_cbcPrintTwoBarCodeServlet";    } else if ("**********".equals(orgId)){		if(["6","8","9","10"].contains(dataSource?.enquiry?.insurePerson.idCardType.toString())){		     return "robot-2046-qrcode_login,robot-2046-qrcode_editCheckFlag,robot-2046-qrcode_gotoJfcd,robot-2046-qrcode_prepareEditByJF,robot-2046-qrcode_getBusinessIn" +                ",robot-2046-qrcode_checkBeforeCalculate,robot-2046-qrcode_saveByJF,robot-2046-qrcode_getBusinessIn_alipay,robot-2046-qrcode_editFeeInfor,robot-2046-qrcode_editPayFeeByWeChat,robot-2046-qrcode_saveByWeChat,robot-2046-qrcode_save";		} else {					return  "robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-bj-editIDCardCheck,robot-2046-apply-selectIsNetProp,robot-2046-apply-saveCheckCode,robot-2046-qrcode_editCheckFlag,robot-2046-qrcode_gotoJfcd,robot-2046-qrcode_prepareEditByJF,robot-2046-qrcode_getBusinessIn" +",robot-2046-qrcode_checkBeforeCalculate,robot-2046-qrcode_saveByJF,robot-2046-qrcode_getBusinessIn_alipay,robot-2046-qrcode_editFeeInfor,robot-2046-qrcode_editPayFeeByWeChat,robot-2046-qrcode_saveByWeChat,robot-2046-qrcode_save";		}}    else {              return "robot-2046-qrcode_login,robot-2046-qrcode_editCheckFlag,robot-2046-qrcode_gotoJfcd,robot-2046-qrcode_prepareEditByJF,robot-2046-qrcode_getBusinessIn" +                ",robot-2046-qrcode_checkBeforeCalculate,robot-2046-qrcode_saveByJF,robot-2046-qrcode_getBusinessIn_alipay,robot-2046-qrcode_editFeeInfor,robot-2046-qrcode_editPayFeeByWeChat,robot-2046-qrcode_saveByWeChat,robot-2046-qrcode_save";    }}', '{}', '{}', b'1', 31, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('qrcode_approvedquery', '2046', 'yongan', '21', '20', 'pro', 'robot', b'1', '{}', '精灵-永安-二维码支付承保查询', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    AutoTask autoTask = dataSource    String s = "robot-2046-login";    if("true".equals(autoTask.tempValues.isPaymentDept)){        s +=  ",robot-2046-qrcode_query_editCheckFlag,robot-2046-qrcode_query_gotoJfcd,robot-2046-qrcode_query_prepareEditByJF" +                ",robot-2046-qrcode_query_editMainInfor,robot-2046-qrcode_query_getBusinessIn,robot-2046-qrcode_query_proposalToPolicy";    }    s +=  ",robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ" +            ",robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind,robot-2046-nomotor-query";    return s}', '{}', '{}', b'1', 8, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('applypinbj', '2046', 'yongan', '15', '6', 'pro', 'other', b'1', '{}', '精灵-永安-验证码', 'def getTemplateGroup(dataSource) {    if("0".equals(dataSource.enquiry.pinType))        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-IdCarChekc" //申请验证码    else{        return"robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-editIDCardCheck,robot-2046-apply-saveCheckCode" //提交验证码    }}', '{}', '{}', b'1', 1, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('policyquery', '2046', 'yongan', '10001', '10000', 'pro', 'robot', b'1', '{}', '精灵-永安-线下保单查询', 'def getTemplateGroup(dataSource){    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectPolicyefc,robot-2046-selectPolicybiz,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ" +            ",robot-2046-showCitemCarQ,robot-2046-showCinsuredQ,robot-2046-showCitemKindCI,robot-2046-browseProposalS,robot-2046-showCitemCarS" +            ",robot-2046-showCinsuredS,robot-2046-showCitemKind";}', '{}', '{}', b'1', 3, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('imageupload', '2046', 'yongan', '33', '32', 'pro', 'robot', b'1', '{}', '精灵影像上传', 'import com.cheche365.bc.model.car.Enquiryimport com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource){    /**AutoTask autoTask=dataSource    autoTask.taskType="robot-2046-quotequery";    Enquiry enquiry=autoTask.getTaskEntity();    enquiry.bizProposeNum="TDAA201842010000415909"    enquiry.efcProposeNum="TDZA201842010000414931"    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectProposalQ,robot-2046-selectProposalS,robot-2046-browseProposalQ,robot-2046-uploadImages" ;*/}', '{}', '{}', b'1', 6, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2046', 'yongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永安-获取支付短信验证码', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-bj-editIDCardCheck,robot-2046-apply-bj-IdCarChekc";    } else {        s = "robot-2046-qrcode_login,robot-2046-qrcode_editCheckFlag,robot-2046-qrcode_getIdentifyingCode";    }    return s;}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('quote', '2046', 'yongan', 'A1', 'A', 'pro', 'edi', b'0', '{}', 'edi永安报价接口', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2046_ask_charge,edi_2046_noMotor_quote"	} else {		return "edi_2046_ask_chargeold"	}     }', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('autoinsure', '2046', 'yongan', '33', '32', 'pro', 'edi', b'0', '{}', 'edi-永安-自核', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){		return "edi_2046_ask_charge,edi_2046_noMotor_quote,edi_2046_askInsure,edi_2046_uploadImg,edi_2046_submitInsure,edi_2046_noMotor_submit" 	  	} else {		return "edi_2046_ask_chargeold,edi_2046_askInsure,edi_2046_uploadImg,edi_2046_submitInsure" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insure', '2046', 'yongan', '12', '11', 'pro', 'edi', b'0', '{}', 'edi-永安-核保暂存', 'def getTemplateGroup(dataSource){	if ("true".equals(dataSource?.enquiry?.SQ?.reform2020?.toString())){      return "edi_2046_ask_charge,edi_2046_noMotor_quote,edi_2046_askInsure,edi_2046_uploadImg" 	} else {		return "edi_2046_ask_chargeold,edi_2046_askInsure,edi_2046_uploadImg" 	}}', '{}', '{}', b'1', 10, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('approvedquery', '2046', 'yongan', '21', '20', 'pro', 'edi', b'0', '{}', 'edi-永安-承保查询', 'def getTemplateGroup(dataSource){  return "edi_2046_efc_policyinfo,edi_2046_biz_policyinfo,edi_2046_noMotor_policyquery"}', '{}', '{}', b'1', 5, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('insurequery', '2046', 'yongan', '15', '14', 'pro', 'edi', b'0', '{}', 'edi-永安-核保查询', 'def getTemplateGroup(dataSource){    return "edi_2046_efc_insurequery,edi_2046_biz_insurequery,edi_2046_noMotor_insurequery"}', '{}', '{}', b'1', 8, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2046', 'yongan', '4', '6', 'pro', 'robot', b'1', '{}', '精灵北京永安短信验证', 'def getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-bj-editIDCardCheck,robot-2046-apply-saveCheckCode,robot-2046-apply-selectIsNetProp";    } else {        s = "robot-2046-qrcode_login,robot-2046-apply-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-qrcode_saveCode";    }    return s;}', '{}', '{}', b'1', 1, 'enq');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsAndSave', '2046', 'yongan', '7', '6', 'pro', 'robot', b'1', '{}', '精灵-永安-身份信息保存', 'import com.cheche365.bc.task.AutoTaskdef getTemplateGroup(dataSource) {    String orgId = dataSource.enquiry.configInfo.configMap.orgId    def s = ""    if ("**********".equals(orgId)) {        s = "robot-2046-bj-qrcode_login,robot-2046-apply-bj-prepare,robot-2046-apply-select-jq,robot-2046-apply-select-sy,robot-2046-apply-bj-editIDCardCheck,robot-2046-apply-bj-idenditySave";    } else {           }    return s;}', '{}', '{}', b'1', 4, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('getSmsForPay', '2046', 'yongan', '7', '6', 'pro', 'edi', b'0', '{}', 'edi-永安-短信验证码申请', 'def getTemplateGroup(dataSource){	return "edi_2046_getSms"}', '{}', '{}', b'1', 3, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerify', '2046', 'yongan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi永安北京身份采集', 'def getTemplateGroup(dataSource){			return "edi_2046_idcard_collect"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('identifyVerifyWrite', '2046', 'yongan', '10001', '10000', 'pro', 'edi', b'0', NULL, 'edi永安北京身份采集回写', 'def getTemplateGroup(dataSource){			return "edi_2046_idcard_writeback"	     }', NULL, NULL, b'1', 2, 'map');
INSERT INTO `tb_api_new`(`action`, `comId`, `comCode`, `defaultFailedStatus`, `defaultSuccessStatus`, `env`, `intType`, `keepSession`, `proConfig`, `remark`, `term`, `testConfig`, `uatConfig`, `useTaskConfig`, `version`, `dataStructureType`) VALUES ('extractPolicyList', '2046', 'yongan', 'D1', 'D', 'test', 'robot', b'0', '{}', '精灵-永安-承保查询', 'def getTemplateGroup(dataSource){    return "robot-2046-login,robot-2046-prepareQueryCode,robot-2046-selectProposalSlists"}', '{}', '{}', b'1', 7, 'enq');
