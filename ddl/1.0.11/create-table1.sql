DROP TABLE if exists `tb_platforminfo_key`;
CREATE TABLE `tb_platforminfo_key` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '平台信息key',
  `key_description` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '平台信息描述',
  `userid` bigint(20) DEFAULT NULL COMMENT '最后编辑人',
  `key_status` tinyint(4) DEFAULT '0' COMMENT '启用1禁用0',
  `key_station` bigint(11) DEFAULT NULL COMMENT '使用者1磐石',
  `key_company` int(255) DEFAULT NULL COMMENT '所属保司',
  `key_path` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '数据路径',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='平台信息key管理表';

