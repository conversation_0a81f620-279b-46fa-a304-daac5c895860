/*Table structure for table `bc_menu_info` */

DROP TABLE IF EXISTS `bc_menu_info`;

CREATE TABLE `bc_menu_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL COMMENT '菜单名称',
  `level` int(11) DEFAULT NULL COMMENT '菜单层级',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父菜单id',
  `url` varchar(100) DEFAULT NULL COMMENT '链接地址',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标地址',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` tinyint(4) DEFAULT '0' COMMENT '菜单状态（0：启用  1：禁用）',
  `menu_code` varchar(45) DEFAULT NULL COMMENT '菜单编码',
  `locale` varchar(45) DEFAULT NULL COMMENT '国际化',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='菜单表';

/*Data for the table `bc_menu_info` */

insert  into `bc_menu_info`(`id`,`name`,`level`,`parent_id`,`url`,`icon`,`sort`,`status`,`menu_code`,`locale`) values 
(1,'线上',0,NULL,NULL,NULL,1,0,NULL,NULL),
(2,'线下',0,NULL,NULL,NULL,2,1,NULL,NULL),
(3,'接口管理',1,1,NULL,NULL,1,0,'interface',NULL),
(4,'模板管理',1,1,NULL,NULL,2,0,'template',NULL),
(5,'日志查询',1,1,NULL,NULL,3,0,'autoTaskLog',NULL),
(6,'用户管理',1,1,NULL,NULL,5,0,'userManage',NULL),
(7,'操作记录',2,6,NULL,NULL,3,0,'listUserLog',NULL),
(8,'异常配置',2,15,NULL,NULL,1,0,'errorCategory',NULL),
(10,'Actor管理',2,15,NULL,NULL,2,0,'accountMgn',NULL),
(11,'工作时间',2,15,NULL,NULL,3,0,'workerTime',NULL),
(12,'用户管理列表',2,6,NULL,NULL,1,0,'listUserInfo',NULL),
(13,'角色与权限管理',2,6,NULL,NULL,2,0,'listRoleInfo',NULL),
(15,'系统配置',1,1,NULL,NULL,4,0,'systemManage',NULL);

/*Table structure for table `bc_permission_info` */

DROP TABLE IF EXISTS `bc_permission_info`;

CREATE TABLE `bc_permission_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL COMMENT '权限名称',
  `url` varchar(100) DEFAULT NULL COMMENT '权限url',
  `menu_id` varchar(200) NOT NULL COMMENT '权限所属菜单id',
  `permission_code` varchar(45) NOT NULL COMMENT '权限编码',
  `status` tinyint(4) DEFAULT '1' COMMENT '1正常,0不可用',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `sort` tinyint(4) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_menu_id` (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='权限表';

/*Data for the table `bc_permission_info` */

insert  into `bc_permission_info`(`id`,`name`,`url`,`menu_id`,`permission_code`,`status`,`description`,`sort`) values 
(17,'查看',NULL,'12','userinfo-getPage',1,'用户管理-用户管理列表-查看',1),
(18,'编辑',NULL,'12','userinfo-update',1,'用户管理-用户管理列表-编辑',2),
(19,'新建',NULL,'12','userinfo-save',1,'用户管理-用户管理列表-新建',3),
(20,'启用/禁用',NULL,'12','userinfo-setstatus',1,'用户管理-用户管理列表-启用/禁用',4),
(21,'删除',NULL,'12','userinfo-delete',1,'用户管理-用户管理列表-删除',5),
(22,'查看',NULL,'13','roleinfo-getPage',1,'用户管理-角色与权限管理-查看',1),
(23,'新建',NULL,'13','roleinfo-save',1,'用户管理-角色与权限管理-新建',2),
(24,'编辑',NULL,'13','roleinfo-update',1,'用户管理-角色与权限管理-编辑',3),
(25,'启用/禁用',NULL,'13','roleinfo-setstatus',1,'用户管理-角色与权限管理-启用/禁用',4),
(26,'删除',NULL,'13','roleinfo-delete',1,'用户管理-角色与权限管理-删除',5),
(27,'查看',NULL,'7','userlog-getPage',1,'用户管理-操作记录-查看',1);

/*Table structure for table `bc_role_info` */

DROP TABLE IF EXISTS `bc_role_info`;

CREATE TABLE `bc_role_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL,
  `code` varchar(20) DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL COMMENT '0禁用1启用',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建用户',
  `description` varchar(250) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `bc_role_info` */

insert  into `bc_role_info`(`id`,`name`,`code`,`status`,`create_user`,`description`,`create_time`,`update_time`) values 
(1,'超级管理员','admin',1,NULL,NULL,NULL,NULL),
(2,'用户管理员',NULL,1,'admin','负责创建用户和角色','2019-12-26 17:00:49','2020-07-24 16:54:55');

/*Table structure for table `bc_role_permission` */

DROP TABLE IF EXISTS `bc_role_permission`;

CREATE TABLE `bc_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色id',
  `permission_id` bigint(20) DEFAULT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_role_id` (`role_id`) USING BTREE,
  KEY `idx_permission_id` (`permission_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=84 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='角色权限表';

/*Data for the table `bc_role_permission` */

insert  into `bc_role_permission`(`id`,`role_id`,`permission_id`) values 
(1,1,1),
(2,1,2),
(3,1,3),
(4,1,4),
(5,1,5),
(6,1,6),
(7,1,7),
(8,1,8),
(9,1,9),
(10,1,10),
(11,1,11),
(12,1,12),
(13,1,13),
(14,1,14),
(15,1,16),
(16,1,17),
(17,1,18),
(18,1,19),
(19,1,20),
(20,1,21),
(21,1,22),
(22,1,23),
(23,1,24),
(24,1,25),
(25,1,26),
(26,1,27),
(63,2,17),
(64,2,18),
(65,2,19),
(66,2,20),
(67,2,21),
(68,2,22),
(69,2,23),
(70,2,24),
(71,2,25),
(72,2,26);

/*Table structure for table `bc_user_info` */

DROP TABLE IF EXISTS `bc_user_info`;

CREATE TABLE `bc_user_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) DEFAULT NULL,
  `name` varchar(45) DEFAULT NULL,
  `institution` varchar(100) DEFAULT NULL COMMENT '机构名称',
  `password` varchar(45) DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL COMMENT '1启用  0禁用',
  `source_type_ids` varchar(100) DEFAULT NULL COMMENT '数据范围：1线上数据 2北京线下数据 3泛华数据  4其他，用逗号分隔',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构id',
  `org_code` varchar(100) DEFAULT NULL COMMENT '所属机构代码',
  `org_name` varchar(100) DEFAULT NULL COMMENT '所属机构名称',
  `org_level` tinyint(4) DEFAULT NULL COMMENT '所属机构等级',
  `first_login` tinyint(1) DEFAULT '1' COMMENT '是否首次登陆',
  `login_permission` varchar(10) DEFAULT NULL COMMENT '登录权限（1线上，2线下 3线上&线下）',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(50) DEFAULT NULL COMMENT '创建用户名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '最新修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `das_user_id` (`id`) USING BTREE,
  UNIQUE KEY `email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1833 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `bc_user_info` */

insert  into `bc_user_info`(`id`,`email`,`name`,`institution`,`password`,`status`,`source_type_ids`,`org_id`,`org_code`,`org_name`,`org_level`,`first_login`,`login_permission`,`create_user_id`,`create_user_name`,`create_time`,`update_time`) values 
(1,'admin','管理员',NULL,'e64b78fc3bc91bcbc7dc232ba8ec59e0',1,'',1200000000,'010','车险运营平台',0,0,'1',NULL,NULL,NULL,'2019-12-19 14:14:10'),
(2,'user_admin','user_admin',NULL,'46e44aa0bc21d8a826d79344df38be4b',1,NULL,1200000000,NULL,NULL,0,1,NULL,1,'管理员','2019-12-26 18:20:00','2020-07-15 16:53:55');

/*Table structure for table `bc_user_log` */

DROP TABLE IF EXISTS `bc_user_log`;

CREATE TABLE `bc_user_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色id',
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名称',
  `operate_content` tinyint(4) DEFAULT NULL COMMENT '操作内容',
  `operate_user_id` bigint(20) DEFAULT NULL COMMENT '操作人id',
  `operate_user_name` varchar(50) DEFAULT NULL COMMENT '操作人名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `note` text COMMENT '记录',
  `operate_log` text COMMENT '操作日志',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_operate_user_id` (`operate_user_id`),
  KEY `idx_operate_user_name` (`operate_user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `bc_user_log` */

/*Table structure for table `bc_user_role` */

DROP TABLE IF EXISTS `bc_user_role`;

CREATE TABLE `bc_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT ' ',
  `user` bigint(20) DEFAULT NULL,
  `role` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_USER_ROLE_REF_USER` (`user`) USING BTREE,
  KEY `FK_USER_ROLE_REF_ROLE` (`role`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `bc_user_role` */

insert  into `bc_user_role`(`id`,`user`,`role`) values 
(1,1,1),
(15,8,1),
(16,9,1),
(20,7,1),
(63,2,2);
