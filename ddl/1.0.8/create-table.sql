DROP TABLE IF EXISTS `tb_policy_source`;
CREATE TABLE `tb_policy_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyno` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '保单号',
  `policytype` int(255) DEFAULT NULL COMMENT '保单类型1交强2商业3非车',
  `sourceid` varchar(500) CHARACTER SET utf8 DEFAULT NULL COMMENT '资源id',
  `insid` int(11) DEFAULT NULL COMMENT '保司编号',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `key1` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备用1',
  `key2` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备用2',
  PRIMARY KEY (`id`),
  KEY `idx_policyno` (`policyno`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4
