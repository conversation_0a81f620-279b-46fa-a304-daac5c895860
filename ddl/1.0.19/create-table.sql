DROP TABLE IF EXISTS `policy_data_statistics`;
CREATE TABLE `policy_data_statistics`
(
  `id`           bigint(11)      NOT NULL AUTO_INCREMENT,
  `com_id`       varchar(10)     NOT NULL COMMENT '保司编号',
  `company_abbr` varchar(50)     DEFAULT NULL COMMENT '保司缩写',
  `company_name` varchar(50)     NOT NULL COMMENT '保司名称',
  `month`        char(10)        NOT NULL COMMENT '月份',
  `count`        int(11)         NOT NULL COMMENT '数量',
  `channel_type` varchar(50)     NOT NULL COMMENT '渠道类型',
  `create_time` datetime(0)      DEFAULT NULL COMMENT '创建日期',
  `ext1` varchar(255)            DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255)            DEFAULT NULL COMMENT '扩展字段1',
  PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='数据抓取统计表';
