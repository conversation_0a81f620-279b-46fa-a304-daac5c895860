DROP TABLE IF EXISTS `channel`;
CREATE TABLE `channel` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                           `app_key` varchar(100) NOT NULL,
                           `secret_key` varchar(255) NOT NULL,
                           `app_id` varchar(100) DEFAULT NULL,
                           `channel_code` varchar(50) DEFAULT NULL,
                           `channel_name` varchar(50) NOT NULL,
                           `call_back_url` varchar(255) NOT NULL,
                           `type` varchar(10) NOT NULL,
                           `allow_ips` varchar(255) DEFAULT NULL,
                           `allow_ins` varchar(255) DEFAULT NULL,
                           `version` varchar(10) DEFAULT NULL,
                           `status` tinyint(4) DEFAULT NULL COMMENT '0禁用1启用',
                           `left_query_car_model_policy_times` int(11) DEFAULT NULL,
                           `left_query_car_times` int(11) DEFAULT NULL,
                           `left_query_plat_times` int(11) DEFAULT NULL,
                           `left_query_policy_times` int(11) DEFAULT NULL,
                           `left_quote_times` int(11) DEFAULT NULL,
                           `total_query_car_model_policy_times` int(11) DEFAULT NULL,
                           `total_query_car_times` int(11) DEFAULT NULL,
                           `total_query_plat_times` int(11) DEFAULT NULL,
                           `total_query_policy_times` int(11) DEFAULT NULL,
                           `total_quote_times` int(11) DEFAULT NULL,
                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
                           `ext1` varchar(255) DEFAULT NULL,
                           `ext2` varchar(255) DEFAULT NULL,
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


