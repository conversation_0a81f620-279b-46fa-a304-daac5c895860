package com.cheche365.bc.river.service.fetchPolicy.impl;

import cn.hutool.json.JSONUtil;
import com.cheche365.bc.entity.AccountInfo;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.exception.ProcessDataException;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.river.service.fetchPolicy.PolicyExtractService;
import com.cheche365.bc.service.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.util.ExtractPolicyUtils;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.TaskUtil;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;

import static com.cheche365.bc.util.ExtractPolicyUtils.*;


@Slf4j
@Service
public class PolicyExtractServiceImpl implements PolicyExtractService {

    @Resource
    PolicySimplifyInfoService policyService;
    @Resource
    PolicySimplifyInfoService policySimplifyInfoService;
    @Resource
    PolicyAccountService accountService;
    @Resource
    PolicyWriteBackService policyWriteBackService;
    @Resource
    private InterfaceService interfaceService;
    @Resource
    private TemplateService templateService;

    private final String defaultStartDate = "2020-10-01";

    public void extractPolicy(List<AccountInfo> accountInfoList) {
        // 遍历保险公司，获取对应的账号列表
        accountInfoList.forEach(accountInfo -> {
            try {
                CompletableFuture.runAsync(() -> doExtractPolicy(accountInfo));
            } catch (Exception e) {
                error("账号：{}-{}抓取异常：{}", accountInfo.getComId(), accountInfo.getUsername(), e.getMessage());
            }
        });
    }

    /**
     * 根据账号抓取保单详情
     *
     * @param account 账号信息
     */
    @SneakyThrows
    private void doExtractPolicy(AccountInfo account) {
        AutoTask task = new AutoTask();
        String login = account.getUsername();
        String password = account.getPassword();
        String comId = account.getComId();
        Map<String, Object> config = account.getConfig();
        LocalDate lastCompleteTime = account.getLastCompleteTime();
        LocalDate realLastCompleteDate = lastCompleteTime != null ? lastCompleteTime : policyService.findLastDate(comId, login, true);
        LocalDate startDate = realLastCompleteDate != null ? realLastCompleteDate.plusDays(1) : LocalDate.parse(defaultStartDate);
        info("账号：{}-{},lastCompleteTime：{},上次完成日期为：{}", comId, login, lastCompleteTime, realLastCompleteDate);
        config.put("login", login);
        config.put("password", password);
        config.put("accountArea", account.getAreaName());
        config.put("accountType", account.getType());
        config.putIfAbsent("startDate", startDate.toString());
        LocalDate endDate = null;
        if (StringUtil.isEmpty((String) config.get("endDate"))) {
            // 默认终止日期为起始日期
            endDate = LocalDate.now().minusDays(1);
            config.put("endDate", endDate.toString());
        }
        // 装载接口
        task.setTaskType("robot-" + comId + "-extractPolicyList");
        Interface itf = initTaskForGetPolicyNosWithAccount(task, comId, login, config);
        String taskKey = account.getUniqueKey() + "-" + UUID.randomUUID().toString().replaceAll("-", "");

        Map<String, Object> map = new HashMap<>(2);
        map.put("enquiryId", task.getTraceKey().concat("@").concat(task.getCompanyId()));
        task.setTempValues(map);

        CompletableFuture<AutoTask> resp = CompletableFuture.supplyAsync(() ->
                {
                    policyService.setAllowRunningAccount(taskKey, task);
                    try {
                        return AkkaTaskUtil.process(itf, task);
                    } catch (Exception e) {
                        log.error("抓取保单失败:{}", ExceptionUtils.getStackTrace(e));
                        throw new RuntimeException(e);
                    }
                }
        );

        resp.thenAccept(handlerSuccess(task, account, taskKey, endDate));

        resp.exceptionally(e -> {
            handlerFail(task, account, taskKey);
            return null;
        });
    }


    private Consumer<AutoTask> handlerSuccess(AutoTask task, AccountInfo account, String taskKey, LocalDate endDate) {
        Map<String, Object> config = task.getConfigs();
        return autoTask -> {
            String messageCom = String.format("账号：%s,日期：%s至%s,", taskKey, config.get("startDate"), config.get("endDate"));
            policySimplifyInfoService.deleteAllowRunningAccount(taskKey);
            if (StringUtil.isNoEmpty(task.getCallBackUrl())) {
                policyWriteBackService.writeBackPolicyByAccountNo(task, null);
            }
            if ("0".equals(task.getTaskStatus()))
                error("{}抓取任务被手动中断", messageCom);
            else {
                // 正常抓取结束的标记下结束时间, 目的更新最新成功日期，如果是手动补全的任务则不更新日期
                if (endDate != null && account.getId() != null) {
                    account.setFailTimes(0);
                    account.setLastCompleteTime(endDate);
                    accountService.updateById(account);
                }
                Integer policyListSize = (Integer) task.getTempValues().get(POLICY_SIMPLIFY_INFO_LIST_SIZE);
                info("{}抓取完毕;共抓取：{}条记录", messageCom, policyListSize == null ? "0" : policyListSize);
            }
        };
    }

    private Function<Throwable, AutoTask> handlerFail(AutoTask task, AccountInfo account, String taskKey) {
        Map<String, Object> config = task.getConfigs();
        return failure -> {
            String messageCom = String.format("账号：%s,日期：%s至%s,", taskKey, config.get("startDate"), config.get("endDate"));
            policySimplifyInfoService.deleteAllowRunningAccount(taskKey);
            if (StringUtil.isNoEmpty(task.getCallBackUrl())) {
                if (failure != null) task.setFailureCause(failure.getMessage());
                policyWriteBackService.writeBackPolicyByAccountNo(task, failure);
            }
            if (account.getId() != null && !(failure instanceof ProcessDataException)) {
                // 失败次数达到3次(默认值可配置)或者账号密码错误的则禁用账号，如果是磐石的账号则直接删除
                Integer failTimes = account.getFailTimes();
                Integer failTimesMax = account.getFailTimesMax();
                boolean isLoginError = failure instanceof UniqueException && ((UniqueException) failure).getCode() == UniqueException.IllegalAuth;
                if (failTimes >= failTimesMax || isLoginError) {
                    account.setStatus(CommStatus.DISABLED);
                    account.setErrorMessage(failure.getMessage());
                }
                // 如果为磐石账号且账号密码错误则直接删除账号，否则禁用账号
                if (isLoginError && "1".equals(account.getType())) {
                    accountService.removeById(account.getId());
                } else if (failTimesMax < 99 &&
                        (failure.getMessage().matches(".*(登陆|登录).*")
                                || failure.getMessage().contains("Duplicate entry"))) {
                    // 修改为只有包含登录失败等信息的账号才禁用，
                    account.setFailTimes(failTimes + 1);
                    ExtractPolicyUtils.detailDuplicateKeyAccount(failure.getMessage(), account);
                    accountService.updateById(account);
                }
            }
            error("{}抓取失败:{}", messageCom, failure.getMessage(), failure);
            return null;
        };
    }

    /**
     * 初始化单账号的 autoTask（获取投保单列表使用）
     */
    private Interface initTaskForGetPolicyNosWithAccount(AutoTask task, String comId, String login, Map<String, Object> config) throws Exception {
        Interface itf = interfaceService.getInterface(task, task.getTaskType());
        HashMap<String, Object> configs = (HashMap<String, Object>) itf.loadConfig();
        if (configs == null) configs = new HashMap<>();
        if (config != null && !config.isEmpty()) configs.putAll(config);
        configs.put("notSaveMongo", true);
        task.setConfigs(configs);
        task.setCallBackUrl((String) configs.get("callBackUrl"));
        task.setHttpClient(TaskUtil.createHttpClientWithProxy(task, (Boolean) configs.getOrDefault("useNewSSL", false)));
        task.setCompanyId(comId);
        task.setTraceKey(login);
        task.setTaskStatus("1");
        task.setTaskEntity(initEnquiry());
        //加载模板
        templateService.loadTemplate(itf, task);
        return itf;
    }

    private String getRequestBody(Interface itf, AutoTask autoTask) {
        HashMap<String, Object> hashMap = new HashMap<>(2) {
            {
                put("itf", itf);
                put("autoTask", autoTask);
            }
        };

        return JSONUtil.toJsonStr(hashMap);
    }
}
