package com.cheche365.bc.river.service.callback;

import com.cheche365.bc.enums.InsCompanyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class ZhongHangAnMengCallBackHandler extends AbstractCallBackHandler {

    @Override
    public boolean needHandle(String companyId) {
        return String.valueOf(InsCompanyEnum.AMIC.getCode()).equals(companyId);
    }

    @Override
    public String handleCallBackBody(Map param) {
        String callbackBody = (String) param.get("callbackBody");
        return callbackBody.substring(0, callbackBody.lastIndexOf("="));

    }

    @Override
    public String handleResponseBody(Map param) {
        return (String) param.get("responseContent");
    }
}
