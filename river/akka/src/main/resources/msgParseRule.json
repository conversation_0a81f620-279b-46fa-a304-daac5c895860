{"ediFlag": "insure<PERSON><PERSON>", "keyReplaceRule": [{"key": "applicantPersonInfo", "keyEdi": "insure<PERSON><PERSON>"}, {"key": "beneficiaryPersonList", "keyEdi": "beneficiary<PERSON><PERSON><PERSON>"}, {"key": "legal", "keyEdi": "isLegal"}, {"key": "isLoanManyYearsFlag", "keyEdi": "loanManyYearsFlag"}, {"key": "invoice", "keyEdi": "isInvoice"}, {"key": "driverPersonList", "keyEdi": "driverInfoList"}, {"key": "trackInfoList", "keyEdi": "trackInfo"}, {"key": "sq", "keyEdi": "SQ"}, {"key": "insuredPersonInfoList", "keyEdi": "insuredPersonList"}], "keySpecialRule": [{"key": "providerInfoList", "keyEdi": "providerInfo", "changeTypeToEdi": "listToObject", "listItemKey": ""}, {"key": "sq.misc", "keyEdi": "SQ.misc", "changeTypeToEdi": "stringToMap"}, {"key": "payInfo.misc", "keyEdi": "payInfo.misc", "changeTypeToEdi": "stringToMap"}, {"key": "carInfo.misc", "keyEdi": "carInfo.misc", "changeTypeToEdi": "stringToMap"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carOwner", "keyEdi": "carOwnerInfo.name"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carLicense", "keyEdi": "carInfo.plateNum"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carVin", "keyEdi": "carInfo.vin"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carEngineNum", "keyEdi": "carInfo.engineNum"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.lastCommercialPoliceyNum", "keyEdi": "sq.sypolicyno"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.insuredName", "keyEdi": "insuredPersonInfoList[0].name"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carLicenseType", "keyEdi": "carInfo.plateType"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carFirstRegDate", "keyEdi": "carInfo.firstRegDate"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carOwnerIdType", "keyEdi": "carOwnerInfo.idCardType"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.carOwnerIdNum", "keyEdi": "carOwnerInfo.idCard"}, {"changeTypeToEdi": "renewalKeyChange", "key": "renewalquoteitem.insuredIdNum", "keyEdi": "insuredPersonInfoList[0].idCard"}]}