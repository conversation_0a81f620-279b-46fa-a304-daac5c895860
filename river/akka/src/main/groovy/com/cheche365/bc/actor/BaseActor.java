package com.cheche365.bc.actor;

import akka.actor.ActorRef;
import akka.actor.ActorSelection;
import akka.actor.Props;
import akka.actor.UntypedAbstractActor;
import akka.util.Timeout;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.constants.TraceConstants;
import com.cheche365.bc.task.AutoTask;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import scala.PartialFunction;
import scala.concurrent.duration.Duration;
import scala.runtime.BoxedUnit;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Actor基类
 * - Actor路径管理
 * - 消息发送工具方法
 * - 日志MDC管理
 * - Actor生命周期管理
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/19 13:53.
 */
public abstract class BaseActor extends UntypedAbstractActor {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    public static Timeout timeout = new Timeout(Duration.create(5, "seconds"));

    private static final int MAX_ACTORS_NUM = 1000000;

    /**
     * Actor不多时候使用,Actor的key和对应的路径
     */
    public static ConcurrentMap<String, String> allActors = new ConcurrentHashMap<>();

    protected String actorKey = "";

    protected String pathStr = "";


    /**
     * 使用这必须保证key的Actor存在，要不消息会丢失
     *
     * @param key 路径key
     * @param msg 消息实体
     */
    public void tell(String key, Object msg) throws Exception {
        tell(key, msg, null);
    }

    /**
     * 使用这必须保证key的Actor存在，要不消息会丢失
     *
     * @param key     路径key
     * @param clazz   需要新建的Actor类对象
     * @param objects clazz对象构建的参数列表
     * @param msg     消息实体
     */
    public void tell(String key, Object msg, Class clazz, Object... objects) throws Exception {
        synchronized (key) {
            if (allActors.containsKey(key)) {
                String truePath = allActors.get(key);
                log.info("{}选择子actor的{}路径为{}", actorKey, key, truePath);
                ActorSelection actorSelection = context().actorSelection(truePath);
                actorSelection.tell(msg, self());
            } else if (clazz != null) {
                if (allActors.size() > MAX_ACTORS_NUM) {
                    throw new Exception("创建的Actor超过" + MAX_ACTORS_NUM + ",不给创建！");
                }
                String trueKey = UUID.randomUUID().toString();
                ActorRef actor = context().actorOf(Props.create(clazz, objects), trueKey);
                log.info("新创建的{}actor路径为{}", trueKey, actor.path());
                actor.tell(msg, self());
            }
        }

    }

    @Override
    public void preStart() throws Exception {
        if (StringUtils.isEmpty(actorKey)) {
            throw new Exception("没有设置ActorKey,不可以使用");
        } else {
            pathStr = self().path().toString();
            log.info("Actor{}启动{}", actorKey, pathStr);
            allActors.put(actorKey, pathStr);
        }
        super.preStart();
    }

    @Override
    public void postStop() throws Exception {
        log.info("Actor{}停止{}", actorKey, pathStr);
        allActors.remove(actorKey);
        super.postStop();
    }

    @Override
    public void aroundReceive(PartialFunction<Object, BoxedUnit> receive, Object msg) {
        if (msg instanceof AutoTask) {
            MDC.put(TraceConstants.TRACE_ID, ((AutoTask) msg).getMetaData().getFlowId());
        } else if (msg instanceof TransTaskMsg) {
            MDC.put(TraceConstants.TRACE_ID, ((TransTaskMsg) msg).getAutoTask().getMetaData().getFlowId());
        }

        try {
            super.aroundReceive(receive, msg);
        } finally {
            MDC.clear();
        }
    }

}
