package com.cheche365.bc.cache;

import cn.hutool.json.JSONUtil;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.ClueUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.StringRedisTemplate;

import static com.cheche365.bc.constants.Constants.*;

@Slf4j
public class SubscribeListener implements MessageListener {
    private final StringRedisTemplate redisTemplate = RedisCache.getStringRedis();

    /**
     * <h2>消息回调</h2>
     *
     * @param message {@link Message} 消息体 + ChannelName
     * @param pattern 订阅的 pattern, ChannelName 的模式匹配
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());
        String body = new String(message.getBody());
        log.info("接口同步收到消息:{}，body={}，channel={}", message, body, channel);
        switch (channel) {
            case INTERFACE_CACHE_KEY_PREFIX -> AkkaTaskUtil.updateScriptCache(body);
            case EP_POLICY_CLOSE -> AkkaTaskUtil.forceShutDownByAccount(body);
            case KILL_WIN -> AkkaTaskUtil.killWinActors(body);
            case CLUE_PUSH -> {
                AutoTask autoTask = JSONUtil.toBean(body, AutoTask.class);
                if (redisTemplate.opsForSet().remove(CLUE_PUSH_ENQUIRY_ID, autoTask.getTraceKey()) > 0) {
                    ClueUtil.pushData(body);
                }
            }
        }
    }

}
