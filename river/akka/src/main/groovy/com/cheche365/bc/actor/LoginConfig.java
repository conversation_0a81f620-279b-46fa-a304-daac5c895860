package com.cheche365.bc.actor;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.entity.ActionLog;
import com.cheche365.bc.entity.Template;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.refresh.BaseRefreshHandler;
import com.cheche365.bc.service.TemplateService;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.RedisUtil;
import com.cheche365.bc.utils.Util;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.cheche365.bc.constants.Constants.AUTHORIZATION_CACHE_KEY_PREFIX;
import static com.cheche365.bc.constants.Constants.COOKIE_STRING_CACHE_KEY_PREFIX;
import static com.cheche365.bc.utils.AkkaTaskUtil.populateTransTaskErrorMsg;

/**
 * 精灵登陆配置
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/20 11:38.
 */
@Component
@Getter
@Setter
@Slf4j
public class LoginConfig {

    @Autowired
    private TemplateService tempSerice;

    private static TemplateService templateService;

    private String refreshUrl;
    /**
     * 进行刷新动作时的预期成功标志
     */
    private String expectSuccessFlag;
    private String loginTemplate;
    private int refreshMin = 50;
    private RequestConfig requestConfig;
    private int keepSessionStartHour = 7;
    private int keepSessionEndHour = 20;
    private int keepSessionWinNum = 10;
    private Boolean usePoolConnect = Boolean.TRUE;
    private int quoteLimit;
    private int autoinsureLimit;
    private int insurequeryLimit;
    private int approvedqueryLimit;
    private Boolean useNewSSL = Boolean.FALSE;
    private String keepSessionElement;
    private String companyId;
    /**
     * 登录账号
     */
    private String login;
    private String keepSessionPostString;
    /**
     * post,get(默认)
     */
    private String keepSessionMethodType;


    /**
     * 登陆类型：1-httpClient 2-selenium
     */
    private int loginType = 1;

    private List<BaseRefreshHandler> handlers;

    @PostConstruct
    public void init() {
        templateService = tempSerice;
    }

    public boolean login(CloseableHttpClient closeableHttpClient, TransTaskMsg transTaskMsg) {
        try {
            log.warn("任务:{}@{}执行中出现出现未登陆提示,将进行辅助登陆", transTaskMsg.getAutoTask().getTraceKey(), transTaskMsg.getAutoTask().getCompanyId());
            transTaskMsg.getAutoTask().setHttpClient(closeableHttpClient);
            ActionLog actionLog = new ActionLog();
            //获取登录模板
            Template template = templateService.getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, transTaskMsg.getAutoTask().getTaskType() + "-login"));
            if (template == null && !Strings.isNullOrEmpty(this.loginTemplate)) {
                template = templateService.getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, this.loginTemplate));
            }
            if (template == null) {
                log.warn("任务:{}@{}执行辅助登陆过程中,未找到登录模板!", transTaskMsg.getAutoTask().getTraceKey(), transTaskMsg.getAutoTask().getCompanyId());
                populateTransTaskErrorMsg(transTaskMsg, new RuntimeException("执行辅助登陆过程中,未找到登录模板!"));
                return false;
            }
            AkkaTaskUtil.process(transTaskMsg.getItf(), template, transTaskMsg.getAutoTask(), actionLog);
        } catch (Exception e) {
            //在autoTask 中的临时变量进行循环
            final Map<String, Object> tempValues = transTaskMsg.getAutoTask().getTempValues();
            final String loginTimes = (String) tempValues.get("loginTimes");
            if (e instanceof UniqueException && Util.isNotEmpty(loginTimes)) {
                if (null != tempValues.get("loginI")) {
                    transTaskMsg.getAutoTask().getTempValues().put("loginI", (int) tempValues.get("loginI") + 1);
                } else {
                    transTaskMsg.getAutoTask().getTempValues().put("loginI", 1);
                }
                if ((int) tempValues.get("loginI") < Integer.parseInt(loginTimes) - 2) {
                    login(closeableHttpClient, transTaskMsg);
                }
                log.error("任务:{}@{}辅助登录次数：{}依然失败", transTaskMsg.getAutoTask().getTraceKey(), transTaskMsg.getAutoTask().getCompanyId(), tempValues.get("loginI"), e);
            }
            populateTransTaskErrorMsg(transTaskMsg, e);
            log.error("任务:{}@{}辅助登录过程出现异常", transTaskMsg.getAutoTask().getTraceKey(), transTaskMsg.getAutoTask().getCompanyId(), e);
            return false;
        }
        return true;
    }

    public Boolean refresh(CloseableHttpClient closeableHttpClient, boolean showRefreshContent, String taskId) throws Exception {
        if (String.valueOf(InsCompanyEnum.TPIC.getCode()).equals(companyId)) {
            return false;
        }
        if (Strings.isNullOrEmpty(refreshUrl)) {
            return false;
        }

        String cookie = null;
        String authorization = null;
        if (timeAllow()) {
            try {
                String result;
                Optional<BaseRefreshHandler> optional = handlers.stream()
                    .filter(handler -> handler.isSpecial(companyId))
                    .findFirst();
                if (optional.isPresent()) {
                    result = optional.get().refresh(refreshUrl, closeableHttpClient, requestConfig, login);
                } else {
                    Map<String, String> headers = Maps.newHashMap();
                    cookie = RedisUtil.get(COOKIE_STRING_CACHE_KEY_PREFIX + companyId + ":" + login);
                    authorization = RedisUtil.get(AUTHORIZATION_CACHE_KEY_PREFIX + companyId + ":" + login);
                    if (StringUtils.isNotBlank(cookie)) {
                        headers.put("Cookie", cookie);
                    }
                    if (StringUtils.isNotBlank(authorization)) {
                        headers.put("Authorization", authorization);
                    }
                    result = (String) HttpSender.doGet(closeableHttpClient, refreshUrl, headers, null, "UTF-8", requestConfig, false);
                }
                if (StringUtils.isNotBlank(result) && result.contains(expectSuccessFlag)) {
                    return true;
                }
            } catch (Exception e) {
                log.error("任务:{}-刷新会话链接:{}出现异常{}", taskId, refreshUrl, Util.getStackTrace(e));
            }
            removeCookeAndAuth(cookie, authorization);
            return false;
        }
        return false;
    }

    private void removeCookeAndAuth(String cookie, String authorization) {
        if (StringUtils.isNotBlank(cookie)) {
            RedisUtil.delete(COOKIE_STRING_CACHE_KEY_PREFIX + companyId + ":" + login);
        }
        if (StringUtils.isNotBlank(authorization)) {
            RedisUtil.delete(AUTHORIZATION_CACHE_KEY_PREFIX + companyId + ":" + login);
        }
    }

    private boolean timeAllow() {
        int hour = LocalDateTime.now().getHour();
        return hour >= this.keepSessionStartHour && hour <= this.keepSessionEndHour;
    }
}
