package com.cheche365.bc.service;

import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.task.AutoTask;

public interface TaskProcessService extends Comparable<TaskProcessService> {

    /**
     * 执行扩展任务
     * 返回true表示执行成功，当前任务已完成
     *
     * @param autoTask
     * @return
     */
    boolean processTask(Interface itf, AutoTask autoTask) throws Exception;

    /**
     * 判断是否扩展任务
     *
     * @param autoTask
     * @return
     */
    boolean isProcessTask(AutoTask autoTask);

    /**
     * 顺序
     *
     * @return 默认按加载顺序
     */
    default int order() {
        return 0;
    }

    default int compareTo(TaskProcessService o) {
        return Integer.compare(this.order(), o.order());
    }

}
