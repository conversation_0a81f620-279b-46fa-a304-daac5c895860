package com.cheche365.bc.service.impl;

import com.cheche365.bc.entity.ErrorCategory;
import com.cheche365.bc.service.ErrorCategoryService;
import com.cheche365.bc.service.ErrorHandlingService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.utils.ErrorInfoUtil;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 错误处理服务实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ErrorHandingServiceImpl implements ErrorHandlingService {

    private final ErrorCategoryService errorCategoryService;

    @Override
    public void handleTaskError(AutoTask task) {
        boolean hasErrorInfo = task.getErrorInfo() != null && task.getErrorInfo().get(ErrorInfoKeys.ERROR_DESC) != null;
        boolean hasFailureCause = !Strings.isNullOrEmpty(task.getFailureCause());
        if (hasErrorInfo || hasFailureCause) {
            handle(task);
        }
    }

    private void handle(AutoTask task) {
        Map<String, Object> errorInfo = task.getErrorInfo();
        String errordesc = (String) errorInfo.get(ErrorInfoKeys.ERROR_DESC);
        if (Strings.isNullOrEmpty(errordesc)) {
            errordesc = task.getFailureCause();
        } else {
            task.setFailureCause(errordesc);
        }

        if (Strings.isNullOrEmpty(errordesc)) {
            return;
        }

        task.setFailureCause(errordesc);
        ErrorCategory errorCategory = errorCategoryService.errorCategoryMatch(errordesc);
        if (errorCategory != null) {
            updateErrorInfo(errorInfo, errorCategory, errordesc);
            task.setFailureCauseCategory(errorCategory.getName());
        }
    }

    private void updateErrorInfo(Map<String, Object> errorInfo, ErrorCategory category, String description) {
        errorInfo.put("stop", category.getNeedStop());
        errorInfo.put("remark", category.getRemark());
        errorInfo.put("keyword", category.getKeyword());
        errorInfo.putAll(ErrorInfoUtil.build(category.getCode(), String.format("【%s】%s", category.getName(), description)));
        if (!Strings.isNullOrEmpty(category.getErrorTranslation())) {
            errorInfo.put("errorTranslation", category.getErrorTranslation());
        }
    }

}
