package com.cheche365.bc.service;

import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.task.AutoTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务执行服务
 */
@Service
@Slf4j
public class TaskProcessFactory {

    private final List<TaskProcessService> taskProcessServices;

    public TaskProcessFactory(List<TaskProcessService> taskProcessServices) {
        this.taskProcessServices = taskProcessServices.stream().sorted().toList();
    }

    /**
     * @param itf
     * @param autoTask
     */
    public void processTask(Interface itf, AutoTask autoTask) throws Exception {
        for (TaskProcessService service : taskProcessServices) {
            if (service.isProcessTask(autoTask) && service.processTask(itf, autoTask)) {
                break;
            }
        }
    }

}
