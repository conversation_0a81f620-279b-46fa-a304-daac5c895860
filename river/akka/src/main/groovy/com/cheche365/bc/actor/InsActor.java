package com.cheche365.bc.actor;

import akka.actor.ActorSelection;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.actor.msg.StatusMsg;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.AccountUtil;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 保险公司Actor，启动的时候初始化一些常见的，没有的公司自动创建
 * <p>
 * - 管理保险公司级别的任务
 * - 多账号配置管理
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/17 16:32.
 */
@Getter
@Setter
public class InsActor extends BaseActor {

    /**
     * 保险公司key
     */
    private String comId;

    /**
     * 保险公司名称
     */
    private String comName;

    private AtomicInteger acceptCount = new AtomicInteger(0);
    /**
     * ???
     */
    private AtomicInteger transCount = new AtomicInteger(0);
    /**
     * key：主账号
     * value=map:
     * -> key:账号
     * -> value=pair:
     * --> left:次数
     * --> right:配置
     */
    private Map<String, Map> multiAccountMap = Maps.newHashMap();

    public InsActor(String comId, String comName) {
        this.comId = comId;
        this.comName = comName;
        this.actorKey = this.comId;
    }

    protected ConcurrentMap<String, String> caches = new ConcurrentHashMap<>();


    @Override
    public void onReceive(Object msg) throws Exception {
        try {
            if (msg instanceof TransTaskMsg transTaskMsg) {
                int acceptCountTmp = acceptCount.incrementAndGet();
                log.info("公司窗口{}收到任务{},总数{}", this.comId, transTaskMsg.getUniqueKey(), acceptCountTmp);
                if (AccountUtil.multiAccountProcess(transTaskMsg, multiAccountMap)) {
                    String key = this.comId + "-" + transTaskMsg.getAccount();
                    tell(key, transTaskMsg, AccountActor.class, this.comId, transTaskMsg.getAccount(), transTaskMsg.getLoginConfig().getKeepSessionWinNum());
                } else {
                    AutoTask autoTask = transTaskMsg.getAutoTask();
                    autoTask.setEndFlag(true);
                    autoTask.setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                    autoTask.getErrorInfo().put("errordesc", "多帐号配置login不符合要求");
                    autoTask.getErrorInfo().put("errorcode", ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                    ActorSelection taskActor = context().system().actorSelection("akka://sys/user/taskActor");
                    taskActor.tell(autoTask, self());
                }
            } else if (msg instanceof StatusMsg) {
                List<Object> list = new ArrayList<>();
                list.add(multiAccountMap);
                list.add(caches);
                sender().tell(JSON.toJSONString(list), self());
            } else {
                log.info("公司窗口{}（{}）Actor收到未知的消息{}", this.comName, this.comId, msg);
            }
        } catch (Throwable ex) {
            log.error(StrUtil.format("公司窗口{}Actor处理消息出现异常！", self().path().toString()), ex);
        }
    }

    @Override
    public void preStart() throws Exception {
        log.info("公司窗口{}启动", this.comId);
        super.preStart();
    }

    @Override
    public void postStop() throws Exception {
        log.info("公司窗口{}停止", this.comId);
        super.postStop();
    }
}
