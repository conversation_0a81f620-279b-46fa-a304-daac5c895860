package com.cheche365.bc.service.impl.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.TaskProcessService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.utils.ErrorInfoUtil;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskProcessServiceImplByInsureQuery implements TaskProcessService {

    private final InterfaceService interfaceService;

    private final AutoTaskService autoTaskService;

    @Override
    public boolean processTask(Interface itf, AutoTask autoTask) throws Exception {
        //没配置核保回调
        Interface ediInterface = interfaceService.getInterface(autoTask, autoTask.getTaskType() + "_callback");
        if (ediInterface == null) return false;

        //没有核保回调
        List<com.cheche365.bc.entity.AutoTask> callbackTasks = autoTaskService.getTaskByTaskIdComTaskType(autoTask.getTraceKey(), autoTask.getCompanyId(), String.format("%s-%s-insure_callback", ediInterface.getIntType(), ediInterface.getComId()));
        if (CollUtil.isEmpty(callbackTasks)) {
            autoTask.setTaskStatus(TaskStatus.AUTO_INSURE_WAIT_QUERY.getState());
            autoTask.setResultStr("核保查询失败,因为还没收到保险公司回调!");
            autoTask.getErrorInfo().putAll(ErrorInfoUtil.build(6, "还没回调或回调处理失败了！"));
            return false;
        }
        autoTask.setEndFlag(true);
        autoTask.setEndTime(LocalDateTime.now());
        autoTask.setTaskStatus(ediInterface.getDefaultSuccessStatus());

        com.cheche365.bc.entity.AutoTask callbackTask = callbackTasks.get(0);
        if (ObjUtil.isNotEmpty(callbackTask)) {

            //设置回写状态
            autoTask.setTaskStatus(callbackTask.getTaskStatus());

            JSONObject taskErrorInfo = JSONObject.parseObject(callbackTask.getFeedbackJson())
                .getJSONObject("enquiry")
                .getJSONObject("errorInfo");

            Map<String, Object> errorInfo = Maps.newHashMap();
            if (callbackTask.getTaskStatus().equals(TaskStatus.INSURE_FAILED.getState())
                || callbackTask.getTaskStatus().equals(TaskStatus.AUTO_INSURE_FAILED.getState())) { //TODO 核保失败或自核失败

                //错误描述
                String errorDesc = Optional.ofNullable(taskErrorInfo)
                    .map(e -> e.getString(ErrorInfoKeys.ERROR_DESC))
                    .filter(StrUtil::isNotBlank)
                    .orElse("核保失败！");
                errorInfo = ErrorInfoUtil.build(11, errorDesc);
            } else if (!callbackTask.getTaskStatus().equals(TaskStatus.INSURE_SUCCESS.getState())
                || callbackTask.getTaskStatus().equals(TaskStatus.AUTO_INSURE_SUCCESS.getState())) { //TODO 不是核保成功 或 自核成功

                errorInfo = ErrorInfoUtil.build(6, "还未核保通过!");
            }
            autoTask.getErrorInfo().putAll(errorInfo);

            JSONObject jsonObject = JSONObject.parseObject(autoTask.getApplyJson());
            jsonObject.put("taskStatus", callbackTask.getTaskStatus());
            autoTask.setFeedbackJson(jsonObject.toString());

        }

        return true;
    }

    @Override
    public boolean isProcessTask(AutoTask autoTask) {
        String taskType = autoTask.getTaskType();
        return taskType.startsWith("edi") && taskType.contains("-" + TaskType.INSURE_QUERY.code) && MapUtils.getBoolean(autoTask.getConfigs(), "callbackDataBaseQuery", true);
    }

}
