package com.cheche365.bc.mock;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.cheche365.bc.task.AutoTask;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;

/**
 * 请求mock的dto
 */
@Data
@Builder
public class MockRequest {

    private HashMap<String, Object> data;
    private MetaData metaData;

    @Data
    @Builder
    private static class MetaData {
        private String taskType;
        private String insComId;
        private String monitorId;
    }

    public static MockRequest createByAutoTask(AutoTask autoTask) {

        return MockRequest.builder()
                .data(
                        JSONUtil.toBean(
                                autoTask.getApplyJson(),
                                new TypeReference<HashMap<String, Object>>() {
                                },
                                false
                        )
                )
                .metaData(
                        MetaData.builder()
                                .insComId(autoTask.getCompanyId())
                                .taskType(autoTask.getTaskType())
                                .monitorId(autoTask.getTempValues().get("monitorid").toString())
                                .build()
                ).build();
    }
}
