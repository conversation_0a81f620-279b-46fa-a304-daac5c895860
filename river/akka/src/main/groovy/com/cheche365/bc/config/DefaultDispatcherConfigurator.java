package com.cheche365.bc.config;

import akka.dispatch.*;
import com.typesafe.config.Config;
import org.slf4j.MDC;
import scala.concurrent.duration.Duration;
import scala.concurrent.duration.FiniteDuration;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class DefaultDispatcherConfigurator extends MessageDispatcherConfigurator {

    private final Dispatcher instance;

    public DefaultDispatcherConfigurator(Config config, DispatcherPrerequisites prerequisites) {
        super(config, prerequisites);
        this.instance = new MDCPropagatingDispatcherConfigurator(
                this,
                config.getString("id"),
                config.getInt("throughput"),
                new FiniteDuration(config.getDuration("throughput-deadline-time", TimeUnit.NANOSECONDS), TimeUnit.NANOSECONDS),
                configureExecutor(),
                new FiniteDuration(config.getDuration("shutdown-timeout", TimeUnit.MILLISECONDS), TimeUnit.MILLISECONDS));
    }

    @Override
    public MessageDispatcher dispatcher() {
        return instance;
    }

    static class MDCPropagatingDispatcherConfigurator extends Dispatcher {

        public MDCPropagatingDispatcherConfigurator(MessageDispatcherConfigurator _configurator, String id, int throughput, Duration throughputDeadlineTime, ExecutorServiceFactoryProvider executorServiceFactoryProvider, FiniteDuration shutdownTimeout) {
            super(_configurator, id, throughput, throughputDeadlineTime, executorServiceFactoryProvider, shutdownTimeout);
        }


        @Override
        public void execute(Runnable r) {
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
//            RunnableWrapper runnableWrapper = RunnableWrapper.of(r);
            super.execute(() -> {
                if (copyOfContextMap != null) {
                    MDC.setContextMap(copyOfContextMap);
                }
                try {
                    r.run();
                } finally {
                    MDC.clear();
                }
            });
        }
    }

}
