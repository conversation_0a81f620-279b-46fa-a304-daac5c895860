package com.cheche365.bc.mock;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.cheche365.bc.task.AutoTask;

/**
 * 请求mock-server用的httpClient
 */
public class MockClient {

    public static AutoTask call(AutoTask autoTask) {
        MockRequest mockRequest = MockRequest.createByAutoTask(autoTask);
        String responseBody = HttpUtil.post(uri(), JSONUtil.toJsonStr(mockRequest));
        autoTask.setFeedbackJson(responseBody);
        return autoTask;
    }

    public static String uri() {
        return "http://road-paver.huawei-dev2.svc.cluster.local:32272/river/auto/insurance";
    }
}
