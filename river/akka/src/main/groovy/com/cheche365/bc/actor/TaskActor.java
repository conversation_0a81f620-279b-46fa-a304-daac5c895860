package com.cheche365.bc.actor;

import akka.actor.ActorRef;
import akka.dispatch.Futures;
import cn.hutool.core.bean.BeanUtil;
import com.cheche365.bc.actor.callable.CallbackAndTransform;
import com.cheche365.bc.actor.callable.ProcessTask;
import com.cheche365.bc.actor.callable.RequestTaskAndTransform;
import com.cheche365.bc.actor.callable.TaskOnComplete;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.entity.Template;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.exception.TaskException;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.refresh.BaseRefreshHandler;
import com.cheche365.bc.service.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.KeepSessionConfig;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.ErrorInfoUtil;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务转发处理器
 * - 负责任务的分发和执行
 * - 处理任务回调
 * - 错误处理
 * Created by austin on 16/5/19.
 */
@Component("taskActor")
@Lazy
@Slf4j
public class TaskActor extends BaseActor {

    @Autowired
    private InterfaceService interfaceSrv;
    @Autowired
    private AutoTaskService taskService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private PolicySourceService policySourceService;

    @Autowired
    private TaskSyncService taskSyncService;

    @Autowired
    private TransformService transformService;

    @Autowired
    private TaskProcessFactory taskProcessFactory;

    @Autowired
    private ErrorHandlingService errorHandlingService;

    @Autowired
    private CallbackService callbackService;

    @Resource
    private List<BaseRefreshHandler> handlers;

    public TaskActor() {
        this.actorKey = "whole-taskActor";
    }

    @Override
    public void onReceive(Object o) {
        if (o instanceof AutoTask) {
            AutoTask t = (AutoTask) o;
            try {
                //判断任务流程是否执行结束
                if (t.isEndFlag()) {
                    //转换任务执行结果数据并回写业务端
                    writeBusinessData(t);
                } else {
                    //判断taskEntity是否为null，如果不为null代表已经获取了业务数据
                    if (t.getTaskEntity() != null && (t.getTaskEntity() instanceof Map || t.getTaskEntity() instanceof Enquiry)) {
                        //执行任务流程
                        executeTaskProcess(t);
                    } else {
                        //通过请求requestDataUrl获取业务数据，初始化taskEntity
                        getRequestData(t);
                    }
                }
            } catch (Throwable ex) {
                log.error(String.valueOf(ex), "任务:{}执行主流程异常:{}", String.format("%s@%s", t.getTraceKey(), t.getCompanyId()), ex.getMessage(), ex);
                AkkaTaskUtil.disposeFailed(t, ex, self());
            }
        } else {
            log.warn("未知的消息类型,非Task类型,不进行处理:{}", o);
        }

    }

    @Override
    public void preStart() throws Exception {
        log.info("任务Actor启动");
        this.actorKey = "whole-taskActor";
        super.preStart();
    }

    @Override
    public void postStop() throws Exception {
        log.info("任务Actor停止");
        super.postStop();
    }

    /**
     * 转换数据格式并回写给业务端
     */
    private void writeBusinessData(AutoTask autoTask) {
        autoTask.setEndTime(LocalDateTime.now());
        Futures.future(new CallbackAndTransform(autoTask, taskService, errorHandlingService, taskSyncService, transformService, callbackService), context().dispatcher())
            .onComplete(TaskOnComplete.builder()
                .autoTask(autoTask)
                .interfaceService(interfaceSrv)
                .handlerType(TaskOnComplete.HandlerType.CALLBACK)
                .build(), context().dispatcher());
    }

    /**
     * 执行任务
     */
    private void executeTaskProcess(AutoTask t) throws Exception {
        String taskId = t.getTaskId();
        log.info("开始进入任务:{}接口调用流程", taskId);
        //1。加载任务接口，模板，配置信息
        Interface itf = loadTaskData(t);
        if (Objects.isNull(itf)) return;
        //2. 区分精灵EDI，执行任务流程
        if (itf.getKeepSession()) {
            //精灵
            //是否满足保持会话三要素
            if (t.getConfigs().containsKey(KeepSessionConfig.KEEP_SESSION_URL)
                && t.getConfigs().containsKey(KeepSessionConfig.LOGIN)
                && t.getConfigs().containsKey(KeepSessionConfig.EXPECT_SUCCESS_FLAG)) {
                keepSession(t, itf);
            } else {
                final String errorMsg = String.format("任务:%s期望保持会话，但是却不满足保持会话的基本约定(keepSessionUrl、login、expectSuccessFlag)!", t.getTaskId());
                log.info(errorMsg);
                t.setEndFlag(true);
                t.setResultFlag(false);
                t.setTaskStatus(itf.getDefaultFailedStatus());
                t.getErrorInfo().putAll(ErrorInfoUtil.build(ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde(), errorMsg));
                t.setResultStr(errorMsg);
                self().tell(t, ActorRef.noSender());
            }
        } else {
            //EDI
            ediProcess(t, itf);
        }

    }

    /**
     * 执行EDI任务流程
     */
    private void ediProcess(AutoTask t, Interface itf) {
        Futures.future(new ProcessTask(t, itf, taskProcessFactory), context().dispatcher())
            .onComplete(TaskOnComplete.builder()
                    .interfaceService(interfaceSrv)
                    .actorRef(self())
                    .handlerType(TaskOnComplete.HandlerType.PROCESS)
                    .build()
                , context().dispatcher());
    }

    /**
     * 加载任务对应接口，模板，配置信息
     */
    private Interface loadTaskData(AutoTask t) {
        try {
            //装载接口
            Interface itf = interfaceSrv.getInterface(t, t.getTaskType());
            //加载配置
            AkkaTaskUtil.loadConfig(itf, t);
            //加载模板
            templateService.loadTemplate(itf, t);
            return itf;
        } catch (Exception e) {
            String cause = String.format("任务:%s装载流程及配置信息时出现异常:%s", t.getTaskId(), ExceptionUtils.getStackTrace(e));
            log.error(cause);
            AkkaTaskUtil.disposeFailed(t, new TaskException(t, e.getMessage()), self());
            return null;
        }
    }

    /**
     * 获取业务数据并转换数据格式
     */
    private void getRequestData(AutoTask t) {
        Futures.future(new RequestTaskAndTransform(t, transformService, taskSyncService), context().dispatcher())
            .onComplete(TaskOnComplete.builder()
                .policySourceService(policySourceService)
                .actorRef(self())
                .handlerType(TaskOnComplete.HandlerType.REQUEST)
                .build(), context().dispatcher());
    }


    /**
     * 精灵流程
     *
     * @param t
     * @param itf
     * @throws Exception
     */
    private void keepSession(AutoTask t, Interface itf) throws Exception {

        TransTaskMsg transTaskMsg = new TransTaskMsg();
        transTaskMsg.setAutoTask(t);
        transTaskMsg.setItf(itf);
        transTaskMsg.setComId(t.getCompanyId());
        transTaskMsg.setAccount(t.getConfigs().get(KeepSessionConfig.LOGIN).toString().trim());

        LoginConfig loginConfig = new LoginConfig();
        loginConfig.setHandlers(handlers);
        loginConfig.setLogin(transTaskMsg.getAccount());
        //刷新会话的地址
        loginConfig.setRefreshUrl(t.getConfigs().get(KeepSessionConfig.KEEP_SESSION_URL).toString().trim());
        //是否使用连接池
        loginConfig.setUsePoolConnect(Objects.isNull(t.getConfigs().get(KeepSessionConfig.USE_POOL_CONNECT)) || Boolean.parseBoolean(t.getConfigs().get(KeepSessionConfig.USE_POOL_CONNECT).toString()));
        //是否只使用新协议（TLSv1.2
        loginConfig.setUseNewSSL(Objects.nonNull(t.getConfigs().get(KeepSessionConfig.USE_NEW_SSL)) && Boolean.parseBoolean(t.getConfigs().get(KeepSessionConfig.USE_NEW_SSL).toString()));
        //刷新会话的预期成功标志
        loginConfig.setExpectSuccessFlag((String) t.getConfigs().get(KeepSessionConfig.EXPECT_SUCCESS_FLAG));
        //过滤配置项，不合法的有默认值
        Map<String, Object> filterMap = t.getConfigs().entrySet().stream()
            .filter(entry -> KeepSessionConfig.KEEP_SESSION_NUMERIC_CONFIGS.contains(entry.getKey()))
            .filter(entry -> Objects.nonNull(entry.getValue()))
            .filter(entry -> StringUtils.isNumeric(entry.getValue().toString()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        filterMap.forEach((key, value) -> {
            try {
                BeanUtil.setFieldValue(loginConfig, key, Integer.parseInt(value.toString()));
            } catch (Exception e) {
                log.info("保持登录参数：{} 赋值：{} 异常：{}", key, value, ExceptionUtils.getStackTrace(e));
            }
        });
        //登陆模板的名称可在参数配置中指定
        Object loginTemplateName = t.getConfigs().get(KeepSessionConfig.LOGIN_TEMPLATE_NAME);
        if (loginTemplateName != null) {
            loginConfig.setLoginTemplate((String) loginTemplateName);
        } else {
            Optional<Template> templateOptional = itf.getTemplates().stream().filter(template -> template.getName().endsWith("-login")).findFirst();
//            //设置登陆模板缓存key,key约定{taskType}-login,taskType约定{robot/edi}-{comId}-{接口类型}
            loginConfig.setLoginTemplate(templateOptional.isPresent() ? templateOptional.get().getName() : "");
        }
        if (t.getConfigs().containsKey(HttpSender.proxyHost)
            && t.getConfigs().containsKey(HttpSender.proxyPort)
            && t.getConfigs().containsKey(HttpSender.proxyName)
            && t.getConfigs().containsKey(HttpSender.proxyPass)) {
            loginConfig.setRequestConfig(TaskUtil.buildReqConfig(t));
        }
        transTaskMsg.setLoginConfig(loginConfig);
        //通知对应comId的actor
        String comIdTmp = t.getCompanyId();
        if (StringUtils.isNoneEmpty(comIdTmp) && comIdTmp.length() > 4) {
            comIdTmp = comIdTmp.substring(0, 4);
        }
        loginConfig.setCompanyId(comIdTmp);
        if (t.getConfigs().containsKey(KeepSessionConfig.KEEP_SESSION_METHOD_TYPE)) {
            loginConfig.setKeepSessionMethodType((String) t.getConfigs().get(KeepSessionConfig.KEEP_SESSION_METHOD_TYPE));
        }
        if (t.getConfigs().containsKey(KeepSessionConfig.KEEP_SESSION_POST_STRING)) {
            loginConfig.setKeepSessionPostString((String) t.getConfigs().get(KeepSessionConfig.KEEP_SESSION_POST_STRING));
        }
        tell(comIdTmp, transTaskMsg, InsActor.class, comIdTmp, "");
    }

}
