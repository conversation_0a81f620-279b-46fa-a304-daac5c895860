package com.cheche365.bc.actor;

import akka.actor.ActorRef;
import akka.actor.Cancellable;
import akka.actor.PoisonPill;
import akka.actor.Props;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.actor.msg.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.KeepSessionConfig;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import scala.concurrent.duration.Duration;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 某一个保险公司的一个账号的Actor，账户负责调度任务并分配给子Actor
 *
 * - 管理每个保险公司账号
 * - 调度分配任务给WinActor
 * - 维护窗口状态
 * <AUTHOR>
 * @Created by austinChen on 2017/4/17 16:33.
 */
@Scope("prototype")
public class AccountActor extends BaseActor {

    private final static int MAX_WINS = 10;


    private String comId;
    private String accountId;
    private LoginConfig loginConfig;
    private int maxWins = MAX_WINS;

    private AtomicInteger acceptCount = new AtomicInteger(0);
    private AtomicInteger completeCount = new AtomicInteger(0);

    /**
     *
     */
    protected ConcurrentMap<ActorRef, WinStateMsg> caches = new ConcurrentHashMap<>();


    protected List<TransTaskMsg> tasks = new ArrayList<>();
    private Cancellable cancellable = null;


    public AccountActor(String comId, String accountId, int maxWins) {
        this.comId = comId;
        this.accountId = accountId;
        this.actorKey = comId + "-" + accountId;
        this.maxWins = maxWins;
        //1分钟检测一次空闲窗口
        cancellable = context().system().scheduler().schedule(Duration.Zero(),
                Duration.create(2, TimeUnit.MINUTES), self(), FreeWinCheckMsg.getInstance(),
                context().system().dispatcher(), null);
    }

    public AccountActor(String comId, String accountId) {
        this.comId = comId;
        this.accountId = accountId;
        this.actorKey = comId + "-" + accountId;
    }


    private void dispatch(TransTaskMsg transTaskMsg) {
        log.info("账号窗口{}-{}开始尝试调度新任务{}", this.comId, this.accountId, transTaskMsg.getUniqueKey());
        AutoTask autoTask = transTaskMsg.getAutoTask();
        if (autoTask.getConfigs().containsKey(KeepSessionConfig.KEEP_SESSION_WIN_NUM)) {
            this.maxWins = !"".equals(autoTask.getConfigs().get(KeepSessionConfig.KEEP_SESSION_WIN_NUM).toString()) ? Integer.parseInt(autoTask.getConfigs().get(KeepSessionConfig.KEEP_SESSION_WIN_NUM).toString()) : MAX_WINS;
        }
        Boolean assignedFlag = false;
        for (Map.Entry<ActorRef, WinStateMsg> entry : caches.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue().getTaskKey())) {
                this.assignTask(entry.getKey(), transTaskMsg);
                assignedFlag = true;
                break;
            }
        }
        if (!assignedFlag && caches.size() < this.maxWins) {
            int no = caches.size() + 1;
            String winKey = UUID.randomUUID().toString();
            ActorRef actor = context().actorOf(Props.create(WinActor.class, no, loginConfig, this.comId + "-" + this.accountId), winKey);
            caches.put(actor, new WinStateMsg("", 0, 0));
            this.assignTask(actor, transTaskMsg);
        }

    }

    private void dispatch() {
        if (!tasks.isEmpty()) {
            TransTaskMsg transTaskMsg = tasks.get(0);
            dispatch(transTaskMsg);
        } else {
            log.info("{}账号下没有任务，不调度", self().path());
        }
    }

    @Override
    public void onReceive(Object msg) throws Exception {

        try {
            if (msg instanceof TransTaskMsg) {
                TransTaskMsg transTaskMsg = (TransTaskMsg) msg;
                this.loginConfig = transTaskMsg.getLoginConfig();
                if (transTaskMsg.getLoginConfig().getKeepSessionWinNum() > maxWins) {
                    this.maxWins = this.loginConfig.getKeepSessionWinNum();
                }
                int acceptCountTmp = acceptCount.incrementAndGet();
                log.info("账号窗口{}-{}收到任务{},总数{}", this.comId, this.accountId, transTaskMsg.getUniqueKey(), acceptCountTmp);
                tasks.add(transTaskMsg);
                self().tell(BeginDispatchMsg.getInstance(), null);
            } else if (msg instanceof BeginDispatchMsg) {
                log.info("账号窗口{}-{}开始新的任务调度", this.comId, this.accountId);
                this.dispatch();
            } else if (msg instanceof StatusMsg) {
                final HashMap<String, Object> map = new HashMap<>(16);
                final HashMap<String, Object> win = new HashMap<>(16);
                caches.forEach((k, v) -> win.put(k.path().toString(), v));
                map.put("wins", win);
                map.put("tasks", tasks);
                sender().tell(JSON.toJSONString(map), self());
            } else if (msg instanceof WinStateMsg) {
                completeCount.getAndIncrement();
                log.info("账号窗口{}-{}完成任务{},总完成数{}", this.comId, this.accountId, ((WinStateMsg) msg).getTaskKey(), completeCount);
                if (caches.containsKey(sender())) {
                    caches.put(sender(), (WinStateMsg) msg);
                }
                self().tell(BeginDispatchMsg.getInstance(), null);
            } else if ((msg instanceof String) && "exKill".equals(msg.toString())) {
                log.warn("账号窗口{}-{}收到手工kill请求", this.comId, this.accountId);
                self().tell(PoisonPill.getInstance(), ActorRef.noSender());
            } else if (msg instanceof FreeWinCheckMsg) {
                //定时检测 收敛空闲的窗口 减少资源消耗
                this.checkFreeWinAndKill();
            } else if (msg instanceof WinCloseMsg) {
                //children健康程度低于0.2
                final ActorRef children = sender();
                log.warn("账号窗口{}-{}收到业务窗口{}主动关闭消息", this.comId, this.accountId, children.path().name());
                if (caches.containsKey(children)) {
                    WinStateMsg winStateMsg = caches.remove(children);
                    //关闭children
                    log.info("账号窗口{}-{}通知业务窗口:{}关闭", this.comId, this.accountId, winStateMsg.getTaskKey());
                    children.tell(PoisonPill.getInstance(), ActorRef.noSender());
                }
            } else {
                log.info("账号窗口{}-{}收到未知的消息{}", this.comId, this.accountId, msg);
            }
        } catch (Throwable ex) {
            log.error("账号窗口" + this.comId + "-" + this.accountId + "Actor处理消息出现异常！", ex);
        }
    }

    private void checkFreeWinAndKill() {
        final List<ActorRef> freeWins = new ArrayList<>();
        caches.forEach((key, value) -> {
            if (Strings.isNullOrEmpty(value.getTaskKey())) {
                freeWins.add(key);
            }
        });
        //如果2/3的窗口都空闲,保证至少有3个空闲窗口在
        if (freeWins.size() > 3) {
            String name = freeWins.get(0).path().name();
            caches.remove(freeWins.get(0));
            freeWins.get(0).tell(PoisonPill.getInstance(), ActorRef.noSender());
            log.info("账号窗口{}-{}治理机制,回收了空闲窗口:{},剩余窗口数:{}", this.comId, this.accountId, name, caches.size());
        }
    }

    private void assignTask(ActorRef ref, TransTaskMsg taskMsg) {
        log.info("账号窗口{}-{}分配任务{}给{}", this.comId, this.accountId, taskMsg.getUniqueKey(), ref.path());
        WinStateMsg stateMsg = caches.get(ref);
        stateMsg.setTaskKey(taskMsg.getUniqueKey());
        caches.put(ref, stateMsg);
        ref.tell(taskMsg, self());
        tasks.remove(taskMsg);
        log.info("账号窗口{}-{}移走任务完成{}", this.comId, this.accountId, taskMsg.getUniqueKey());
        if (tasks.size() > 0) {
            for (Map.Entry<ActorRef, WinStateMsg> entry : caches.entrySet()) {
                if (StringUtils.isEmpty(entry.getValue().getTaskKey())) {
                    self().tell(BeginDispatchMsg.getInstance(), ActorRef.noSender());
                    break;
                }
            }
        }
    }


    public AccountActor() {
        super();
    }

    @Override
    public void preStart() throws Exception {
        log.info("账号窗口{}-{}启动", this.comId, this.accountId);
        super.preStart();
    }

    @Override
    public void postStop() throws Exception {
        log.info("账号窗口{}-{}停止", this.comId, this.accountId);
        cancellable.cancel();
        super.postStop();
    }
}
