package com.cheche365.bc.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class ClueUtil {

    public static void pushData(String autoTaskJson) {
        AutoTask autoTask = JSONUtil.toBean(autoTaskJson, AutoTask.class);
        String pushData = (String) autoTask.getTempValues().get("pushData");

        if (StringUtils.isNotBlank(pushData)) {
            String url = autoTask.getWriteBackUrl();
            String data = (String) autoTask.getTempValues().get("pushData");
            log.info("线索单号{},推送信息--- url: {}, 报文: {}", autoTask.getTraceKey(), url, data);
            try {
                String response = HttpSender.doPost(url, data);
                log.info("线索单号{}推送结果{}", autoTask.getTraceKey(), response);
            } catch (Exception e) {
                log.error("线索单号{}推送信息失败，{}", autoTask.getTraceKey(), Util.getStackTrace(e));
            }
        } else {
            log.error("线索单号{}推送信息失败，报文或metadata为空, pushData:{}", autoTask.getTraceKey(), pushData);
        }
    }
}
