package com.cheche365.bc.actor.msg;

import lombok.Getter;
import lombok.Setter;

/**
 * 窗口状态消息实体
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/19 11:15.
 */
@Getter
@Setter
public class WinStateMsg {

    private int successTimes;
    private int failTimes;
    private int totalTimes;
    private String taskKey = "";

    public WinStateMsg(String taskKey, int successTimes, int failTimes) {
        this.taskKey = taskKey;
        this.successTimes = successTimes;
        this.failTimes = failTimes;
        this.totalTimes = failTimes + successTimes;
    }

    public WinStateMsg(TransTaskMsg transTaskMsg, int successTimes, int failTimes) {
        this.taskKey = transTaskMsg.getUniqueKey();
        this.successTimes = successTimes;
        this.failTimes = failTimes;
        this.totalTimes = failTimes + successTimes;
    }


}
