package akka;

import akka.actor.ActorRef;
import akka.actor.ActorSystem;
import akka.actor.Props;
import akka.actor.UntypedAbstractActor;
import akka.dispatch.Futures;
import akka.dispatch.OnComplete;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.cheche365.bc.constants.TraceConstants;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import scala.PartialFunction;
import scala.concurrent.Future;
import scala.runtime.BoxedUnit;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
public class ActorTest {
    private static final Logger log = LoggerFactory.getLogger(ActorTest.class);

    private static final String MDC_KEY = TraceConstants.TRACE_ID;

    private ActorSystem actorSystem;


        @Before
    public void before() {
        initActorSystem();
    }

        @Test
    public void test() throws IOException, InterruptedException, ExecutionException {
//        for (int i = 0; i < 1000; i++) {
        String flowId = IdUtil.fastSimpleUUID();
        MDC.put(MDC_KEY, flowId);
        log.warn("=====================================");
        ActorRef actorRef = this.actorSystem.actorOf(TestActor.props(), RandomUtil.randomString(5));
        actorRef.tell(flowId, ActorRef.noSender());
//        }


//        ExecutorService executorService = Executors.newSingleThreadExecutor();
//        java.util.concurrent.Future<String> submit = executorService.submit(new TestCallback(flowId));
//        submit.get();
        Thread.sleep(2000L);
        log.warn("=====================================");
        MDC.clear();
        this.actorSystem.terminate();
    }


    private void initActorSystem() {
        String akkaPath = "akka.conf";
        Config config = ConfigFactory.load(akkaPath);
        log.info("ActorSystem 初始化...");
        log.info("akka.default-dispatcher 初始化参数 parallelism-min:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-min"));
        log.info("akka.default-dispatcher 初始化参数 parallelism-factor:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-factor"));
        log.info("akka.default-dispatcher 初始化参数 parallelism-max:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-max"));
        actorSystem = ActorSystem.create("sys", config);
    }

    static class CallbackTest implements Callable<String> {

        private static final Logger log = LoggerFactory.getLogger(CallbackTest.class);

        private String flowId;

        public CallbackTest(String flowId) {
            this.flowId = flowId;
        }


        @Override
        public String call() throws Exception {
            String flowId = MDC.get(MDC_KEY);
            log.error("CallbackTest:call:【original {},mdc:{}】", this.flowId, flowId);
            return this.flowId;
        }
    }

    static class TestActor extends UntypedAbstractActor {

        @Override
        public void aroundReceive(PartialFunction<Object, BoxedUnit> receive, Object msg) {
            MDC.getCopyOfContextMap().forEach((k, v) -> {
                log.info("k:{},v:{}", k, v);
            });
            MDC.put(TraceConstants.TRACE_ID, msg.toString());

            super.aroundReceive(receive, msg);
            String traceId = MDC.get(TraceConstants.TRACE_ID);
            log.info("TestActor:aroundReceive:【original:{},mdc:{}】", msg, traceId);
            MDC.clear();
        }


        public static Props props() {
            return Props.create(TestActor.class, TestActor::new);
        }

        @Override
        public void onReceive(Object message) throws Throwable, Throwable {
            String flowId = MDC.get(MDC_KEY);
            log.error("TestActor:onReceive:【original:{},mdc:{}】", message, flowId);
            Future<String> future = Futures.future(new CallbackTest(String.valueOf(message)), getContext().dispatcher());
            future.onComplete(new OnComplete<String>() {

                @Override
                public void onComplete(Throwable failure, String value) throws Throwable, Throwable {
                    String flowId = MDC.get(MDC_KEY);
                    log.error("Future:onComplete:【original:{},mdc:{}】", value, flowId);
                    getContext().stop(getSelf());
                }

            }, context().dispatcher());
        }
    }

    static class TestCallback implements Callable<String> {

        private String flowId;

        public TestCallback(String flowId) {
            this.flowId = flowId;
        }

        @Override
        public String call() throws Exception {
            String flowId = MDC.get(MDC_KEY);
            log.info("TestCallback:call:【original:{},mdc:{}】", this.flowId, flowId);
            return this.flowId;
        }
    }

}
