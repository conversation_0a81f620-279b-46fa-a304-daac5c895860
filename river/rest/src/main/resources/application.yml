server:
  port: 8080
  servlet:
    context-path: /
  undertow:
    io-threads: 16
    worker-threads: 256

spring:
  datasource:
    dynamic:
      primary: bcs
      druid:
        initial-size: 2
        max-active: 10
        min-idle: 1
        max-wait: 60000
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 30000
        min-evictable-idle-time-millis: 60000
        validation-query: SELECT 1
        filters: wall,stat,slf4j,config
        init-connection-sqls: set NAMES utf8mb4;
      datasource:
        bcs:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# Actuator
management:
  endpoints:
    web:
      base-path: /app-manage
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  health:
    mongo:
      enabled: false

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  typeEnumsPackage: com.cheche365.bc.**.entity.**.enums
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

web:
  groovy-files: ../river_templates/src/main/groovy

#api-interface
api:
  sign-key: BC_SIGN


clue:
  guoshou:
    aesKey: 1234567812345678
