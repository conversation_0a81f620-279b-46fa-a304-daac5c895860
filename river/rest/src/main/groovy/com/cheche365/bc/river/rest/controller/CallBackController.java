package com.cheche365.bc.river.rest.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.entity.Template;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.river.service.callback.AbstractCallBackHandler;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.TemplateService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.util.ScriptUtil;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.Util;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Created by Zengweixiong on 2015/11/27.
 */
@RestController
@Slf4j
@Lazy
public class CallBackController {

    @Resource
    private AutoTaskService taskService;
    @Resource
    private TemplateService templateService;
    @Resource
    private InterfaceService interfaceService;
    @Resource
    private HttpServletRequest request;
    @Resource
    private List<AbstractCallBackHandler> handlers;

    /**
     * 根据保险公司以及保单号查询保单详情
     */
    @ResponseBody
    @PostMapping(value = "/api/callback/{comId}/{taskType}")
    public Object insCallbackApi(@RequestBody String policyInfo, @PathVariable String comId, @PathVariable String taskType) throws Exception {
        return execute(taskType, comId, policyInfo);
    }

    /*保险公司网销回调EDI接口*/
    @ResponseBody
    @RequestMapping(value = "callback/ns/{comId}/{taskType}", method = RequestMethod.POST, consumes = "application/xml;charset=UTF-8", produces = "application/xml;charset=UTF-8")
    public Object insNsCallBack4Xml(@PathVariable String taskType, @PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        log.info("收到{}公司{}类型数据回调, 回调内容为:{}", comId, taskType, callbackContent);
        return execute(taskType, comId, callbackContent);
    }


    /*保险公司回调EDI接口*/
    @ResponseBody
    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.POST, consumes = "application/x-www-form-urlencoded")
    public Object insCompanyCallBack4Form(@PathVariable String taskType, @PathVariable String comId, @RequestBody String params) throws Exception {

        return execute(taskType, comId, URLDecoder.decode(params, StandardCharsets.UTF_8));
    }

    /*保险公司回调EDI接口*/
    @ResponseBody
    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.POST, consumes = "text/xml;charset=UTF-8", produces = "text/xml;charset=UTF-8")
    public Object insCompanyCallBack4Xml(@PathVariable String taskType, @PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        return execute(taskType, comId, callbackContent);
    }

    /**
     * ep定时任务抓单
     */
    @ResponseBody
    @RequestMapping(value = "ep/callback/{comId}/{taskType}", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    public Object epCallBack(@PathVariable String taskType, @PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        return execute(taskType, comId, callbackContent);
    }

    /*保险公司回调EDI接口*/
    @ResponseBody
    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    public Object insCompanyCallBack4Json(@PathVariable String taskType, @PathVariable String comId, @RequestBody Map callbackContent) throws Exception {
        return JSON.parseObject((String) execute(taskType, comId, JSONObject.toJSONString(callbackContent)));
    }

    /*保险公司回调EDI接口 2027*/
    @ResponseBody
    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.POST, consumes = "application/json;charset=GBK", produces = "application/json;charset=GBK")
    public Object insCompanyCallBackOthers_GBK(HttpServletResponse response, @PathVariable String taskType, @PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        if (comId.equals("2027")) {
            callbackContent = callbackContent + "," + request.getHeader("GW_CH_SIGN");
        }
        Object object = execute(taskType, comId, callbackContent);
        if (comId.equals("2027")) {
            JSONObject jsonObject = JSONObject.parseObject((String) object);
            response.addHeader("GW_CH_SIGN", jsonObject.getString("GW_CH_SIGN"));
            return jsonObject.getString("CONTENT");
        }
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        return object;
    }

    /*保险公司回调EDI接口*/
    @ResponseBody
    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.POST)
    public Object insCompanyCallBackOthers(@PathVariable String taskType, @PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        return execute(taskType, comId, callbackContent);
    }

    @RequestMapping(value = "callback/{comId}/{taskType}", method = RequestMethod.GET)
    @ResponseBody
    public Object insCompanyCallBack4Get(@PathVariable String taskType, @PathVariable String comId, @RequestParam Map requestParam) throws Exception {
        return execute(taskType, comId, JSONObject.toJSONString(requestParam));
    }

    public Object execute(String taskType, String comId, String callbackBody) throws Exception {
        if (StringUtils.isBlank(callbackBody)) {
            return "";
        }
        log.info("收到{}公司{}类型数据回调,回调内容为:{}", comId, taskType, callbackBody);
        //初始化任务对象
        AutoTask callbackTask = initCallbackTask(callbackBody, comId, taskType);

        //初始化 engine 的 binding。
        Map<String, Object> dataSource = initDataSource(callbackTask);

        //获取模板配置信息
        Template template = templateService.getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, callbackTask.getTaskType()), false);
        if (Objects.isNull(template)) throw new InsReturnException("不支持此流程");

        //获取接口配置信息
        Interface ediInterface = interfaceService.getInterface(callbackTask, callbackTask.getTaskType());
        if (Objects.isNull(ediInterface)) throw new Exception("未设置回调接口，无法获取模板路径");

        //获取回调报文处理方法
        Optional<AbstractCallBackHandler> optional = handlers.stream()
            .filter(handler -> handler.needHandle(comId))
            .findFirst();

        try {

            AkkaTaskUtil.loadConfig(ediInterface, callbackTask);
            dataSource.put("config", callbackTask.getConfigs());

            Map<String, Object> param = Maps.newHashMap();
            param.put("callbackBody", callbackBody);
            param.put("dataSource", dataSource);

            //解密
            if (optional.isPresent()) {
                callbackBody = optional.get().handleCallBackBody(param);
            }

            if ("2021".equals(comId) && TaskType.POLICY_APPROVED.code.equals(taskType)) {
                callbackTask.setApplyJson("数据过长，日志不展示数据信息");
            } else {
                callbackTask.setApplyJson(callbackBody);
            }

            dataSource.put("root", DataUtil.parse(callbackBody));
            dataSource.put("callbackBody", callbackBody);

            // datasource包含
            // 1.root 回调报文反序列化后的对象
            // 2.callbackBody 回调原始字符串报文
            // 3.config 配置信息
            // 4.autoTask 任务对象
            // 5.comId 公司id
            // 6.enquiry 空Map
            ScriptUtil.engine(template.getName() + "_rep", ediInterface, dataSource);
            //ep项目保司推单不涉及
            if (TaskType.INSURE.code.equals(taskType) || TaskType.APPROVED.code.equals(taskType)) {
                setData(callbackTask, dataSource);
            }
        } catch (Exception e) {
            if (Objects.isNull(dataSource.get("root"))) {
                dataSource.put("root", callbackTask.getApplyJson());
            }
            callbackTask.setResultStr(String.format("业务处理失败：%s,", e.getMessage()));
            log.error("保司{}，{}回调执行报文解析模板异常：{}", comId, taskType, ExceptionUtils.getStackTrace(e));
        }
        try {
            String responseContent = ScriptUtil.engine(template.getName(), ediInterface, dataSource);
            log.info("返回给 {}公司 {}类型 {}任务号数据回调,回写内容为:{}", comId, taskType, callbackTask.getTraceKey(), responseContent);
            callbackTask.setResultStr(String.format("响应保司报文：\n%s", responseContent));
            callbackTask.setEndTime(LocalDateTime.now());
            callbackTask.setEndFlag(true);
            if (callbackTask.getTaskType().contains(TaskType.POLICY_APPROVED.code)) {
                callbackTask.setResultStr(Strings.nullToEmpty(callbackTask.getResultStr()) + callbackTask.getBizProposeNo());
            }

            taskService.save(callbackTask);

            Map<String, Object> param = Maps.newHashMap();
            param.put("responseContent", responseContent);
            param.put("dataSource", dataSource);

            if (optional.isPresent()) {
                responseContent = optional.get().handleResponseBody(param);
            }
            return responseContent;
        } catch (Exception e) {
            log.error("返回报文渲染失败,{}", ExceptionUtils.getStackTrace(e));
            return ScriptUtil.engine(template.getName(), ediInterface, dataSource);
        }
    }

    private AutoTask initCallbackTask(String callbackBody, String comId, String taskType) {
        AutoTask callbackTask = new AutoTask();
        callbackTask.setAutoTraceId(UUID.randomUUID().toString());
        callbackTask.setStartTime(LocalDateTime.now());
        callbackTask.setCompanyId(comId);
        callbackTask.setApplyJson(callbackBody);
        callbackTask.setTaskStatus(TaskStatus.getSuccessState(taskType));
        callbackTask.setResultStr("收到保险公司" + taskType + "状态回调");

        String callbackTaskType;
        if (callbackBody.contains("policyapprovedFlag") && TaskType.POLICY_APPROVED.code.equals(taskType)) {
            //互联互通
            callbackTaskType = "edi-1111-policyapproved_callback";
        } else {
            callbackTaskType = String.format("edi-%s-%s_callback", comId, taskType);
        }
        callbackTask.setTaskType(callbackTaskType);

        return callbackTask;
    }

    private Map<String, Object> initDataSource(AutoTask callbackTask) throws Exception {
        Map<String, Object> dataSource = Maps.newHashMap();
        dataSource.put("enquiry", Maps.newHashMap());
        dataSource.put("comId", callbackTask.getCompanyId());
        dataSource.put("autoTask", callbackTask);
        return dataSource;
    }

    private boolean strIsJson(String str) {
        if (StringUtils.isBlank(str)) return false;
        try {
            JSONObject.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void setData(AutoTask callbackTask, Map<String, Object> dataSource) throws Exception {
        //回写信息
        String feedbackBody = JSONObject.toJSONString(DataUtil.parseOutEdi(JSON.toJSONString(dataSource)));
        callbackTask.setFeedbackJson(feedbackBody);
        int noType = 1;

        String proposeNum = (String) DataUtil.get("enquiry.SQ.bizProposeNum", dataSource);
        callbackTask.setBizProposeNo(proposeNum);
        if (Util.isEmpty(proposeNum)) {
            proposeNum = (String) DataUtil.get("enquiry.SQ.efcProposeNum", dataSource);
            noType = 2;
            callbackTask.setEfcProposeNo(proposeNum);
        }
        if (Util.isEmpty(proposeNum)) {
            proposeNum = (String) DataUtil.get("enquiry.SQ.orderNo", dataSource);
            noType = 3;
        }
        if (Util.isNotEmpty(proposeNum)) {
            //先查看是否已经回调过
            com.cheche365.bc.entity.AutoTask task = taskService.getTaskByTaskTypeProposeNumOrOrderNo(callbackTask.getTaskType(), proposeNum, noType);
            if (Objects.isNull(task)) {
                task = taskService.getTaskByTaskTypeProposeNumOrOrderNo(null, proposeNum, noType);
            }
            if (Objects.isNull(task)) {
                log.warn("所回调的投保单号(或订单号):{},在edi系统无法找到对应的任务Id或当前状态为不可回调状态", proposeNum);
                throw new InsReturnException("所回调的投保单号(或订单号):" + proposeNum + ",在edi系统无法找到对应的任务Id或当前状态为不可回调状态");
            }

            if (strIsJson(task.getApplyJson())) {
                JSONObject enquiry = JSON.parseObject(task.getApplyJson());
                if (DataUtil.containsKey(enquiry, "configInfo.configMap")) {
                    //TODO config 被覆盖了
                    dataSource.put("config", DataUtil.get("configInfo.configMap", enquiry));
                }
            }

            callbackTask.setCompanyId(task.getCompanyId());
            callbackTask.setPlateNo(task.getPlateNo());
            callbackTask.setTraceKey(task.getTraceKey());
            if (StringUtils.isNotBlank(task.getBizProposeNo())) {
                callbackTask.setBizProposeNo(task.getBizProposeNo());
            }
            if (StringUtils.isNotBlank(task.getEfcProposeNo())) {
                callbackTask.setEfcProposeNo(task.getEfcProposeNo());
            }
            //承保任务保存回调的保单号
            if (callbackTask.getTaskType().contains(TaskType.APPROVED.code)) {
                String bizPolicyCode = (String) DataUtil.getValue("enquiry.SQ.bizPolicyCode", dataSource);
                String efcPolicyCode = (String) DataUtil.getValue("enquiry.SQ.efcPolicyCode", dataSource);
                if (StringUtils.isNotBlank(bizPolicyCode)) callbackTask.setBizPolicyNo(bizPolicyCode);
                if (StringUtils.isNotBlank(efcPolicyCode)) callbackTask.setEfcPolicyNo(efcPolicyCode);
            }
            String taskStatus = (String) DataUtil.get("enquiry.taskStatus", dataSource);
            if (StringUtils.isNotBlank(taskStatus)) {
                callbackTask.setTaskStatus(taskStatus);
            }
            Map errorInfo = (Map) DataUtil.get("enquiry.errorInfo", dataSource);
            if (Objects.nonNull(errorInfo)) callbackTask.getErrorInfo().put("errordesc", errorInfo.get("errordesc"));
            //添加 磐石请求的 任务号/单方ID
            dataSource.put("traceKey", task.getTraceKey());
        } else {
            throw new Exception("找不到投保单号");
        }
    }

}
