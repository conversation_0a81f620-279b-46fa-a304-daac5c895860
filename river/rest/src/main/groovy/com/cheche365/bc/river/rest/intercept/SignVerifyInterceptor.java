package com.cheche365.bc.river.rest.intercept;

import com.cheche365.cheche.signature.APISignature;
import com.cheche365.cheche.signature.Parameters;
import com.cheche365.cheche.signature.Secrets;
import com.cheche365.cheche.signature.SignatureException;
import com.cheche365.cheche.signature.api.ServletPreSignRequest;
import com.cheche365.cheche.signature.spi.PreSignRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Objects;

/**
 * <AUTHOR> shanxf
 * @desc
 * @Date 2021/5/7 15:33
 */
@Slf4j
@Component
public class SignVerifyInterceptor implements HandlerInterceptor {

    @Value("${secret.key}")
    private String secretKey;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        PreSignRequest signRequest = new ServletPreSignRequest(request);
        Parameters parameters = new Parameters().readRequest(signRequest);

        if (Objects.isNull(parameters) || StringUtils.isBlank(parameters.getAppId())) {
            throw new SignatureException("签名header不存在");
        }
        Secrets secrets = new Secrets().appSecret(secretKey);
        if (APISignature.verify(signRequest, parameters, secrets)) {
            return true;
        } else {
            throw new SignatureException("签名校验失败");
        }
    }
}
