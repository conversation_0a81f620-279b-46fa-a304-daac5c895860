package com.cheche365.bc.river.rest.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.entity.AutoRequestMetaData;
import com.cheche365.bc.entity.Template;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.handler.BaseClueHandler;
import com.cheche365.bc.handler.clue.ClueCallbackDTO;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.repo.AutoRequestMetaDataRepo;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.TemplateService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.MetaData;
import com.cheche365.bc.util.ScriptUtil;
import com.cheche365.bc.utils.DataUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

import static com.cheche365.bc.constants.Constants.CLUE_PUSH;
import static com.cheche365.bc.constants.Constants.CLUE_PUSH_ENQUIRY_ID;
import static com.cheche365.bc.message.TaskType.CLUE;

@RestController
@Slf4j
@AllArgsConstructor
public class ClueCallBackController {

    private final TemplateService templateService;
    private final AutoTaskService taskService;
    private final StringRedisTemplate stringRedisTemplate;
    private final AutoRequestMetaDataRepo autoRequestMetaDataRepo;
    private final List<BaseClueHandler> baseClueHandlerList;

    @ResponseBody
    @RequestMapping(value = "/callback/clue/{comId}", method = RequestMethod.POST)
    public Object clueCallback(@PathVariable String comId, @RequestBody String callbackContent) throws Exception {
        log.info("收到{}公司线索数据回调,回调内容为:{}", comId, callbackContent);
        Assert.notBlank(callbackContent, "报文解析失败请检查");
        return clueExecute(comId, callbackContent);
    }

    public Object clueExecute(String comId, String callbackBody) throws Exception {

        ClueCallbackDTO callbackDTO = decryptCallbackData(comId, callbackBody);

        AutoRequestMetaData autoRequestMetaData = autoRequestMetaDataRepo.findFirstByEnquiryIdOrderByCreateDateDesc(callbackDTO.getEnquiryId());
        Assert.notNull(autoRequestMetaData, "回调处理失败，请检查数据");

        // 初始化 autoTask
        AutoTask autoTask = initClueCallbackTask(comId, callbackDTO.getResponse(), autoRequestMetaData, callbackDTO.getEnquiryId());
        // 初始化 engin 的 binding
        Map<String, Object> clueDataSource = initClueDataSource(autoTask, callbackDTO.getResponse(), autoRequestMetaData);

        // 判断是否配置了线索回调模板
        String templateKey = getTemplateKey(comId);
        String templatePath = getTemplatePath(templateKey, comId);
        Template template = templateService.getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, templateKey));
        Assert.notNull(template, "回调处理失败，未配置线索模板。");

        try {
            // 走线索回调模板得到给保司的响应 & 以及在模板里处理保司回调的数据
            String responseContent = ScriptUtil.engineWithTemplateName(templatePath + ".groovy", clueDataSource);
            log.info("任务号{}返回给{}公司线索报文: {}", autoTask.getTraceKey(), comId, responseContent);
            autoTask.setResultStr(String.format("\n响应保司报文：\n%s", responseContent));
            autoTask.setEndTime(LocalDateTime.now());
            taskService.save(autoTask);

            stringRedisTemplate.opsForSet().add(CLUE_PUSH_ENQUIRY_ID, autoTask.getTraceKey());
            stringRedisTemplate.convertAndSend(CLUE_PUSH, JSONUtil.toJsonStr(autoTask));
            // 返回给保司响应
            return responseContent;
        } catch (Exception e) {
            log.error("保司{}线索回调执行报文解析模板异常：{}", comId, ExceptionUtils.getStackTrace(e));
            return "回调解析异常";
        }
    }

    private ClueCallbackDTO decryptCallbackData(String comId, String callbackBody) throws Exception {
        // 回调使用 handler 解密
        BaseClueHandler handler = baseClueHandlerList
            .stream()
            .filter(baseClueHandler -> baseClueHandler.companyId().equals(comId))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("该保司无线索能力"));

        return handler.decrypt(callbackBody);
    }

    private AutoTask initClueCallbackTask(String comId, String callbackBody, AutoRequestMetaData metadata, String enquiryId) {
        AutoTask clueAutoTask = new AutoTask();
        clueAutoTask.setMetaData(MetaData.buildByMDC());
        clueAutoTask.setAutoTraceId(UUID.randomUUID().toString());
        clueAutoTask.setStartTime(LocalDateTime.now());
        clueAutoTask.setCompanyId(comId);
        clueAutoTask.setTaskType(String.format("edi-%s-clueCallback", comId));
        clueAutoTask.setApplyJson(callbackBody);
        clueAutoTask.setTaskStatus(TaskStatus.getSuccessState(CLUE.code));
        clueAutoTask.setTraceKey(enquiryId.split("@")[0]);
        clueAutoTask.setWriteBackUrl(metadata.getWriteBackUrl());
        clueAutoTask.setTempValues(new HashMap<>() {
            {
                put("clueFlag", true);
            }
        });
        if (metadata.getConfig() instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> config = (Map<String, Object>) metadata.getConfig();
            clueAutoTask.setConfigs(config);
        }

        return clueAutoTask;
    }

    private Map<String, Object> initClueDataSource(AutoTask callbackTask, String callbackBody, AutoRequestMetaData metaData) throws Exception {
        Map<String, Object> dataSource = Maps.newHashMap();
        dataSource.put("autoTask", callbackTask);
        dataSource.put("root", DataUtil.parse(callbackBody));
        dataSource.put("autoRequestMetaData", BeanUtil.beanToMap(metaData));
        return dataSource;
    }

    private String getTemplateKey(String comId) {
        return String.format("edi-%s-clue_callback", comId);
    }

    private String getTemplatePath(String templateKey, String comId) {
        return String.format(InsCompanyEnum.get(Integer.parseInt(comId)).getText() + "/edi/" + templateKey, comId);
    }
}
