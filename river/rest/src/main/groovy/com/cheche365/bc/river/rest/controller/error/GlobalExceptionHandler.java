package com.cheche365.bc.river.rest.controller.error;

import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.cheche.signature.SignatureException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Component;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.stream.Collectors;

import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("REST API exception handler got a message not readable exception message:{}", getRequestBody(request));
        return new ResponseEntity<>(new RestResponse<>(HttpStatus.UNPROCESSABLE_ENTITY.value(), ExceptionCde.DATA_DEFECT.getName()), OK);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String errorMessage = ex.getBindingResult().getFieldErrors()
                .stream().map(FieldError::getDefaultMessage).collect(Collectors.joining(";"));
        return new ResponseEntity<>(new RestResponse<>(ExceptionCde.PARAM_ERROR.getCde(), errorMessage), OK);
    }

    @ExceptionHandler({IllegalArgumentException.class,SignatureException.class})
    protected ResponseEntity<Object> handlerArgumentException(Exception ex, WebRequest request) {
        log.error("参数异常：{}\nat：{}", ex.getMessage(), ExceptionUtils.getStackTrace(ex).replaceAll(",", "\n   "));
        return new ResponseEntity<>(new RestResponse<>(ExceptionCde.PARAM_ERROR.getCde(), ex.getMessage()), OK);
    }

    @ExceptionHandler(Throwable.class)
    protected ResponseEntity<Object> handleGeneralException(Throwable ex, WebRequest request) {
        log.error("未知异常：{}\nat：{}", ex.getMessage(), ExceptionUtils.getStackTrace(ex).replaceAll(",", "\n   "));
        return new ResponseEntity<>(new RestResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()), OK);
    }

    private static String getRequestBody(WebRequest request) {
        HttpServletRequest httpRequest = ((ServletRequestAttributes) request).getRequest();
        StringBuilder stringBuilder = new StringBuilder(httpRequest.getRequestURI());
        try (BufferedReader reader = httpRequest.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            log.error("获取REQUEST-BODY失败：{}", ExceptionUtils.getStackTrace(e));
        }
        return stringBuilder.toString();
    }
}
