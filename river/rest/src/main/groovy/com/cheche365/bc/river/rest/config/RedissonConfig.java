package com.cheche365.bc.river.rest.config;

import org.apache.logging.log4j.util.Strings;
import com.cheche365.redisson.holder.RedissonClientHolder;
import org.apache.logging.log4j.util.Strings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host}")
    private String address;

    @Value("${spring.data.redis.port}")
    private String port;

    @Value("${spring.data.redis.password:#{null}}")
    private String password;

    @Value("${spring.data.redis.database}")
    private int database;

    @Bean
    RedissonClient redisson() {
        String redisAddress = "redis://" + address + ":" + port;
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer()
            .setAddress(redisAddress)
            .setDatabase(database);
        if (Strings.isNotBlank(password)) {
            singleServerConfig.setPassword(password);
        }
        return Redisson.create(config);
    }

    @Bean(initMethod = "init", destroyMethod = "destroy")
    public RedissonClientHolder redissonClientHolder(RedissonClient client) {
        return new RedissonClientHolder(client);
    }

}
