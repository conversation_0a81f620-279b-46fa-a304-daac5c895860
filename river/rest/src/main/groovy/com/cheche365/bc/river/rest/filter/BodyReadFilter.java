package com.cheche365.bc.river.rest.filter;

import com.cheche365.cheche.signature.BodyReadRequestWrapper;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@WebFilter(filterName = "BodyReadFilter", urlPatterns = "/api/account/queryPolicy")
@Order(Ordered.HIGHEST_PRECEDENCE)
public class BodyReadFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        ServletRequest wrapper = new BodyReadRequestWrapper(req);
        chain.doFilter(wrapper, response);
    }
}
