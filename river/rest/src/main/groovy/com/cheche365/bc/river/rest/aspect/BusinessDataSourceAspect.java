package com.cheche365.bc.river.rest.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.BusinessDataSource;
import com.cheche365.bc.river.rest.dto.request.AutoRequest;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import com.cheche365.bc.service.BusinessDataSourceService;
import com.cheche365.bc.utils.Util;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.cheche365.bc.constants.Constants.REDIS_LOCK_KEY_PREFIX;

/**
 * <AUTHOR>
 */

@Aspect
@Component
public class BusinessDataSourceAspect {

    private static final Logger logger = LoggerFactory.getLogger("businessDataSourceAspect");

    BusinessDataSourceService businessDataSourceService;

    @Autowired
    public void setBusinessDataSourceService(BusinessDataSourceService businessDataSourceService) {
        this.businessDataSourceService = businessDataSourceService;
    }

    BusinessDataSourceAutoTaskLogService businessDataSourceAutoTaskLogService;

    @Autowired
    public void setBusinessDataSourceAutoTaskLogService(BusinessDataSourceAutoTaskLogService businessDataSourceAutoTaskLogService) {
        this.businessDataSourceAutoTaskLogService = businessDataSourceAutoTaskLogService;
    }

    AutoTaskService autoTaskService;

    @Autowired
    public void setAutoTaskService(AutoTaskService autoTaskService) {
        this.autoTaskService = autoTaskService;
    }

    RedissonClient redissonClient;

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @AfterReturning(value = "execution(* com.cheche365.bc.river.rest.controller.AutoController.auto(..))", returning = "result")
    public void afterReturning(JoinPoint point, Object result) {
        Map autoMap = (Map) result;
        boolean accept = (boolean) autoMap.get("accept");
        if (!accept) {
            return;
        }

        RLock lock = null;
        try {
            Object[] args = point.getArgs();
            AutoRequest autoRequest = (AutoRequest) args[0];
            JSONObject taskApply = JSONObject.parseObject(JSON.toJSONString(autoRequest));
            String uuid = autoRequest.getAutoTraceId();
            BusinessDataSource dataSourceResult;
            int dataSourceLogId;
            if (Objects.nonNull(taskApply.getJSONObject("requestSource"))) {
                String lockName = REDIS_LOCK_KEY_PREFIX + taskApply.getString("requestSource");
                lock = redissonClient.getLock(lockName);
                if (!lock.tryLock(0, 300, TimeUnit.MILLISECONDS)) {
                    logger.info("lockName 重复，已由其他线程加锁写入数据，enquiryId:{}", taskApply.get("enquiryId"));
                    return;
                }

                // 若请求包含 requestSource，则拿取 requestSource 的字段，通过这三个字段获取此条配置
                JSONObject requestSource = taskApply.getJSONObject("requestSource");
                String product = getValueByKey(requestSource, "sourceProduct");
                String scenario = getValueByKey(requestSource, "sourceScenario");
                String channel = getValueByKey(requestSource, "sourceChannel");
                String desc = getValueByKey(requestSource, "desc");
                if ("che_eco".equals(product) && StringUtils.isBlank(channel)) {
                    logger.warn("车生态sourceChannel字段为空，任务号：{}", taskApply.get("enquiryId"));
                    return;
                }

                // 通过 sourceProduct, sourceScenario, sourceChannel 获取配置
                dataSourceResult = businessDataSourceService.getDataSource(product, scenario, channel);

                // 若此配置为空，则新增一条此配置
                if (Objects.isNull(dataSourceResult)) {
                    businessDataSourceService.insertDataSource(product, scenario, channel, 1, desc);
                }

                // 设置日志界面数据来源下拉框 id
                dataSourceLogId = businessDataSourceAutoTaskLogService.getDataSourceLogId(product, scenario, channel);

            } else {
                // 若请求不包含 requestSource，则给予默认 “ps” 标识
                dataSourceResult = businessDataSourceService.getDataSource("ps", "ps", null);
                dataSourceLogId = businessDataSourceAutoTaskLogService.getDataSourceLogId("ps", "ps", null);
            }

            // 判断请求是否来源于内网，若来源于内网，则设置来源归类为6，若不来源于内网，则设置来源归类为 dataSourceResult 此条配置的 sourceKind 字段
            String isSourceCheche = taskApply.containsKey("isSourceCheche") ? taskApply.getString("isSourceCheche") : null;

            int sourceKind;
            if ("0".equals(isSourceCheche)) {
                sourceKind = Objects.isNull(dataSourceResult) ? 1 : dataSourceResult.getSourceKind();
            } else {
                sourceKind = 6;
            }

            if (-1 != dataSourceLogId) {
                LambdaUpdateWrapper<AutoTask> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(AutoTask::getAutoTraceId, uuid)
                        .set(AutoTask::getSourceKind, sourceKind)
                        .set(AutoTask::getDataSourceLogId, dataSourceLogId);

                autoTaskService.update(wrapper);
            }

        } catch (Exception e) {
            logger.error("任务单号:{} 切面数据新增失败：{}", autoMap.get("taskId"), Util.getStackTrace(e));
        } finally {
            try {
                if (null != lock && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }

            } catch (Exception e) {
                logger.error("任务单号:{} 释放锁失败：{}", autoMap.get("taskId"), Util.getStackTrace(e));
            }

        }
    }

    public String getValueByKey(JSONObject requestSource, String key) {
        if (requestSource.containsKey(key)) {
            return requestSource.getString(key);
        }

        return null;
    }


}
