package com.cheche365.bc.river.rest.controller;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api")
@Slf4j
@AllArgsConstructor
public class ApiController {

    private final StringRedisTemplate redisTemplate;

    /**
     * @param body:目前只包含两个参数信息companyId(保司编号), account(账号)目前暂不考虑是否增加地区判断
     * @return
     */
    @RequestMapping(value = "/killAcount", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    public RestResponse killAcount(@RequestBody String body) {
        log.info("关闭账号：{}", body);
        if (StringUtils.isNotBlank(body)) {
            JSONObject json = JSONObject.parseObject(body);
            String companyId = json.getString("companyId");
            String account = json.getString("account");
            if (!StringUtils.isAnyBlank(companyId, account)) {
                redisTemplate.convertAndSend("killWin", companyId + "-" + account);
                return new RestResponse(1, "成功");
            }
        }
        return new RestResponse(0, "失败");
    }

    /**
     * 同killAcount
     *
     * @param body
     * @return
     */
    @RequestMapping(value = "/resetAcount", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8", produces = "application/json;charset=UTF-8")
    public RestResponse resetAcount(@RequestBody String body) {
        log.info("重置账号登陆次数：{}", body);
        if (StringUtils.isNotBlank(body)) {
            JSONObject json = JSONObject.parseObject(body);
            String companyId = json.getString("companyId");
            String account = json.getString("account");
            if (!StringUtils.isAnyBlank(companyId, account)) {
                redisTemplate.opsForValue().set("auto:account:illegal:" + companyId + "-" + account, "0");
                return new RestResponse(1, "成功");
            }
        }
        return new RestResponse(0, "失败");
    }
}
