package com.cheche365.bc.river.rest.controller;

import com.cheche365.bc.annotation.RateLimit;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.river.service.fetchPolicy.PolicyExtractService;
import com.cheche365.bc.util.ExtractPolicyUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开放API controller
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
@AllArgsConstructor
public class PolicyController {

    private final PolicyExtractService policyExtractService;

    /**
     * 澎湃保根据账号信息以及时间区间抓取账号下的保单信息
     *
     * @param request        拦截器用
     * @param companyInfoStr 账号信息的 JSONString
     * @return 状态信息
     */
    @PostMapping(value = "/account/queryPolicy")
    @RateLimit(seconds = 60 * 60, maxCount = 10, key = "queryPolicyByAccount")
    public RestResponse queryPolicyByAccount(HttpServletRequest request, @RequestBody String companyInfoStr) {
        try {
            policyExtractService.extractPolicy(ExtractPolicyUtils.validateAndGetAccountParams(companyInfoStr));
        } catch (Exception e) {
            return RestResponse.failedMessage(e.getMessage());
        }
        return RestResponse.success();
    }
}
