server:
  port: ${profiles_port}
  undertow:
    io-threads: ${undertow_io_threads}
    worker-threads: ${undertow_worker_threads}

spring:
  datasource:
    dynamic:
      datasource:
        bcs:
          username: ${mysql_username}
          password: ${mysql_password}
          url: jdbc:mysql://${mysql_host}:${mysql_port}/${mysql_db}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      database: ${redis_database}

web:
  groovy-files: ${web_groovy_files}
feishu:
  url: ${feishu_url}
  secret: ${feishu_secret}
