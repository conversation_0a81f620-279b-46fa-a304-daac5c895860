dependencies {
    api project(':core')

    // Spring Frameworks
    api "com.baomidou:dynamic-datasource-spring-boot3-starter:$dynamic_datasource_starter_version"
    api "org.springframework.boot:spring-boot-starter-actuator"

    testImplementation "com.baomidou:mybatis-plus-generator:$mybatis_plus_version"

    // DB
    implementation "mysql:mysql-connector-java:$mysql_connector_java_version",
            "com.alibaba:druid-spring-boot-3-starter:$alibaba_druid_starter_version"

    implementation "org.apache.velocity:velocity-engine-core:$apache_velocity_engine_version"
    implementation "org.hibernate.validator:hibernate-validator:$hibernate_validator_version"
    api "commons-io:commons-io:$apache_commons_io_version",
            "com.fasterxml.jackson.core:jackson-annotations:$jackson_annotations_version"
}
