<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.UserRoleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user, role
    </sql>

    <select id="listRoleCodeByUserInfo" parameterType="java.util.Map" resultType="com.cheche365.bc.entity.RoleInfo">
        SELECT
        r.code,r.id
        FROM
        bc_role_info r, bc_user_role ur, bc_user_info u
        <where>
            ur.role = r.id AND ur.user = u.id AND r.status = #{status} AND u.id = #{id}
        </where>

    </select>

</mapper>
