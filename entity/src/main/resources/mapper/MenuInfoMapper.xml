<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.MenuInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, level, parent_id, url, icon, sort, status
    </sql>
    <select id="listMenuByUserId" resultType="com.cheche365.bc.entity.MenuInfo">
        select t4.*
        from bc_user_role t1,
             bc_permission_info t2,
             bc_role_permission t3,
             bc_menu_info t4
        where t1.user = #{userId}
          and t1.role = t3.role_id
          and t3.permission_id = t2.id
          and t2.menu_id = t4.id
          and t4.status = #{status}
        group by t4.id
        union all
        select t5.*
        from bc_user_role t1,
             bc_permission_info t2,
             bc_role_permission t3,
             bc_menu_info t4,
             bc_menu_info t5
        where t1.user = #{userId}
          and t1.role = t3.role_id
          and t3.permission_id = t2.id
          and t2.menu_id = t4.id
          and t4.status = #{status}
          and t4.parent_id = t5.id
        group by t5.id
    </select>

    <select id="listMenuByPermissionCode" resultType="com.cheche365.bc.entity.MenuInfo">
        select m.*
        from bc_permission_info p
        left join bc_menu_info m
        on p.menu_id = m.id
        <if test="permissionCodeList != null and permissionCodeList.size() > 0">
            and p.permission_code in
            <foreach collection="permissionCodeList" item="it" separator="," open="(" close=")">
                #{it}
            </foreach>
        </if>
        group by m.id
    </select>

</mapper>
