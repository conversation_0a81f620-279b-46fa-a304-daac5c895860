<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.RoleInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, status, create_user, description, create_time, update_time
    </sql>

    <resultMap id="roleInfo" type="com.cheche365.bc.entity.RoleInfo">
        <collection property="permissionList" ofType="long"
                    select="listPermissionIdByRole" column="roleId=id"/>
    </resultMap>

    <select id="listPermissionIdByRole" resultType="long">
        select permission_id
        from bc_role_permission
        where role_id = #{roleId}
    </select>

</mapper>
