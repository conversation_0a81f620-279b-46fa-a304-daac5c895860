package com.cheche365.bc.converter;

import com.cheche365.bc.entity.enums.LoginPermission;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;

/**
 * <AUTHOR>
 */
public class LoginPermissionConverter implements Converter<Integer, LoginPermission> {

    @Override
    public LoginPermission convert(Integer value) {
        return LoginPermission.get(String.valueOf(value));
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return typeFactory.constructType(Integer.class);
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return typeFactory.constructType(LoginPermission.class);
    }
}
