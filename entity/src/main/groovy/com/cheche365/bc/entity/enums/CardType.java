package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * Created by ya<PERSON><PERSON> on 2019/1/17 10:53
 * 磐石系统证件类型
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CardType {
    //身份证
    ID_CARD(1, "身份证"),
    PASSPORT(2, "护照"),
    OFFICER_CARD(3, "军官证/士兵证"),
    VISIT_PERMIT(4, "港澳回乡证"),
    TEMP_ID_CARD(5, "临时身份证"),
    ACCOUNT_BOOK(6, "户口本"),
    POLICE_CARD(7, "警官证"),
    MTP(8, "台胞证"),
    BUSINESS_LICENSE(9, "营业执照"),
    OTHER(10, "其他证件"),
    ORG_CODE(11, "组织机构代码"),
    REGISTER_CODE(12, "工商注册号码"),
    SOCIAL_CREDIT_CODE(13, "统一社会信用代码"),
    HOMETOWN_PERMIT(14, "港澳同胞回乡证（通行卡）"),
    TAIWAN_PASS(15, "台湾居民来往大陆通行证"),
    HK_CARD(16, "香港身份证"),
    TAX_CARD(17, "税务登记证"),
    DRIVER_CARD(18, "驾照"),
    ;

    @EnumValue
    private final int code;

    private final String text;

    CardType(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public static CardType get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(OTHER);
    }

    @JsonCreator
    public static CardType get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(OTHER);
    }
}
