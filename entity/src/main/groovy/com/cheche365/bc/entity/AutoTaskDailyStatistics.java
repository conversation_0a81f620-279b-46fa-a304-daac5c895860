package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 百川任务每日统计
 *
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-12-06
 */
@Data
@TableName(value = "auto_task_daily_statistics")
public class AutoTaskDailyStatistics {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 保险公司 ID
     */
    @TableField(value = "company_id")
    private String companyId;

    /**
     * 处理类型: robot; edi;
     */
    @TableField(value = "process_type")
    private String processType;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 任务总数
     */
    @TableField(value = "total_count")
    private Integer totalCount;

    /**
     * 任务成功数
     */
    @TableField(value = "success_count")
    private Integer successCount;

    /**
     * 任务失败数
     */
    @TableField(value = "failed_count")
    private Integer failedCount;

    /**
     * 业务问题数
     */
    @TableField(value = "business_issues_count")
    private Integer businessIssuesCount;

    /**
     * 统计日期
     */
    @TableField(value = "statistics_date")
    private String statisticsDate;

    /**
     * 请求来源 ID
     */
    @TableField(value = "request_source_id")
    private Long requestSourceId;

    /**
     * 是否网销: 0: 否; 1: 是;
     */
    @TableField(value = "internet_sales")
    private Integer internetSales;

}
