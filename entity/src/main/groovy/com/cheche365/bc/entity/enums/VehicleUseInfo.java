package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * 车辆使用性质
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum VehicleUseInfo {

    /**
     * 车辆使用性质
     */
    HOME("1", "家庭自用汽车"),
    TAX("2", "出租租赁营业客车"),
    BUS("3", "城市公交营业客车"),
    COACH("4", "公路客运营业客车"),
    REVERSED_ENDORSEMENT("6", "营业货车"),
    COMPANY_NON_OPERATING_BUS("10", "企业非营业客车"),
    NON_OPERATING_BUS("11", "机关非营业客车"),
    NON_OPERATING_TRUCK("12", "非营业货车"),
    OPERATING_SPECIAL("15", "营业特种车"),
    NON_OPERATING_SPECIAL("16", "非营业特种车"),
    RESERVE("17", "预约出租客运"),
    ;

    private final String code;

    @EnumValue
    private final String text;

    VehicleUseInfo(String code, String text) {
        this.code = code;
        this.text = text;
    }


    @JsonCreator
    public static VehicleUseInfo getByCode(String code) {
        return Arrays.stream(values()).filter(it -> it.getCode().equals(code)).findAny().orElse(null);
    }

    public static VehicleUseInfo getByText(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }
}
