package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)

public enum OperationState {

    /**
     * 操作状态
     */
    FAILED(1, "失败"),
    PARTIALLY_SUCCESSFUL(2, "部分成功"),
    SUCCESSFUL(3, "成功"),
    ;

    @EnumValue
//    @JsonValue
    private final int code;

    private final String text;

    OperationState(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static OperationState get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static OperationState get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
