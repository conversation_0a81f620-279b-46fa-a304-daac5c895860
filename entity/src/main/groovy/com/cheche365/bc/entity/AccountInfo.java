package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.entity.enums.CommStatus;
import lombok.Data;
import org.nustaq.serialization.annotations.Version;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;

@Data
@TableName("policy_account_info")
public class AccountInfo extends Model<AccountInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 保险公司Id
     */
    @NotNull(message = "公司ID不能为空")
    private String comId;

    /**
     * 账号
     */
    @NotNull(message = "账号不能为空")
    private String username;

    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    private String password;

    @TableField("config")
    private String configStr;

    /**
     * 配置信息
     */
    @TableField(exist = false)
    private HashMap<String, Object> config = new HashMap<>();

    /**
     * 状态
     */
    private CommStatus status;

    /**
     * 当前账号失败次数
     */
    private Integer failTimes = 0;

    /**
     * 当前账号最大失败次数；99:不限制
     */
    private Integer failTimesMax = 3;

    /**
     * 账号所属地区编码
     */
    @NotNull(message = "地区编码 areaName 不能为空")
    private String areaName;

    /**
     * 账号类型 （1:磐石，2：易宝通，3：澎湃保,99:未知）
     */
    @NotNull(message = "账号类型 type 不能为空")
    private String type;

    /**
     * 异常信息，账号中断后的异常信息
     */
    private String errorMessage;

    /**
     * 当前账号上次抓取完成时间
     */
    private LocalDate lastCompleteTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 账号是否用于定时任务抓取保单
     */
    @Version(1)
    private Boolean isQueryPolicy = true;

    /**
     * 账号密码相同的情况下多个账号存在
     */
    @Version(2)
    private Integer multiAccountNum = 1;

    @Override
    public int hashCode() {
        int result = comId.hashCode();
        result = 17 * result + username.hashCode();
        result = 17 * result + password.hashCode();
        return result;
    }


    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof AccountInfo)) {
            return false;
        }
        AccountInfo stuObj = (AccountInfo) obj;
        if (this == stuObj) {
            return true;
        }
        return stuObj.comId.equals(this.comId)
                && stuObj.username.equals(this.username)
                && stuObj.password.equals(this.password);
    }

    public String getUniqueKey() {
        return this.getComId() + "-" + this.getUsername();
    }

    @Override
    public String toString() {
        return this.username + "-" + this.comId + "-" + this.password;
    }

}
