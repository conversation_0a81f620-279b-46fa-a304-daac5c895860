package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.entity.enums.CommStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "bc_role_info", resultMap = "roleInfo")
public class RoleInfo extends Model<RoleInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "角色名称不能为空")
    @Length(max = 20, message = "角色名称只能输入20字以内的字符")
    private String name;

    private String code;

    private CommStatus status;

    @Length(max = 200, message = "角色说明只能输入200字以内的字符")
    private String description;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField(exist = false)
    private List<Long> permissionList;

    @TableField(exist = false)
    private String operateLog;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
