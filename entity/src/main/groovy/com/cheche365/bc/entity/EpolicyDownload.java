package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_epolicy_download")
public class EpolicyDownload extends Model<EpolicyDownload> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    @TableField("plateNum")
    private String plateNum;

    /**
     * 发动机号码
     */
    @TableField("engineNum")
    private String engineNum;

    /**
     * 投保人名称
     */
    @TableField("appName")
    private String appName;

    /**
     * 投保人证件号码
     */
    @TableField("appIDNum")
    private String appIDNum;

    /**
     * 商业险保单号
     */
    @TableField("bizNum")
    private String bizNum;

    /**
     * 交强险保单号
     */
    @TableField("efcNum")
    private String efcNum;

    /**
     * 商业险保单资源id
     */
    @TableField("bizSourceNum")
    private String bizSourceNum;

    /**
     * 交强险保单资源id
     */
    @TableField("efcSourceNum")
    private String efcSourceNum;

    /**
     * 电子保单下载时间
     */
    @TableField("downloadTime")
    private LocalDateTime downloadTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
