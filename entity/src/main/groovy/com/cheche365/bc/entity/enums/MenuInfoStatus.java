package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * author:<PERSON><PERSON>haoliang
 * Date:2019/4/15 17:45
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MenuInfoStatus {
    ENABLE(0, "菜单栏启用"),
    DISENABLE(1, "菜单栏禁用")
    ;

    @EnumValue
    private int status;
    private String text;

    MenuInfoStatus(Integer status, String text) {
        this.status = status;
        this.text = text;
    }
}
