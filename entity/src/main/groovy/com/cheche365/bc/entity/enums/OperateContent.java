package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * author:<PERSON><PERSON><PERSON>liang
 * Date:2019/12/5 18:20
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OperateContent {
    ADD(1, "创建"),
    EDIT(2, "编辑"),
    DELETE(3, "删除"),
    ENABLE(4, "启用"),
    DISABLE(5, "禁用");

    @EnumValue
    private final int code;

    private final String text;

    OperateContent(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static OperateContent get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static OperateContent get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }
}
