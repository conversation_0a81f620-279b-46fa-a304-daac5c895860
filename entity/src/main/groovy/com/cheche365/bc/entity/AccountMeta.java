package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("third_account_meta")
public class AccountMeta extends Model<AccountMeta> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 保险公司Id
     */
    @NotNull(message = "公司ID不能为空")
    private String comId;

    /**
     * 账号所属地区编码
     */
    @NotNull(message = "地区编码 areaCode 不能为空")
    private String areaCode;

    /**
     * 账号配置信息说明
     */
    private String meta;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
