package com.cheche365.bc.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.http.util.Args;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_interface_new")
public class Interface extends Model<Interface> {

    private static final long serialVersionUID = 1L;
    public static final String Env_Uat = "uat";
    public static final String Env_Test = "test";
    public static final String Env_Pro = "pro";
    public static final String STRUCTURE_TYPE_ENQ = "enq";
    public static final String STRUCTURE_TYPE_MAP = "map";

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务类型
     */
    private String action;
    /**
     * 保险公司ID
     */
    @TableField("comId")
    private String comId;
    /**
     * 保险公司code
     */
    @TableField("comCode")
    private String comCode;
    /**
     * 当任务本身没对状态进行描述的时候根据任务返回的失败结果进行描述状态
     */
    @TableField("defaultFailedStatus")
    private String defaultFailedStatus;
    /**
     * 当任务本身没对状态进行描述的时候根据任务返回的成功结果进行描述状态
     */
    @TableField("defaultSuccessStatus")
    private String defaultSuccessStatus;
    /**
     * 使用环境，可选择值有 test uat pro
     */
    private String env;
    /**
     * 接口类型 EDI,精灵 规则，选择项目有edi,robot,rule
     */
    @TableField("intType")
    private String intType;

    @TableField("keepSession")
    private Boolean keepSession;

    @TableField("proConfig")
    private String proConfig;

    private String remark;
    /**
     * 前置条件，是否必要待验证
     */
    private String term;
    /**
     * 测试环境的配置
     */
    @TableField("testConfig")
    private String testConfig;
    /**
     * uat环境的配置
     */
    @TableField("uatConfig")
    private String uatConfig;
    /**
     * 是否使用任务的配置
     */
    @TableField("useTaskConfig")
    private Boolean useTaskConfig;

    private Integer version;
    /**
     * 支持的数据结构类型
     */
    @TableField("dataStructureType")
    private String dataStructureType;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
    /**
     * 当前接口执行过程中，模板被跳过的次数
     */
    @TableField(exist = false)
    private int tempSkippedTimes;
    /**
     * 根据term算出的模板列表对象，不持续化到数据库
     */
    @TableField(exist = false)
    private List<Template> templates = new ArrayList<>();

    public Map loadConfig() {
        return loadConfig(this.getEnv());
    }

    public Map loadConfig(String env) {
        Args.notEmpty(env, "环境变量env");
        Map config = null;
        switch (env) {
            case Interface.Env_Pro:
                config = JSONObject.parseObject(this.getProConfig(), Map.class);
                break;
            case Interface.Env_Uat:
                config = JSONObject.parseObject(this.getUatConfig(), Map.class);
                break;
            case Interface.Env_Test:
                config = JSONObject.parseObject(this.getTestConfig(), Map.class);
                break;
            default:
        }
        return config;
    }

    public Interface copy() throws IOException, ClassNotFoundException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            try (ObjectOutputStream oos = new ObjectOutputStream(bos)) {
                oos.writeObject(this);
                ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(bos.toByteArray()));
                return (Interface) ois.readObject();
            }
        }
    }
}
