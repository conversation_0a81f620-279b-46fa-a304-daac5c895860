package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> zhangying
 * @date 2022-03-24
 * @descript :澎湃保中介机构表
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("ppb_intermediary")
public class PPBInterMediary extends Model<PPBInterMediary> {
    /**
     * 中介许可证号
     */
    @TableId(value = "intermediary_no")
    private String intermediaryNo;

    /**
     * 中介机构名称
     */
    @TableField("intermediary_name")
    private String intermediaryName;

    @TableField("com_id")
    private String comId;

    @TableField("status")   //1启用 0禁用
    private int status;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("ext1")
    private String ext1;

    @TableField("ext2")
    private String ext2;
}
