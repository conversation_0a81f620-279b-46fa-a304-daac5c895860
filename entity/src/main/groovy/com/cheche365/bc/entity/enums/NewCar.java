package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum NewCar {
    // 正常数据（有车牌 是）
    NORMAL(0, "是"),
    // （没有车牌 否）
    NEWCAR(1, "否"),
    ;

    @EnumValue
//    @JsonValue
    private final int code;

    private final String text;

    NewCar(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static NewCar get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static NewCar get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }
}
