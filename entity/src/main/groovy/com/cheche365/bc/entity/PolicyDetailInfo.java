package com.cheche365.bc.entity;

import cn.hutool.core.date.DatePattern;
import com.cheche365.bc.model.car.Enquiry;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Created by xcl on 2021/03/09.
 */
@Document(collection = "policy_info")
@Getter
@Setter
public class PolicyDetailInfo implements Serializable {

    @Id
    private String id;

    /*交强 商业 保单号*/
    private String policyNo;

    /*投保单号*/
    private String proposalNo;

    /*账号*/
    private String accountNo;

    /*账号类型*/
    private String accountType; // 账号类型（掌中宝:1，易宝通:2,...）

    /*地区 ID*/
    private String accountArea;

    /*保险公司 ID*/
    private String comId;

    /*enquiry 类*/
    private Enquiry enquiry;

    /*承保日期*/
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private LocalDate underWriteDate;

    /*回写保单时间*/
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime sendTime;

    /*保存保单时间*/
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime createTime;

}
