package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.entity.enums.CommStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *   渠道信息实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("channel")
public class Channel extends Model<Channel> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "渠道接入商的应用 appKey 不能为空")
    @FieldDoc(des = "渠道接入商的应用appKey", remark = "")
    private String appKey;

    @NotNull(message = "加密和解密的 secretKey 不能为空")
    @FieldDoc(des = "加密和解密的key", remark = "")
    private String secretKey;

    @FieldDoc(des = "渠道接入商的应用appId", need = true, remark = "")
    private String appId;

    @NotNull(message = "渠道名称 channelName 不能为空")
    @FieldDoc(des = "渠道名称", need = true, remark = "")
    private String channelName;

    @NotNull(message = "渠道代码 channelCode 不能为空")
    @FieldDoc(des = "渠道代码", need = true, remark = "")
    private String channelCode;

    @NotNull(message = "回调地址 callBackUrl 不能为空")
    @FieldDoc(des = "回调地址", remark = "")
    private String callBackUrl;

    /**
     * 参照 AccountInfo type 字段
     */
    @NotNull(message = "账号类型 type 不能为空")
    private String type;

    @FieldDoc(des = "启用状态", remark = "")
    private CommStatus status;

    @FieldDoc(des = "允许渠道接入IP", remark = "")
    private String allowIps;

    @FieldDoc(des = "允许的保险公司", remark = "")
    private String allowIns;

    @FieldDoc(des = "协议版本号", remark = "")
    private String version = "1.0";

    /**
     * 总共的车辆查询次数
     */
    private Integer leftQueryCarTimes;
    private Integer totalQueryCarTimes;

    /**
     * 总共的平台查询次数
     */
    private Integer leftQueryPlatTimes;
    private Integer totalQueryPlatTimes;

    /**
     * 总共的保单查询次数
     */
    private Integer leftQueryPolicyTimes;
    private Integer totalQueryPolicyTimes;

    /**
     * 已使用报价次数
     */
    private Integer leftQuoteTimes;
    private Integer totalQuoteTimes;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
