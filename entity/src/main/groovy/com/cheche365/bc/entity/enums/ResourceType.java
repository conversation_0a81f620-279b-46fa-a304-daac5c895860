package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * author:<PERSON><PERSON><PERSON>liang
 * Date:2019/1/13 16:18
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ResourceType {

    MENU_TYPE(1, "菜单"),
    BUTTON_TYPE(2, "按钮");

    @EnumValue
//    @JsonValue
    private final int type;

    private final String text;

    ResourceType(int type, String text) {
        this.type = type;
        this.text = text;
    }

    @JsonCreator
    public static ResourceType get(int value) {
        return Arrays.stream(values()).filter(it -> it.getType() == value).findAny().orElse(null);
    }
    
    public static ResourceType get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }
    
    @Override
    public String toString() {
        return text;
    }
}
