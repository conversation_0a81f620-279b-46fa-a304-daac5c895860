package com.cheche365.bc.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by Administrator on 2015/10/22.
 */
@Document(collection = "action_log_new")
@Getter
@Setter
public class ActionLog {

    private String _id;

    /*task表的ID*/
    private String autoTraceId;

    /*动作名称*/
    private String actionName;

    /*请求地址*/
    private String url;

    /*动作发送报文*/
    private String requestBody;

    /*动作返回报文*/
    private String responseBody;

    /*传入action的数据*/
    private String inTaskBody;

    /*解析完传出action的数据*/
    private String outTaskBody;

    /*发送报文时间*/
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date sendTime;

    /*接收报文时间*/
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date receiveTime;

    /*异常信息*/
    private String exceptionInfo;

}
