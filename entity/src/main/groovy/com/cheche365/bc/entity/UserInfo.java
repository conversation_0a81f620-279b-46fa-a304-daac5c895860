package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.converter.LoginPermissionConverter;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.LoginPermission;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bc_user_info")
public class UserInfo extends Model<UserInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String email;

    private String name;

    private String institution;

    private String password;

    /**
     * 所属机构id
     */
    private String orgId;

    /**
     * 所属机构代码
     */
    private String orgCode;

    /**
     * 所属机构名称
     */
    private String orgName;

    /**
     * 所属机构等级
     */
    private Integer orgLevel;

    private CommStatus status;

    /**
     * 是否首次登陆
     */
    private Boolean firstLogin;

    /**
     * 登录权限 1线上 2线下
     */
    @JsonDeserialize(converter = LoginPermissionConverter.class)
    private LoginPermission loginPermission;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建用户名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private Boolean changePassword;

    @TableField(exist = false)
    private List<Long> dataTypeIds;

    @TableField(exist = false)
    private List<Long> platformIds;

    @TableField(exist = false)
    private List<Long> applyIds;

    @TableField(exist = false)
    private List<Long> subjectIds;

    @TableField(exist = false)
    private List<Long> roleIds;

    /**
     * 权限列表
     */
    @TableField(exist = false)
    private List<String> permissionList;

    @TableField(exist = false)
    private String operateLog;

    @Override
    public Serializable pkVal() {
        return this.id;
    }


}
