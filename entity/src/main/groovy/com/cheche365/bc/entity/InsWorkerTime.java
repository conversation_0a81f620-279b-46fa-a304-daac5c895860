package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_ins_worker_time")
public class InsWorkerTime extends Model<InsWorkerTime> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("comId")
    private String comId;

    @TableField("createTime")
    private LocalDateTime createTime;

    @TableField("endHour")
    private Integer endHour;

    @TableField("startHour")
    private Integer startHour;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
