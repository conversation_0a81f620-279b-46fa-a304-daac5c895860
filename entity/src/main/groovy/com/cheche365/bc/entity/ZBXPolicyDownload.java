package com.cheche365.bc.entity;

import com.cheche365.bc.annotation.ClassDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description:
 * @Date: 2021/5/20 2:03 下午
 */
@Getter
@Setter
@ClassDoc(remark = "订单信息")
@AllArgsConstructor
@NoArgsConstructor
public class ZBXPolicyDownload {
    @NotBlank(message = "批次号不能为空")
    private String enquiryId;
    private String province;
    @NotNull(message = "证件类型不能为空")
    private Integer idCardType;
    @NotBlank(message = "证件号码不能为空")
    private String idCard;
    @NotBlank(message = "证件名称不能为空")
    private String name;
    @NotBlank(message = "发动机号不能为空")
    private String engineNum;
    @NotBlank(message = "车牌号不能为空")
    private String plateNum;
    private String efcPolicyCode;
    private String bizPolicyCode;
    private String accidentPolicyCode;
}
