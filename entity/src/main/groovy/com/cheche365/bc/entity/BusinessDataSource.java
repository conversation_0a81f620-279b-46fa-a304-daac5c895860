package com.cheche365.bc.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */

@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_data_source")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BusinessDataSource extends Model {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 来源产品
     */
    @TableField(value = "source_product")
    private String sourceProduct;

    /**
     * 来源场景
     */
    @TableField(value = "source_scenario")
    private String sourceScenario;

    /**
     * 来源渠道
     */
    @TableField(value = "source_channel")
    private String sourceChannel;

    /**
     * 来源类型
     */
    @TableField(value = "source_kind")
    private Integer sourceKind;

    /**
     * 备注
     */
    @TableField(value = "description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private String createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private String updateTime;

}
