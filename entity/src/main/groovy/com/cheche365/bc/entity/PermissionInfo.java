package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.entity.enums.CommStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bc_permission_info")
public class PermissionInfo extends Model<PermissionInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限url
     */
    private String url;

    /**
     * 权限所属菜单id
     */
    private String menuId;

    /**
     * 权限唯一标识
     */
    private String permissionCode;

    /**
     * 状态
     */
    private CommStatus status;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
