package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @since 2022-06-20
 */
@TableName(value = "tb_request_source")
@Data
public class RequestSource {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 上级渠道 ID
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
