package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@TableName("link")
public class Link extends Model<Link> {

    private static final long serialVersionUID = 1L;

    /**
     *  
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String shortLink;

    private String originalLink;

    private LocalDateTime createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
