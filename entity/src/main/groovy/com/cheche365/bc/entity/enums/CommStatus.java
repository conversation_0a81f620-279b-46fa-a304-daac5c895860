package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * author:<PERSON><PERSON>haoliang
 * Date:2019/12/5 15:13
 */

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CommStatus {

    // 启用
    ENABLE(1, "启用"),
    // 禁用
    DISABLED(0, "禁用");

    @EnumValue
    private final int code;

    private final String text;

    CommStatus(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static CommStatus get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(ENABLE);
    }

    public static CommStatus get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(ENABLE);
    }

    @Override
    public String toString() {
        return text;
    }

}
