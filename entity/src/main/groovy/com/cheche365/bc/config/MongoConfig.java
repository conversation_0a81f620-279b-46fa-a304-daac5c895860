package com.cheche365.bc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories("com.cheche365.bc.repo")
public class MongoConfig {

    @Bean
    MappingMongoConverter mongoConverter(MongoDatabaseFactory databaseFactory, MongoMappingContext mongoMappingContext) throws Exception {
        MappingMongoConverter mongoConverter = new MappingMongoConverter(new DefaultDbRefResolver(databaseFactory), mongoMappingContext);
        mongoConverter.setMapKeyDotReplacement("_");
        return mongoConverter;
    }
}
