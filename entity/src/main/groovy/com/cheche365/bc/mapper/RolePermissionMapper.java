package com.cheche365.bc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cheche365.bc.entity.RolePermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
public interface RolePermissionMapper extends BaseMapper<RolePermission> {

    List<RolePermission> listRolePermissionByRoleId(@Param("roleId") Long roleId, @Param("isAdmin") Boolean isAdmin);
}
