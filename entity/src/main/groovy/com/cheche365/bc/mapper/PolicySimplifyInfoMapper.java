package com.cheche365.bc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface PolicySimplifyInfoMapper extends BaseMapper<PolicySimplifyInfo> {

    @Select("SELECT concat(if(!account_type||isnull(account_type),'99',account_type), '|:' ,account_no, '|:' ,com_id, '|:', if(!account_area||isnull(account_area),'99',account_area), '|:', count(*)) as result FROM policy_simplify_info WHERE create_time >= #{startDate} and create_time < #{endDate} and downloaded = true group by if(!account_type||isnull(account_type),'99',account_type), account_no, com_id, if(!account_area||isnull(account_area),'99',account_area)")
    List<String> findPolicyCountFromTime(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);


    @Select("SELECT com_id as comId, if(!account_area||isnull(account_area),'99',account_area) as accountArea, count(*) as size FROM policy_simplify_info WHERE create_time >= #{startDate} and create_time < #{endDate} and downloaded = true group by com_id, accountArea")
    List<Map<String, Object>> findPolicyCountGroupByArea(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

}
