package com.cheche365.bc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cheche365.bc.entity.PolicyDataStatistics;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> zhangying
 * @date 2023-01-04
 * @descript :
 */
public interface PolicyDataStatisticsMapper extends BaseMapper<PolicyDataStatistics> {
    @Select("SELECT *,totalcount FROM policy_data_statistics p LEFT JOIN (SELECT com_id cid,SUM(count) AS totalcount FROM policy_data_statistics GROUP BY com_id ORDER BY totalcount DESC) S ON p.com_id = S.cid WHERE channel_type = #{channelType} AND MONTH >=  #{startDate} AND MONTH <=  #{endDate} ORDER BY totaLcount DESC, company_name ASC, month ASC")
    List<PolicyDataStatistics> queryPolicyStatisticsSumByComId(@Param("channelType") String channelType, @Param("startDate") String startDate, @Param("endDate") String endDate);
}
