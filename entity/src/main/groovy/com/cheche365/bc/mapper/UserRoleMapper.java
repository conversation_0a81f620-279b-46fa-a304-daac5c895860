package com.cheche365.bc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cheche365.bc.entity.RoleInfo;
import com.cheche365.bc.entity.UserRole;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
public interface UserRoleMapper extends BaseMapper<UserRole> {

    List<RoleInfo> listRoleCodeByUserInfo(Map<String, Object> params);

}
