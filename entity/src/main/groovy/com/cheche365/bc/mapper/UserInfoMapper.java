package com.cheche365.bc.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.entity.UserInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    UserInfo getUserInfoByEmail(Map<String, Object> params);

    List<UserInfo> pageList(
            Page<UserInfo> page,
            @Param("ew") QueryWrapper<UserInfo> wrapper,
            @Param("map") Map<String, String> map
    );

    Map<String, Object> getUserInfoRefById(
            @Param("id") Long id
    );
}
