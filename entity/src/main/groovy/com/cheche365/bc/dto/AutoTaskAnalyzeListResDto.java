package com.cheche365.bc.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * 任务成功率统计响应实体
 *
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-12-10
 */
@Data
public class AutoTaskAnalyzeListResDto {

    /**
     * 保险公司
     */
    private String companyId;

    /**
     * 处理类型 robot edi
     */
    private String processType;

    /**
     * 报价任务成功数
     */
    private Integer quoteSuccess = 0;

    /**
     * 报价任务总数
     */
    private Integer quoteTotal = 0;

    /**
     * 报价成功率
     */
    private BigDecimal quoteSuccessRate;

    /**
     * 去除非技术问题报价总数
     */
    private Integer exceptBussinessIssuesQuote = 0;

    /**
     * 去除非技术问题报价成功率
     */
    private BigDecimal exceptBussinessIssuesQuoteRate;

    /**
     * 核保暂存成功任务数
     */
    private Integer insureSuccess = 0;

    /**
     * 核保暂存任务总数
     */
    private Integer insureTotal = 0;

    /**
     * 核保暂存成功率
     */
    private BigDecimal insureSuccessRate;

    /**
     * 去除非技术问题核保暂存总数
     */
    private Integer exceptBussinessIssuesInsure = 0;

    /**
     * 去除非技术问题核保暂存成功率
     */
    private BigDecimal exceptBussinessIssuesInsureRate;

    /**
     * 自动核保任务成功数量
     */
    private Integer autoinsureSuccess = 0;

    /**
     * 自动核保任务总数
     */
    private Integer autoinsureTotal = 0;

    /**
     * 自动核保成功率
     */
    private BigDecimal autoinsureSuccessRate;

    /**
     * 去除非技术问题自动核保总数
     */
    private Integer exceptBussinessIssuesAutoinsure = 0;

    /**
     * 去除非技术问题自动核保成功率
     */
    private BigDecimal exceptBussinessIssuesAutoinsureRate;

    public void setQuoteSuccess(Integer quoteSuccess) {
        this.quoteSuccess = Optional.ofNullable(this.quoteSuccess).orElse(0) + quoteSuccess;
    }

    public void setQuoteTotal(Integer quoteTotal) {
        this.quoteTotal = Optional.ofNullable(this.quoteTotal).orElse(0) + quoteTotal;
    }

    public BigDecimal getQuoteSuccessRate() {
        if (this.quoteTotal == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(this.quoteSuccess).divide(
                new BigDecimal(this.quoteTotal), 4, RoundingMode.HALF_UP);
    }

    public void setExceptBussinessIssuesQuote(Integer exceptBussinessIssuesQuote) {
        this.exceptBussinessIssuesQuote =
                Optional.ofNullable(this.exceptBussinessIssuesQuote).orElse(0) +
                        exceptBussinessIssuesQuote;
    }

    public BigDecimal getExceptBussinessIssuesQuoteRate() {
        if (this.exceptBussinessIssuesQuote == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(this.quoteSuccess).divide(
                new BigDecimal(this.exceptBussinessIssuesQuote), 4, RoundingMode.HALF_UP);
    }

    public void setInsureSuccess(Integer insureSuccess) {
        this.insureSuccess = Optional.ofNullable(this.insureSuccess).orElse(0) +
                insureSuccess;
    }

    public void setInsureTotal(Integer insureTotal) {
        this.insureTotal = Optional.ofNullable(this.insureTotal).orElse(0) + insureTotal;
    }

    public BigDecimal getInsureSuccessRate() {
        if (this.insureTotal == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(Optional.ofNullable(this.insureSuccess).orElse(0)).divide(
                new BigDecimal(this.insureTotal), 4, RoundingMode.HALF_UP);
    }

    public void setExceptBussinessIssuesInsure(Integer exceptBussinessIssuesInsure) {
        this.exceptBussinessIssuesInsure =
                Optional.ofNullable(this.exceptBussinessIssuesInsure).orElse(0) +
                        exceptBussinessIssuesInsure;
    }

    public BigDecimal getExceptBussinessIssuesInsureRate() {
        if (this.exceptBussinessIssuesInsure == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(Optional.ofNullable(this.insureSuccess).orElse(0)).divide(
                new BigDecimal(this.exceptBussinessIssuesInsure), 4, RoundingMode.HALF_UP);
    }

    public void setAutoinsureSuccess(Integer autoinsureSuccess) {
        this.autoinsureSuccess = Optional.ofNullable(this.autoinsureSuccess).orElse(0) +
                autoinsureSuccess;
    }

    public void setAutoinsureTotal(Integer autoinsureTotal) {
        this.autoinsureTotal = Optional.ofNullable(this.autoinsureTotal).orElse(0) +
                autoinsureTotal;
    }

    public BigDecimal getAutoinsureSuccessRate() {
        if (this.autoinsureTotal == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(Optional.ofNullable(this.autoinsureSuccess).orElse(0)).divide(
                new BigDecimal(this.autoinsureTotal), 4, RoundingMode.HALF_UP);
    }

    public void setExceptBussinessIssuesAutoinsure(Integer exceptBussinessIssuesAutoinsure) {
        this.exceptBussinessIssuesAutoinsure =
                Optional.ofNullable(this.exceptBussinessIssuesAutoinsure).orElse(0) +
                        exceptBussinessIssuesAutoinsure;
    }

    public BigDecimal getExceptBussinessIssuesAutoinsureRate() {
        if (this.exceptBussinessIssuesAutoinsure == 0) {
            return new BigDecimal(0);
        }

        return new BigDecimal(Optional.ofNullable(this.autoinsureSuccess).orElse(0)).divide(
                new BigDecimal(this.exceptBussinessIssuesAutoinsure), 4, RoundingMode.HALF_UP);
    }

}
