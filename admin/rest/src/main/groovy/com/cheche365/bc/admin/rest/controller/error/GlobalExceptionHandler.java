package com.cheche365.bc.admin.rest.controller.error;

import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.model.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import static org.springframework.http.HttpStatus.OK;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(Throwable.class)
    protected ResponseEntity<Object> handleGeneralException(Throwable ex, WebRequest request) {
        log.error("未知异常：{}\nat：{}", ex.getMessage(), ExceptionUtils.getStackTrace(ex).replaceAll(",", "\n   "));
        return new ResponseEntity<>(new RestResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()), OK);
    }

    @ExceptionHandler(BadSqlGrammarException.class)
    protected ResponseEntity<Object> handleMysqlException(BadSqlGrammarException ex, WebRequest request) {
        String message = ex.getMessage();
        if (StringUtils.isNotBlank(message) && message.contains("command denied")) {
            return new ResponseEntity<>(new RestResponse<>(ExceptionCde.FORBIDDEN_OPERATION.getCde(), ExceptionCde.FORBIDDEN_OPERATION.getName()), OK);
        }
        return new ResponseEntity<>(new RestResponse<>(ExceptionCde.DB_ERROR.getCde(), ExceptionCde.DB_ERROR.getName()), OK);
    }
}
