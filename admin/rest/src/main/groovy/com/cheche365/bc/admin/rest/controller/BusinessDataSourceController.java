package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.dto.DataSourceConfigDto;
import com.cheche365.bc.entity.BusinessDataSource;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.BusinessDataSourceService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */
@RestController
@RequestMapping(value = "dataSourceConfig")
@AllArgsConstructor
public class BusinessDataSourceController extends BaseController<BusinessDataSourceService, BusinessDataSource> {

    private final BusinessDataSourceService businessDataSourceService;

    /**
     * 来源归类-新增
     *
     * @param dto 请求实体
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.DATA_SOURCE_CONFIG + COMMA + PermissionCode.DATA_SOURCE_CONFIG_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse save(@RequestBody DataSourceConfigDto dto) {
        BusinessDataSource existDataSource = businessDataSourceService.getDataSource(dto.getSourceProduct(), dto.getSourceScenario(), dto.getSourceChannel());
        if (null != existDataSource) {
            return RestResponse.failedMessage("数据来源类型已经存在，创建失败！");
        }

        BusinessDataSource source = new BusinessDataSource();
        BeanUtils.copyProperties(dto, source);
        if (businessDataSourceService.save(source)) {
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }

    }


    /**
     * 来源归类-更新
     *
     * @param dto 请求实体
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.DATA_SOURCE_CONFIG + COMMA + PermissionCode.DATA_SOURCE_CONFIG_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse update(@RequestBody DataSourceConfigDto dto) {
        BusinessDataSource requestDataSource = businessDataSourceService.getById(dto.getId());
        if (null == requestDataSource) {
            return RestResponse.failedMessage("id错误，更新失败");
        }

        BusinessDataSource existDataSource = businessDataSourceService.getDataSource(dto.getSourceProduct(), dto.getSourceScenario(), dto.getSourceChannel());
        if ((null != existDataSource) && (!dto.getId().equals(existDataSource.getId()))) {
            return RestResponse.failedMessage("不能更新为已存在的配置！");
        }

        BusinessDataSource dataSource = new BusinessDataSource();
        BeanUtils.copyProperties(dto, dataSource);
        if (businessDataSourceService.updateById(dataSource)) {
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }

    }

    /**
     * 来源归类-删除
     *
     * @param id 来源归类 ID
     * @return RestResponse
     */
    @Override
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.DATA_SOURCE_CONFIG + COMMA + PermissionCode.DATA_SOURCE_CONFIG_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id) {
        if (businessDataSourceService.removeById(id)) {
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }

    }
}
