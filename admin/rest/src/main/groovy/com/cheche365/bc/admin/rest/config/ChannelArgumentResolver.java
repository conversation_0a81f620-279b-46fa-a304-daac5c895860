package com.cheche365.bc.admin.rest.config;

import com.cheche365.bc.admin.service.service.ChannelService;
import com.cheche365.bc.entity.Channel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import static com.cheche365.bc.constants.Constants.SECURE_APP_KEY;

/**
 * <p>
 * 控制器参数转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Service
public class ChannelArgumentResolver implements HandlerMethodArgumentResolver {


    @Autowired
    ChannelService channelService;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType() == Channel.class;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        String requestAppKey = webRequest.getHeader(SECURE_APP_KEY);
        if (requestAppKey != null) {
            return channelService.findByAppKey(requestAppKey);
        }
        return null;
    }
}
