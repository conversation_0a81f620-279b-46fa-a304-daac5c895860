package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */
@RestController
@RequestMapping(value = "dataSourceConfigAutoTaskLog")
@AllArgsConstructor
public class BusinessDataSourceAutoTaskLogController {

    private final BusinessDataSourceAutoTaskLogService businessDataSourceAutoTaskLogService;

    @GetMapping(value = "treeList")
    @ResponseBody
    public List<Map<String, Object>> treeList() {
        return businessDataSourceAutoTaskLogService.getTreeList();
    }
}
