package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.rest.security.ManageCommonSecurityUser;
import com.cheche365.bc.service.PolicyAccountService;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.AccountInfo;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.tools.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SecurityConstants.ROLE_ADMIN;
import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;
import static com.cheche365.bc.constants.Constants.EP_POLICY_CLOSE;

/**
 * <p>
 * 抓取保单账号以及报价账号控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-16
 */
@RestController
@RequestMapping("/account")
@Slf4j
@RequiredArgsConstructor
public class PolicyAccountInfoController extends BaseController<PolicyAccountService, AccountInfo> {

    private final StringRedisTemplate stringRedisTemplate;
    private final PolicyAccountService accountService;

    @GetMapping
    @Override
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_GETPAGE + SUFFIX)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<AccountInfo> page = createPage(map);
        String areaCode = ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo().getOrgCode();
        if (StringUtil.isNoEmpty(areaCode) && authentication.getAuthorities().stream().noneMatch(it -> it.getAuthority().contains(ROLE_ADMIN)))
            map.put("areaName", areaCode);
        // 获取并设置检索条件
        if (StringUtil.isNoEmpty(map.get("id"))) {
            service.page(page, new QueryWrapper<AccountInfo>().eq("id", Long.valueOf(map.get("id"))));
            return RestResponse.success(page);
        }
        QueryWrapper<AccountInfo> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_time");
        map.putIfAbsent("isQueryPolicy", "true");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @PostMapping("save")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_UPDATE + SUFFIX)
    public RestResponse saveAccount(@RequestBody AccountInfo companyInfo) {
        // 新增将id置空
        companyInfo.setId(null);
        boolean status;
        try {
            status = service.saveOrUpdate(companyInfo);
        } catch (Exception e) {
            return RestResponse.failedMessage("新增账号失败.请检查账号是否已存在，或信息有误");
        }
        if (!status) {
            return RestResponse.failedMessage("新增账号信息保存失败.");
        }
        return RestResponse.success();
    }

    @RequestMapping(value = "setstatus")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_UPDATE + SUFFIX)
    public RestResponse resetStatus(@RequestParam int status, @RequestParam Long id, Authentication authentication) {
        if (!(status == CommStatus.DISABLED.getCode() || status == CommStatus.ENABLE.getCode())) {
            return RestResponse.failedMessage("不合法操作");
        }
        AccountInfo accountInfo = service.getById(id);
        if (accountInfo == null) {
            return RestResponse.failedMessage("当前账号不存在");
        }
        log.info("[AccountInfoController resetStatus] user modify role enable, params: status:{}, id, {}, operateName:{}",
                status, id, authentication.getName());
        accountInfo.setStatus(CommStatus.get(status));
        accountInfo.setUpdateTime(LocalDateTime.now());
        if (status == CommStatus.DISABLED.getCode())
            // 中断当前账号抓取任务
            stringRedisTemplate.convertAndSend(EP_POLICY_CLOSE, accountInfo.getUniqueKey());
        else {
            accountInfo.setFailTimes(0);
            accountInfo.setErrorMessage("");
        }
        service.updateById(accountInfo);
        return RestResponse.success();
    }

    @RequestMapping(value = "restart")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_UPDATE + SUFFIX)
    public RestResponse restartById(@RequestParam Long id) {
        // 重新启用当前账号
        List<AccountInfo> accountInfoList = accountService.getAccountSFormDatabase(new HashMap<String, Object>() {{
            put("id", id);
        }});
        log.info("重启账号：{}", accountInfoList.toString());
        return RestResponse.success();
    }

    @RequestMapping(value = "delete/{id}", method = RequestMethod.GET)
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_UPDATE + SUFFIX)
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        AccountInfo accountInfo = service.getById(id);
        if (accountInfo == null) {
            return RestResponse.failedMessage("当前账号不存在");
        }
        log.info("[AccountInfoController delete] user delete role, params, id: {}, operateName: {}",
                id, authentication.getName());
        service.removeById(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "update")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_NO_LIST + COMMA + PermissionCode.ACCOUNTNOLIST_UPDATE + SUFFIX)
    public RestResponse update(@RequestBody AccountInfo accountInfo) {
        if (accountInfo == null) {
            return RestResponse.failedMessage("请填写账号相关信息");
        } else if (accountInfo.getId() == null) {
            return RestResponse.failedMessage("请选择需要更新的账号信息");
        }
        accountInfo.setUpdateTime(LocalDateTime.now());
        boolean status = service.saveOrUpdate(accountInfo);
        if (!status) {
            return RestResponse.failedMessage("更新账号信息失败.");
        }
        return RestResponse.success();
    }
}

