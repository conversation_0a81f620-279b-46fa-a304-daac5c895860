package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.InsWorkerTime;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.InsWorkerTimeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@RequestMapping("/insWorkerTime")
public class InsWorkerTimeController extends BaseController<InsWorkerTimeService, InsWorkerTime> {

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.WORKER_TIME + COMMA + PermissionCode.WORKERTIME_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.WORKER_TIME + COMMA + PermissionCode.WORKERTIME_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse saveInsWorkerTime(@RequestBody InsWorkerTime insWorkerTime) {
        insWorkerTime.setCreateTime(LocalDateTime.now());
        if(service.saveOrUpdate(insWorkerTime)){
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.WORKER_TIME + COMMA + PermissionCode.WORKERTIME_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateInsWorkerTime(@RequestBody InsWorkerTime insWorkerTime) {
        if(service.saveOrUpdate(insWorkerTime)){
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.WORKER_TIME + COMMA + PermissionCode.WORKERTIME_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id) {
        if(service.removeById(id)){
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }
    }
}

