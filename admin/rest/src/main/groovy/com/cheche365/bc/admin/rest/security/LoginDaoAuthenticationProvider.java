package com.cheche365.bc.admin.rest.security;

import com.cheche365.bc.admin.service.constant.SecurityConstants;
import com.cheche365.bc.utils.encrypt.MD5;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


@Service
public class LoginDaoAuthenticationProvider extends DaoAuthenticationProvider {
    private PasswordEncoder passwordEncoder = new PasswordEncode();

    @Autowired
    public LoginDaoAuthenticationProvider(SecurityUserService userService) {
        this.setUserDetailsService(userService);
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        if (authentication.getCredentials() == null || "".equals(authentication.getCredentials().toString())) {
            throw new BadCredentialsException(SecurityConstants.AUTHENTICATION_NONE_PASSWORD);
        }

        String presentedPassword = authentication.getCredentials().toString();

        boolean samePassword = checkPassword(userDetails, presentedPassword);
        if(!samePassword) {
            throw new BadCredentialsException(SecurityConstants.LOGIN_ERROR_MESSAGE);
        }

    }

    private boolean checkPassword(UserDetails userDetails, String presentedPassword) {
        return passwordEncoder.matches(presentedPassword, userDetails.getPassword());
    }

    class PasswordEncode implements PasswordEncoder {

        @Override
        public String encode(CharSequence rawPassword) {
            return MD5.toHex(rawPassword.toString());
        }

        @Override
        public boolean matches(CharSequence rawPassword, String encodedPassword) {
            return encodedPassword.equals(encode(rawPassword));
        }
    }

}
