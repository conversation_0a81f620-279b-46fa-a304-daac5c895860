package com.cheche365.bc.admin.rest.security;

import com.cheche365.bc.admin.rest.config.BcPermissionEvaluator;
import com.cheche365.bc.model.RestResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.security.SpringSessionBackedSessionRegistry;

import java.io.PrintWriter;


/**
 * Security配置
 * Created by luocong on 2018/12/11.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfiguration {

    @Value("${security.login-url}")
    private String LOGIN_URL;

    private final FindByIndexNameSessionRepository sessionRepository;
    private final LoginDaoAuthenticationProvider loginDaoAuthenticationProvider;

    private final BcPermissionEvaluator bcPermissionEvaluator;

    @Bean
    AuthenticationManager authenticationManager() {
        return new ProviderManager(loginDaoAuthenticationProvider);
    }

    @Bean
    public DefaultMethodSecurityExpressionHandler expressionHandler() {
        DefaultMethodSecurityExpressionHandler expressionHandler = new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(bcPermissionEvaluator);
        return expressionHandler;
    }

    @Bean
    SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {

        http.authorizeHttpRequests(
                        (authorize -> authorize
                                .requestMatchers("/api/**", "/userinfo/login", "/userinfo/logout", "/userinfo/currentUser", "/app-manage/**").permitAll()
                                .requestMatchers("/static/**", "/*.html", "/*.css", "/*.js", "/*.ico", "/*.json", "/css/**", "/js/**", "/img/**", "/fonts/**", "/font-awesome/**").permitAll()
                                .anyRequest().authenticated())
                ).formLogin((login) -> login.loginPage(LOGIN_URL).loginProcessingUrl("/userinfo/login").successHandler((req, resp, authentication) -> {
                    RestResponse success = RestResponse.success("登录成功！!", authentication.getPrincipal());
                    resp.setContentType("application/json;charset=utf-8");
                    PrintWriter out = resp.getWriter();
                    out.write(new ObjectMapper().writeValueAsString(success));
                    out.flush();
                    out.close();
                }).failureHandler((req, resp, e) -> {
                    RestResponse error = RestResponse.failedMessage("登录失败！！");
                    resp.setContentType("application/json;charset=utf-8");
                    PrintWriter out = resp.getWriter();
                    out.write(new ObjectMapper().writeValueAsString(error));
                    out.flush();
                    out.close();
                }).permitAll()).logout((logout) -> logout.logoutUrl("/userinfo/logout").logoutSuccessHandler((req, resp, authentication) -> {
                            RestResponse ok = RestResponse.successMessage("注销成功！");
                            resp.setContentType("application/json;charset=utf-8");
                            PrintWriter out = resp.getWriter();
                            out.write(new ObjectMapper().writeValueAsString(ok));
                            out.flush();
                            out.close();
                        }).permitAll()

                ).exceptionHandling((e) -> e.defaultAuthenticationEntryPointFor(new LoginUrlAuthenticationEntryPoint(), new AjaxRequestMatcher()).accessDeniedHandler((req, resp, e1) -> {
                    RestResponse fail = RestResponse.failedMessage("权限不足，访问失败！");
                    resp.setStatus(403);
                    resp.setContentType("application/json;charset=utf-8");
                    PrintWriter out = resp.getWriter();
                    out.write(new ObjectMapper().writeValueAsString(fail));
                    out.flush();
                    out.close();
                }))
                .csrf(AbstractHttpConfigurer::disable)
                .addFilterAt(customAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
                .sessionManagement((session) -> session.maximumSessions(2)
                        .sessionRegistry(sessionRegistry()));

        return http.build();
    }

    /**
     * 为security指定session注册器
     * <p>
     * 让security结合spring-session实现session共享
     *
     * @return the spring session backed session registry
     */
    @Bean
    SpringSessionBackedSessionRegistry sessionRegistry() {
        return new SpringSessionBackedSessionRegistry<>(sessionRepository);
    }

    public CustomAuthenticationFilter customAuthenticationFilter() throws Exception {
        CustomAuthenticationFilter filter = new CustomAuthenticationFilter();
        filter.setAuthenticationSuccessHandler((req, resp, authentication) -> {
            resp.setContentType("application/json;charset=utf-8");
            PrintWriter out = resp.getWriter();
            RestResponse respBean = RestResponse.success("登录成功！", authentication.getPrincipal());
            SecurityContextRepository repository = new HttpSessionSecurityContextRepository();
            repository.saveContext(SecurityContextHolder.getContext(), req, resp);
            out.write(new ObjectMapper().writeValueAsString(respBean));
            out.flush();
            out.close();
        });
        filter.setAuthenticationFailureHandler((req, resp, e) -> {
            resp.setContentType("application/json;charset=utf-8");
            PrintWriter out = resp.getWriter();
            RestResponse respBean = RestResponse.failedMessage(e.getMessage());
            out.write(new ObjectMapper().writeValueAsString(respBean));
            out.flush();
            out.close();
        });
        filter.setAuthenticationManager(authenticationManager());
        return filter;
    }
}
