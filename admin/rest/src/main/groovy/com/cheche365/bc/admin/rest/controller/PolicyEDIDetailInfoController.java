package com.cheche365.bc.admin.rest.controller;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.PolicyEDIDetailInfo;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.util.ExtractPolicyUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhangying
 * @date 2022-05-17
 * @descript :互联互通查询
 */
@RestController
@AllArgsConstructor
@Slf4j
public class PolicyEDIDetailInfoController {

    private final MongoTemplate mongoTemplate;

    /*互联互通查询接口*/
    @ResponseBody
    @RequestMapping(value = "callback/queryEDIPolicy", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE, produces = "application/json;charset=UTF-8")
    public Object queryEDIPolicy(HttpServletRequest request, @RequestBody String param) throws Exception {
        JSONObject jsonParam = JSONObject.parseObject(param);
        Map<String, Object> params = new HashMap<>();
        if (StringUtil.isNoEmpty(jsonParam.getString("startDate"))) {
            params.put("startDate", jsonParam.getString("startDate"));
            params.put("endDate", jsonParam.containsKey("endDate") ? jsonParam.getString("endDate") : LocalDate.parse(jsonParam.getString("startDate"), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).toString());
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("comId"))) {
            params.put("comId", jsonParam.getString("comId"));
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("policyType"))) {   //保单类型 1商业 2交强
            params.put("policyType", jsonParam.getString("policyType"));
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("accountNo"))) {    //账号
            params.put("accountNo", jsonParam.getString("accountNo"));
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("orgName"))) {      //代理机构名称
            params.put("enquiry.order.agentInfo.orgName", jsonParam.getString("orgName"));
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("insCorpName"))) {  //保险公司名称
            params.put("enquiry.config.insCorpName", jsonParam.getString("insCorpName"));
        }
        if (StringUtil.isNoEmpty(jsonParam.getString("policyNo"))) {  //保单号
            params.put("policyNo", jsonParam.getString("policyNo"));
        }

        List<PolicyEDIDetailInfo> EDIPolicyInfos = mongoTemplate.find(ExtractPolicyUtils.getQuery(params), PolicyEDIDetailInfo.class);
        log.info("互联互通查询EDIPolicy MongoDB表数据，条件为{}，共查到{}条数据", params, EDIPolicyInfos.size());
        return EDIPolicyInfos;
    }
}
