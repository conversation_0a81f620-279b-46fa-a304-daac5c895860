package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.service.ChannelService;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.Channel;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  渠道信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@RestController
@RequestMapping("/channel")
@Slf4j
@AllArgsConstructor
public class ChannelController extends BaseController<ChannelService, Channel> {

    private final ChannelService channelService;

    @GetMapping("")
    @Override
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.CHANNEL_INFO + COMMA + PermissionCode.CHANNEL_INFO_GETPAGE + SUFFIX)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<Channel> page = createPage(map);
        // 获取并设置检索条件
        QueryWrapper<Channel> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_time");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @PostMapping("save")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.CHANNEL_INFO + COMMA + PermissionCode.CHANNEL_INFO_UPDATE + SUFFIX)
    public RestResponse saveChannel(@RequestBody Channel companyInfo) {
        // 新增将id置空
        companyInfo.setId(null);
        boolean status;
        try {
            status = service.saveOrUpdate(companyInfo);
        } catch (Exception e) {
            return RestResponse.failedMessage("新增渠道失败.请检查渠道是否已存在，或信息有误");
        }
        if (!status) {
            return RestResponse.failedMessage("新增渠道信息保存失败.");
        }
        return RestResponse.success();
    }


    @RequestMapping(value = "setstatus")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.CHANNEL_INFO + COMMA + PermissionCode.CHANNEL_INFO_UPDATE + SUFFIX)
    public RestResponse resetStatus(@RequestParam int status, @RequestParam Long id, Authentication authentication) {
        if (!(status == CommStatus.DISABLED.getCode() || status == CommStatus.ENABLE.getCode())) {
            return RestResponse.failedMessage("不合法操作");
        }
        Channel channel = service.getById(id);
        if (channel == null) {
            return RestResponse.failedMessage("当前渠道不存在");
        }
        log.info("[ChannelController resetStatus] user modify role enable, params: status:{}, id, {}, operateName:{}",
                status, id, authentication.getName());
        channel.setStatus(CommStatus.get(status));
        channel.setUpdateTime(LocalDateTime.now());
        service.updateById(channel);
        return RestResponse.success();
    }


    @RequestMapping(value = "delete/{id}", method = RequestMethod.GET)
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.CHANNEL_INFO + COMMA + PermissionCode.CHANNEL_INFO_UPDATE + SUFFIX)
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        Channel channel = service.getById(id);
        if (channel == null) {
            return RestResponse.failedMessage("当前渠道不存在");
        }
        log.info("[ChannelController delete] user delete role, params, id: {}, operateName: {}",
                id, authentication.getName());
        service.removeById(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "update")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.CHANNEL_INFO + COMMA + PermissionCode.CHANNEL_INFO_UPDATE + SUFFIX)
    public RestResponse update(@RequestBody Channel channel) {
        if (channel == null) {
            return RestResponse.failedMessage("请填写渠道相关信息");
        } else if (channel.getId() == null) {
            return RestResponse.failedMessage("请选择需要更新的渠道信息");
        }
        channel.setUpdateTime(LocalDateTime.now());
        boolean status = service.saveOrUpdate(channel);
        if (!status) {
            return RestResponse.failedMessage("更新渠道信息失败.");
        }
        return RestResponse.success();
    }

}

