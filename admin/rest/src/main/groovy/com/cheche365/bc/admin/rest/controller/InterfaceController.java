package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.InterfaceService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.cheche365.bc.constants.Constants.INTERFACE_CACHE_KEY_PREFIX;
import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@RequestMapping("/interface")
@AllArgsConstructor
public class InterfaceController extends BaseController<InterfaceService,Interface> {

    private final StringRedisTemplate redisTemplate;

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.INTERFACE + COMMA + PermissionCode.INTERFACE_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.INTERFACE + COMMA + PermissionCode.INTERFACE_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse saveInterface(@RequestBody Interface interfaceNew, Authentication authentication) {
        InsCompanyEnum ice = InsCompanyEnum.get(Integer.parseInt(interfaceNew.getComId()));
        if(ice != null) {
            interfaceNew.setComCode(ice.toString());
        } else {
            return RestResponse.failedMessage("该保险公司ID未识别，请确认是否输入正确！");
        }
        boolean ret = service.saveOrUpdate(interfaceNew);
        if(ret){
            redisTemplate.convertAndSend(INTERFACE_CACHE_KEY_PREFIX, interfaceNew.getIntType() + "-" + interfaceNew.getComId() + "-" + interfaceNew.getAction());
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.INTERFACE + COMMA + PermissionCode.INTERFACE_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateInterface(@RequestBody Interface interfaceNew, Authentication authentication) {
        InsCompanyEnum ice = InsCompanyEnum.get(Integer.parseInt(interfaceNew.getComId()));
        if(ice != null) {
            interfaceNew.setComCode(ice.toString());
        } else {
            return RestResponse.failedMessage("该保险公司ID未识别，请确认是否输入正确！");
        }
        boolean ret = service.saveOrUpdate(interfaceNew);
        if(ret){
            redisTemplate.convertAndSend(INTERFACE_CACHE_KEY_PREFIX, interfaceNew.getIntType() + "-" + interfaceNew.getComId() + "-" + interfaceNew.getAction());
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.INTERFACE + COMMA + PermissionCode.INTERFACE_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        Interface itf = service.getById(id);
        if(itf != null && service.removeById(id)){
            redisTemplate.convertAndSend(INTERFACE_CACHE_KEY_PREFIX, itf.getIntType() + "-" + itf.getComId() + "-" + itf.getAction());
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }
    }
}

