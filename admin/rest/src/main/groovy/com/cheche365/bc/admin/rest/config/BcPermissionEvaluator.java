package com.cheche365.bc.admin.rest.config;

import com.cheche365.bc.admin.service.constant.SecurityConstants;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * Created by ya<PERSON><PERSON> on 2019/4/15 12:00
 */
@Component
public class BcPermissionEvaluator implements PermissionEvaluator {

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        String adminAuthority = authentication.getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .filter(SecurityConstants.ROLE_ADMIN::equals)
                .findAny()
                .orElse(null);
        if (adminAuthority != null) {
            return true;
        }
        boolean accessable = false;
        String privilege = targetDomainObject + "-" + permission;
        for (GrantedAuthority authority : authentication.getAuthorities()) {
            if (privilege.equalsIgnoreCase(authority.getAuthority())) {
                accessable = true;
                break;
            }
        }

        return accessable;

    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return false;
    }
}
