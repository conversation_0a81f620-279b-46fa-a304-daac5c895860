package com.cheche365.bc.admin.rest.controller;


import com.cheche365.bc.admin.service.service.MenuInfoService;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.MenuInfo;
import com.cheche365.bc.entity.MenuPermission;
import com.cheche365.bc.model.RestResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-12
 */
@Log4j2
@RestController
@RequestMapping("/menuInfo")
public class MenuInfoController extends BaseController<MenuInfoService, MenuInfo> {

    @RequestMapping(value = "allMenuAndPermission")
    public RestResponse allMenuAndPermission(Authentication authentication) {

        List<MenuPermission> list = service.allMenuAndPermission();

        return RestResponse.success(list);
    }
}

