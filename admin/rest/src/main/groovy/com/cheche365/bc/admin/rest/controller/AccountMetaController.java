package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.service.AccountMetaService;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.AccountMeta;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 * 账号元数据控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@RestController
@RequestMapping("/accountMeta")
@Slf4j
@AllArgsConstructor
public class AccountMetaController extends BaseController<AccountMetaService, AccountMeta> {

    private final AccountMetaService accountMetaService;

    @GetMapping("")
    @Override
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_META + COMMA + PermissionCode.ACCOUNT_META_GETPAGE + SUFFIX)
    public RestResponse getPage(@RequestParam java.util.Map<String, String> map, Authentication authentication) {
        Page<AccountMeta> page = createPage(map);
        // 获取并设置检索条件
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<AccountMeta> wrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        wrapper.orderByDesc("create_time");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @PostMapping("save")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_META + COMMA + PermissionCode.ACCOUNT_META_UPDATE + SUFFIX)
    public RestResponse saveAccountMeta(@RequestBody AccountMeta accountMeta) {
        // 新增将id置空
        accountMeta.setId(null);
        boolean status;
        try {
            status = service.saveOrUpdate(accountMeta);
        } catch (Exception e) {
            return RestResponse.failedMessage("新增账号元数据失败.请检查账号元数据是否已存在，或信息有误");
        }
        if (!status) {
            return RestResponse.failedMessage("新增账号元数据信息保存失败.");
        }
        return RestResponse.success();
    }


    @RequestMapping(value = "setstatus")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_META + COMMA + PermissionCode.ACCOUNT_META_UPDATE + SUFFIX)
    public RestResponse resetStatus(@RequestParam int status, @RequestParam Long id, Authentication authentication) {
        if (!(status == CommStatus.DISABLED.getCode() || status == CommStatus.ENABLE.getCode())) {
            return RestResponse.failedMessage("不合法操作");
        }
        AccountMeta accountMeta = service.getById(id);
        if (accountMeta == null) {
            return RestResponse.failedMessage("当前账号元数据不存在");
        }
        log.info("[AccountMetaController resetStatus] user modify role enable, params: status:{}, id, {}, operateName:{}",
                status, id, authentication.getName());
        accountMeta.setUpdateTime(LocalDateTime.now());
        service.updateById(accountMeta);
        return RestResponse.success();
    }


    @RequestMapping(value = "delete/{id}", method = RequestMethod.GET)
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_META + COMMA + PermissionCode.ACCOUNT_META_UPDATE + SUFFIX)
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        AccountMeta accountMeta = service.getById(id);
        if (accountMeta == null) {
            return RestResponse.failedMessage("当前账号元数据不存在");
        }
        log.info("[AccountMetaController delete] user delete role, params, id: {}, operateName: {}",
                id, authentication.getName());
        service.removeById(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "update")
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ACCOUNT_META + COMMA + PermissionCode.ACCOUNT_META_UPDATE + SUFFIX)
    public RestResponse update(@RequestBody AccountMeta accountMeta) {
        if (accountMeta == null) {
            return RestResponse.failedMessage("请填写账号元数据相关信息");
        } else if (accountMeta.getId() == null) {
            return RestResponse.failedMessage("请选择需要更新的账号元数据信息");
        }
        accountMeta.setUpdateTime(LocalDateTime.now());
        boolean status = service.saveOrUpdate(accountMeta);
        if (!status) {
            return RestResponse.failedMessage("更新账号元数据信息失败.");
        }
        return RestResponse.success();
    }

}

