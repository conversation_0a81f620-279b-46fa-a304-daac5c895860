package com.cheche365.bc.admin.rest.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.rest.security.ManageCommonSecurityUser;
import com.cheche365.bc.admin.service.service.UserInfoService;
import com.cheche365.bc.admin.service.service.UserLogService;
import com.cheche365.bc.admin.service.service.UserRoleService;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.service.constant.SecurityConstants;
import com.cheche365.bc.admin.service.constant.UserLogConstants;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.MenuInfo;
import com.cheche365.bc.entity.UserInfo;
import com.cheche365.bc.entity.UserRole;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.OperateContent;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;


/**
 * Created by luocong on 2019/01/11.
 */
@RestController
@RequestMapping("userinfo")
@AllArgsConstructor
public class UserInfoController extends BaseController<UserInfoService, UserInfo> {

    private final UserRoleService userRoleService;
    private final FindByIndexNameSessionRepository sessionRepository;
    private final UserLogService userLogService;

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_INFO + COMMA + PermissionCode.USERINFO_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<UserInfo> page = createPage(map);
        QueryWrapper<UserInfo> wrapper = getQueryWrapper(map, authentication);
        wrapper.lambda().ne(UserInfo::getEmail, SecurityConstants.USER_ADMIN);
        page.setRecords(service.pageList(wrapper, map, page));
        return RestResponse.success(page);
    }

    @Override
    @RequestMapping(value = "{id}", method = RequestMethod.GET)
    public RestResponse getType(@PathVariable Long id) {
        if (service.getById(id) == null) {
            return RestResponse.failedMessage("未查询到该用户");
        }
        return RestResponse.success(service.getUserInfoById(id));
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_INFO + COMMA + PermissionCode.USERINFO_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse saveUser(@RequestBody UserInfo userInfo, Authentication authentication) {
        return service.saveOrUpdateUser(userInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo());
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_INFO + COMMA + PermissionCode.USERINFO_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateUser(@RequestBody UserInfo userInfo, Authentication authentication) {
        if (userInfo.getId() == null) {
            return RestResponse.failedMessage("缺少必要的对象id");
        }
        return service.saveOrUpdateUser(userInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo());
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.USER_INFO + COMMA + PermissionCode.USERINFO_CHANGE_STATUS + SUFFIX)
    @RequestMapping(value = "changestatus/{id}", method = RequestMethod.GET)
    public RestResponse changeStatus(@PathVariable Long id) {
        service.changeStatus(id);
        return RestResponse.success();
    }

    @RequestMapping(value = "updatepassword")
    public RestResponse updatePassword(@RequestBody JSONObject object, Authentication authentication) {
        String userName = authentication.getName();
        RestResponse restResponse = service.updatePassword(object, userName);

        ManageCommonSecurityUser securityUser = (ManageCommonSecurityUser) authentication.getPrincipal();
        securityUser.getUserInfo().setChangePassword(false);

        Map<String, Session> sessions = sessionRepository.findByPrincipalName(authentication.getName());

        sessions.values().forEach(session -> {
            SecurityContextImpl securityContext = session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
            securityContext.setAuthentication(authentication);
            session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, securityContext);
            sessionRepository.save(session);
        });

        return restResponse;
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.USER_INFO + COMMA + PermissionCode.USERINFO_ALL + SUFFIX)
    @RequestMapping(value = "all", method = RequestMethod.GET)
    @Override
    public RestResponse all(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.all(map, authentication);
    }

    @RequestMapping(value = "listusermenu", method = {RequestMethod.GET, RequestMethod.POST})
    public RestResponse listUserMenu(Authentication authentication) {
        List<MenuInfo> menuList = ((ManageCommonSecurityUser) authentication.getPrincipal()).getMenuList();
        if (CollectionUtils.isEmpty(menuList)) {
            return RestResponse.failed("此用户角色下未分配菜单");
        }
        List<MenuInfo> menuInfoList = menuList.stream().filter(menuInfo -> menuInfo.getLevel() == 1).sorted(Comparator.comparing(MenuInfo::getSort)).collect(Collectors.toList());
        menuInfoList.forEach(menuInfo -> {
            menuInfo.setChildList(menuList.stream().filter(menu ->
                            menuInfo.getId().equals(menu.getParentId()) && !menu.getId().equals(menuInfo.getId()))
                    .sorted(Comparator.comparing(MenuInfo::getSort))
                    .collect(Collectors.toList()));
        });

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("menuList", menuInfoList);
        return RestResponse.success(resultMap);
    }

    @GetMapping("currentUser")
    public RestResponse currentUser(Authentication authentication) {
        try {
            ManageCommonSecurityUser securityUser = (ManageCommonSecurityUser) authentication.getPrincipal();
            UserInfo userInfo = securityUser.getUserInfo();
            userInfo.setPermissionList(securityUser.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()));
            userInfo.setPassword(null);
            return RestResponse.success(userInfo);
        } catch (Exception e) {
            return new RestResponse(-1, "请重新登陆！");
        }
    }

    @GetMapping("menus")
    public RestResponse menus(Authentication authentication) {
        try {
            List<MenuInfo> menuList = ((ManageCommonSecurityUser) authentication.getPrincipal()).getMenuList();
            return RestResponse.success(menuList);
        } catch (Exception e) {
            return new RestResponse(-1, "请重新登陆！");
        }
    }

    @PostMapping("login/account")
    public RestResponse getAccountInfo() {
        Map<String, Object> content = new HashMap<>();
        content.put("type", "account");
        content.put("currentAuthority", "admin");
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return new RestResponse<>(1, "成功", content);
    }

    /**
     * 启用禁用用户
     *
     * @param id             用户id
     * @param status         1：启用 | 0：禁用
     * @param authentication
     * @return
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_INFO + COMMA + PermissionCode.USERINFO_SET_STATUS + SUFFIX)
    @GetMapping("setstatus")
    public RestResponse resetStatus(@RequestParam Long id, @RequestParam int status, Authentication authentication) {
        UserInfo userInfo = service.getById(id);
        if (userInfo == null) {
            return RestResponse.failedMessage("当前用户不存在");
        }

        CommStatus commStatus = CommStatus.get(status);
        userInfo.setStatus(commStatus);
        userInfo.setUpdateTime(LocalDateTime.now());
        service.updateById(userInfo);

        OperateContent operateContent = commStatus == CommStatus.ENABLE ? OperateContent.ENABLE : OperateContent.DISABLE;
        String template = commStatus == CommStatus.ENABLE ? UserLogConstants.USER_INFO_ENABLE : UserLogConstants.USER_INFO_DISABLE;
        userLogService.saveUserLog(userInfo,
                ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo(),
                operateContent,
                UserLogConstants.getUserLogNote(template, authentication.getName(), userInfo.getName(), userInfo.getEmail())
        );
        return RestResponse.success();
    }

    /**
     * 删除用户
     *
     * @param id
     * @param authentication
     * @return
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_INFO + COMMA + PermissionCode.USERINFO_DELETE_BY_ID + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        UserInfo userInfo = service.getById(id);
        if (userInfo == null) {
            return RestResponse.failedMessage("当前用户不存在");
        }

        service.removeById(id);
        userRoleService.remove(new QueryWrapper<UserRole>().lambda().eq(UserRole::getUser, id));
        userLogService.saveUserLog(userInfo,
                ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo(),
                OperateContent.DELETE,
                UserLogConstants.getUserLogNote(UserLogConstants.USER_INFO_DELETE, authentication.getName(), userInfo.getName(), userInfo.getEmail())
        );
        return RestResponse.success();
    }

}
