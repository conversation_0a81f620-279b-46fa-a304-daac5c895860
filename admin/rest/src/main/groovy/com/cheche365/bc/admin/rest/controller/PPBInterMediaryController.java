package com.cheche365.bc.admin.rest.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.admin.service.service.PPBInterMediaryService;
import com.cheche365.bc.entity.PPBInterMediary;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;


@RestController
@Slf4j
@AllArgsConstructor
public class PPBInterMediaryController {

    private final PPBInterMediaryService ppbInterMediaryService;

    /**
     * 澎湃保新增中介机构
     *
     * @param ppbInterMediary
     * @return
     * @throws Exception
     */

    @ResponseBody
    @PostMapping(value = "/api/ppb/save")
    public Object insertInterMediary(@RequestBody PPBInterMediary ppbInterMediary) throws Exception {
        ppbInterMediary.setCreateTime(LocalDateTime.now());
        try {
            ppbInterMediaryService.setInterMediary(ppbInterMediary);
        } catch (Exception e) {
            throw new Exception("新增中介数据异常：" + e.getMessage());
        }
        PPBInterMediary interMediary = ppbInterMediaryService.findInterMediaryFromNo(ppbInterMediary.getIntermediaryNo());
        return JSONObject.toJSONString(interMediary);

    }

    /**
     * 澎湃保中介机构查询
     *
     * @param
     * @return
     * @throws Exception
     */

    @ResponseBody
    @PostMapping(value = "/api/ppb/find")
    public Object getInterMediary(@RequestParam String comId) {
        //获取中介许可证
        java.util.List<PPBInterMediary> interMediarys = ppbInterMediaryService.findInterMediaryNoFromComId(comId);
        log.info("总条数：{},列表：{}", interMediarys.size(), JSONArray.toJSONString(interMediarys));
        return JSONArray.toJSONString(interMediarys);
    }
}

