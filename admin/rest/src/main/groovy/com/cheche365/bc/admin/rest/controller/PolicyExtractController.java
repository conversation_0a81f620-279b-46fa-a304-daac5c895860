package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.PolicyDetailInfo;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.PolicySimplifyInfoService;
import com.cheche365.bc.tools.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 * 抓取保单列表控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
@RestController
@RequestMapping("/policy")
@AllArgsConstructor
@PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.POLICY_LIST + COMMA + PermissionCode.POLICYLIST_GETPAGE + SUFFIX)
public class PolicyExtractController extends BaseController<PolicySimplifyInfoService, PolicySimplifyInfo> {

    private final MongoTemplate mongoTemplate;
    private final PolicySimplifyInfoService policySimplifyInfoService;

    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<PolicySimplifyInfo> page = createPage(map);
        // 获取并设置检索条件
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<PolicySimplifyInfo> wrapper = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        wrapper.orderByDesc("underwrite_date");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @GetMapping("detail/{id}")
    public PolicyDetailInfo getPolicyDetailInfo(@PathVariable String id) {
        return mongoTemplate.findById(id, PolicyDetailInfo.class);
    }

    @GetMapping("getRunningTask")
    public RestResponse getRunningTask() {
        return RestResponse.successMessage(policySimplifyInfoService.getAllowRunningAccountList().toString());
    }

    //{"account":"353978","startDate":"2021-01-22","endDate":"2021-01-22"}
    @PostMapping("delete")
    public List<String> delete(@RequestBody Map<String, String> params) {
        List<PolicySimplifyInfo> policyList = service.findPolicyNoListFromTime(params.get("account"), LocalDate.parse(params.get("startDate")), LocalDate.parse(params.get("endDate")));
        List<String> result = new ArrayList<>();
        policyList.forEach(policy -> {
            service.removeById(policy.getId());
            if (StringUtil.isNoEmpty(policy.getDetailId())) {
                mongoTemplate.remove(Objects.requireNonNull(mongoTemplate.findById(policy.getDetailId(), PolicyDetailInfo.class)));
            }
            result.add(policy.getPolicyNo());
        });
        return result;
    }

}

