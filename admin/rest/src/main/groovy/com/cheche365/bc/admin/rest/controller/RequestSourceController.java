package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.service.RequestSourceService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @since 2022-06-20
 */
@RestController
@RequestMapping(value = "requestSource")
@AllArgsConstructor
public class RequestSourceController {

    private final RequestSourceService requestSourceService;

    @GetMapping(value = "treeList")
    @ResponseBody
    public List<Map<String, Object>> treeList() {
        return requestSourceService.getTreeList();
    }

}
