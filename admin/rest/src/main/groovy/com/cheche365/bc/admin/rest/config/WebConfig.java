package com.cheche365.bc.admin.rest.config;

import com.cheche365.bc.admin.rest.intercept.LogInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.MultipartConfigElement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisIndexedHttpSession;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.session.web.http.DefaultCookieSerializer;
import org.springframework.util.unit.DataSize;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.List;

@EnableWebMvc
@Configuration
@ComponentScan("com.cheche365.bc")
@EnableRedisIndexedHttpSession(redisNamespace = "spring:session:baichuan", maxInactiveIntervalInSeconds = 36000)
@Slf4j
public class WebConfig implements WebServerFactoryCustomizer<UndertowServletWebServerFactory> {


    @Value("${cookie-name}")
    private String cookieName;

    @Autowired
    private LogInterceptor logInterceptor;

    @Autowired
    private ChannelArgumentResolver channelArgumentResolver;

    @Autowired
    private ObjectMapper objectMapper;

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setFileSizeThreshold(DataSize.ofMegabytes(2));
        factory.setMaxFileSize(DataSize.ofMegabytes(100));
        return factory.createMultipartConfig();
    }

    /**
     * 自定义cookie序列化,用于session共享
     *
     * @return the cookie serializer
     */
    @Bean
    public CookieSerializer cookieSerializer() {
        DefaultCookieSerializer serializer = new DefaultCookieSerializer();
        serializer.setCookieName(cookieName);
        serializer.setCookiePath("/");
        serializer.setDomainNamePattern("^.+?\\.(\\w+\\.[a-z]+)$");
        // 开启跨域cookie发送
        serializer.setSameSite(null);
        return serializer;
    }

    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(logInterceptor).addPathPatterns("/**");
            }

            @Override
            public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
                argumentResolvers.add(channelArgumentResolver);
            }

            @Override
            public void addViewControllers(ViewControllerRegistry registry) {
                registry.addViewController("/").setViewName("forward:/index.html");
            }

            @Override
            public void addResourceHandlers(ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/**")
                        .addResourceLocations("classpath:/static/");
            }

            @Override
            public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
                converters.removeIf((x) -> x instanceof StringHttpMessageConverter || x instanceof AbstractJackson2HttpMessageConverter);
                converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
                converters.add(new MappingJackson2HttpMessageConverter(objectMapper));
            }
        };
    }

    @Override
    public void customize(UndertowServletWebServerFactory factory) {

    }
}
