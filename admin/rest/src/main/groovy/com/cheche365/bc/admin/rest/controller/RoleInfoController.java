package com.cheche365.bc.admin.rest.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.admin.rest.security.ManageCommonSecurityUser;
import com.cheche365.bc.admin.service.service.RoleInfoService;
import com.cheche365.bc.admin.service.service.RolePermissionService;
import com.cheche365.bc.admin.service.service.UserLogService;
import com.cheche365.bc.admin.service.service.UserRoleService;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.service.constant.UserLogConstants;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.RoleInfo;
import com.cheche365.bc.entity.RolePermission;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.OperateContent;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * Created by luocong on 2019/01/11.
 */
@Slf4j
@RestController
@RequestMapping(value = "roleinfo")
@AllArgsConstructor
public class RoleInfoController extends BaseController<RoleInfoService, RoleInfo> {

    private final UserRoleService userRoleService;
    private final UserLogService userLogService;
    private final RolePermissionService rolePermissionService;

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_ROLE_INFO + COMMA + PermissionCode.ROLEINFO_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }

    @RequestMapping(value = "rolelistbyuser")
    public RestResponse<Map> getRoleListByUser(@RequestBody JSONObject object) {
        // 用户角色
        List<String> listRoleCode = null;
        String id = object.getString("id");
        if (StringUtils.isNotEmpty(id)) {
            listRoleCode = userRoleService.listRoleCodeByUserInfo(Long.parseLong(id), CommStatus.ENABLE.getCode())
                    .stream().map(RoleInfo::getCode).collect(Collectors.toList());
        } else {
            listRoleCode = new ArrayList<>();
        }
        // 角色列表
        List<RoleInfo> listRole = service.list(new QueryWrapper<RoleInfo>().eq("status", CommStatus.ENABLE.getCode()));
        Map<String, Object> map = new HashMap<>(2);
        map.put("listRole", listRole);
        map.put("listRoleCode", listRoleCode);
        return RestResponse.success(map);
    }

    /**
     * 角色启用或者禁用
     *
     * @param status         1 启用  | 0 禁用
     * @param id
     * @param authentication
     * @return
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_ROLE_INFO + COMMA + PermissionCode.ROLEINFO_SET_STATUS + SUFFIX)
    @RequestMapping(value = "setstatus")
    public RestResponse resetStatus(@RequestParam int status, @RequestParam Long id, Authentication authentication) {
        if (!(status == CommStatus.DISABLED.getCode() || status == CommStatus.ENABLE.getCode())) {
            return RestResponse.failedMessage("不合法操作");
        }

        RoleInfo roleInfo = service.getById(id);
        if (roleInfo == null) {
            return RestResponse.failedMessage("当前角色不存在");
        }

        log.info("[RoleInfoController resetStatus] user modify role enable, params: status:{}, id, {}, operateName:{}",
                status, id, authentication.getName());
        CommStatus commStatus = CommStatus.get(status);
        roleInfo.setStatus(commStatus);
        roleInfo.setUpdateTime(LocalDateTime.now());
        service.updateById(roleInfo);

        OperateContent operateContent = commStatus == CommStatus.ENABLE ? OperateContent.ENABLE : OperateContent.DISABLE;
        String note = commStatus == CommStatus.ENABLE ?
                UserLogConstants.ROLE_INFO_ENABLE : UserLogConstants.ROLE_INFO_DISABLE;
        userLogService.saveRoleLog(roleInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo(),
                operateContent, UserLogConstants.getUserLogNote(note, authentication.getName(), roleInfo.getName()));
        return RestResponse.success();
    }

    /**
     * 删除角色
     *
     * @param id
     * @param authentication
     * @return
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_ROLE_INFO + COMMA + PermissionCode.ROLEINFO_DELETE + SUFFIX)
    @RequestMapping(value = "delete/{id}", method = RequestMethod.GET)
    public RestResponse delete(@PathVariable Long id, Authentication authentication) {
        RoleInfo roleInfo = service.getById(id);
        if (roleInfo == null) {
            return RestResponse.failedMessage("当前角色不存在");
        }
        log.info("[RoleInfoController delete] user delete role, params, id: {}, operateName: {}",
                id, authentication.getName());
        service.removeById(id);
        rolePermissionService.remove(new QueryWrapper<RolePermission>().lambda().eq(RolePermission::getRoleId, id));
        userLogService.saveRoleLog(roleInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo(),
                OperateContent.DELETE, UserLogConstants.getUserLogNote(UserLogConstants.ROLE_INFO_DELETE,
                        authentication.getName(), roleInfo.getName()));
        return RestResponse.success();
    }

    /**
     * 添加
     *
     * @param roleInfo
     * @return
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_ROLE_INFO + COMMA + PermissionCode.ROLEINFO_SAVE + SUFFIX)
    @RequestMapping(value = "save")
    public RestResponse save(@RequestBody RoleInfo roleInfo, Authentication authentication) {
        if (roleInfo == null) {
            return RestResponse.failedMessage("请填写角色相关信息");
        }
        // 新增将id置空
        roleInfo.setId(null);
        RestResponse check = service.checkRole(roleInfo);
        if (!check.isSuccess()) {
            return check;
        }

        boolean status = service.addOrUpdate(roleInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo());
        if (!status) {
            return RestResponse.failedMessage("新增角色信息保存失败.");
        }
        return RestResponse.success();
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_ROLE_INFO + COMMA + PermissionCode.ROLEINFO_UPDATE + SUFFIX)
    @RequestMapping(value = "update")
    public RestResponse update(@RequestBody RoleInfo roleInfo, Authentication authentication) {
        if (roleInfo == null) {
            return RestResponse.failedMessage("请填写角色相关信息");
        } else if (roleInfo.getId() == null) {
            return RestResponse.failedMessage("请选择需要更新的角色信息");
        }
        RestResponse check = service.checkRole(roleInfo);
        if (!check.isSuccess()) {
            return check;
        }
        boolean status = service.addOrUpdate(roleInfo, ((ManageCommonSecurityUser) authentication.getPrincipal()).getUserInfo());
        if (!status) {
            return RestResponse.failedMessage("更新角色信息失败.");
        }
        return RestResponse.success();
    }

    /**
     * 验证角色信息
     *
     * @param roleInfo
     * @return
     */
    @RequestMapping(value = "checkRole")
    public RestResponse checkRole(@RequestBody RoleInfo roleInfo) {
        return service.checkRole(roleInfo);
    }

    /**
     * 获取权限信息
     *
     * @param id
     * @return
     */
    @Override
    @RequestMapping(value = "{id}", method = RequestMethod.GET)
    public RestResponse getType(@PathVariable Long id) {
        RoleInfo info = service.getById(id);
        if (info == null) {
            return RestResponse.failedMessage("未查询到该角色信息");
        }

        return RestResponse.success(info);
    }

    @GetMapping(value = "allroles")
    public RestResponse allRoles() {
        return RestResponse.success(service.list(new QueryWrapper<RoleInfo>()
                .eq("status", CommStatus.ENABLE.getCode())
                .last("order by id asc")
        ));
    }
}
