package com.cheche365.bc.admin.rest.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.web.util.matcher.RequestMatcher;

public class AjaxRequestMatcher implements RequestMatcher {
    @Override public boolean matches(HttpServletRequest request) {
        return "xmlHttpRequest".equals(request.getHeader("X-Requested-With")) || request.getHeader("Accept") != null && request.getHeader("Accept").contains("application/json");
    }
}
