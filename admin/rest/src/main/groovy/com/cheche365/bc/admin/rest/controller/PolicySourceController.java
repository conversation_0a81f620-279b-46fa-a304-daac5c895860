package com.cheche365.bc.admin.rest.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.PolicySource;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.PolicySourceService;
import com.cheche365.bc.utils.sdas.SDASUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <AUTHOR>
 * @date 2022年10月21日 11:02
 * @description
 */
@RestController
@RequestMapping(value = "/policySource")
public class PolicySourceController extends BaseController<PolicySourceService, PolicySource> {

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_POLICYSOURCE + COMMA + PermissionCode.POLICYSOURCE_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        if (map.get("policyNo") != null) {
            map.put("policyNo", new StringBuilder(map.get("policyNo")).reverse().toString());
        }
        Page<PolicySource> page = createPage(map);
        QueryWrapper<PolicySource> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("create_time");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        List<PolicySource> policySourceList = page.getRecords();
        if (policySourceList != null && policySourceList.size() > 0) {
            policySourceList.forEach(policySource -> {
                policySource.setPolicyNo(new StringBuilder(policySource.getPolicyNo()).reverse().toString());
            });
            page.setRecords(policySourceList);
        }
        return RestResponse.success(page);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_POLICYSOURCE + COMMA + PermissionCode.POLICYSOURCE_DELETE + SUFFIX)
    @RequestMapping( "delete/{id}")
    @Override
    public RestResponse delete(@PathVariable Long id) {
        return super.delete(id);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_POLICYSOURCE + COMMA + PermissionCode.POLICYSOURCE_GETPOLICY + SUFFIX)
    @RequestMapping( "getPolicy/{sourceId}")
    public RestResponse look(@PathVariable String sourceId) {
        String location = SDASUtils.getLocationById(sourceId);
        if (StringUtils.isNotBlank(location)) {
            return RestResponse.success(location);
        } else {
            return RestResponse.failed();
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_POLICYSOURCE + COMMA + PermissionCode.POLICYSOURCE_DELETE_BATCH + SUFFIX)
    @RequestMapping("deleteBatch")
    public RestResponse deleteBatch(@RequestBody JSONObject jsonObject) {
        boolean flag = service.remove(new QueryWrapper<PolicySource>().in("id", (List) jsonObject.get("selectedList")));
        if (flag) {
            return RestResponse.success();
        } else {
            return RestResponse.failed();
        }
    }
}
