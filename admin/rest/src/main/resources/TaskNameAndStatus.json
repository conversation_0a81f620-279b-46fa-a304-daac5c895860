[{"code": "quote", "name": "报价", "order": 1, "successStatus": "A", "failedStatus": "A1"}, {"code": "quotequery", "name": "报价查询", "order": 2, "successStatus": "6", "failedStatus": "7"}, {"code": "insure", "name": "核保暂存", "order": 3, "successStatus": "11", "failedStatus": "12"}, {"code": "autoinsure", "name": "自动核保", "order": 4, "successStatus": "32", "failedStatus": "33"}, {"code": "insurequery", "name": "核保查询", "order": 5, "successStatus": "14", "failedStatus": "15"}, {"code": "identifyVerify", "name": "电子投保确认", "order": 6, "successStatus": "10000", "failedStatus": "10001"}, {"code": "qrcode_insurequery", "name": "支付申请", "order": 7, "successStatus": "18", "failedStatus": "17"}, {"code": "getSmsForPay", "name": "发送支付验证码", "order": 8, "successStatus": "10000", "failedStatus": "10001"}, {"code": "qrcode_approvedquery", "name": "支付结果查询", "order": 9, "successStatus": "20", "failedStatus": "21"}, {"code": "approvedquery", "name": "承保查询", "order": 10, "successStatus": "20", "failedStatus": "21"}, {"code": "policyDownload", "name": "电子保单下载", "order": 11, "successStatus": "10000", "failedStatus": "10001"}, {"code": "insure_callback", "name": "核保回调", "order": 12, "successStatus": "B", "failedStatus": "B1"}, {"code": "approved_callback", "name": "承保回调", "order": 13, "successStatus": "D", "failedStatus": "D1"}, {"code": "insurequery_callback", "name": "EDI 核保查询", "order": 14, "successStatus": "14", "failedStatus": "15"}, {"code": "approvedquery_callback", "name": "EDI 承保查询", "order": 15, "successStatus": "20", "failedStatus": "21"}, {"code": "queryVehicles", "name": "查车", "order": 16, "successStatus": "10000", "failedStatus": "10001"}, {"code": "policyquery", "name": "线下保单查询", "order": 17, "successStatus": "20", "failedStatus": "21"}]