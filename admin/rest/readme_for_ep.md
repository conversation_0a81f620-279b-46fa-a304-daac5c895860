#一.保单抓取项目简介
    改项目是一个定时+接口形式抓单系统，基于SpringBoot+线程池实现。
    定时任务，依据 xxl-job 分布式定时任务调度系统来做到定时抓取保单的作用（https://www.xuxueli.com/xxl-job/）
    [详见](https://cc.chetimes.com:5100/xxl-job-admin/jobinfo?jobGroup=7)
    定时任务简介 
        都可以根据 xxl-job 中执行的 param 参数定制化些流程需求，详见代码
        1.edi保单回调通知	BEAN：ediPolicyCallbackJobHandler 用于澎湃保抓取 edi 承保单信息

### 项目核心功能简介（主要在 com.cheche365.bc.jobhandler 包下)
    一. XxlJobConfig：只有在 配置文件中配置了 schedule.xxl.job.admin 定时任务才会启动。 
        1.CallbackJobHandler 该定时任务主要用于 edi 保单回写用(见 一.3)
    二.ApiController 该类主要用于对外接口调用使用，包含了磐石同步账号信息，澎湃保实时抓取保单信息，等待接口
       ApiSecureAdvice 该类主要用于配合 ApiController 增加一些鉴权，访问次数限制等功能。     
### 项目用到的一系列地址
    保单抓取 测试环境 dev2
        定时任务地址：https://dev2.cc.chetimes.com:5100/xxl-job-admin/
        admin  dyeQbLv7
        日志地址：
        http://dev4.cc.chetimes.com:8000/river_service_qa/
        登陆地址
        http://dev2.river.chetimes.com:32582/index.html#/user/login
        admin Admin123
        测试环境build 地址：
        http://192.168.1.251:8080/job/dev2/view/%E7%99%BE%E5%B7%9D/job/river-ins-auto-service_ep/
        cheche
        chechebuild
        测试环境 数据库地址：
            mysql：
                ************************************* /ep_dev2?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useLegacyDatetimeCode=false&serverTimezone=CTT
              username: ep_dev2
              password: q3sunGusbc4K
            mongodb：
                dev2.cheche365.com:23717
                username:ep_dev2
                password:7qYsiwoo3aIs
            
    生产地址：
        登陆地址
        http://ep.skyinsurdata.com:32580/index.html#/user/login
        admin !BaiChuan2020#@
        定时任务地址
        https://cc.chetimes.com:5100/xxl-job-admin/jobinfo?jobGroup=7
        cheche   5Qfza9iO
        日志地址：
        http://112.126.65.191:17698/download/river_service/
            
