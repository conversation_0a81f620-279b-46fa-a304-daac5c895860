server:
  port: ${profiles_port}
  undertow:
    io-threads: ${undertow_io_threads}
    worker-threads: ${undertow_worker_threads}

spring:
  datasource:
    dynamic:
      datasource:
        bcs:
          username: ${mysql_username}
          password: ${mysql_password}
          url: jdbc:mysql://${mysql_host}:${mysql_port}/${mysql_db}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      database: ${redis_database}

schedule:
  xxl:
    job:
      admin:
        addresses: ${xxl_job_admin_addresses}
      executor:
        ip: ${xxl_job_executor_ip}
        port: ${xxl_job_executor_port}

export:
  file-path: ${web_report_file}

ep:
  domain: ${ep_domain}