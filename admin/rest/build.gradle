
dependencies {

    implementation project(':admin:admin-service')
    implementation project(':admin:admin-schedule')

    implementation "org.springframework.boot:spring-boot-starter-web"
    api "org.springframework.boot:spring-boot-starter-security"

    implementation "com.xuxueli:xxl-job-core:$xxl_version"
    implementation "com.alibaba:easyexcel:$alibaba_easyexcel_version"

    runtimeOnly("org.springframework.boot:spring-boot-starter-undertow") {
        exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
    }
}
