package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.ChannelService;
import com.cheche365.bc.entity.Channel;
import com.cheche365.bc.mapper.ChannelMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2021-04-13
 */
@Service
@Log4j2
public class ChannelInfoServiceImpl extends ServiceImpl<ChannelMapper, Channel> implements ChannelService {

    public Channel findByAppKey(String appKey) {
        return baseMapper.selectOne(new QueryWrapper<Channel>().eq("app_key", appKey));
    }

}
