package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.AutoTaskDailyStatisticsService;
import com.cheche365.bc.entity.AutoTaskDailyStatistics;
import com.cheche365.bc.mapper.AutoTaskDailyStatisticsMapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @description
 * @since 2021-12-09
 */
@Service
@AllArgsConstructor
public class AutoTaskDailyStatisticsServiceImpl
        extends ServiceImpl<AutoTaskDailyStatisticsMapper, AutoTaskDailyStatistics>
        implements AutoTaskDailyStatisticsService {

    private final AutoTaskDailyStatisticsMapper autoTaskDailyStatisticsMapper;

    @Override
    public void deleteByStatisticsDate(String statisticsDate) {
        QueryWrapper<AutoTaskDailyStatistics> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AutoTaskDailyStatistics::getStatisticsDate, statisticsDate);
        autoTaskDailyStatisticsMapper.delete(wrapper);
    }
}
