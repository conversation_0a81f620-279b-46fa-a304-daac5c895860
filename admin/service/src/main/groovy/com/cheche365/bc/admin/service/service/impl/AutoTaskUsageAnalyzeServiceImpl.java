package com.cheche365.bc.admin.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.admin.service.service.AutoTaskUsageAnalyzeService;
import com.cheche365.bc.entity.AutoTaskDailyStatistics;
import com.cheche365.bc.entity.RequestSource;
import com.cheche365.bc.mapper.AutoTaskDailyStatisticsMapper;
import com.cheche365.bc.service.RequestSourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 百川能力使用情况统计
 *
 * <AUTHOR> href="<EMAIL>>王杨</a>
 * @since 2022-12-26
 */
@Service
@RequiredArgsConstructor
public class AutoTaskUsageAnalyzeServiceImpl implements AutoTaskUsageAnalyzeService {

    private final AutoTaskDailyStatisticsMapper autoTaskDailyStatisticsMapper;

    private final RequestSourceService requestSourceService;

    private Map<String, String> companyMap;

    public void setCompanyMap(Map<String, String> companyMap) {
        this.companyMap = companyMap;
    }

    @Override
    public List<JSONObject> listByBU(Integer companyId) {
        List<JSONObject> resultList = new ArrayList<>();
        // 磐石
        resultList.addAll(getByBUName("磐石", companyId));
        // 车生态
        resultList.addAll(getByBUName("车生态", companyId));
        // 澎湃保
        resultList.addAll(getByBUName("澎湃保", companyId));
        return resultList;
    }

    @Override
    public List<JSONObject> listByProcessType(Integer companyId) {
        return processResult(totalCountList("processType", companyId, null));
    }

    @Override
    public List<JSONObject> listByInternetSales(Integer companyId) {
        return processResult(totalCountList("internetSales", companyId, null));
    }

    @Override
    public List<JSONObject> listByBUMonth(String month) {
        List<JSONObject> resultList = new ArrayList<>();
        String startDate = month + "-01";
        String endDate = LocalDate.parse(startDate)
                .plusMonths(1).minusDays(1).toString();
        // 磐石
        resultList.addAll(getByBUNameAndMonth("磐石", startDate, endDate));
        // 车生态
        resultList.addAll(getByBUNameAndMonth("车生态", startDate, endDate));
        // 澎湃保
        resultList.addAll(getByBUNameAndMonth("澎湃保", startDate, endDate));
        QueryWrapper<AutoTaskDailyStatistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("company_id companyId, SUM(total_count) totalCount")
                .eq("task_type", "quote")
                .ge("statistics_date", startDate)
                .le("statistics_date", endDate)
                .groupBy("companyId")
                .orderByDesc("totalCount");
        List<Map<String, Object>> maps = autoTaskDailyStatisticsMapper.selectMaps(queryWrapper);
        Map<String, Integer> companyOrder = new HashMap<>();
        AtomicReference<Integer> order = new AtomicReference<>(1);
        maps.forEach(item -> companyOrder.put(companyMap.get(item.get("companyId").toString()), order.getAndSet(order.get() + 1)));
        resultList.sort(Comparator.comparingInt(item -> companyOrder.get(item.get("companyName").toString())));
        return resultList;
    }

    private List<Integer> requestSourceIdListByName(String name) {
        List<Integer> requestSourceIdList = new ArrayList<>();
        QueryWrapper<RequestSource> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(RequestSource::getName, name);
        List<RequestSource> requestSourceList = requestSourceService.list(wrapper);
        requestSourceList.forEach(item -> {
            requestSourceIdList.addAll(requestSourceService.getIdListByParentId(item.getId()));
        });
        return requestSourceIdList;
    }

    private List<JSONObject> getByBUName(String name, Integer companyId) {
        List<Integer> requestSourceIdList = requestSourceIdListByName(name);
        List<Map<String, Object>> list = totalCountList("bu", companyId, requestSourceIdList);
        list.forEach(item -> item.put("type", name));
        return processResult(list);
    }

    private List<JSONObject> getByBUNameAndMonth(String name, String startDate, String endDate) {
        QueryWrapper<AutoTaskDailyStatistics> wrapper = new QueryWrapper<>();
        List<Integer> requestSourceIdList = requestSourceIdListByName(name);
        wrapper.select("SUM(total_count) totalCount, company_id companyId")
                .eq("task_type", "quote")
                .ge("statistics_date", startDate)
                .le("statistics_date", endDate)
                .in("request_source_id", requestSourceIdList)
                .groupBy("companyId");
        List<Map<String, Object>> maps = autoTaskDailyStatisticsMapper.selectMaps(wrapper);
        List<JSONObject> resultList = new ArrayList<>();
        maps.forEach(item -> {
            JSONObject json = new JSONObject();
            json.put("companyName", companyMap.get(item.get("companyId").toString()));
            json.put("totalCount", Integer.parseInt(item.get("totalCount").toString()));
            json.put("type", name);
            resultList.add(json);
        });
        return resultList;
    }

    private List<Map<String, Object>> totalCountList(String type, Integer companyId,
                                                     List<Integer> requestSourceIdList) {
        LocalDate endDate = LocalDate.now().withDayOfMonth(1).minusDays(1);
        LocalDate startDate = LocalDate.now().withDayOfMonth(1).minusYears(1);
        QueryWrapper<AutoTaskDailyStatistics> queryWrapper = new QueryWrapper<>();
        if (type.equals("bu")) {
            queryWrapper.select("SUM(total_count) totalCount, " +
                            "SUBSTRING( statistics_date, 1, 7 ) month")
                    .eq("task_type", "quote")
                    .ge("statistics_date", startDate.toString())
                    .le("statistics_date", endDate.toString())
                    .in("request_source_id", requestSourceIdList)
                    .groupBy("month");
            if (companyId != 0) {
                queryWrapper.eq("company_id", companyId);
            }
        }

        if (type.equals("processType")) { // processType
            queryWrapper.select("process_type type, SUM(total_count) totalCount, " +
                            "SUBSTRING( statistics_date, 1, 7 ) month")
                    .eq("task_type", "quote")
                    .eq("company_id", companyId)
                    .ge("statistics_date", startDate.toString())
                    .le("statistics_date", endDate.toString())
                    .groupBy("process_type, month");
        }

        if (type.equals("internetSales")) { // internetSales
            queryWrapper.select("SUM(total_count) totalCount, " +
                            "SUBSTRING( statistics_date, 1, 7 ) month, " +
                            "CASE internet_sales " +
                            "WHEN 1 THEN '网销' WHEN 0 " +
                            "THEN '车商' END AS 'type'")
                    .eq("task_type", "quote")
                    .eq("process_type", "edi")
                    .eq("company_id", companyId)
                    .ge("statistics_date", startDate.toString())
                    .le("statistics_date", endDate.toString())
                    .groupBy("internet_sales, month");
        }

        return autoTaskDailyStatisticsMapper.selectMaps(queryWrapper);
    }

    private List<JSONObject> processResult(List<Map<String, Object>> maps) {
        LocalDate endDate = LocalDate.now().withDayOfMonth(1).minusDays(1);
        LocalDate startDate = LocalDate.now().withDayOfMonth(1).minusYears(1);
        List<JSONObject> resultList = new ArrayList<>();
        List<String> monthList = new ArrayList<>();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        while (startDate.isBefore(endDate)) {
            monthList.add(startDate.format(dtf));
            startDate = startDate.plusMonths(1);
        }

        Set<String> typeList = maps.stream().map(item -> item.get("type").toString())
                .collect(Collectors.toSet());
        monthList.forEach(month -> {
            typeList.forEach(type -> {
                Optional<Map<String, Object>> first = maps.stream()
                        .filter(item -> item.get("month").toString().equals(month) &&
                                item.get("type").toString().equals(type))
                        .findFirst();
                if (first.isPresent()) {
                    Map<String, Object> map = first.get();
                    JSONObject json = new JSONObject();
                    json.put("month", map.get("month").toString().replace("-", "年") + "月");
                    json.put("type", map.get("type"));
                    json.put("totalCount", map.get("totalCount"));
                    resultList.add(json);
                } else {
                    JSONObject json = new JSONObject();
                    json.put("month", month.replace("-", "年") + "月");
                    json.put("type", type);
                    json.put("totalCount", 0);
                    resultList.add(json);
                }
            });
        });
        resultList.sort(Comparator.comparing(result -> result.getString("month")));
        return resultList;
    }

}
