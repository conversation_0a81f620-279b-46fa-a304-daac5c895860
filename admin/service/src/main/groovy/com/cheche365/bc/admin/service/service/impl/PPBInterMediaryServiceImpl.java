package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.PPBInterMediaryService;
import com.cheche365.bc.entity.PPBInterMediary;
import com.cheche365.bc.mapper.PPBInterMediaryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-24
 * @descript
 */
@Service
public class PPBInterMediaryServiceImpl extends ServiceImpl<PPBInterMediaryMapper, PPBInterMediary> implements PPBInterMediaryService {

    @Override
    public List<PPBInterMediary> findInterMediaryNoFromComId(String comId) {
        LambdaQueryWrapper<PPBInterMediary> queryWrapper = new QueryWrapper<PPBInterMediary>()
                .lambda()
                .eq(PPBInterMediary::getComId, comId)
                .eq(PPBInterMediary::getStatus,1);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void setInterMediary(PPBInterMediary ppbInterMediary) {
        baseMapper.insert(ppbInterMediary);
    }

    @Override
    public PPBInterMediary findInterMediaryFromNo(String interMediaryNo) {
        LambdaQueryWrapper<PPBInterMediary> queryWrapper = new QueryWrapper<PPBInterMediary>()
                .lambda()
                .eq(PPBInterMediary::getIntermediaryNo, interMediaryNo);
        return baseMapper.selectOne(queryWrapper);
    }
}
