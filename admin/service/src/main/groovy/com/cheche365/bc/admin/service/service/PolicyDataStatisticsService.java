package com.cheche365.bc.admin.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.PolicyDataStatistics;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhangying
 * @date 2023-01-04
 * @descript :
 */
public interface PolicyDataStatisticsService extends IService<PolicyDataStatistics> {

    PolicyDataStatistics getPolicyInfo(Map policy);

    List<PolicyDataStatistics> queryPolicyStatisticsByComId(String channelType, String startDate, String endDate);

    List<PolicyDataStatistics> queryPolicyStatisticsSumByMonth(String channelType, String startDate, String endDate);
}
