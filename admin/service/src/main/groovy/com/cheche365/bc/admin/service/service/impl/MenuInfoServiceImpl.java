package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.MenuInfoService;
import com.cheche365.bc.admin.service.service.PermissionInfoService;
import com.cheche365.bc.admin.service.service.UserRoleService;
import com.cheche365.bc.admin.service.constant.SecurityConstants;
import com.cheche365.bc.entity.MenuInfo;
import com.cheche365.bc.entity.MenuPermission;
import com.cheche365.bc.entity.PermissionInfo;
import com.cheche365.bc.entity.RoleInfo;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.MenuInfoStatus;
import com.cheche365.bc.mapper.MenuInfoMapper;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
@Service
@AllArgsConstructor
public class MenuInfoServiceImpl extends ServiceImpl<MenuInfoMapper, MenuInfo> implements MenuInfoService {

    private final UserRoleService userRoleService;
    private final PermissionInfoService permissionInfoService;

    @Override
    public List<MenuInfo> listMenuByUserId(Long userId, MenuInfoStatus menuInfoStatus) {
        List<RoleInfo> roleCodeList = userRoleService.listRoleCodeByUserInfo(userId, CommStatus.ENABLE.getCode());
        if (CollectionUtils.isEmpty(roleCodeList)) {
            return null;
        }

        //管理员角色
        RoleInfo adminRole = roleCodeList.stream().filter(roleInfo -> SecurityConstants.ROLE_CODE_ADMIN.equals(roleInfo.getCode())).findAny().orElse(null);
        if (adminRole != null) {
            return list(new QueryWrapper<MenuInfo>().eq("status", menuInfoStatus));
        }

        return baseMapper.listMenuByUserId(userId, menuInfoStatus.getStatus());
    }

    @Override
    public List<MenuPermission> allMenuAndPermission() {
        // 顶级菜单
        List<MenuInfo> rootList = super.list(new QueryWrapper<MenuInfo>()
                .lambda()
                .eq(MenuInfo::getLevel, MenuInfo.ROOT_LEVEL)
                .eq(MenuInfo::getStatus, MenuInfoStatus.ENABLE)
                .orderByAsc(MenuInfo::getSort, MenuInfo::getId));

        if (CollectionUtils.isEmpty(rootList)) {
            return Collections.emptyList();
        }

        List<MenuPermission> menuPerList = new ArrayList<>(rootList.size());
        rootList.forEach(it -> {
            MenuPermission menuPermission = MenuPermission.of(it);
            childListByMenu(menuPermission);
            menuPerList.add(menuPermission);
        });


        return menuPerList;
    }

    /**
     * 获取子菜单以及权限列表
     *
     * @param menuPermission
     */
    @Override
    public void childListByMenu(MenuPermission menuPermission) {

        if (menuPermission == null || menuPermission.getType() == MenuPermission.MenuPermissionType.PERMISSION) {
            return;
        }

        // 菜单id
        Long menuId = menuPermission.realMenuId();

        List<MenuInfo> menuInfos = super.list(new QueryWrapper<MenuInfo>()
                .lambda()
                .eq(MenuInfo::getParentId, menuId)
                .ne(MenuInfo::getId, menuId) // 防止自己查询自己, 无限递归
                .eq(MenuInfo::getStatus, MenuInfoStatus.ENABLE)
                .orderByAsc(MenuInfo::getSort, MenuInfo::getId));

        // 当前菜单为空，查询当前菜单关联的权限
        if (CollectionUtils.isEmpty(menuInfos)) {
            List<PermissionInfo> permissionInfoList = permissionInfoService.list(
                    new QueryWrapper<PermissionInfo>().lambda()
                            .eq(PermissionInfo::getMenuId, menuId)
                            .eq(PermissionInfo::getStatus, CommStatus.ENABLE.getCode())
                            .orderByAsc(PermissionInfo::getSort));

            menuPermission.setSubList(permissionInfoList.stream().map(MenuPermission::of).collect(Collectors.toList()));
            return;
        }

        List<MenuPermission> menuPermissionList = new ArrayList<>(menuInfos.size());
        menuInfos.forEach(it -> {
            MenuPermission subMenuPer = MenuPermission.of(it);
            childListByMenu(subMenuPer);
            menuPermissionList.add(subMenuPer);
        });
        menuPermission.setSubList(menuPermissionList);
    }
}
