package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.UserLogService;
import com.cheche365.bc.entity.RoleInfo;
import com.cheche365.bc.entity.UserInfo;
import com.cheche365.bc.entity.UserLog;
import com.cheche365.bc.entity.enums.OperateContent;
import com.cheche365.bc.mapper.UserLogMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-05
 */
@Service
public class UserLogServiceImpl extends ServiceImpl<UserLogMapper, UserLog> implements UserLogService {

    @Override
    public void saveRoleLog(RoleInfo roleInfo, UserInfo operateUser, OperateContent operateContent, String note) {
        UserLog userLog = new UserLog();
        userLog.setRoleId(roleInfo.getId());
        userLog.setRoleName(roleInfo.getName());
        userLog.setOperateLog(roleInfo.getOperateLog());
        saveLog(userLog, operateContent, operateUser, note);
    }

    @Override
    public void saveUserLog(UserInfo userInfo, UserInfo operateUser, OperateContent operateContent, String note) {
        UserLog userLog = new UserLog();
        userLog.setUserId(userInfo.getId());
        userLog.setUserName(userInfo.getEmail());
        userLog.setOperateLog(userInfo.getOperateLog());
        saveLog(userLog, operateContent, operateUser, note);
    }

    private void saveLog(UserLog userLog, OperateContent operateContent, UserInfo operateUser, String note) {
        userLog.setOperateContent(operateContent);
        userLog.setOperateUserId(operateUser.getId());
        userLog.setOperateUserName(operateUser.getEmail());
        userLog.setCreateTime(LocalDateTime.now());
        userLog.setNote(note);
        super.save(userLog);
    }

}
