package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.PolicyDataStatisticsService;
import com.cheche365.bc.entity.PolicyDataStatistics;
import com.cheche365.bc.mapper.PolicyDataStatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhangying
 * @date 2023-01-04
 * @descript :
 */
@Service
public class PolicyDataStatisticsServiceImpl extends ServiceImpl<PolicyDataStatisticsMapper, PolicyDataStatistics> implements PolicyDataStatisticsService {

    @Autowired
    private PolicyDataStatisticsMapper policyDataStatisticsMapper;

    @Override
    public PolicyDataStatistics getPolicyInfo(Map policy) {
        return baseMapper.selectOne(new QueryWrapper<PolicyDataStatistics>().eq("com_id", policy.get("comId")).eq("channel_type", policy.get("channelType")).eq("month", policy.get("month")));
    }

    /**
     * 查询每个保司每月的总数量
     * @param channelType
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<PolicyDataStatistics> queryPolicyStatisticsByComId(String channelType, String startDate, String endDate) {
        return policyDataStatisticsMapper.queryPolicyStatisticsSumByComId(channelType, startDate, endDate);
    }

    /**
     * 计算所有保司每月的总数量
     * @param channelType
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<PolicyDataStatistics> queryPolicyStatisticsSumByMonth(String channelType, String startDate, String endDate) {
        QueryWrapper<PolicyDataStatistics> wrapper = new QueryWrapper();
        wrapper.select("company_name,month,SUM(count) count ");
        wrapper.lambda().eq(PolicyDataStatistics::getChannelType, channelType)
                .ge(PolicyDataStatistics::getMonth, startDate)
                .le(PolicyDataStatistics::getMonth, endDate)
                .groupBy(PolicyDataStatistics::getMonth)
                .orderByAsc(PolicyDataStatistics::getMonth);
        return baseMapper.selectList(wrapper);
    }

}
