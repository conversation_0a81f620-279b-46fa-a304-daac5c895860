package com.cheche365.bc.admin.schedule.job;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.admin.schedule.service.impl.AutoTaskAnalyzeUtils;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.admin.schedule.util.ExcelUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-11-18
 */
@Component
@JobHandler(value = "autoTaskLogAnalyzeJobHandler")
public class AutoTaskLogAnalyzeJobHandler extends IJobHandler {

    private AutoTaskAnalyzeUtils utils;

    @Autowired
    public void setUtils(AutoTaskAnalyzeUtils utils) {
        this.utils = utils;
    }

    @Value(value = "${export.file-path}")
    private String exportFilePath;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = LocalDate.now().atStartOfDay().minusDays(1).format(dtf);
        String endTime = LocalDate.now().atStartOfDay().minusSeconds(1).format(dtf);
        Map<String, Integer> failedCountMap = utils.failedCountMap(startTime, endTime);
        if (failedCountMap.size() == 0) {
            String msg = LocalDate.now().minusDays(1) + " 没有错误日志";
            XxlJobLogger.log(msg);
            return new ReturnT<>(msg);
        }

        Map<String, Integer> newFailedCountMap = new HashMap<>();
        failedCountMap.forEach((k, v) -> {
            String[] split = k.split("-");
            String companyId = split[0];
            String processType = split[1];
            String taskType = split[2];

            String key = companyId + "-" + processType + "-" + taskType;
            Integer count = newFailedCountMap.getOrDefault(key, 0) + v;
            newFailedCountMap.put(companyId + "-" + processType + "-" + taskType, count);
        });

        // 由于需要计算 companyId + processType 分类的异常数量
        Map<String, Integer> countMap = new LinkedHashMap<>();
        // 同时为了后续处理异常信息需要，处理异常信息列表为指定形式的 Map。为了保证有序，使用 LinkedHashMap
        // [ companyId : [ processType : [ taskType : failedList ]]]
        Map<String, Map<String, Map<String, List<JSONObject>>>> failedListMap = new LinkedHashMap<>();
        newFailedCountMap.forEach((k, v) -> {
            String[] split = k.split("-");
            String companyId = split[0];
            String processType = split[1];
            String taskType = split[2];
            String key = companyId + "-" + processType;
            countMap.put(key, countMap.getOrDefault(key, 0) + v);
            Map<String, Map<String, List<JSONObject>>> companyIdMap =
                    failedListMap.getOrDefault(companyId, new LinkedHashMap<>());
            Map<String, List<JSONObject>> processTypeMap =
                    companyIdMap.getOrDefault(processType, new LinkedHashMap<>());
            List<AutoTask> failedList = utils.failedList(startTime, endTime, k);
            List<JSONObject> resultList = utils.processFailedList(companyId, processType,
                    taskType, failedList);
            if (resultList.size() > 0) {
                processTypeMap.put(taskType, resultList);
                companyIdMap.put(processType, processTypeMap);
                failedListMap.put(companyId, companyIdMap);
            }
        });

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 处理异常信息并生成 excel 的 sheet 数据
        failedListMap.forEach((companyId, companyIdMap) -> {
            Map<String, List<JSONObject>> contentMap = new LinkedHashMap<>();
            companyIdMap.forEach((processType, processTypeMap) -> {
                List<JSONObject> exceptionList = new ArrayList<>();
                processTypeMap.forEach((taskType, failedList) -> {
                    // 处理异常信息
                    processException(companyId, processType, taskType, failedList, countMap,
                            exceptionList);
                });

                Map<String, Integer> taskOrderMap = utils.getTaskOrderMap();
                Map<String, String> taskNameMap = utils.getTaskNameMap();
                exceptionList.sort((e1, e2) -> {
                    String taskType1 = e1.getString("taskType");
                    String taskType2 = e2.getString("taskType");
                    if (Objects.equals(taskOrderMap.get(taskType1), taskOrderMap.get(taskType2))) {
                        return e2.getInteger("count") - e1.getInteger("count");
                    } else {
                        return taskOrderMap.get(taskType1) - taskOrderMap.get(taskType2);
                    }
                });
                exceptionList.forEach(e -> {
                    String taskStatus = e.getString("taskStatus");
                    if (StringUtils.isNotBlank(taskStatus) && TaskStatus.isCallbackStatus(taskStatus)) {
                        taskStatus = TaskStatus.getTaskStatus(taskStatus).getCn();
                    } else {
                        taskStatus = "";
                    }

                    e.put("taskType", taskNameMap.get(e.getString("taskType")));
                    e.put("taskStatus", taskStatus);
                });

                contentMap.put(processType, exceptionList);
            });
            // 将信息写入 sheet
            writeToWorkbook(workbook, contentMap, companyId);
        });

        String excelTitle = "百川异常信息监控报表" + LocalDate.now().minusDays(1) + ".XLSX";
        XxlJobLogger.log("导出文件目标路径：{}", exportFilePath);
        File exportFileDir = new File(exportFilePath);
        if (!exportFileDir.exists()) {
            boolean mkdir = exportFileDir.mkdirs();
            if (!mkdir) {
                throw new Exception("百川错误记录导出，创建文件夹失败");
            }
        }

        File file = new File(exportFilePath + "/" + excelTitle);
        FileOutputStream fos = new FileOutputStream(file);
        workbook.write(fos);
        fos.close();
        workbook.dispose();

        return ReturnT.SUCCESS;
    }

    private void processException(String companyId, String processType, String taskType,
                                  List<JSONObject> failedList, Map<String, Integer> countMap,
                                  List<JSONObject> exceptionList) {
        Integer total = countMap.get(companyId + "-" + processType);
        Map<String, JSONObject> exceptionMap = new LinkedHashMap<>();
        failedList.forEach(failed -> {
            // 统计相同任务类型下的异常数量
            String key = taskType + failed.getString("exception");
            JSONObject exception = exceptionMap.get(key);
            String failedTraceKey = failed.getString("traceKey");
            failedTraceKey = Objects.nonNull(failedTraceKey) ? failedTraceKey : "";
            if (null == exception) {
                exception = new JSONObject();
                exception.put("taskType", taskType);
                exception.put("taskStatus", failed.getString("taskStatus"));
                exception.put("tempName", failed.getString("tempName"));
                exception.put("exception", failed.getString("exception"));
                exception.put("exceptionType", failed.getString("exceptionType"));
                exception.put("traceKey", failedTraceKey);
                exception.put("count", 1);
            } else {
                String traceKey = exception.getString("traceKey");
                if (StringUtils.isNotBlank(traceKey)) {
                    if (!traceKey.contains(failedTraceKey) &&
                            StringUtils.countMatches(traceKey, "\n") < 9) {
                        exception.put("traceKey", traceKey + "\n" + failedTraceKey);
                    }
                } else {
                    exception.put("traceKey", failedTraceKey);
                }

                exception.put("count", exception.getInteger("count") + 1);
            }

            exceptionMap.put(key, exception);
        });
        exceptionMap.forEach((key, value) -> {
            JSONObject e = new JSONObject();
            e.put("date", LocalDate.now().toString()); // 日期
            e.put("processType", processType); // 类型
            e.put("taskType", value.get("taskType")); // 任务类型
            e.put("taskStatus", value.get("taskStatus")); // 任务
            e.put("tempName", value.get("tempName")); // 过程
            e.put("exceptionType", value.get("exceptionType")); // 异常类型
            e.put("exception", value.get("exception")); // 异常信息
            e.put("traceKey", value.get("traceKey")); // 单方 ID
            e.put("count", value.get("count")); // 数量
            Integer count = value.getInteger("count");
            e.put("percent", new BigDecimal(count)
                    .divide(new BigDecimal(failedList.size()), 4, RoundingMode.HALF_UP)); // 问题占比
            e.put("totalPercent", new BigDecimal(count)
                    .divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)); // 问题总占比
            exceptionList.add(e);
        });
    }

    /**
     * 写入到 WorkBook 的 sheet 中
     */
    private void writeToWorkbook(SXSSFWorkbook workbook, Map<String, List<JSONObject>> contentMap,
                                 String companyId) {
        Map<String, String> titleMap = new LinkedHashMap<>();
        titleMap.put("日期", "date");
        titleMap.put("类型", "processType");
        titleMap.put("任务", "taskType");
        titleMap.put("状态", "taskStatus");
        titleMap.put("过程", "tempName");
        titleMap.put("异常类型", "exceptionType");
        titleMap.put("异常信息", "exception");
        titleMap.put("单方ID", "traceKey");
        titleMap.put("数量", "count");
        titleMap.put("问题占比", "percent");
        titleMap.put("问题总占比", "totalPercent");

        List<JSONObject> contentList = new ArrayList<>();
        if (null != contentMap.get("robot")) {
            contentList.addAll(contentMap.get("robot"));
        }

        // 增加 EDI 与 精灵之间数据的空白行
        if (null != contentMap.get("edi")) {
            if (contentList.size() > 0) {
                JSONObject blankLine = new JSONObject();
                blankLine.put("date", "");
                blankLine.put("processType", "");
                blankLine.put("taskType", "");
                blankLine.put("taskStatus", "");
                blankLine.put("tempName", "");
                blankLine.put("exceptionType", "");
                blankLine.put("exception", "");
                blankLine.put("traceKey", "");
                blankLine.put("count", "");
                blankLine.put("percent", "");
                blankLine.put("totalPercent", "");

                contentList.add(blankLine);
                contentList.add(blankLine);
                contentList.add(blankLine);
            }

            contentList.addAll(contentMap.get("edi"));
        }

        String sheetName = utils.getCompanyNameMap().get(companyId);
        ExcelUtil.exportExcel(workbook, sheetName, titleMap, contentList);
    }

}
