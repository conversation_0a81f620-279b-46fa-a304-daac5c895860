package com.cheche365.bc.admin.schedule.job;

import com.cheche365.bc.admin.schedule.service.impl.AutoTaskAnalyzeUtils;
import com.cheche365.bc.admin.service.service.AutoTaskDailyStatisticsService;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.AutoTaskDailyStatistics;
import com.cheche365.bc.utils.Util;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @description 百川任务数据每日统计定时任务执行器
 * @since 2021-12-06
 */
@Component
@Slf4j
@JobHandler(value = "autoTaskDailyStatisticsJobHandler")
public class AutoTaskDailyStatisticsJobHandler extends IJobHandler {


    private AutoTaskAnalyzeUtils utils;

    @Autowired
    public void setUtils(AutoTaskAnalyzeUtils utils) {
        this.utils = utils;
    }

    private AutoTaskDailyStatisticsService statisticsService;

    @Autowired
    public void setStatisticsService(AutoTaskDailyStatisticsService statisticsService) {
        this.statisticsService = statisticsService;
    }

    @Override
    public ReturnT<String> execute(String param) {
        try {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startTime;
            String endTime;
            String date;
            if (StringUtils.isNotBlank(param)) {
                startTime = LocalDate.parse(param).atStartOfDay().format(dtf);
                endTime = LocalDate.parse(param).atStartOfDay().plusDays(1).minusSeconds(1).format(dtf);
                date = param;
                statisticsService.deleteByStatisticsDate(date);
            } else {
                startTime = LocalDate.now().atStartOfDay().minusDays(1).format(dtf);
                endTime = LocalDate.now().atStartOfDay().minusSeconds(1).format(dtf);
                date = LocalDate.now().minusDays(1).toString();
            }

            Map<String, Integer> countMap = utils.countMap(startTime, endTime, false);
            Map<String, Integer> failedCountMap = utils.failedCountMap(startTime, endTime);

            List<AutoTaskDailyStatistics> list = new ArrayList<>();
            countMap.forEach((k, totalCount) -> {
                AutoTaskDailyStatistics statistics = new AutoTaskDailyStatistics();
                String[] split = k.split("-");
                String companyId = split[0];
                String processType = split[1];
                String taskType = split[2];
                Long requestSourceId = null;
                if (split.length >= 4) {
                    String s = split[3];
                    if (StringUtils.isNotEmpty(s) && !s.equals("null")) {
                        requestSourceId = Long.parseLong(s);
                    }
                }

                Integer internetSales = 0;
                if (split.length >= 5) {
                    String s = split[4];
                    if (StringUtils.isNotEmpty(s) && !s.equals("null")) {
                        internetSales = Integer.parseInt(s);
                    }
                }

                Integer failedCount = failedCountMap.getOrDefault(k, 0);
                statistics.setCompanyId(companyId);
                statistics.setProcessType(processType);
                statistics.setTaskType(taskType);
                statistics.setTotalCount(totalCount);
                statistics.setSuccessCount(totalCount - failedCount);
                statistics.setFailedCount(failedCount);
                List<AutoTask> failedList = utils.failedList(startTime, endTime, k);
                statistics.setBusinessIssuesCount(utils.businessCount(companyId, processType, taskType, failedList));
                statistics.setStatisticsDate(date);
                statistics.setRequestSourceId(requestSourceId);
                statistics.setInternetSales(internetSales);

                list.add(statistics);
            });

            statisticsService.saveBatch(list);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("百川任务数据每日统计任务执行失败，错误原因：{}", Util.getStackTrace(e));
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

}
