package com.cheche365.bc.admin.schedule.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.entity.PolicySource;
import com.cheche365.bc.model.EmailConfig;
import com.cheche365.bc.service.PolicySourceService;
import com.cheche365.bc.tools.EmailUtil;
import com.cheche365.bc.tools.FileUtil;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.sdas.SDASUtils;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

import static com.cheche365.bc.constants.Constants.OBS_POLICY_PATH_PREFIX;


@Component
@Slf4j
@JobHandler(value = "policyDownloadJobHandler")
public class PolicyDownloadJobHandler extends IJobHandler {

    @Resource
    private PolicySourceService policySourceService;

    // 邮箱对用host映射
    private static final Map<String, String> HOST_MAPPING = Maps.newHashMap();

    // 只拉取人保太保为发件人的邮件
    private static final List<String> SENDER_EMAILS_WHITE = List.of("picc.com.cn", "cpic.com.cn", "cheche365.com");

    private static final String NET_EMAIL_PORT = "143";

    private static final String ALI_EMAIL_PORT = "993";

    private static final String NET_HOST = "163.com";

    private static final String CHECHE_HOST = "cheche365.com";

    static {
        HOST_MAPPING.put(CHECHE_HOST, "imap.qiye.aliyun.com");
        HOST_MAPPING.put(NET_HOST, "imap.163.com");
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("执行policyDownloadJobHandler定时任务，参数：{}", param);
        List<JSONObject> list = JSONArray.parseArray(param).toJavaList(JSONObject.class);
        if (Objects.isNull(list) || list.isEmpty()) {
            log.error("未发现要拉取保单的邮箱");
            return FAIL;
        } else {
            list.forEach(this::downloadPolicy);
        }
        return SUCCESS;
    }

    private void downloadPolicy(JSONObject emailObject) {

        String email = emailObject.getString("email");
        if (!StringUtil.isValidEmail(email)) {
            log.error("当前邮箱格式不合法：{}", email);
            return;
        }
        String password = emailObject.getString("password");
        if (StringUtils.isBlank(password)) {
            log.error("当前邮箱: {},密码不能为空!", email);
            return;
        }
        String host = HOST_MAPPING.get(email.split("@")[1]);
        if (StringUtils.isNotBlank(host)) {
            EmailUtil emailUtil = null;
            try {
                EmailConfig emailInfo = initEmailConfig(email, password, host);
                //查找所有未读消息
                emailUtil = new EmailUtil(emailInfo);
                Message[] unreadMessages = emailUtil.getAllUnreadMessages();
                log.info("拉取邮箱：{}的保单，共{}封未读邮件", email, unreadMessages.length);
                //处理邮件保单附件
                Arrays.asList(unreadMessages).forEach(this::getAttachment);
            } catch (Exception e) {
                log.error("从邮箱:{}下载保单异常{}", email, ExceptionUtils.getStackTrace(e));
            } finally {
                if (Objects.nonNull(emailUtil)) {
                    emailUtil.close();
                }
            }
        } else {
            log.error("未找到邮箱:{}对应的host", email);
        }
    }

    private static EmailConfig initEmailConfig(String email, String password, String host) {
        return EmailConfig.builder()
                .email(email)
                .password(password)
                .host(host)
                .port(email.contains(NET_HOST) ? NET_EMAIL_PORT : ALI_EMAIL_PORT)
                .build();
    }

    private void getAttachment(Message message) {
        try {
            if (allowDownloadFile(message)) {

                List<BodyPart> policyFiles = findAllPolicyFiles(message);

                policyFiles.forEach(policyFile ->
                        {
                            try {
                                String fileName = MimeUtility.decodeText(policyFile.getFileName());
                                Map<String, String> map = parsePolicyType(fileName);
                                byte[] bytes = extractBytes(policyFile, map);
                                String contentType = map.containsKey("zipEncrypted") ? MediaType.APPLICATION_OCTET_STREAM : Constants.PDF_CONTENT_TYPE;
                                if ("zip".equals(map.get("fileType")) && !map.containsKey("zipEncrypted")) {
                                    // 不含密码的zip 返回的字节数组是解压后的pdf,修改文件名
                                    fileName = fileName.replace(".zip", ".pdf");
                                }
                                String sourceId = uploadOBS(fileName, bytes, contentType);
                                if (StringUtils.isNotBlank(sourceId)) {
                                    map.put("sourceId", sourceId);
                                    savePolicySource(map);
                                }
                                message.setFlag(Flags.Flag.SEEN, true);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                );

            }
        } catch (Exception e) {
            log.error("下载保单文件失败:{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    private static byte[] extractBytes(BodyPart policyFile, Map<String, String> map) throws Exception {

        byte[] bytes = null;
        if ("zip".equals(map.get("fileType"))) {
            bytes = unZip(MimeUtility.decodeText(policyFile.getFileName()), policyFile);
            if (Objects.isNull(bytes)) {
                //带密码的zip
                map.put("zipEncrypted", "yes");
            }
        }
        if (bytes == null || bytes.length == 0) {
            bytes = policyFile.getInputStream().readAllBytes();
        }
        return bytes;
    }

    private static boolean allowDownloadFile(Message message) throws MessagingException {
        boolean inWhites = SENDER_EMAILS_WHITE.stream().anyMatch(email -> {
            try {
                return InternetAddress.toString(message.getFrom()).contains(email);
            } catch (MessagingException e) {
                throw new RuntimeException(e);
            }
        });
        return inWhites && message.isMimeType("multipart/*");
    }

    private static List<BodyPart> findAllPolicyFiles(Message message) throws MessagingException, IOException {
        List<BodyPart> policyFiles = new ArrayList<>();
        Multipart multipart = (Multipart) message.getContent();
        for (int j = 0; j < multipart.getCount(); j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                if (StringUtil.isAlphanumeric(fileName.substring(0, fileName.lastIndexOf(".")))) {
                    policyFiles.add(bodyPart);
                }
            }
        }
        return policyFiles;
    }

    private static Map<String, String> parsePolicyType(String fileName) {
        Map<String, String> map = Maps.newHashMap();
        map.put("policyNo", fileName.substring(0, fileName.lastIndexOf(".")));
        map.put("fileType", fileName.substring(fileName.lastIndexOf(".") + 1));
        return map;
    }


    private static byte[] unZip(String fileName, BodyPart bodyPart) throws Exception {
        return FileUtil.getStreamAsByteArray(bodyPart.getInputStream().readAllBytes(), null, fileName);
    }

    private String uploadOBS(String fileName, byte[] bytes, String contentType) {
        String path = Path.of(OBS_POLICY_PATH_PREFIX, (contentType.contains("pdf") ? "pdf" : "zip"), fileName).toString();
        return SDASUtils.uploadOBS(fileName, bytes, contentType, path);
    }

    private void savePolicySource(Map<String, String> param) {
        PolicySource source = new PolicySource();
        source.setCreateTime(LocalDateTime.now());
        String reverseNo = new StringBuilder(param.get("policyNo")).reverse().toString();
        source.setPolicyNo(reverseNo);
        if (param.containsKey("zipEncrypted")) {
            source.setZipSourceId(param.get("sourceId"));
        } else {
            source.setSourceId(param.get("sourceId"));
        }
        policySourceService.savePolicySource(source);
    }
}
