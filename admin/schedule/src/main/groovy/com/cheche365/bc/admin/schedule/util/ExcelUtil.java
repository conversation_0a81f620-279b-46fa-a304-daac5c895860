package com.cheche365.bc.admin.schedule.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Log4j2
@Component
public class ExcelUtil {


    /**
     * Excel 导出
     */
    public static void exportExcel(SXSSFWorkbook sxssFWorkbook, String sheetName,
                                   Map<String, String> titleMap, List<JSONObject> contentList) {
        SXSSFWorkbook workbook;
        if (null != sxssFWorkbook) {
            workbook = sxssFWorkbook;
        } else {
            workbook = new SXSSFWorkbook();
        }

        SXSSFSheet sheet = workbook.createSheet(sheetName);
        sheet.trackAllColumnsForAutoSizing();
        // 设置表头单元格样式
        CellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleCellStyle.setAlignment(HorizontalAlignment.LEFT);
        Font contentFont = workbook.createFont();
        contentFont.setBold(true);
        contentFont.setFontHeightInPoints((short) 14);
        titleCellStyle.setFont(contentFont);
        // 正文样式
        contentFont = workbook.createFont();
        contentFont.setFontHeightInPoints((short) 12);
        // 字符串单元格样式
        CellStyle stringCellStyle = workbook.createCellStyle();
        stringCellStyle.setFont(contentFont);
        stringCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        stringCellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 整型单元格样式
        CellStyle integerCellStyle = workbook.createCellStyle();
        integerCellStyle.setFont(contentFont);
        integerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        integerCellStyle.setAlignment(HorizontalAlignment.RIGHT);
        // 浮点型单元格样式
        CellStyle floatCellStyle = workbook.createCellStyle();
        floatCellStyle.setFont(contentFont);
        floatCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        floatCellStyle.setAlignment(HorizontalAlignment.RIGHT);

        final int[] rowNum = {0};
        final int[] colNum = {0};
        List<String> titleList = new ArrayList<>();
        final SXSSFRow[] rowArr = {sheet.createRow(rowNum[0]++)};
        titleMap.forEach((key, value) -> {
            SXSSFCell cell = rowArr[0].createCell(colNum[0]++);
            cell.setCellValue(key);
            cell.setCellStyle(titleCellStyle);

            titleList.add(value);
        });

        contentList.forEach(content -> {
            SXSSFRow row = sheet.createRow(rowNum[0]++);
            content.forEach((key, value) -> {
                SXSSFCell cell = row.createCell(titleList.indexOf(key));
                if (null == value) {
                    cell.setCellType(CellType.STRING);
                    cell.setCellStyle(stringCellStyle);
                    cell.setCellValue("");
                }

                if (value instanceof String) {
                    // 字符串数据
                    cell.setCellType(CellType.STRING);
                    cell.setCellStyle(stringCellStyle);
                    cell.setCellValue((String) value);
                } else if (value instanceof Integer) {
                    // 整型数据
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(integerCellStyle);
                    cell.setCellValue((Integer) value);
                } else if (value instanceof Long) {
                    // 长整型数据
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(integerCellStyle);
                    cell.setCellValue((Long) value);
                } else if (value instanceof Double) {
                    // 浮点型数据
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(floatCellStyle);
                    cell.setCellValue((Double) value);
                } else if (value instanceof BigDecimal) {
                    // 浮点型数据
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(floatCellStyle);
                    cell.setCellValue(new BigDecimal(value.toString()).doubleValue());
                }
            });
        });

        // 设置宽度自适应
        for (int i = 0; i < titleList.size(); i++) {
            sheet.autoSizeColumn(i, true);
        }
    }

}
