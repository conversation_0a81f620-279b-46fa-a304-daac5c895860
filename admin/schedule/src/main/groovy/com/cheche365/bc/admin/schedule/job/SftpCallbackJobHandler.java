package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.cheche365.bc.admin.schedule.config.SftpProps;
import com.cheche365.bc.admin.service.service.PPBInterMediaryService;
import com.cheche365.bc.entity.PPBInterMediary;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.SftpUtils;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import com.google.common.net.MediaType;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * @date 2021-06-24
 * @descript :定时任务获取承保回调数据(从百川sftp获取)
 */
@Component
@Slf4j
@JobHandler(value = "sftpPolicyCallbackJobHandler")
public class SftpCallbackJobHandler extends IJobHandler {

    private static final Map<String, String> HEADERS = Maps.newHashMapWithExpectedSize(1);

    static {
        HEADERS.put(HttpHeaders.CONTENT_TYPE, MediaType.JSON_UTF_8.toString());
    }

    @Value("${ep.domain}")
    private String epDomain;

    @Autowired
    private SftpProps sftpProps;

    @Autowired
    private PPBInterMediaryService ppbInterMediaryService;

    /**
     * 定时从sftp拉取承保回调数据
     *
     * @param param [{"comId":"2103","path":"/huahai/upload"},{"comId":"2021","path":"/dadi/upload"}]
     *              [{"comId":"2103","path":"/huahai/upload","file":"","key":""}]
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StrUtil.isBlank(param)) {
            log.error("未配置sftp参数");
            return FAIL;
        }
        List<Config> configs = JSONArray.parseArray(param, Config.class);
        if (CollUtil.isEmpty(configs)) {
            log.error("sftp配置参数为空");
            return FAIL;
        }

        List<PPBInterMediary> interMediarys = ppbInterMediaryService.findInterMediaryNoFromComId("2103");
        AtomicInteger count = new AtomicInteger(0);
        CloseableHttpClient closeableHttpClient = buildHttpClient();
        BiConsumer<String, String> callback = callback(closeableHttpClient, TaskType.POLICY_APPROVED.code, count);
        for (Config config : configs) {
            if (StrUtil.isNotBlank(config.getFile())) {
                PPBInterMediary ppbInterMediary = new PPBInterMediary();
                if ("2103".equals(config.getComId())) {
                    ppbInterMediary.setExt1(config.getKey());//华海下载单个文件时，要赋值解密的key
                }
                String root = downloadFile(ppbInterMediary, config);
                callback.accept(config.getComId(), root);
                break;
            } else if ("2103".equals(config.getComId())) {
                for (PPBInterMediary interMediary : interMediarys) {
                    String root = downloadFile(interMediary, config);
                    callback.accept(config.getComId(), root);
                }
            } else {
                String root = downloadFile(null, config);
                callback.accept(config.getComId(), root);
            }
        }
        log.info("sftp下载文件并推送到澎湃保,执行的文件个数：{}", count);
        return SUCCESS;
    }

    private CloseableHttpClient buildHttpClient() throws Exception {
        CloseableHttpClient closeableHttpClient = this.epDomain.startsWith("https") ? HttpSender.buildHttpClient() :
                HttpSender.buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
        return closeableHttpClient;
    }

    private BiConsumer<String, String> callback(CloseableHttpClient closeableHttpClient, String taskType, AtomicInteger count) {
        return (comId, reqBody) -> {
            if (StringUtil.isNoEmpty(reqBody)) {
                postCallback(closeableHttpClient, comId, taskType, reqBody);
                count.incrementAndGet();
            }
        };
    }

    private void postCallback(CloseableHttpClient httpClient, String comId, String taskType, String reqBody) {
        try {
            String url = buildUrl(comId, taskType);
            HttpSender.doPost(httpClient, url, reqBody, null, HEADERS, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            log.error(StrUtil.format("调用回调接口失败：[comId:{},taskType:{},body:{}]", comId, taskType, reqBody), e);
        }
    }

    private String buildUrl(String comId, String taskType) {
        return this.epDomain + "/callback/" + comId + "/" + taskType;
    }

    /**
     * sftp获取文件
     *
     * @return
     * @throws Exception
     */
    private String downloadFile(PPBInterMediary interMediary, Config config) {
        SftpUtils sftpUtils = new SftpUtils();
        String root = "";
        ChannelSftp sftp = null;
        BufferedReader reader = null;
        String fileName = buildFileName(interMediary, config.getComId(), config.getFile());
        log.info("sftp切换到文件路径:{},文件名:{}", config.getPath(), fileName);
        try {
            sftp = sftpUtils.createSftp(
                    sftpProps.getUsername(),
                    sftpProps.getHost(),
                    sftpProps.getPassword(),
                    sftpProps.getPort(),
                    sftpProps.getSessionConnectTimeout(),
                    sftpProps.getProtocol(),
                    sftpProps.getChannelConnectedTimeout()
            );
            sftp.cd(config.getPath());
            //判断文件是否存在
            sftp.lstat(fileName);
            InputStream in = sftp.get(fileName);
            //解析文件,得到请求模板的报文
            reader = new BufferedReader(new InputStreamReader(in));
            root = reader.lines().collect(Collectors.joining(System.lineSeparator()));
            if ("2103".equals(config.getComId()))
                root = root.concat("," + interMediary.getExt1());
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                log.warn("sftp文件下载异常-文件不存在:{}", fileName);
            } else {
                log.error("sftp文件下载异常", e);
            }
        } catch (Exception e) {
            log.error("sftp文件下载异常", e);
        } finally {
            if (ObjUtil.isNotEmpty(reader)) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.warn("", e);
                }
            }
            if (ObjUtil.isNotEmpty(sftp)) {
                sftp.disconnect();
            }
        }
        return root;
    }

    /**
     * 构建文件名称
     *
     * @param interMediary
     * @param comId
     * @param file
     * @return
     */
    private static String buildFileName(PPBInterMediary interMediary, String comId, String file) {
        String now = LocalDate.now().toString().replaceAll("-", "");
        String fileName = "";
        if (StringUtil.isNoEmpty(file)) {
            fileName = file;
        } else if ("2021".equals(comId)) {
            fileName = "P" + now + "ZJDJPPBFTP0001" + ".TXT";
        } else if ("2103".equals(comId)) {//华海
            fileName = interMediary.getIntermediaryNo() + "-" + now + ".txt";
        }
        return fileName;
    }

    @Data
    static class Config implements Serializable {

        private String comId;

        private String path;

        private String file;

        private String key;

    }
}
