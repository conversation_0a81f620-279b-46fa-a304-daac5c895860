package com.cheche365.bc.admin.schedule.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "sftp.client")
@Data
@Component
public class SftpProps {

    /*
     * sftp服务器地址
     */
    private String host;
    /*
     * sftp服务器端口
     */
    private int port;
    /*
     * sftp服务器用户名
     */
    private String username;
    /*
     * sftp服务器密码
     */
    private String password;
    /*
     * sftp服务器连接协议
     */
    private String protocol;
    /*
     * sftp服务器会话连接超时时间
     */
    private int sessionConnectTimeout;
    /*
     * sftp服务器通道连接超时时间
     */
    private int channelConnectedTimeout;
}
