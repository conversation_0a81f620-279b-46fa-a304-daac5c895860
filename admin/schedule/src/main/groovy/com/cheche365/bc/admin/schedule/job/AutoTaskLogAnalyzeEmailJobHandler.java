package com.cheche365.bc.admin.schedule.job;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-11-18
 */
@Component
@JobHandler(value = "autoTaskLogAnalyzeEmailJobHandler")
public class AutoTaskLogAnalyzeEmailJobHandler extends IJobHandler {

    private static final String[] DEFAULT_EMAILS = new String[]{"<EMAIL>"};

    @Value(value = "${export.file-path}")
    private String exportFilePath;

    @Value(value = "${spring.mail.username}")
    private String sendFrom;

    @Resource
    private JavaMailSender javaMailSender;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        LocalDate localDate = LocalDate.now().minusDays(1);
        String fileName = "百川异常信息监控报表" + localDate + ".XLSX";
        File file = new File(exportFilePath + "/" + fileName);
        if (file.exists()) {
            List<String> list = JSON.parseArray(param, String.class);
            String[] toArr = list.stream()
                    .map(item -> item + "@cheche365.com")
                    .toArray(String[]::new);
            String date = localDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            helper.setFrom(sendFrom);
            helper.setTo(toArr);
            helper.setSubject("百川异常信息监控报表");
            helper.setText(date + "百川异常信息监控报表，内容详见附件");
            helper.addAttachment(fileName, file);
        } else {
            helper.setSubject("百川异常信息监控报表生成异常");
            helper.setTo(DEFAULT_EMAILS);
            helper.setText("【" + localDate + "】百川异常信息监控报表生成异常，请检查错误日志");
        }
        javaMailSender.send(message);
        return ReturnT.SUCCESS;
    }
}
