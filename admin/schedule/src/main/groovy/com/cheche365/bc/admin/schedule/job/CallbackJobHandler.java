package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import com.google.common.net.MediaType;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @date 2021-06-24
 * @descript :定时任务获取承保回调数据
 */
@Component
@Slf4j
@JobHandler(value = "ediPolicyCallbackJobHandler")
public class CallbackJobHandler extends IJobHandler {

    private static final Map<String, String> HEADERS = Maps.newHashMapWithExpectedSize(1);

    static {
        HEADERS.put(HttpHeaders.CONTENT_TYPE, MediaType.JSON_UTF_8.toString());
    }

    @Value("${ep.domain:''}")
    private String epDomain;

    @Value(value = "${spring.mail.username}")
    private String sendFrom;

    @Resource
    private JavaMailSender javaMailSender;

    /**
     * 定时拉取保单回调数据
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("执行ediPolicyCallbackJobHandler定时任务，参数：{}", param);
        List<JSONObject> list = JSONArray.parseArray(param).toJavaList(JSONObject.class);
        CloseableHttpClient closeableHttpClient = buildHttpClient();
        for (JSONObject jsonObject : list) {
            Object result = postCallback(closeableHttpClient, jsonObject.getString("comId"), TaskType.POLICY_APPROVED.code, JSONObject.toJSONString(param));
            log.info("执行ediPolicyCallbackJobHandler定时任务， 请求callback接口返回数据：{}", result);
            if ("2065".equals(jsonObject.getString("comId")) && jsonObject.containsKey("sendTo") && result.toString().contains("失败")) {
                MimeMessage message = javaMailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message);
                helper.setSubject("诚泰互联互通数据接收异常信息");
                String[] convertClass = new String[]{};
                String[] sendTo = jsonObject.getJSONArray("sendTo").toArray(convertClass);
                helper.setFrom(sendFrom);
                helper.setTo(sendTo);
                helper.setText(LocalDate.now() + "诚泰互联互通数据接收异常信息，详情见日志");
                javaMailSender.send(message);
            }
        }
        return SUCCESS;
    }

    private CloseableHttpClient buildHttpClient() throws Exception {
        CloseableHttpClient closeableHttpClient = this.epDomain.startsWith("https") ? HttpSender.buildHttpClient() :
                HttpSender.buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
        return closeableHttpClient;
    }

    private Object postCallback(CloseableHttpClient httpClient, String comId, String taskType, String reqBody) {
        try {
            String url = buildUrl(comId, taskType);
            return HttpSender.doPost(httpClient, url, reqBody, null, HEADERS, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            log.error(StrUtil.format("调用回调接口失败：[comId:{},taskType:{},body:{}]", comId, taskType, reqBody), e);
        }
        return null;
    }

    private String buildUrl(String comId, String taskType) {
        return this.epDomain + "/ep/callback/" + comId + "/" + taskType;
    }
}
