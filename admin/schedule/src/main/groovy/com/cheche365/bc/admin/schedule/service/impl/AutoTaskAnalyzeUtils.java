package com.cheche365.bc.admin.schedule.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cheche365.bc.entity.ActionLog;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.AutoTaskExceptionConfig;
import com.cheche365.bc.service.AutoTaskExceptionConfigService;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.tools.FileUtil;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @since 2021-12-06
 */
@Component
@RequiredArgsConstructor
public class AutoTaskAnalyzeUtils {
    private final AutoTaskService autoTaskService;
    private final AutoTaskExceptionConfigService configService;
    private final MongoTemplate mongoTemplate;

    /**
     * 保险公司顺序
     */
    private Map<String, Integer> companyOrderMap;

    /**
     * 保险公司名称
     */
    private Map<String, String> companyNameMap;

    /**
     * 任务顺序
     */
    private Map<String, Integer> taskOrderMap;

    /**
     * 任务名称
     */
    private Map<String, String> taskNameMap;

    /**
     * 任务状态
     */
    private Map<String, Map<String, String>> taskStatusMap;

    /**
     * 保险公司 ID 列表
     */
    private List<String> companyIdList;

    /**
     * 能力类型列表
     */
    private List<String> processTypeList;

    /**
     * 任务类型列表
     */
    private List<String> taskTypeList;

    @PostConstruct
    void initConfig() {
        companyOrderMap = new LinkedHashMap<>();
        companyNameMap = new LinkedHashMap<>();
        String json = FileUtil.readResource("/CompanyNameAndOrder.json", AutoTaskAnalyzeUtils.class);
        JSON.parseArray(json, JSONObject.class).forEach(config -> {
            companyOrderMap.put(config.getString("code"), config.getInteger("order"));
            companyNameMap.put(config.getString("code"), config.getString("name"));
        });

        taskOrderMap = new LinkedHashMap<>();
        taskNameMap = new LinkedHashMap<>();
        taskStatusMap = new LinkedHashMap<>();
        json = FileUtil.readResource("/TaskNameAndStatus.json", AutoTaskAnalyzeUtils.class);
        JSON.parseArray(json, JSONObject.class).forEach(config -> {
            taskOrderMap.put(config.getString("code"), config.getInteger("order"));
            taskNameMap.put(config.getString("code"), config.getString("name"));

            Map<String, String> map = new HashMap<>();
            map.put("successStatus", config.getString("successStatus"));
            map.put("failedStatus", config.getString("failedStatus"));
            taskStatusMap.put(config.getString("code"), map);
        });

        companyIdList = new ArrayList<>(companyOrderMap.keySet());
        processTypeList = Stream.of("robot", "edi").collect(Collectors.toList());
        taskTypeList = new ArrayList<>(taskOrderMap.keySet());
    }

    public Map<String, String> getCompanyNameMap() {
        return companyNameMap;
    }

    public Map<String, Integer> getTaskOrderMap() {
        return taskOrderMap;
    }

    public Map<String, String> getTaskNameMap() {
        return taskNameMap;
    }

    public Map<String, Integer> countMap(String startTime, String endTime, boolean failedStatus) {
        Map<String, Integer> countMap = new LinkedHashMap<>();
        companyIdList.forEach(c -> processTypeList.forEach(p -> taskTypeList.forEach(t -> {
            StringJoiner joiner = new StringJoiner("-");
            String taskType = joiner.add(p).add(c).add(t).toString();
            QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                .eq(AutoTask::getTaskType, taskType)
                .ge(AutoTask::getStartTime, startTime)
                .le(AutoTask::getEndTime, endTime);
            if (failedStatus) {
                if (t.equals("autoinsure")) {
                    wrapper.lambda().and(w ->
                        w.eq(AutoTask::getTaskStatus, "31") // 自动核保暂存失败
                            .or().eq(AutoTask::getTaskStatus, "33") // 自核失败
                            .or().isNull(AutoTask::getTaskStatus)
                            .or().eq(AutoTask::getTaskStatus, null)
                    );
                } else {
                    wrapper.lambda().and(w ->
                        w.eq(AutoTask::getTaskStatus, taskStatusMap.get(t).get("failedStatus"))
                            .or().isNull(AutoTask::getTaskStatus)
                            .or().eq(AutoTask::getTaskStatus, null)
                    );
                }
            }

            wrapper.select("request_source_id requestSourceId, internet_sales internetSales, COUNT(*) count")
                .groupBy("request_source_id", "internet_sales");
            List<Map<String, Object>> maps = autoTaskService.listMaps(wrapper);
            for (Map<String, Object> map : maps) {
                Long count = (Long) map.get("count");
                if (count > 0) {
                    String key = new StringJoiner("-")
                        .add(c).add(p).add(t)
                        .add(String.valueOf(map.get("requestSourceId")))
                        .add(String.valueOf(map.get("internetSales")))
                        .toString();
                    countMap.put(key, count.intValue());
                }
            }
        })));

        return countMap;
    }

    public Map<String, Integer> failedCountMap(String startTime, String endTime) {
        return countMap(startTime, endTime, true);
    }

    /**
     * 查询失败的任务列表
     */
    public List<AutoTask> failedList(String startTime, String endTime, String taskType) {
        String[] split = taskType.split("-");
        String companyId = split[0];
        String processType = split[1];
        String type = split[2];
        Long requestSourceId = null;
        if (split.length >= 4) {
            String s = split[3];
            if (StringUtils.isNotEmpty(s) && !s.equals("null")) {
                requestSourceId = Long.parseLong(s);
            }
        }

        Integer internetSales = 0;
        if (split.length >= 5) {
            String s = split[4];
            if (StringUtils.isNotEmpty(s) && !s.equals("null")) {
                internetSales = Integer.parseInt(s);
            }
        }

        StringJoiner joiner = new StringJoiner("-");
        taskType = joiner.add(processType).add(companyId).add(type).toString();
        QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .select(AutoTask::getActionLogs, AutoTask::getResultStr, AutoTask::getTaskStatus,
                AutoTask::getTraceKey, AutoTask::getFailureCause)
            .eq(AutoTask::getTaskType, taskType)
            .ge(AutoTask::getStartTime, startTime)
            .le(AutoTask::getEndTime, endTime);
        if (null != requestSourceId) {
            wrapper.lambda().eq(AutoTask::getRequestSourceId, requestSourceId);
        }

        if (null != internetSales) {
            wrapper.lambda().eq(AutoTask::getInternetSales, internetSales);
        }

        if (Objects.equals(type, "autoinsure")) {
            wrapper.lambda().and(w ->
                w.eq(AutoTask::getTaskStatus, taskStatusMap.get(type).get("failedStatus")) // 自动核保失败
                    .or().eq(AutoTask::getTaskStatus, "31") // 自动核保暂存失败
                    .or().isNull(AutoTask::getTaskStatus)
                    .or().eq(AutoTask::getTaskStatus, "")
            );
        } else {
            // 任务失败或者任务状态为空
            wrapper.lambda().and(w ->
                w.eq(AutoTask::getTaskStatus, taskStatusMap.get(type).get("failedStatus"))
                    .or().isNull(AutoTask::getTaskStatus)
                    .or().eq(AutoTask::getTaskStatus, "")
            );
        }

        return autoTaskService.list(wrapper);
    }

    /**
     * 业务异常数量
     */
    public int businessCount(String companyId, String processType, String taskType, List<AutoTask> autoTaskFailedList) {
        List<AutoTaskExceptionConfig> configList =
            configService.listByCompanyIdAndProcessTypeAndTaskType(companyId, processType, taskType);
        int count = 0;
        for (AutoTask autoTask : autoTaskFailedList) {
            String e = getException(autoTask);
            for (AutoTaskExceptionConfig config : configList) {
                if (configService.matchException(e, config)) {
                    count++;
                    break;
                }
            }
        }

        return count;
    }

    /**
     * 处理异常任务
     */
    public List<JSONObject> processFailedList(String companyId, String processType, String taskType,
                                              List<AutoTask> autoTaskFailedList) {
        List<AutoTaskExceptionConfig> configList =
            configService.listByCompanyIdAndProcessTypeAndTaskType(companyId, processType, taskType);
        List<JSONObject> resultList = new ArrayList<>();
        if (configList.isEmpty()) {
            for (AutoTask autoTask : autoTaskFailedList) {
                resultList.add(convertAutoTaskToJson(companyId, processType, taskType, autoTask));
            }
        } else {
            for (AutoTask autoTask : autoTaskFailedList) {
                JSONObject processed = convertAutoTaskToJson(companyId, processType, taskType, autoTask);
                String errorMsg = getException(autoTask);
                String exceptionConversion = configService.findExceptionConversion(errorMsg, configList);
                if (StringUtils.isNotEmpty(exceptionConversion)) {
                    processed.put("exception", exceptionConversion);
                    processed.put("exceptionType", "非技术异常");
                } else {
                    processed.put("exception", errorMsg);
                }
                resultList.add(processed);
            }
        }

        return resultList;
    }

    private JSONObject convertAutoTaskToJson(String companyId, String processType, String taskType, AutoTask autoTask) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyId", companyId);
        jsonObject.put("processType", processType);
        jsonObject.put("taskType", taskType);
        jsonObject.put("traceKey", autoTask.getTraceKey());
        jsonObject.put("taskStatus", autoTask.getTaskStatus());
        jsonObject.put("tempName", getTempName(autoTask));
        jsonObject.put("exception", getException(autoTask));
        jsonObject.put("exceptionType", "技术异常");
        return jsonObject;
    }

    private String getTempName(AutoTask autoTask) {
        String actionLogs = autoTask.getActionLogs();
        String tempName = "";
        if (StringUtils.isNotBlank(actionLogs)) {
            String[] actionLogsSplit = actionLogs.split(",");
            String lastAction = actionLogsSplit[actionLogsSplit.length - 1];
            String[] lastActions = lastAction.split("@");
            tempName = lastActions[lastActions.length - 1];
        }

        return tempName;
    }

    private String getException(AutoTask autoTask) {
        String exception = "";
        String actionLogs = autoTask.getActionLogs();
        String failureCause = autoTask.getFailureCause();
        String resultStr = autoTask.getResultStr();
        if (StringUtils.isBlank(actionLogs) && StringUtils.isBlank(failureCause)) {
            // 没有执行模板直接抛出异常
            String regex = "\\{.*}([\\s\\S\\n]+)";
            Matcher matcher = Pattern.compile(regex).matcher(resultStr);
            if (matcher.find()) {
                exception = matcher.group(1);
            }
        } else if (StringUtils.isBlank(failureCause)) {
            String[] actionLogsSplit = actionLogs.split(",");
            String lastAction = actionLogsSplit[actionLogsSplit.length - 1];
            String[] lastActions = lastAction.split("@");
            String logId = lastActions[0];
            ActionLog actionLog = mongoTemplate.findById(logId, ActionLog.class);
            if (Objects.nonNull(actionLog)) {
                exception = actionLog.getExceptionInfo();
            }
        } else {
            exception = failureCause;
        }

        return StringUtils.isNotBlank(exception) ? exception.trim() : "";
    }

}
