# general
.#*
._*
.DS_Store
.buildinfo
*.bak
*~
*.setting
*.pyc
*.odem
*.ws
*.dtd
*.log
*.pid
*.port
.gradle/
build/
target/
pom.xml
pom.xml.asc

# intellij idea
*.iml
*.ipr
*.iws
.idea/
classes/

# eclipse
.project
.classpath
.settings/
out/

# vagrant
.vagrant
local-conf.rb
cloud-conf
Vagrantfile
**/devops/env/local/shared/persistent/*

# clojure/leiningen
checkouts/
.lein-*
.nrepl-*
profiles.clj

# node
node_modules/

# svn
.svn
lombok.config

# vscode
.history
.vscode
bin

.cursorrules
.cursorignore
*.code-workspace
.groovylintrc.json