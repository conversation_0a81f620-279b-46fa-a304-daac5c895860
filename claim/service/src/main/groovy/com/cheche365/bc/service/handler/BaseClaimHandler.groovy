package com.cheche365.bc.service.handler

import com.cheche365.bc.utils.HttpClientUtils
import com.cheche365.bc.utils.sender.HttpParams
import groovyx.net.http.ContentType

import static groovyx.net.http.ContentType.JSON

abstract class BaseClaimHandler {

    abstract Boolean support(String companyId);

    def execute(Map<String, Object> context, Map<String, Object> step, String reqArgs) {
        HttpParams httpParams = new HttpParams(
            requestContentType: (context.requestContentType ?: JSON) as ContentType,
            contentType: (context.contentType ?: JSON) as ContentType,
            headers: context.headers as Map,
            method: step.method,
            path: step.url,
            body: reqArgs,
            query: (context.params ?: [:]) as Map,
            parseResp: (context.parseResp ?: HttpClientUtils.JSON_RESPONSE_PARSE) as Closure
        )
        HttpClientUtils.call(httpParams)
    }
}
