package com.cheche365.bc.service.util;

/**
 * 10机制、62进制转换器
 */
public class LinkUtils {

    private static final String BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int BASE62 = 62;

    public static long base62ToDecimal(String base62Number) {
        long result = 0;
        int power = 0;

        for (int i = base62Number.length() - 1; i >= 0; i--) {
            char c = base62Number.charAt(i);
            int digit = BASE62_CHARS.indexOf(c);
            result += digit * Math.pow(BASE62, power);
            power++;
        }
        return result;
    }

    public static String decimalToBase62(long decimalNumber) {
        StringBuilder result = new StringBuilder();
        if (decimalNumber == 0) {
            return BASE62_CHARS.substring(0, 1);
        }
        while (decimalNumber > 0) {
            int digit = (int) (decimalNumber % BASE62);
            result.insert(0, BASE62_CHARS.charAt(digit));
            decimalNumber /= BASE62;
        }
        return result.toString();
    }
}
