package com.cheche365.bc.service.handler.yangguang;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Calendar;
import java.util.Random;

public class YangguangEncryptionUtil {
    /***
     * <AUTHOR>
     * 加密算法
     * 加密
     * 解密
     */
    public static String Encode = "Encode";
    public static String Decode = "Decode";


    /**
     * @param str:原字符串
     * @param startIndex:子字符串的起始位置
     * @param length
     * @功能：从字符串的指定位置截取指定长度的子字符串
     * @return:子字符串
     */
    public static String cutString(String str, int startIndex, int length) {
        if (startIndex >= 0) {
            if (length < 0) {
                length = length * -1;
                if (startIndex - length < 0) {
                    length = startIndex;
                    startIndex = 0;
                } else {
                    startIndex = startIndex - length;
                }
            }

            if (startIndex > str.length()) {
                return "";
            }

        } else {
            if (length < 0) {
                return "";
            } else {
                if (length + startIndex > 0) {
                    length = length + startIndex;
                    startIndex = 0;
                } else {
                    return "";
                }
            }
        }

        if (str.length() - startIndex < length) {

            length = str.length() - startIndex;
        }

        return str.substring(startIndex, startIndex + length);
    }

    /**
     * @param str:原字符串
     * @param startIndex:子字符串的起始位置
     * @功能：从字符串的指定位置开始截取到字符串结尾的了符串
     * @return:子字符串
     */
    public static String cutString(String str, int startIndex) {
        return cutString(str, startIndex, str.length());
    }

    /**
     * @param str:原始字符串
     * @：功能MD5函数
     * @return:原始字符串
     */
    public static String md5(String str) {
        // return md5.convert(str);
        StringBuffer sb = new StringBuffer();
        String part = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5 = md.digest(str.getBytes("utf-8"));

            for (int i = 0; i < md5.length; i++) {
                part = Integer.toHexString(md5[i] & 0xFF);
                if (part.length() == 1) {
                    part = "0" + part;
                }
                sb.append(part);
            }

        } catch (NoSuchAlgorithmException | UnsupportedEncodingException ex) {
            ex.printStackTrace();
        }
        return sb.toString();
    }

    /**
     * 功能：用于 RC4 处理密码
     *
     * @param pass:密码字串
     * @param kLen:>密钥长度，一般为 256
     * @return
     */
    static private byte[] getKey(byte[] pass, int kLen) {
        byte[] mBox = new byte[kLen];

        for (int i = 0; i < kLen; i++) {
            mBox[i] = (byte) i;
        }

        int j = 0;
        for (int i = 0; i < kLen; i++) {

            j = (j + (int) ((mBox[i] + 256) % 256) + pass[i % pass.length])
                    % kLen;

            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;
        }

        return mBox;
    }

    /**
     * 功能：生成随机字符
     *
     * @param lens:随机字符长度
     * @return:随机字符
     */
    public static String RandomString(int lens) {
        char[] CharArray = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k',
                'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
                'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        int clens = CharArray.length;
        String sCode = "";
        Random random = new Random();
        for (int i = 0; i < lens; i++) {
            sCode += CharArray[Math.abs(random.nextInt(clens))];
        }
        return sCode;
    }

    /**
     * @param source：需要加密的字符串
     * @param key：加密的秘钥
     * @param
     * @return
     * <AUTHOR>
     * @功能：加密算法
     * @date 20150107
     */
    public static String authcodeEncode(String source, String key) {
        return decipher(source, key, Encode, 0).replaceAll("\n", "");
    }


    /**
     * 功能:RC4 原始算法
     *
     * @param input:原始字串数组
     * @param pass:密钥
     * @return:处理后的字串数组
     */
    private static byte[] RC4(byte[] input, String pass) throws UnsupportedEncodingException {
        if (input == null || pass == null)
            return null;

        byte[] output = new byte[input.length];
        byte[] mBox = getKey(pass.getBytes("utf-8"), 256);

        // 加密
        int i = 0;
        int j = 0;

        for (int offset = 0; offset < input.length; offset++) {
            i = (i + 1) % mBox.length;
            j = (j + (int) ((mBox[i] + 256) % 256)) % mBox.length;

            byte temp = mBox[i];
            mBox[i] = mBox[j];
            mBox[j] = temp;
            byte a = input[offset];

            // byte b = mBox[(mBox[i] + mBox[j] % mBox.Length) % mBox.Length];
            // mBox[j] 一定比 mBox.Length 小，不需要在取模
            byte b = mBox[(toInt(mBox[i]) + toInt(mBox[j])) % mBox.length];

            output[offset] = (byte) ((int) a ^ (int) toInt(b));
        }

        return output;
    }

    public static int toInt(byte b) {
        return (int) ((b + 256) % 256);
    }

    public long getUnixTimestamp() {
        Calendar cal = Calendar.getInstance();
        return cal.getTimeInMillis() / 1000;
    }

    /**
     * @param source:原始字符串
     * @param key：秘钥
     * @param operation：加解密操作
     * @param expiry：加密字串过期时间
     * @return：加密后的字符串
     */
    private static String decipher(String source, String key,
                                   String operation, int expiry) {
        try {
            if (source == null || key == null) {
                return "";
            }

            int ckey_length = 4;
            String keya, keyb, keyc, cryptkey, result;

            key = md5(key);

            keya = md5(cutString(key, 0, 16));

            keyb = md5(cutString(key, 16, 16));

            keyc = ckey_length > 0 ? (operation.equals(Decode) ? cutString(
                    source, 0, ckey_length) : RandomString(ckey_length))
                    : "";

            cryptkey = keya + md5(keya + keyc);

            source = "0000000000" + cutString(md5(source + keyb), 0, 16)
                    + source;

            byte[] temp = RC4(source.getBytes("UTF-8"), cryptkey);

            return keyc + Base64.encodeBase64String(temp).replaceAll("\r\n", "");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * @param source:原始字符串
     * @param key：秘钥
     * @param operation：加解密操作
     * @param expiry：加密字串过期时间
     * @return：加密后的字符串
     */
    private static String authcode(String source, String key,
                                   String operation, int expiry) {
        try {
            if (source == null || key == null) {
                return "";
            }

            int ckey_length = 4;
            String keya, keyb, keyc, cryptkey, result;

            key = md5(key);

            keya = md5(cutString(key, 0, 16));
            keyb = md5(cutString(key, 16, 16));
            keyc = ckey_length > 0 ? (operation.equals(Decode) ? cutString(
                    source, 0, ckey_length) : RandomString(ckey_length))
                    : "";
            cryptkey = keya + md5(keya + keyc);

            if (operation.equals(Decode)) {
                byte[] temp;

                temp = Base64.decodeBase64(cutString(source, ckey_length));
                result = new String(RC4(temp, cryptkey), "utf-8");
                String A = cutString(result, 10, 16);
                String B = cutString(md5(cutString(result, 26) + keyb), 0, 16);
                if (cutString(result, 10, 16).equals(
                        cutString(md5(cutString(result, 26) + keyb), 0, 16))) {
                    return cutString(result, 26);
                } else {
                    temp = Base64.decodeBase64(cutString(source + "=", ckey_length));
                    result = new String(RC4(temp, cryptkey));
                    if (cutString(result, 10, 16)
                            .equals(cutString(
                                    md5(cutString(result, 26) + keyb), 0, 16))) {
                        return cutString(result, 26);
                    } else {
                        temp = Base64.decodeBase64(cutString(source + "==",
                                ckey_length));
                        result = new String(RC4(temp, cryptkey));
                        if (cutString(result, 10, 16).equals(
                                cutString(md5(cutString(result, 26) + keyb), 0,
                                        16))) {
                            return cutString(result, 26);
                        } else {
                            return "2";
                        }
                    }
                }
            } else {
                return "加密操作不正确";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * @param source
     * @param key
     * @param
     * @return
     * <AUTHOR>
     * @功能：解密算法
     * @date 20150107
     */
    public static String authcodeDecode(String source, String key) {
        return authcode(source, key, Decode, 0);

    }

    /**
     * 英华保单数据交换加密
     *
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public byte[] encrypt(byte[] content, String key) throws Exception {
        byte[] iv = key.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(iv, "AES");
        Cipher cipher = Cipher.getInstance("AES/OFB/NoPadding"); // "算法/模式/补码方式"
        IvParameterSpec ivps = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivps);
        return cipher.doFinal(content);
    }

    /**
     * 英华保单数据交换解密
     *
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public byte[] decrypt(byte[] content, String key) throws Exception {
        byte[] iv = key.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(iv, "AES");
        Cipher cipher = Cipher.getInstance("AES/OFB/NoPadding"); // "算法/模式/补码方式"
        IvParameterSpec ivps = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivps);
        return cipher.doFinal(content);
    }


    public static String sign(String data, String pri) throws InvalidKeySpecException, NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException, SignatureException {
        //签名 获取publicKey+加密json
        byte[] dataEncode = Base64.encodeBase64(data.getBytes("UTF-8"));
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(pri));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
        Signature signatureChecker = Signature.getInstance("SHA256WITHRSA");
        signatureChecker.initSign(privateKey);
        signatureChecker.update(dataEncode);
        byte[] sign = signatureChecker.sign();
        return new String(Base64.encodeBase64(sign));
    }

}
