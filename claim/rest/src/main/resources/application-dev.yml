spring:
  datasource:
    dynamic:
      datasource:
        bcs:
          username: river_zzb
          password: Prfc65_fkzqU
          url: ****************************************************************************************************************************************************************
  data:
    mongodb:
      database: river_zzb
      host: *************
      port: 27017
      username: admin
      password: admin123
    redis:
      host: *************
      port: 6379
      password: cheche
      database: 0

domain: https://ins-router.chetimes.com

# scriptDir
router:
  script:
    dir: D:\workspace2024\ins-router-scripts\src\main\groovy



sdas:
  app:
    key: 17e209f5-7a8c-4ad9-8e90-2e8e4232a99f
  secret:
    key: abfff95c-4bcb-4f40-ade6-29bd113cf738
  url: http://sdas.h.bedrock.chetimes.com/api/assets

