package com.cheche365.bc.rest.error


import com.cheche365.bc.exception.FlowException
import com.cheche365.bc.message.Response
import com.cheche365.bc.rest.exception.RestException
import groovy.util.logging.Slf4j
import jakarta.validation.ConstraintViolationException
import org.apache.commons.lang3.exception.ExceptionUtils
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.context.request.ServletWebRequest
import org.springframework.web.context.request.WebRequest

import static org.springframework.http.HttpStatus.OK

@RestControllerAdvice
@Slf4j
class ErrorHandler {

    private static final String COMMON_ERROR_MESSAGE = "服务器内部异常，请联系对应开发人员!"

    @ExceptionHandler(ConstraintViolationException.class)
    ResponseEntity<Object> constraintExceptionHandler(ConstraintViolationException cve) {
        new ResponseEntity<>(new Response(400, '', cve.getMessage()), OK)
    }

    @ExceptionHandler(FlowException.class)
    ResponseEntity<Object> restExceptionExceptionHandler(FlowException re) {
        new ResponseEntity<>(new Response(501, '', re.getMessage()), OK)
    }

    @ExceptionHandler(RestException.class)
    ResponseEntity<Object> restExceptionExceptionHandler(RestException re) {
        new ResponseEntity<>((new Response(re.code, '', re.getMessage())), OK)
    }

    @ExceptionHandler(Exception.class)
    ResponseEntity<Object> handleGeneralException(Exception ex, WebRequest request) {
        log.error('uri call: {} handler got a unexpected exception: {} ', ((ServletWebRequest) request).getRequest().getRequestURI(), ExceptionUtils.getStackTrace(ex))
        new ResponseEntity<>((new Response(500, '', COMMON_ERROR_MESSAGE)), OK)
    }
}
