package com.cheche365.bc.config;


import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class ZooKeeperConfiguration {

    @Bean
    @ConditionalOnProperty(name = "zookeeper.config.address")
    public ZooKeeperConfigProperties zooKeeperConfigProperties() {
        return new ZooKeeperConfigProperties();
    }
}