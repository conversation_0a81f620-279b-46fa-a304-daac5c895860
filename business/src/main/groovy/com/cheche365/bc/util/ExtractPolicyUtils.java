package com.cheche365.bc.util;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.AccountInfo;
import com.cheche365.bc.entity.PolicyDetailInfo;
import com.cheche365.bc.entity.PolicyEDIDetailInfo;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.exception.ProcessDataException;
import com.cheche365.bc.model.car.*;
import com.cheche365.bc.service.PolicySimplifyInfoService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.StringUtil;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.cheche365.bc.utils.CommonUtils.transBean2Map;
import static com.cheche365.bc.utils.CommonUtils.transMap2Bean;

/**
 * <p>
 * 抓取保单详情定时任务
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Component
@Slf4j
public class ExtractPolicyUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;
    private static PolicySimplifyInfoService policyService;
    private static MongoTemplate mongoTemplate;

    public static final String POLICY_LIST_FOR_WRITE_BACK = "policyListForWriteBack";
    public static final String POLICY_SIMPLIFY_INFO_LIST = "policySimplifyInfoList";
    public static final String POLICY_SIMPLIFY_INFO_LIST_SIZE = "policySimplifyInfoListSize";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ExtractPolicyUtils.applicationContext = applicationContext;
        policyService = applicationContext.getBean("policySimplifyInfoServiceImpl", PolicySimplifyInfoService.class);
        mongoTemplate = applicationContext.getBean("mongoTemplate", MongoTemplate.class);
    }

    /**
     * 传入查到的所有保单列表,该方法根据传入的时间段往前推对应时间的单子去重,
     * 以及把该时间段未保存的单子写回去,所以最终以该方法返回的结果集为准
     *
     * @param task         AutoTask 主要取 tempValues 中的 policySimplifyInfoList 保单列表
     * @param startDateStr 当前传入保单列表的开始日期，格式 yyyy-MM-dd
     * @param endDateStr   当前传入保单列表的结束日期，格式 yyyy-MM-dd
     * @return 筛选后的最终结果
     */
    public static List<PolicySimplifyInfo> findAllNeedDownloadPolicy(AutoTask task, String startDateStr, String endDateStr) {
        List<PolicySimplifyInfo> policyList = (List<PolicySimplifyInfo>) task.getTempValues().get(POLICY_SIMPLIFY_INFO_LIST);
        if (policyList == null)
            return Collections.emptyList();
        // 保存根据限定条件（如保单号，车架号，车牌号...）查询到的保单列表
        if (task.getConfigs().get("queryType") != null)
            return savePolices(task, policyList);
        if (!Boolean.TRUE.equals(task.getConfigs().get("noStop")) && (LocalDateTime.now().getHour() > 18)) // !LIANZ_DAMA.equals(Constant.DAMA_MODE)
            throw new ProcessDataException("当前时间超过19点保单抓取任务停止,或者手动强制停止运行");
        Map<String, Object> configs = task.getConfigs();
        String account = (String) configs.get("login");
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        long minusDays = startDate.until(endDate, ChronoUnit.DAYS);
        LocalDate lastCompleteDate = startDate.minusDays(minusDays > 7 ? 7 : (minusDays > 0 ? minusDays : 1));
        // 根据账号及起始终止日期查询保单列表
        List<PolicySimplifyInfo> lastDayPolicyList = policyService.findPolicyNoListFromTime(account, lastCompleteDate, endDate);
        // 获取保单号列表
        List<String> lastPolicyNoList = lastDayPolicyList.stream()
            .map(PolicySimplifyInfo::getPolicyNo)
            .collect(Collectors.toList());
        // 已经保存过的保单列表
        List<String> hasSavePolicyList = policyList.stream().map(PolicySimplifyInfo::getPolicyNo).filter(lastPolicyNoList::contains).collect(Collectors.toList());
        // 没有保存过的保单列表
        List<PolicySimplifyInfo> lastPolicyListNoDownload = lastDayPolicyList.stream()
            .filter(PolicySimplifyInfo::noDownload)
            .collect(Collectors.toList());
        info("账号：{}-{}在日期：{}至{},已经保存的单子列表：{},未下载的单子：{},日期{}至{},共抓取{}条记录，正在保存单子详情...", task.getCompanyId(), account, lastCompleteDate.toString(), startDateStr, hasSavePolicyList, lastPolicyListNoDownload.stream().map(PolicySimplifyInfo::getPolicyNo).collect(Collectors.toList()), startDateStr, endDateStr, policyList.size());
        // 去除之前保存过的保单号
        policyList = policyList.stream()
            .distinct()
            .filter(policy -> !lastPolicyNoList.contains(policy.getPolicyNo()))
            .collect(Collectors.toList());
        if (!policyList.isEmpty()) policyService.saveBatch(policyList, 10);
        // 获取上一天未成功的单子
        if (!lastPolicyListNoDownload.isEmpty())
            policyList.addAll(0, lastPolicyListNoDownload);
        task.getTempValues().put(POLICY_SIMPLIFY_INFO_LIST, policyList);
        Integer oldValue = (Integer) task.getTempValues().get(POLICY_SIMPLIFY_INFO_LIST_SIZE);
        task.getTempValues().put(POLICY_SIMPLIFY_INFO_LIST_SIZE, oldValue != null ? oldValue + policyList.size() : policyList.size());
        return policyList;
    }

    /**
     * 保存详情方法存到 mongodb 库中并且与 policySimplifyInfo 关联
     *
     * @param autoTask           包含详情的实体类
     * @param policySimplifyInfo 列表信息的实体类
     */
    public static void savePolicyDetail(AutoTask autoTask, PolicySimplifyInfo policySimplifyInfo) throws Exception {
        if (policySimplifyInfo == null)
            return;
        Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
        policySimplifyInfo.setAccountArea((String) autoTask.getConfigs().get("accountArea"));
        policySimplifyInfo.setAccountType((String) autoTask.getConfigs().get("accountType"));
        if (StringUtil.isEmpty(enquiry.getState()))
            enquiry.setState(policySimplifyInfo.getPolicyType());
        Map<String, Object> map = enquiry.getMisc() != null ? enquiry.getMisc() : new HashMap<>();
        map.putAll(transBean2Map(policySimplifyInfo));
        Map<String, Object> policyInfoProperties = map.entrySet().stream()
            .filter((e) -> e.getValue() != null && !" detailId, downloaded, plateNo, createTime, href, id, class, ext2, ext1".contains(e.getKey()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // 将 underWriteDate 由 LocalDate -> String
        policyInfoProperties.computeIfPresent("underWriteDate", (key, oldValue) -> policyInfoProperties.get(key).toString());
        enquiry.setMisc(policyInfoProperties);
        PolicyDetailInfo policyInfo = transMap2Bean(policyInfoProperties, PolicyDetailInfo.class);
        policyInfo.setEnquiry(enquiry);
        policyInfo.setCreateTime(LocalDateTime.now());
        policyInfo = doSaveMongodbEntity(policyInfo);
        policySimplifyInfo.setDownloaded(true);
        policySimplifyInfo.setDetailId(policyInfo.getId());
        policyService.saveOrUpdate(policySimplifyInfo);
        autoTask.setTaskEntity(initEnquiry());
    }

    /**
     * 保存保单详情到 mongodb 数据库
     *
     * @param policyInfo 保单详情实体
     * @return 保存过后的 policyInfo
     */
    private static PolicyDetailInfo doSaveMongodbEntity(PolicyDetailInfo policyInfo) {
        try {
            mongoTemplate.save(policyInfo);
        } catch (DuplicateKeyException e) {
            String errorMessage = String.format("保存账号：%s，下保单：%s,异常；", policyInfo.getAccountNo(), policyInfo.getPolicyNo());
            log.error(errorMessage, e);
            PolicySimplifyInfo policy = policyService.getPolicyByNo(policyInfo.getComId(), policyInfo.getPolicyNo());
            if (policy.noDownload())
                policyInfo = findPolicyDetailByPolicyNo(policy.getComId(), policy.getPolicyNo());
            else
                throw new RuntimeException(errorMessage + "请检查是否多个任务在跑同一账号相同时间的保单");
        } catch (Exception e) {
            throw new RuntimeException(String.format("保存账号：%s，下保单：%s；异常实体详情信息：%s", policyInfo.getAccountNo(), policyInfo.getPolicyNo(), JSONObject.toJSONString(policyInfo)), e);
        }
        return policyInfo;
    }

    /**
     * 根据保单号查询 mongodb 中的详情
     *
     * @param comId    保险公司 ID
     * @param policyNo 保单号
     */
    public static PolicyDetailInfo findPolicyDetailByPolicyNo(String comId, String policyNo) {
        return mongoTemplate.findOne(getQuery(new HashMap<String, Object>() {{
            put("comId", comId);
            put("policyNo", policyNo);
        }}), PolicyDetailInfo.class);
    }

    /**
     * 校验参请求数完整性
     *
     * @param objectMap 实体类的 JSON 字符串
     * @param clazz     实体类对应的 class
     */
    public static void validDateParams(Object objectMap, Class<?> clazz) {
        if (objectMap instanceof List<?>)
            ((List) objectMap).forEach(subMap -> validDateParams(subMap, clazz));
        else
            Arrays.stream(clazz.getDeclaredFields()).forEach(field -> {
                NotNull notNull = field.getAnnotation(NotNull.class);
                Object object = ((Map) objectMap).get(field.getName());
                if (notNull != null) {
                    if (object == null)
                        throw new IllegalArgumentException(notNull.message());
                    if (notNull.groups().length > 0) {
                        if (object instanceof List<?>)
                            ((List<Map>) object).stream().forEach(subField -> validDateParams(subField, notNull.groups()[0]));
                        else validDateParams(object, notNull.groups()[0]);
                    }
                }
            });
    }

    /**
     * 同时记录日志到项目日志系统，和 xxl-job 日志系统中
     *
     * @param messagePattern     the format string
     * @param appendLogArguments arguments a list of 3 or more arguments
     */
    public static void info(final String messagePattern, Object... appendLogArguments) {
        log.info(messagePattern, appendLogArguments);
    }

    public static void error(final String messagePattern, Object... appendLogArguments) {
        log.error(messagePattern, appendLogArguments);
    }

    public static Enquiry initEnquiry() {
        Enquiry enquiry = new Enquiry();
        Order order = new Order();
        BaseSuiteInfo baseSuiteInfo = new BaseSuiteInfo();
        baseSuiteInfo.setBizSuiteInfo(new BizSuiteInfo());
        baseSuiteInfo.setEfcSuiteInfo(new EfcSuiteInfo());
        baseSuiteInfo.setTaxSuiteInfo(new TaxSuiteInfo());
        baseSuiteInfo.setExtSuiteInfo(new HashMap<>());
        order.setCarInfo(new CarInfo());
        order.setCarOwnerInfo(new CarOwnerInfo());
        order.setInsuredPersons(new ArrayList<InsurePerson>() {{
            add(new InsurePerson());
        }});
        order.setInsurePerson(new InsurePerson());
        order.setSuiteInfo(baseSuiteInfo);
        enquiry.setOrder(order);
        enquiry.getOrder().setPlatformInfo(new HashMap<>());
        return enquiry;
    }

    /**
     * 拼接 mongodb 查询参数
     *
     * @param params mongodb 字段名和 value 值
     * @return String 查询语句
     */
    public static Query getQuery(Map<String, Object> params) {
        Query query = new Query(getCriteria(params));
        log.info("getQuery:{}", query.toString());
        return query;
    }

    public static Criteria getCriteria(Map<String, Object> params) {
        Criteria criteria = new Criteria();
        if (params.get("startDate") != null)
            criteria.and(MapUtils.getString(params, "queryTimeKey", "createTime"))
                .gte(LocalDate.parse((CharSequence) params.get("startDate")))
                .lte(LocalDate.parse((CharSequence) params.get("endDate")));
        params.forEach((key, value) -> {
            if (StringUtil.isEmpty(String.valueOf(value)))
                criteria.and(key).exists(false);
            else if (!"startDate,endDate,queryTimeKey,pageSize".contains(key))
                criteria.and(key).is(value);
        });
        return criteria;
    }

    /**
     * 保存保单列表，如果某个保单重复并报错，则取当前账号的最后完成日期为当前账号的完成日期
     *
     * @param message 异常信息
     * @param account 异常账号
     */
    public static void detailDuplicateKeyAccount(String message, AccountInfo account) {
        log.info("保存保单列表异常:" + message);
        Matcher matcher = Pattern.compile("Duplicate entry '(\\w+)-(\\w+)").matcher(message);
        if (matcher.find()) {
            String comId = matcher.group(2);
            String policyNo = matcher.group(1);
            log.info("修改 Duplicate entry 异常：comId:{},accountNo:{}", comId, account);
            account.setLastCompleteTime(policyService.findLastDate(comId, policyService.getPolicyByNo(comId, policyNo).getAccountNo(), true));
            account.setStatus(CommStatus.ENABLE);
        }
    }

    /**
     * 根据保险公司 + 保单号去重，保存保单列表，
     *
     * @param task       AutoTask
     * @param policyList 保单列表
     * @return 去重后的保单列表
     */
    private static List<PolicySimplifyInfo> savePolices(AutoTask task, List<PolicySimplifyInfo> policyList) {
        if (policyList == null)
            return Collections.emptyList();
        task.getTempValues().put(POLICY_LIST_FOR_WRITE_BACK, policyList);
        policyList = policyList.stream()
            .map(policy -> {
                PolicySimplifyInfo simplifyInfo = policyService.getPolicyByNo(policy.getComId(), policy.getPolicyNo());
                return Objects.isNull(simplifyInfo) ? policy : simplifyInfo.noDownload() ? simplifyInfo : null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        if (!policyList.isEmpty()) policyService.saveOrUpdateBatch(policyList, 10);
        task.getTempValues().put(POLICY_SIMPLIFY_INFO_LIST, policyList);
        return policyList;
    }

    /**
     * 查询MongoDB抓单记录(当前日期前12个月的)
     *
     * @return
     * @throws Exception
     */
    public static List<List<Map>> getCriterias(Map<String, Object> params) throws Exception {
        Map<String, Aggregation> stringListMapGrab = new HashMap<>();
        Map<String, Aggregation> stringListMapHLHT = new HashMap<>();

        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        if (StringUtil.isEmpty(startDate) || StringUtil.isEmpty(endDate)) {   //定时任务中日期为空时，自动抓取前一个月的数据
            endDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).toString();
            startDate = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).toString();
        }
        int lastTwelveMonths = StringUtils.isNotBlank((String) params.get("type")) ? timeInterval(startDate, endDate) : 12;
        //查询当前日期前12个月的数据(参数信息)
        for (int i = 1; i <= lastTwelveMonths; i++) {
            Map<String, Object> resultMap = getBeforeFirstMonthdate(i, endDate);

            //抓单数据查询条件
            Criteria criteriaGrab = new Criteria();
            criteriaGrab.and("createTime")
                .gte(LocalDate.parse((String) resultMap.get("startDate"), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().plusHours(8))
                .lte(LocalDate.parse((String) resultMap.get("endDate"), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().plusHours(8))
                .and("accountType").is(params.get("accountType"));   //抓单查询增加账号类型条件
            Aggregation aggregationGrab = Aggregation.newAggregation(Aggregation.match(criteriaGrab),
                Aggregation.group("comId").count().as("sum").max("comId").as("comId")); //按照comId降序
            stringListMapGrab.put((String) resultMap.get("yearAndMonth"), aggregationGrab);

            //互联互通数据查询条件
            Criteria criteriaHLHT = new Criteria();
            criteriaHLHT.and("createTime")
                .gte(LocalDate.parse((String) resultMap.get("startDate"), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().plusHours(8))
                .lte(LocalDate.parse((String) resultMap.get("endDate"), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().plusHours(8));
            Aggregation aggregationHLHT = Aggregation.newAggregation(Aggregation.match(criteriaHLHT),
                Aggregation.group("comId").count().as("sum").max("comId").as("comId")); //按照comId降序
            stringListMapHLHT.put((String) resultMap.get("yearAndMonth"), aggregationHLHT);
        }
        List<Map> policyInfoList = new LinkedList<>();
        List<Map> policyInfoEdiList = new LinkedList<>();

        //查询当前日期前12个月的数据-抓单
        stringListMapGrab.forEach(new BiConsumer<String, Aggregation>() {
            @Override
            public void accept(String month, Aggregation aggregation) {
                AggregationResults<Map> policyInfoResults = mongoTemplate.aggregate(aggregation, PolicyDetailInfo.class, Map.class);
                List<Map> list1 = policyInfoResults.getMappedResults();
                list1.forEach(map -> map.put("month", month));
                policyInfoList.addAll(list1);
            }
        });
        //查询当前日期前12个月的数据-互联互通
        stringListMapHLHT.forEach(new BiConsumer<String, Aggregation>() {
            @Override
            public void accept(String month, Aggregation aggregation) {
                AggregationResults<Map> policyInfoEdiResults = mongoTemplate.aggregate(aggregation, PolicyEDIDetailInfo.class, Map.class);
                List<Map> list2 = policyInfoEdiResults.getMappedResults();
                list2.forEach(map -> map.put("month", month));
                policyInfoEdiList.addAll(list2);
            }
        });
        List<List<Map>> result = new ArrayList<List<Map>>() {{
            add(policyInfoList.stream()
                .filter(map -> String.valueOf(map.get("comId")).matches("[0-9]+"))
                .sorted(Comparator.comparing(map -> (String) map.get("month")))
                .collect(Collectors.toList()));
            add(policyInfoEdiList.stream()
                .filter(map -> String.valueOf(map.get("comId")).matches("[0-9]+"))
                .sorted(Comparator.comparing(map -> (String) map.get("month")))
                .collect(Collectors.toList()));
        }};
        return result;
    }


    /**
     * 计算从endTime开始前推month的月份开始时间、结束时间
     *
     * @param month
     * @param endTime
     * @return
     * @throws Exception
     */
    public static Map<String, Object> getBeforeFirstMonthdate(int month, String endTime) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.now();
        if (StringUtils.isNotBlank(endTime)) localDate = LocalDate.parse(endTime);
        resultMap.put("startDate", localDate.minusMonths(month).with(TemporalAdjusters.firstDayOfMonth()).format(df));
        resultMap.put("endDate", localDate.minusMonths(month - 1).with(TemporalAdjusters.firstDayOfMonth()).format(df));
        resultMap.put("yearAndMonth", localDate.minusMonths(month).with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM")));
        return resultMap;
    }

    /**
     * 计算两个时间间隔月数量
     *
     * @param start
     * @param end
     * @return
     */
    private static int timeInterval(String start, String end) {
        return (int) ChronoUnit.MONTHS.between(LocalDate.parse(start), LocalDate.parse(end));
    }

    public static List<AccountInfo> validateAndGetAccountParams(String param) {
        List<AccountInfo> accountInfoList;
        try {
            List<JSONObject> JSONList = parseObject(param, List.class);
            validDateParams(JSONList, AccountInfo.class);
            accountInfoList = JSONList.stream()
                .map(accountInfo -> {
                    accountInfo.put("configStr", accountInfo.get("config"));
                    return JSONObject.toJavaObject(accountInfo, AccountInfo.class);
                })
                .collect(Collectors.toList());
        } catch (JSONException e) {
            error("JSON参数不合规：{}", e.getMessage(), e);
            throw e;
        }
        return accountInfoList;
    }
}

