package com.cheche365.bc.aspect;

import com.cheche365.bc.annotation.RateLimit;
import com.cheche365.bc.model.RestResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.cheche365.bc.constants.Constants.SECURE_APP_KEY;
import static com.qcloud.cosapi.http.RequestHeaderKey.Authorization;

/**
 * 第三方安全性校验aop
 * Created by xcl on 2021/03/24.
 */
@Aspect
@Component
@Slf4j
@AllArgsConstructor
public class RateLimitAspect {

    private final StringRedisTemplate redisTemplate;

    /**
     * 对访问次数的限制
     *
     * @param pdj       切点
     * @param rateLimit 次数限制注解
     */
    @Around("@annotation(rateLimit)")
    public RestResponse limitAccess(ProceedingJoinPoint pdj, RateLimit rateLimit) throws Throwable {
        HttpServletRequest request = ((HttpServletRequest) pdj.getArgs()[0]);
        String authorization = request.getHeader(Authorization);
        String appKey = request.getHeader(SECURE_APP_KEY);
        if (rateLimit != null) {
            int minutes = rateLimit.seconds();
            int maxCount = rateLimit.maxCount();
            String key = rateLimit.key() + "_" + appKey + "_" + authorization;
            String count = redisTemplate.opsForValue().get(key);
            if (count == null) {
                redisTemplate.opsForValue().set(key, "1", minutes, TimeUnit.SECONDS);
            } else if (Integer.valueOf(count) < maxCount) {
                redisTemplate.opsForValue().increment(key);
            } else {
                RestResponse response = RestResponse.failedMessage("当前 key：" + key + "访问过于频繁");
                log.error(response.getRestMsg());
                return response;
            }
        }
        return (RestResponse) pdj.proceed();
    }
}
