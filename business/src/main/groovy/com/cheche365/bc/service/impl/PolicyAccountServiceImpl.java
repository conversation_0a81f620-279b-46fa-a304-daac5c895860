package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.AccountInfo;
import com.cheche365.bc.mapper.PolicyAccountInfoMapper;
import com.cheche365.bc.service.PolicyAccountService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alibaba.fastjson.JSON.parseObject;

/**
 * <AUTHOR>
 * @since 2021-01-8
 */
@Service
@Log4j2
public class PolicyAccountServiceImpl extends ServiceImpl<PolicyAccountInfoMapper, AccountInfo> implements PolicyAccountService {

    /**
     * 从数据库中获取账号信息（所有操作操作数据库账号都要调用该方法，处理账号中的 config）
     *
     * @param properties 账号查询的删选条件
     * @return 账号列表
     */
    public List<AccountInfo> getAccountSFormDatabase(Map<String, Object> properties) {
        return getAccountSFormDatabase(properties, null);
    }

    public List<AccountInfo> getAccountSFormDatabase(Map<String, Object> properties, Boolean isAsc, String... orderColumns) {
        // 否则则根据账号库中的配置信息抓取保单信息
        QueryWrapper<AccountInfo> query = new QueryWrapper<>();
        properties.forEach(query::eq);
        if (isAsc != null) query.orderBy(true, isAsc, Arrays.asList(orderColumns));
        List<AccountInfo> accountInfoList = baseMapper.selectList(query);
        accountInfoList.stream()
                .forEach(accountInfo -> accountInfo.setConfig(parseObject(accountInfo.getConfigStr(), HashMap.class)));
        return accountInfoList;
    }
}
