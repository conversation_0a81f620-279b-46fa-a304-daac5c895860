package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.PolicySource;
import com.cheche365.bc.mapper.PolicySourceMapper;
import com.cheche365.bc.service.PolicySourceService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * 请注意数据库中存储的policyNo都是正常单号的逆序!!!!!!!!查询和存储时请注意！！！！
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Service
public class PolicySourceServiceImpl extends ServiceImpl<PolicySourceMapper, PolicySource> implements PolicySourceService {

    @Override
    public void savePolicySource(PolicySource source) {
        PolicySource exist = getByPolicyNo(source.getPolicyNo());
        if (Objects.isNull(exist)) {
            save(source);
        }
    }

    @Override
    public PolicySource getByPolicyNo(String policyNo) {
        return getOne(Wrappers.<PolicySource>lambdaQuery().eq(PolicySource::getPolicyNo, policyNo), false);
    }

    @Override
    public List<PolicySource> listByPolicyNos(Collection<String> policyNos) {
        return list(Wrappers.<PolicySource>lambdaQuery().in(PolicySource::getPolicyNo, policyNos));
    }
}
