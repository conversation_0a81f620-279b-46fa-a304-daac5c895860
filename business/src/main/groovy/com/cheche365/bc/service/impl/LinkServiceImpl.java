package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.Link;
import com.cheche365.bc.mapper.LinkMapper;
import com.cheche365.bc.service.LinkService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
public class LinkServiceImpl extends ServiceImpl<LinkMapper, Link> implements LinkService {

    @Override
    public void updateShortLinkById(Link link) {
        UpdateWrapper<Link> updateWrapper = new UpdateWrapper();
        updateWrapper.lambda()
            .eq(Link::getId, link.getId())
            .set(Link::getShortLink, link.getShortLink());
        update(updateWrapper);
    }
}
