package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.BusinessDataSourceAutoTaskLog;
import com.cheche365.bc.mapper.BusinessDataSourceAutoTaskLogMapper;
import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import com.cheche365.bc.utils.Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">蒋昌宝</a>
 */
@Service
public class BusinessDataSourceAutoTaskLogServiceImpl extends ServiceImpl<BusinessDataSourceAutoTaskLogMapper, BusinessDataSourceAutoTaskLog>
        implements BusinessDataSourceAutoTaskLogService {

    private static final Logger logger = LoggerFactory.getLogger("businessDataSourceAutoTaskLog");

    private BusinessDataSourceAutoTaskLogMapper businessDataSourceAutoTaskLogMapper;

    @Autowired
    public void setBusinessDataSourceAutoTaskLogMapper(BusinessDataSourceAutoTaskLogMapper businessDataSourceAutoTaskLogMapper) {
        this.businessDataSourceAutoTaskLogMapper = businessDataSourceAutoTaskLogMapper;
    }


    @Override
    public Integer getDataSourceLogId(String product, String scenario, String channel) {
        // 区分磐石还是车生态，获取 baseParentId 磐石id：1 车生态id：2
        int baseParentId = ("cby".equals(product) || "bedrock".equals(product) || "ps".equals(product)) ? 1 : 2;
        // 查询此 baseParentId 下是否有此 product。若没有则插入
        BusinessDataSourceAutoTaskLog idWithProduct = getByNameAndParentId(product, baseParentId);

        if (Objects.isNull(idWithProduct)) {
            int productId = insertRequestSource(product, baseParentId);
            int scenarioId = insertRequestSource(scenario, productId);
            return insertWithChannel(scenarioId, channel);
        }

        BusinessDataSourceAutoTaskLog idWithScenario = getByNameAndParentId(scenario, idWithProduct.getId());
        if (Objects.isNull(idWithScenario)) {
            int scenarioId = insertRequestSource(scenario, idWithProduct.getId());
            return insertWithChannel(scenarioId, channel);
        }

        if (StringUtils.isBlank(channel)) {
            return idWithScenario.getId();
        }

        BusinessDataSourceAutoTaskLog idWithChannel = getByNameAndParentId(channel, idWithScenario.getId());
        if (Objects.isNull(idWithChannel)) {
            return insertRequestSource(channel, idWithScenario.getId());
        }

        return idWithChannel.getId();
    }


    @Autowired
    public void setRequestSourceMapper(BusinessDataSourceAutoTaskLogMapper businessDataSourceAutoTaskLogMapper) {
        this.businessDataSourceAutoTaskLogMapper = businessDataSourceAutoTaskLogMapper;
    }

    @Override
    public List<Map<String, Object>> getTreeList() {
        QueryWrapper<BusinessDataSourceAutoTaskLog> wrapper = new QueryWrapper<>();
        wrapper.select("id", "name", "parent_id parentId");
        return businessDataSourceAutoTaskLogMapper.selectMaps(wrapper);
    }

    @Override
    public List<Integer> getIdListByParentId(Integer parentId) {
        List<Integer> resultList = new ArrayList<>();
        getIdListByParentId(parentId, resultList);
        return resultList;
    }

    public void getIdListByParentId(Integer parentId, List<Integer> resultList) {
        QueryWrapper<BusinessDataSourceAutoTaskLog> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(BusinessDataSourceAutoTaskLog::getId)
                .eq(BusinessDataSourceAutoTaskLog::getParentId, parentId);

        resultList.add(parentId);
        List<Integer> list = businessDataSourceAutoTaskLogMapper.selectList(wrapper)
                .stream().map(BusinessDataSourceAutoTaskLog::getId).collect(Collectors.toList());
        if (list.size() > 0) {
            for (Integer id : list) {
                getIdListByParentId(id, resultList);
            }
        }
    }

    public BusinessDataSourceAutoTaskLog getByNameAndParentId(String name, Integer parentId) {
        QueryWrapper<BusinessDataSourceAutoTaskLog> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(BusinessDataSourceAutoTaskLog::getId)
                .eq(BusinessDataSourceAutoTaskLog::getName, name)
                .eq(BusinessDataSourceAutoTaskLog::getParentId, parentId);
        return businessDataSourceAutoTaskLogMapper.selectOne(wrapper);
    }

    public int insertRequestSource(String name, int parentId) {
        if (-1 == parentId) {
            return -1;
        }

        BusinessDataSourceAutoTaskLog businessDataSourceAutoTaskLog = new BusinessDataSourceAutoTaskLog();
        businessDataSourceAutoTaskLog.setName(name).setParentId(parentId);
        try {
            businessDataSourceAutoTaskLogMapper.insert(businessDataSourceAutoTaskLog);
        } catch (Exception e) {
            logger.error("businessDataSourceAutoTaskLog新增log下拉框失败：{}", Util.getStackTrace(e));
            return -1;
        }

        return businessDataSourceAutoTaskLog.getId();
    }

    public int insertWithChannel(int scenarioId, String channel) {
        if (StringUtils.isBlank(channel)) {
            return scenarioId;
        } else {
            return insertRequestSource(channel, scenarioId);
        }
    }

}
