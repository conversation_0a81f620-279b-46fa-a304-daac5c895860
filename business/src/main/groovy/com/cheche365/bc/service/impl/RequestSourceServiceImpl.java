package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.RequestSource;
import com.cheche365.bc.mapper.RequestSourceMapper;
import com.cheche365.bc.service.RequestSourceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @since 2022-06-20
 */
@Service
public class RequestSourceServiceImpl extends ServiceImpl<RequestSourceMapper, RequestSource>
        implements RequestSourceService {

    private RequestSourceMapper requestSourceMapper;

    @Autowired
    public void setRequestSourceMapper(RequestSourceMapper requestSourceMapper) {
        this.requestSourceMapper = requestSourceMapper;
    }

    @Override
    public Integer getIdByName(String name) {
        if (StringUtils.isBlank(name)) {
            return 2;
        }

        String[] split = name.split("_");
        String parentName = null;
        String childName;
        if (split.length == 1) {
            name = split[0];
            Integer parentId = 2;
            RequestSource requestSource = getByNameAndParentId(name, parentId);
            if (Objects.isNull(requestSource)) {
                requestSource = new RequestSource();
                requestSource.setName(name);
                requestSource.setParentId(parentId);
                requestSourceMapper.insert(requestSource);
            }

            return requestSource.getId();
        } else {
            Integer parentId = 2;
            for (String s : split) {
                RequestSource requestSource = getByNameAndParentId(s, parentId);
                if (Objects.isNull(requestSource)) {
                    requestSource = new RequestSource();
                    requestSource.setName(s);
                    requestSource.setParentId(parentId);
                    requestSourceMapper.insert(requestSource);
                }

                parentId = requestSource.getId();
            }

            return parentId;
        }
    }

    private RequestSource getByNameAndParentId(String name, Integer parentId) {
        QueryWrapper<RequestSource> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(RequestSource::getId)
                .eq(RequestSource::getName, name)
                .eq(RequestSource::getParentId, parentId);
        return requestSourceMapper.selectOne(wrapper);
    }

    @Override
    public List<Integer> getIdListByParentId(Integer parentId) {
        List<Integer> resultList = new ArrayList<>();
        getIdListByParentId(parentId, resultList);
        return resultList;
    }

    public void getIdListByParentId(Integer parentId, List<Integer> resultList) {
        QueryWrapper<RequestSource> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(RequestSource::getId)
                .eq(RequestSource::getParentId, parentId);
        resultList.add(parentId);
        List<Integer> list = requestSourceMapper.selectList(wrapper)
                .stream().map(RequestSource::getId).collect(Collectors.toList());
        if (list.size() > 0) {
            for (Integer id : list) {
                getIdListByParentId(id, resultList);
            }
        }
    }

    @Override
    public List<Map<String, Object>> getTreeList() {
        QueryWrapper<RequestSource> wrapper = new QueryWrapper<>();
        wrapper.select("id", "name", "parent_id parentId");
        return requestSourceMapper.selectMaps(wrapper);
    }

}
