package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.AutoTaskExceptionConfig;

import java.util.List;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @description
 * @since 2021-12-06
 */
public interface AutoTaskExceptionConfigService extends IService<AutoTaskExceptionConfig> {

    List<AutoTaskExceptionConfig> listByCompanyIdAndProcessTypeAndTaskType(String companyId, String processType, String taskType);

    String findExceptionConversion(String companyId, String processType, String taskType, String errorMsg);

    String findExceptionConversion(String errorMsg, List<AutoTaskExceptionConfig> autoTaskExceptionConfigs);

    Boolean matchException(String e, AutoTaskExceptionConfig config);
}
