package com.cheche365.bc.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.Interface;
import com.cheche365.bc.mapper.InterfaceMapper;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.task.AutoTask;
import com.google.common.collect.Maps;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.cheche365.bc.constants.Constants.INTERFACE_CACHE_KEY_PREFIX;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
@Slf4j
public class InterfaceServiceImpl extends ServiceImpl<InterfaceMapper, Interface> implements InterfaceService {

    private final static ConcurrentMap<String, Script> SCRIPT_OBJ_CACHE = new ConcurrentHashMap<>();

    @Override
    public Script getInterfaceScriptByIntType(String intType) {
        if (SCRIPT_OBJ_CACHE.containsKey(INTERFACE_CACHE_KEY_PREFIX + intType)) {
            return SCRIPT_OBJ_CACHE.get(INTERFACE_CACHE_KEY_PREFIX + intType);
        }
        Script scriptObj = null;
        List<Interface> itfs = listByMap(getParamMap(intType));
        if (CollUtil.isNotEmpty(itfs)) {
            Interface itf = itfs.get(0);
            scriptObj = new GroovyShell().parse(itf.getTerm());
            SCRIPT_OBJ_CACHE.put(INTERFACE_CACHE_KEY_PREFIX + intType, scriptObj);
        }
        return scriptObj;
    }

    @Override
    public boolean hasInterfaceAbility(String taskType) {
        return exists(Wrappers.<Interface>query().allEq(getParamMap(taskType)));
    }

    @Override
    public Interface getInterfaceByTaskType(String taskType) {
        return getOne(Wrappers.<Interface>query().allEq(getParamMap(taskType)), false);
    }

    @Override
    public boolean updateScriptCache(String taskType) {
        Map<String, Object> param = getParamMap(taskType);
        List<Interface> itfs = listByMap(param);
        if (CollUtil.isNotEmpty(itfs)) {
            Interface itf = itfs.get(0);
            Script scriptObj = new GroovyShell().parse(itf.getTerm());
            SCRIPT_OBJ_CACHE.put(INTERFACE_CACHE_KEY_PREFIX + itf.getIntType() + "-" + itf.getComId() + "-" + itf.getAction(), scriptObj);
        } else {
            SCRIPT_OBJ_CACHE.remove(INTERFACE_CACHE_KEY_PREFIX + param.get("intType") + "-" + param.get("comId") + "-" + param.get("action"));
        }
        return true;
    }

    @Override
    public Interface getInterface(AutoTask t, String intType) {
        Interface ediInterface = null;
        try {
            ediInterface = getInterfaceByTaskType(intType);
            if (ediInterface == null) {
                if (!intType.contains("_callback")) {
                    String errorMsg = String.format("未找到对应任务:%s,类型:%s的接口，请确认接口配置是否正确！", String.format("%s@%s", t.getTraceKey(), t.getCompanyId()), intType);
                    log.error(errorMsg);
                    throw new Exception(errorMsg);
                } else {
                    return null;
                }
            }
            return ediInterface.copy();
        } catch (Exception e) {
            if (!intType.contains("_callback")) {
                log.error("获取接口信息失败 autoTask：{},{}", t.getTraceKey(), ExceptionUtils.getStackTrace(e));
            }
        }
        return ediInterface;
    }

    private Map<String, Object> getParamMap(String taskType) {
        Map<String, Object> param = Maps.newHashMap();
        String[] params = taskType.split("-");
        param.put("intType", params[0]);
        param.put("comId", params[1]);
        param.put("action", params[2]);
        return param;
    }
}
