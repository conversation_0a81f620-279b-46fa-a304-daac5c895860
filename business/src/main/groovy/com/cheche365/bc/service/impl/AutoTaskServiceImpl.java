package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.dto.AutoQuoteCoreInfoSchema;
import com.cheche365.bc.dto.AutoTaskBiLogSchema;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.mapper.AutoTaskMapper;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.util.AutoQuoteCoreInfoBiLogUtil;
import com.cheche365.bc.util.AutoTaskBiLogUtil;
import com.cheche365.bi.logger.BiLoggerV2;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class AutoTaskServiceImpl extends ServiceImpl<AutoTaskMapper, AutoTask> implements AutoTaskService {

    @Resource
    private BiLoggerV2 biLogger;

    @Override
    public List<AutoTask> getTaskByTaskIdComTaskType(String taskId, String com, String taskType) {
        return lambdaQuery().eq(AutoTask::getTraceKey, taskId)
                //
                .eq(AutoTask::getCompanyId, com)
                //
                .eq(AutoTask::getTaskType, taskType)
                //
                .orderByDesc(AutoTask::getStartTime)
                .list();
    }

    @Override
    public AutoTask getTaskByTaskTypeProposeNumOrOrderNo(String taskType, String no, int noType) {
        QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskType), "taskType", taskType);
        if (noType == 1)
            wrapper.eq("biz_propose_no", no);
        else if (noType == 2)
            wrapper.eq("efc_propose_no", no);
        else
            wrapper.eq("biz_policy_no", no);
        return getOne(wrapper, false);
    }

    @Override
    public AutoTask getTaskByProposeNumOrOrderNo(String no) {
        return getOne(new QueryWrapper<AutoTask>().lambda()
            .and(
                wrapper -> wrapper.eq(AutoTask::getBizPolicyNo, no)
            ), false
        );
    }

    @Override
    public boolean updateById(AutoTask task) {
        // 处理生成 BI 日志
        try {
            com.cheche365.bc.task.AutoTask autoTaskRequest = (com.cheche365.bc.task.AutoTask) task;
            AutoTaskBiLogSchema schema = AutoTaskBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest);
            if (Objects.nonNull(schema)) {
                biLogger.info(schema);
                if (TaskStatus.QUOTE_SUCCESS.State().equals(task.getTaskStatus()) || TaskStatus.AUTO_INSURE_SUCCESS.State().equals(task.getTaskStatus())) {
                    AutoQuoteCoreInfoSchema autoQuoteCoreInfoSchema = AutoQuoteCoreInfoBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest, schema);
                    biLogger.info(autoQuoteCoreInfoSchema);
                }
            }
        } catch (Exception e) {
            log.error("BI 日志打印失败：{}", ExceptionUtils.getStackTrace(e));
        }
        return super.updateById(task);
    }

    @Override
    public AutoTask getOneTask(String taskId, String com, String[] taskType, Object bizProposeNo, Object efcProposeNo) {
        LambdaQueryWrapper<AutoTask> queryWrapper = new QueryWrapper<AutoTask>().lambda();
        queryWrapper.eq(StringUtils.isNotEmpty(taskId), AutoTask::getTraceKey, taskId);
        queryWrapper.eq(StringUtils.isNotEmpty(com), AutoTask::getCompanyId, com);
        if (null != bizProposeNo) {
            queryWrapper.eq(AutoTask::getBizProposeNo, bizProposeNo.toString());
        }
        if (null == bizProposeNo && null != efcProposeNo) {
            queryWrapper.eq(AutoTask::getEfcProposeNo, efcProposeNo.toString());
        }
        if (taskType.length > 1) {
            queryWrapper.in(AutoTask::getTaskType, Arrays.asList(taskType));
        } else {
            queryWrapper.eq(AutoTask::getTaskType, taskType[0]);
            queryWrapper.orderByDesc(AutoTask::getStartTime);
        }
        return baseMapper.selectOne(queryWrapper.last("limit 1"));
    }

    @Override
    public void updateClaimInfoByAutoTraceId(AutoTask autoTask) {
        UpdateWrapper<AutoTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(AutoTask::getAutoTraceId, autoTask.getAutoTraceId())
                .set(AutoTask::getActionLogs, autoTask.getActionLogs())
                .set(AutoTask::getResultStr, autoTask.getResultStr())
                .set(AutoTask::getEndTime, autoTask.getEndTime())
                .set(AutoTask::getRequestSourceId, autoTask.getRequestSourceId())
                .set(AutoTask::getFeedbackJson, autoTask.getFeedbackJson());
        try {
            update(updateWrapper);
        } catch (Exception e) {
            log.error("update error: {}", ExceptionUtils.getStackTrace(e));
        }

    }

    @Override
    public void updateClaimStatusByAutoTraceId(AutoTask autoTask) {
        UpdateWrapper<AutoTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(AutoTask::getAutoTraceId, autoTask.getAutoTraceId())
                .set(AutoTask::getTaskStatus, autoTask.getTaskStatus())
                .set(AutoTask::getResultStr, autoTask.getResultStr());
        update(updateWrapper);
    }


}
