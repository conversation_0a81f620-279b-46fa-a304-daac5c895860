package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.InsWorkerTime;
import com.cheche365.bc.mapper.InsWorkerTimeMapper;
import com.cheche365.bc.service.InsWorkerTimeService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-04
 */
@Service
public class InsWorkerTimeServiceImpl extends ServiceImpl<InsWorkerTimeMapper, InsWorkerTime> implements InsWorkerTimeService {

    @Override
    public String checkWorkTime(String comId) {
        InsWorkerTime workerTime = baseMapper.selectOne(new QueryWrapper<InsWorkerTime>().lambda()
            .eq(InsWorkerTime::getComId, comId)
                .orderByDesc(InsWorkerTime::getCreateTime));
        int hour = LocalDateTime.now().getHour();
        if (workerTime != null && (hour < workerTime.getStartHour() || hour > workerTime.getEndHour())) {
            return "公司" + comId + "只在" + workerTime.getStartHour() + "点到" + workerTime.getEndHour() + "点提供服务！";
        } else {
            return null;
        }
    }
}
