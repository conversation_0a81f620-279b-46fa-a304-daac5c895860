package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.RequestSource;

import java.util.List;
import java.util.Map;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @since 2022-06-20
 */
public interface RequestSourceService extends IService<RequestSource> {

    Integer getIdByName(String name);

    List<Integer> getIdListByParentId(Integer parentId);

    List<Map<String, Object>> getTreeList();
}
