package com.cheche365.bc.service.impl;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.PolicyDetailInfo;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.service.PolicyWriteBackService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.util.ExtractPolicyUtils;
import com.cheche365.bc.utils.sdas.SDASUtils;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cheche365.bc.util.ExtractPolicyUtils.POLICY_LIST_FOR_WRITE_BACK;
import static com.cheche365.bc.util.ExtractPolicyUtils.findPolicyDetailByPolicyNo;
import static com.cheche365.bc.utils.sender.HttpSender.*;


@Slf4j
@Service
public class PolicyWriteBackServiceImpl implements PolicyWriteBackService {

    @Autowired
    MongoTemplate mongoTemplate;

    @Value("${app.key:''}")
    String appKey;

    @Value("${secret.key:''}")
    String secretKey;

    private Map<String, String> defaultHeader = new HashMap<String, String>() {{
        put("charset", "utf-8");
        put("content-type", "application/json");
    }};

    /**
     * 回写当前 task 下的账号和日期的所有的保单详情
     *
     * @param task    AutoTask
     * @param failure 异常
     */
    public void writeBackPolicyByAccountNo(AutoTask task, Throwable failure) {
        try {
            AtomicInteger num = new AtomicInteger(0);
            Map<String, Object> params = new HashMap<>();
            params.put("startDate", task.getConfigs().get("startDate"));
            params.put("endDate", LocalDate.parse((String) task.getConfigs().get("endDate")).plusDays(1).toString());
            params.put("accountNo", task.getTraceKey());
            params.put("comId", task.getCompanyId());
            params.put("queryTimeKey", "underWriteDate");
            List<PolicyDetailInfo> listPolicy = new ArrayList<>();
            int code = failure instanceof UniqueException ? ((UniqueException) failure).getCode() : -1;
            code = failure != null ? code == 1 ? 11 : code : 1; // 兼容 UniqueException.IllegalArgs 异常 code
            RestResponse<Map<String, Object>> request = new RestResponse<>(code, task.getFailureCause());
            Map<String, Object> context = new HashMap<String, Object>() {{
                put("taskId", task.getConfigs().get("taskId"));
                put("finished", false);
            }};
            Map<String, String> headers = new HashMap<String, String>() {{
                putAll(defaultHeader);
                put("blade-auth", (String) task.getConfigs().get("blade-auth"));
            }};
            log.info("taskId:{},请求头为：{}", context.get("taskId"), headers);
            request.setRestContext(context);
            if (task.getFailureCause() == null) {
                // 根据车牌号等条件查询到的保单详情列表
                List<PolicySimplifyInfo> policySimplifyList = (List<PolicySimplifyInfo>) task.getTempValues().get(POLICY_LIST_FOR_WRITE_BACK);
                if (policySimplifyList != null)
                    listPolicy = policySimplifyList.stream().map(policy -> findPolicyDetailByPolicyNo(policy.getComId(), policy.getPolicyNo())).collect(Collectors.toList());
                else
                    // 根据日期查询保单详情列表
                    listPolicy = mongoTemplate.find(ExtractPolicyUtils.getQuery(params), PolicyDetailInfo.class);
                listPolicy.forEach(policy -> {
                    num.incrementAndGet();
                    Enquiry enquiry = policy.getEnquiry();
                    boolean repeatPushFlag = false;
                    if (("2045".equals(task.getCompanyId()) || "2066".equals(task.getCompanyId())) && "3".equals(enquiry.getMisc().get("policyType"))) {
                        repeatPushFlag = true;
                    } else {
                        repeatPushFlag = false;
                    }
                    String policyNo = (String) enquiry.getMisc().get("policyNo");
                    if (policyNo.equals(enquiry.getBizPolicyCode())) {
                        enquiry.setConfig(new HashMap());
                        enquiry.getMisc().put("policyType", "1");
                        enquiry.getConfig().put("policyNo", policy.getPolicyNo());
                    } else {
                        enquiry.getMisc().put("policyType", "2");
                    }
                    policy.setEnquiry(enquiry);
                    context.put("data", policy);
                    Map<String, Object> response = null;
                    String baseMessage = String.format("回写保单号：%s，地址：%s", policy.getPolicyNo(), task.getCallBackUrl());
                    log.info("{},taskId:{}", baseMessage, context.get("taskId"));
                    try {
                        response = httpExecute(task.getCallBackUrl(), JSONArray.toJSONString(request), headers);
                        log.info("回写保单号：{},返回结果：{}", policy.getPolicyNo(), response.get("data"));
                        if (response.get("data") == null || !((Boolean) ((Map) response.get("data")).get("success"))) {
                            log.error("{},异常：{}", baseMessage, response.get("message"));
                        }

                        // 天安 亚太混保在推送一次
                        if (repeatPushFlag) {

                            enquiry.getMisc().put("policyType", "1");
                            enquiry.getMisc().put("policyNo", enquiry.getBizPolicyCode());
                            policy.setPolicyNo(enquiry.getBizPolicyCode());
                            policy.setEnquiry(enquiry);
                            context.put("data", policy);
                            baseMessage = String.format("回写保单号：%s，地址：%s", policy.getPolicyNo(), task.getCallBackUrl());
                            log.info("{},taskId:{}", baseMessage, context.get("taskId"));
                            response = httpExecute(task.getCallBackUrl(), JSONArray.toJSONString(request), headers);
                            log.info("回写保单号：{},返回结果：{}", policy.getPolicyNo(), response.get("data"));
                            if (response.get("data") == null || !((Boolean) ((Map) response.get("data")).get("success"))) {
                                log.error("{},异常：{}", baseMessage, response.get("message"));
                            }
                            num.incrementAndGet();
                        }

                    } catch (Exception e) {
                        log.error("{},异常：{}", baseMessage, e.getMessage(), e);
                    }
                });
            }
            context.put("finished", true);
            httpExecute(task.getCallBackUrl(), JSONArray.toJSONString(request), headers);
            log.info("回写账号：{}，地址：{}完成，共回写：{}条", task.getTraceKey(), task.getCallBackUrl(), num);
        } catch (Exception e) {
            log.error("保单回写失败taskId：{}，异常：{}", task.getConfigs().get("taskId"), e.getMessage(), e);
        }
    }

    private Map<String, Object> httpExecute(String url, String jsonStr, Map<String, String> headers) throws Exception {
        HttpEntityEnclosingRequestBase httpRequestBase = new HttpPost(url);
        if (StringUtil.isEmpty(jsonStr)) jsonStr = "";
        String sign = SDASUtils.preRequestSign(url, jsonStr, HttpSender.HTTP_POST_METHOD, appKey, secretKey);
        CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"})).build();
        if (StringUtil.isNoEmpty(jsonStr)) {
            httpRequestBase.setEntity(new StringEntity(jsonStr, CharsetUtil.UTF_8));
        }
        Header[] headerList = headers.entrySet().stream()
            .map(e -> new BasicHeader(e.getKey(), e.getValue()))
            .toList().toArray(new Header[]{});
        httpRequestBase.setHeaders(headerList);
        httpRequestBase.addHeader("Authorization", sign);
        Map<String, Object> resultMap = new HashMap<>();
        try (CloseableHttpResponse response = client.execute(httpRequestBase)) {
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity, "utf-8");
            resultMap.put("statusCode", statusCode);
            resultMap.put("data", JSONObject.parseObject(result, Map.class));
        }
        return resultMap;
    }
}
