package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.AutoTaskExceptionConfig;
import com.cheche365.bc.mapper.AutoTaskExceptionConfigServiceMapper;
import com.cheche365.bc.service.AutoTaskExceptionConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-12-06
 */
@Service
public class AutoTaskExceptionConfigServiceImpl
    extends ServiceImpl<AutoTaskExceptionConfigServiceMapper, AutoTaskExceptionConfig>
    implements AutoTaskExceptionConfigService {

    @Override
    public List<AutoTaskExceptionConfig> listByCompanyIdAndProcessTypeAndTaskType(String companyId, String processType,
                                                                                  String taskType) {
        LambdaQueryWrapper<AutoTaskExceptionConfig> wrapper = new QueryWrapper<AutoTaskExceptionConfig>().lambda()
            .eq(AutoTaskExceptionConfig::getCompanyId, companyId)
            .eq(AutoTaskExceptionConfig::getProcessType, processType)
            .eq(AutoTaskExceptionConfig::getTaskType, taskType);

        wrapper.last(" ORDER BY LENGTH(exception_keywords) DESC");
        return list(wrapper);
    }

    @Override
    public String findExceptionConversion(String companyId, String processType, String taskType, String errorMsg) {
        List<AutoTaskExceptionConfig> autoTaskExceptionConfigs = listByCompanyIdAndProcessTypeAndTaskType(companyId, processType, taskType);
        if (autoTaskExceptionConfigs == null || autoTaskExceptionConfigs.isEmpty()) {
            return null;
        }

        return findExceptionConversion(errorMsg, autoTaskExceptionConfigs);
    }

    @Override
    public String findExceptionConversion(String errorMsg, List<AutoTaskExceptionConfig> autoTaskExceptionConfigs) {
        return autoTaskExceptionConfigs
            .stream()
            .filter(autoTaskExceptionConfig ->
                matchException(errorMsg, autoTaskExceptionConfig) && StringUtils.isNotBlank(autoTaskExceptionConfig.getExceptionConversion())
            )
            .findFirst()
            .map(AutoTaskExceptionConfig::getExceptionConversion)
            .orElse(null);
    }

    @Override
    public Boolean matchException(String e, AutoTaskExceptionConfig config) {
        if (StringUtils.isBlank(config.getExceptionConversion())) {
            return false;
        }

        if (config.getUseRegex() == 1) {
            Pattern pattern = Pattern.compile(config.getExceptionKeywords());
            Matcher matcher = pattern.matcher(e);
            return matcher.find();
        } else {
            String[] keywords = config.getExceptionKeywords().split(",");
            for (String s : keywords) {
                if (!e.contains(s)) {
                    return false;
                }
            }
            return true;
        }
    }
}
