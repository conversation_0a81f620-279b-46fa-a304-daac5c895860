package com.cheche365.bc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.AutoRequestMetaData;
import com.cheche365.bc.repo.AutoRequestMetaDataRepo;
import com.cheche365.bc.service.AutoRequestMetaDataService;
import com.cheche365.bc.task.AutoTask;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AutoRequestMetaDataServiceImpl implements AutoRequestMetaDataService {

    private final AutoRequestMetaDataRepo autoRequestMetaDataRepo;

    @Override
    public void saveAutoRequestMetaData(JSONObject enquiry, AutoTask autoTask) {
        AutoRequestMetaData metadata = new AutoRequestMetaData();
        metadata.setEnquiryId(autoTask.getTempValues().get("enquiryId").toString());
        metadata.setTaskType(autoTask.getTaskType());
        metadata.setWriteBackUrl(autoTask.getWriteBackUrl());
        metadata.setConfig(BeanUtil.beanToMap(enquiry.getJSONObject("configInfo").getJSONObject("configMap")));
        autoRequestMetaDataRepo.save(metadata);
    }
}
