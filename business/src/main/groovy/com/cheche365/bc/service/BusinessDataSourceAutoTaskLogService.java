package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.BusinessDataSourceAutoTaskLog;

import java.util.List;
import java.util.Map;

/**
 * 百川来源归类日志
 *
 * <AUTHOR> href="<EMAIL>">蒋昌宝</a>
 */
public interface BusinessDataSourceAutoTaskLogService extends IService<BusinessDataSourceAutoTaskLog> {

    /**
     * 获取新的 BusinessDataSourceAutoTaskLog id
     *
     * @param product  产品
     * @param scenario 场景
     * @param channel  渠道
     * @return requestSourceId
     */
    Integer getDataSourceLogId(String product, String scenario, String channel);


    /**
     * 返回树状图
     *
     * @return 日志查询界面树状图节点
     */
    List<Map<String, Object>> getTreeList();


    /**
     * 返回查询节点
     *
     * @param parentId 父节点
     * @return 日志查询界面搜索节点
     */
    List<Integer> getIdListByParentId(Integer parentId);

}
