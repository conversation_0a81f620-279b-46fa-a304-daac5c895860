package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import com.cheche365.bc.mapper.PolicySimplifyInfoMapper;
import com.cheche365.bc.service.PolicySimplifyInfoService;
import com.cheche365.bc.task.AutoTask;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-01-8
 */
@Service
public class PolicySimplifyInfoServiceImpl extends ServiceImpl<PolicySimplifyInfoMapper, PolicySimplifyInfo> implements PolicySimplifyInfoService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static ConcurrentHashMap<String, AutoTask> allowAccounts = new ConcurrentHashMap<>();

    /**
     * 正在运行的抓取保单任务账号
     */
    public static final String ALLOW_RUNNING_ACCOUNTS = "brf." + "running_accounts";

    @PostConstruct
    private void init() {
        redisTemplate.delete(ALLOW_RUNNING_ACCOUNTS);
    }

    @Override
    public List<PolicySimplifyInfo> findPolicyNoListFromTime(String account, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<PolicySimplifyInfo> queryWrapper = new QueryWrapper<PolicySimplifyInfo>()
                .lambda()
                .eq(PolicySimplifyInfo::getAccountNo, account)
                .ge(PolicySimplifyInfo::getUnderWriteDate, startDate);
        if (endDate != null) {
            queryWrapper.le(PolicySimplifyInfo::getUnderWriteDate, endDate);
        }
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据账号查找保单列表最后完成日期
     *
     * @param comId       保险公司Id
     * @param account     账号信息
     * @param alwaysRetry true：查找保单列表中最后已下载的保单的承包日期（下载失败可以一直重复），
     *                    false：查找保单日期保存的最后日期（不关注是否已下载，下载失败只重试一次）
     * @return 最后完成日期
     */
    @Override
    public LocalDate findLastDate(String comId, String account, Boolean alwaysRetry) {
        LambdaQueryWrapper<PolicySimplifyInfo> queryWrapper = new QueryWrapper<PolicySimplifyInfo>().lambda()
                .eq(PolicySimplifyInfo::getAccountNo, account)
                .eq(PolicySimplifyInfo::getComId, comId)
                .orderByDesc(PolicySimplifyInfo::getUnderWriteDate)
                .last(" limit 1");
        if (alwaysRetry)
            queryWrapper.eq(PolicySimplifyInfo::getDownloaded, true);
        PolicySimplifyInfo policySimplifyInfo = baseMapper.selectOne(queryWrapper);
        if (policySimplifyInfo != null)
            return policySimplifyInfo.getUnderWriteDate();
        return null;
    }

    /**
     * 根据保险公司ID，保单号查询保单列表实体
     *
     * @param comId    保险公司Id
     * @param policyNo 保单号
     * @return PolicySimplifyInfo
     */
    @Override
    public PolicySimplifyInfo getPolicyByNo(String comId, String policyNo) {
        return baseMapper.selectOne(new LambdaQueryWrapper<PolicySimplifyInfo>().eq(PolicySimplifyInfo::getComId, comId).eq(PolicySimplifyInfo::getPolicyNo, policyNo));
    }

    @Override
    public List<String> findPolicyCountFromTime(LocalDate startDate, LocalDate endDate) {
        return baseMapper.findPolicyCountFromTime(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> findPolicyCountGroupByArea(LocalDate startDate, LocalDate endDate) {
        return baseMapper.findPolicyCountGroupByArea(startDate, endDate);
    }

    @Override
    public void setAllowRunningAccount(String account, AutoTask task) {
        allowAccounts.put(account, task);
        redisTemplate.opsForList().leftPush(ALLOW_RUNNING_ACCOUNTS, account);
    }

    @Override
    public List<AutoTask> deleteAllowRunningAccount(String account) {
        return redisTemplate.opsForList().range(ALLOW_RUNNING_ACCOUNTS, 0, -1)
                .stream().filter(accountStr -> accountStr.contains(account))
                .map(accountStr -> {
                    AutoTask autoTask = allowAccounts.remove(accountStr);
                    if (autoTask != null) {
                        redisTemplate.opsForList().remove(ALLOW_RUNNING_ACCOUNTS, 1, accountStr);
                    }
                    return autoTask;
                }).filter(Objects::nonNull).collect(Collectors.toList());

    }

    @Override
    public List<String> getAllowRunningAccountList() {
        return redisTemplate.opsForList().range(ALLOW_RUNNING_ACCOUNTS, 0, -1).stream().map(it -> it.substring(0, it.lastIndexOf("-"))).collect(Collectors.toList());
    }

}
