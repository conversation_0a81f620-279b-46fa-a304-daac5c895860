package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.AccountInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-01-8
 */
public interface PolicyAccountService extends IService<AccountInfo> {

    List<AccountInfo> getAccountSFormDatabase(Map<String, Object> properties);

    List<AccountInfo> getAccountSFormDatabase(Map<String, Object> properties, Boolean isAsc, String... orderColumns);

}
