package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.BusinessDataSource;
import com.cheche365.bc.mapper.BusinessDataSourceMapper;
import com.cheche365.bc.service.BusinessDataSourceService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */

@Service
@AllArgsConstructor
public class BusinessDataSourceServiceImpl extends ServiceImpl<BusinessDataSourceMapper, BusinessDataSource> implements BusinessDataSourceService {

    private final BusinessDataSourceMapper businessDataSourceMapper;

    @Override
    public BusinessDataSource getDataSource(String product, String scenario, String channel) {
        QueryWrapper<BusinessDataSource> wrapper = new QueryWrapper<>();
        wrapper.select("id", "source_kind");
        wrapper.lambda()
                .eq(BusinessDataSource::getSourceProduct, product)
                .eq(BusinessDataSource::getSourceScenario, scenario)
                .eq(StringUtils.isNotBlank(channel), BusinessDataSource::getSourceChannel, channel);

        return businessDataSourceMapper.selectOne(wrapper);
    }

    @Override
    public List<BusinessDataSource> getDataSourceWithProductAndScenario(String product, String scenario) {
        QueryWrapper<BusinessDataSource> wrapper = new QueryWrapper<>();
        wrapper.select("id");
        wrapper.lambda()
                .eq(BusinessDataSource::getSourceProduct, product)
                .eq(BusinessDataSource::getSourceScenario, scenario);

        return businessDataSourceMapper.selectList(wrapper);
    }

    @Override
    public void insertDataSource(String product, String scenario, String channel, int sourceKind, String desc) {
        BusinessDataSource businessDataSource = new BusinessDataSource()
                .setSourceProduct(product)
                .setSourceScenario(scenario)
                .setSourceChannel(channel)
                .setSourceKind(sourceKind)
                .setDescription(desc);
        businessDataSourceMapper.insert(businessDataSource);
    }
}