package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.AutoTask;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
public interface AutoTaskService extends IService<AutoTask> {

    List<AutoTask> getTaskByTaskIdComTaskType(String taskId, String com, String taskType);

    AutoTask getTaskByTaskTypeProposeNumOrOrderNo(String taskType, String no, int noType);

    AutoTask getTaskByProposeNumOrOrderNo(String no);

    AutoTask getOneTask(String taskId, String com, String[] taskType, Object bizProposeNo, Object efcProposeNo);

    void updateClaimInfoByAutoTraceId(AutoTask autoTask);

    void updateClaimStatusByAutoTraceId(AutoTask autoTask);
}
