package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.PolicySimplifyInfo;
import com.cheche365.bc.task.AutoTask;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-01-8
 */
public interface PolicySimplifyInfoService extends IService<PolicySimplifyInfo> {

    List<PolicySimplifyInfo> findPolicyNoListFromTime(String account, LocalDate startDate, LocalDate endDate);

    LocalDate findLastDate(String comId, String account, Boolean alwaysRetry);

    List<String> findPolicyCountFromTime(LocalDate startDate, LocalDate endDate);

    PolicySimplifyInfo getPolicyByNo(String comId, String policyNo);

    List<Map<String, Object>> findPolicyCountGroupByArea(LocalDate startDate, LocalDate endDate);

    void setAllowRunningAccount(String account, AutoTask task);

    public List<AutoTask> deleteAllowRunningAccount(String account);

    List<String> getAllowRunningAccountList();

}
