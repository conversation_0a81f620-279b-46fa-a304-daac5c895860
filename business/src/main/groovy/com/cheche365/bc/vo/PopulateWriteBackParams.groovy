package com.cheche365.bc.vo

import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.car.*

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class PopulateWriteBackParams {

	public static Map<String, String> MAP_KEY_AREA = ["340000": "安徽", "110000": "北京", "210200": "大连", "350000": "福建", "620000": "甘肃", "440000": "广东", "450000": "广西", "520000": "贵州", "460000": "海南", "130000": "河北", "410000": "河南", "230000": "黑龙江", "420000": "湖北", "430000": "湖南", "220000": "吉林", "320000": "江苏", "360000": "江西", "210000": "辽宁", "150000": "内蒙古", "330200": "宁波", "640000": "宁夏", "370200": "青岛", "630000": "青海", "350200": "厦门", "370000": "山东", "140000": "山西", "610000": "陕西", "310000": "上海", "440300": "深圳", "510000": "四川", "120000": "天津", "540000": "西藏", "650000": "新疆", "530000": "云南", "330000": "浙江", "500000": "重庆", "99": "未知"]

	public static Map<String, String> MAP_KEY_ACCOUNT_TYPE =
		["1": "磐石",
		 "2": "易保通",
		 "3": "澎湃保",
		]


	private static def getPersonInfo(PersonInfo personInfo) {
		[[
			 idNo        : personInfo.idCard?.trim(),
			 name        : personInfo.name,
			 identityType: identityIn2Out(personInfo.idCardType),
			 mobilePhone : personInfo.mobile
		 ]]
	}

	public static String createAutoInsurance(Enquiry enquiry) {
		Order order = enquiry.order
		def autoInsurance = [
			autoInfo      : createAutoInfo(enquiry),
			licensePlateNo: enquiry.order.carInfo.plateNum
		]
        EfcSuiteInfo efcSuiteInfo = order.suiteInfo.efcSuiteInfo
        BizSuiteInfo bizSuiteInfo = order.suiteInfo.bizSuiteInfo
		Map<String, Boolean> policyType = getPolicyTypeMap(enquiry)
		String comId = getComID(enquiry.misc.comId as String)
		if (policyType.isEfc) autoInsurance.compulsoryInsurance = [
			licensePlateNo  : autoInsurance.licensePlateNo,
			insuranceTypeId : 5,                                                    // 商业是4、交强是5。
			insuranceCompany: comId,
			applicants      : getPersonInfo(order.insurePerson),
			insureds        : getPersonInfo(order.insuredPersons[0]),
			beneficiaries   : order.beneficiaryPersons ? getPersonInfo(order.beneficiaryPersons[0]) : [[:]],
			proposalNo      : enquiry.efcProposeNum ?: enquiry.misc.proposalNo,
			policyNo        : enquiry.efcPolicyCode ?: enquiry.misc.policyNo,
			autoTaxInCents  : ((order.suiteInfo.taxSuiteInfo?.discountCharge ?: 0) * 100) as int,
			premiumInCents  : ((efcSuiteInfo?.discountCharge ?: 0) * 100) as int,
			discount        : efcSuiteInfo?.discountRate ?: 1.0,
			effectiveDate   : getLocalDateTime(efcSuiteInfo?.start).date,
			effectiveHour   : getLocalDateTime(efcSuiteInfo?.start).hour,
			expireDate      : getLocalDateTime(efcSuiteInfo?.end).date,
			expireHour      : getLocalDateTime(efcSuiteInfo?.end).hour,
			createTime      : enquiry.misc.underWriteDate.toString(),
			updateTime      : enquiry.misc.underWriteDate.toString(),
		]

		if (policyType.isBiz) autoInsurance.commercialInsurance = [
			licensePlateNo  : autoInsurance.licensePlateNo,
			insuranceTypeId : 4,                                                    // 商业是4、交强是5。
			insuranceCompany: comId,
			applicants      : getPersonInfo(order.insurePerson),
			insureds        : getPersonInfo(order.insuredPersons[0]),
			beneficiaries   : order.beneficiaryPersons ? getPersonInfo(order.beneficiaryPersons[0]) : [[:]],
			proposalNo      : enquiry.bizProposeNum ?: enquiry.misc.proposalNo,
			policyNo        : enquiry.bizPolicyCode ?: enquiry.misc.policyNo,
			premiumInCents  : ((bizSuiteInfo?.discountCharge ?: 0) * 100) as int,
			discount        : bizSuiteInfo?.discountRate ?: 1.0,
			effectiveDate   : getLocalDateTime(bizSuiteInfo?.start).date,
			effectiveHour   : getLocalDateTime(bizSuiteInfo?.start).hour,
			expireDate      : getLocalDateTime(bizSuiteInfo?.end).date,
			expireHour      : getLocalDateTime(bizSuiteInfo?.end).hour,
			createTime      : enquiry.misc.underWriteDate.toString(),
			updateTime      : enquiry.misc.underWriteDate.toString(),
		]
		if (policyType.isBiz)
			autoInsurance.commercialInsuranceDetails = bizSuiteInfo.suites?.collect { key, value ->
				def kindCode = _KIND_CODE_CONVERTERS_CONFIG_NEW.find { key == it.value[0] }
				if (kindCode) {
					[
						insuranceKind    : kindCode?.key,
						insuranceKindCode: kindCode?.value?.get(1),
						amountInCents    : ((value.amount ?: 0) * 100) as int,
						premiumInCents   : (value.discountCharge ?: 0) * 100,
					]
				}
			} - null
		// 判断是否需要走新增流程
		if (policyType.isBiz) autoInsurance.commercialPlatformInfo = createPlatformInfo(enquiry, enquiry.bizPolicyCode ?: enquiry.misc.policyNo as String, "4")
		if (policyType.isEfc) autoInsurance.compulsoryPlatformInfo = createPlatformInfo(enquiry, enquiry.efcPolicyCode ?: enquiry.misc.policyNo as String, "5")
		JSONObject.toJSONString(autoInsurance)
	}

	public static Map createAutoInfo(Enquiry enquiry) {
		Order order = enquiry.order
		CarInfo car = order.carInfo
		[
			licensePlateNo   : car.plateNum ?: "新车未上牌",
			ownerName        : order.carOwnerInfo.name,
			vinNo            : car.vin?.trim(),
			engineNo         : car.engineNum?.trim(),
			enrollDate       : car.firstRegDate ? new java.sql.Date(car.firstRegDate.getTime()) : null,
			seat             : car.seatCnt,
			dataSource       : _ACCOUNT_TYPE_MAP[enquiry.misc?.accountType?.toString()] ?: "9",
			areaName         : getAreaCode(car.plateNum ?: ""),
			useCharacterCode : usePropsIn2Out(car.useProps?.toString()),
			fuelTypeName     : car.carModelName?.contains("电") ? "电" : "汽油",
			autoTypeName     : car.carBrandName,
			ownerIdentityType: identityIn2Out(order.carOwnerInfo?.idCardType),
			ownerIdentity    : order.carOwnerInfo.idCard?.trim(),
			vehicleTypeCode  : parseJgVehicleType(car.jgVehicleType, car.carModelName ?: car.carBrandName ?: "")
		]
	}

	public static Map createPlatformInfo(Enquiry enquiry, String policyNo, String policyType) {
		Map<String, Object> platformInfo = enquiry.getOrder().getPlatformInfo();
		Map task = (Map) platformInfo.get("task");
		Map definition = (Map) platformInfo.get("definition") ?: [:];
		String bizScore = definition.get("application.bizScore")
		String bizRate = definition.get("application.bizRate")
		String trafficScore = definition.get("application.trafficScore")
		String trafficRate = definition.get("application.trafficRate")
		long underWriteDate = localDate2Long(enquiry.misc.underWriteDate.toString())
		[
			policyNo                        : policyNo,
			insuranceTypeId                 : policyType,
			dataSourceId                    : _ACCOUNT_TYPE_MAP[enquiry.misc?.accountType?.toString()] ?: "9",
			commercialSelfRate              : definition.get("selfRate") ?: "",
			commercialNcdRate               : task?.get("car.specific.NcdRate") ?: "",
			commercialNcdReasons            : platformInfo.get("noClaimDiscountCoefficientReasons") ?: "",
			commercialContinuityInsureYears : Double.parseDouble(platformInfo.get("bizContinuityInsureYears")?.toString() ?: "0").toInteger() ?: "",
			commercialClaimTimes            : Double.parseDouble(platformInfo.get("application.commercialClaimTimes")?.toString() ?: "0").toInteger() ?: "",
			commercialClaimTimesDesc        : platformInfo.get("bwCommercialClaimTimes") ?: "",
			commercialScore                 : bizScore?.matches(/[0-9,.]*/) ? bizScore : "",
			commercialRank                  : bizRate?.matches(/[0-9,.]*/) ? bizRate : "",
			commercialForecastScore         : definition.get("application.syForecastScore") ?: "",
			commercialLastClaimSum          : 0,
			commercialExpectedLossRate      : definition.get("application.expectLossRatio") ?: "",
			commercialLastInsuranceCompany  : "",
			compulsoryContinuityInsureYears : "",
			compulsoryClaimTimes            : "",
			compulsoryClaimTimesDesc        : platformInfo.get("bwCompulsoryClaimTimes") ?: "",
			compulsoryScore                 : trafficScore?.matches(/[0-9,.]*/) ? trafficScore : "",
			compulsoryRank                  : trafficRate?.matches(/[0-9,.]*/) ? trafficRate : "",
			compulsoryForecastScore         : definition.get("application.jqForecastScore") ?: "",
			compulsoryLastClaimSum          : platformInfo.get("lastCompulsoryClaimSum") ?: "",
			compulsoryExpectedLossRate      : definition.get("application.expectTrafficLossRatio") ?: "",
			compulsoryLastInsuranceCompany  : "",
			mixedExpectedLossRate           : definition.get("application.expectMixedRatio") ?: "",
			firstInsureType                 : "",
			loyalty                         : definition.get("application.loyalty") ?: "",
			loyaltyReasons                  : platformInfo.get("loyaltyReasons") ?: "",
			trafficOffenceTimes             : Double.parseDouble(platformInfo.get("application.trafficOffence")?.toString() ?: "0").toInteger() ?: "",
			trafficOffenceDiscount          : "",
			drunkDrivingRate                : definition.get("drunkenCoefficient") ?: "",
			commercialOriginalPremiumInCents: calculateOrgCharge(enquiry.order.suiteInfo?.bizSuiteInfo),
			compulsoryOriginalPremiumInCents: calculateOrgCharge(enquiry.order.suiteInfo?.efcSuiteInfo),
			commercialUnderwriteDate        : underWriteDate,
			compulsoryUnderwriteDate        : underWriteDate,
			commercialClaimRate             : 0.0, // 无字段
			commercialDiscountRate          : enquiry.order.suiteInfo.bizSuiteInfo?.discountRate ?: "",
			compulsoryClaimRate             : 0.0, // 无字段
			compulsoryDiscountRate          : enquiry.order.suiteInfo.efcSuiteInfo?.discountRate ?: "",
			transferred                     : "2" == definition.get("application.loyalty"),
			newCar                          : enquiry.order.carInfo.isNew,
			mixedInsurance                  : enquiry.order.suiteInfo.bizSuiteInfo?.start && enquiry.order.suiteInfo.efcSuiteInfo?.start
		]
	}

	static Map<String, Boolean> getPolicyTypeMap(Enquiry enquiry) {
		Order order = enquiry.order
		EfcSuiteInfo efcSuiteInfo = order.suiteInfo.efcSuiteInfo
		BizSuiteInfo bizSuiteInfo = order.suiteInfo.bizSuiteInfo
		[isBiz: enquiry.state != "2" && bizSuiteInfo?.start,
		 isEfc: enquiry.state != "1" && efcSuiteInfo?.start]
	}

	private static Long calculateOrgCharge(suiteInfo) {
		def calculateCharge = 0
		if (suiteInfo?.discountCharge && suiteInfo?.discountRate)
			calculateCharge = suiteInfo?.discountCharge / suiteInfo?.discountRate
		((suiteInfo?.orgCharge ?: calculateCharge) * 100) as Long
	}

	static Map getLocalDateTime(String dateTime) {
		LocalDateTime date = dateTime ? LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null
		[date: date ? date.toLocalDate().toString() : null, hour: date?.getHour() ?: "0"]
	}

	static long localDate2Long(String LocalDateStr) {
		Date.from(LocalDate.parse(LocalDateStr).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime()
	}

	static final _KIND_CODE_CONVERTERS_CONFIG_NEW = [
		1L : ['VehicleDamage', '1010'],
		2L : ['ThirdParty', '1020'],
		3L : ['Driver', '1030'],
		4L : ['Passenger', '1040'],
		5L : ['Wheel', '1050'],
		6L : ['Scratch', '1070'],
		7L : ['EngineDamageExclude', '1100'],
		8L : ['ANCVehicleDamage', '1190'],
		9L : ['HolidayDouble', '1130'],
		10L: ['CFMDThirdParty', '1121'],
		11L: ['NIHCThirdParty', '1141'],
		20L: ['ANCThirdParty', '1200'],
		13L: ['CFMDDriver', '1122'],
		14L: ['NIHCDriver', '1142'],
		16L: ['ANCDriver', '1210'],
		17L: ['CFMDPassenger', '1123'],
		18L: ['NIHCPassenger', '1143'],
		19L: ['ANCPassenger', '1120']
	]
	/**
	 * 掌中宝:1，易宝通:2,澎湃保:3
	 * 有E保通的就用9来设置。
	 */
	static final _ACCOUNT_TYPE_MAP = [
		"1": "9",
		"2": "10",
		"3": "11",
	]

	static def getComID(String comId) {
		["2002": "40000", "2005": "10000", "2007": "250000", "2011": "25000", "2016": "260000", "2019": "265000", "2021": "275000", "2022": "280000", "2023": "285000", "2024": "1507000", "2026": "295000", "2027": "300000", "2028": "305000", "2040": "220000", "2041": "180000", "2042": "320000", "2043": "325000", "2044": "160000", "2045": "330000", "2046": "335000", "2050": "345000", "2055": "355000", "2056": "1508000", "2058": "360000", "2062": "370000", "2065": "385000", "2066": "85000", "2072": "400000", "2076": "400000", "2085": "90000", "2086": "230000", "2088": "445000", "2095": "460000", "2096": "465000", "2107": "1510500"][comId]
	}

	static def parseJgVehicleType(code, carModelName) {
		switch (code) {
			case 0: "K21"; break   //载客汽车 => 中型普通客车
			case 1: "H21"; break   //载货汽车 => 中型普通货车
			case 2: "Q21"; break   //半挂牵引车 => 中型半挂牵引车
			case 3:
				if (carModelName.indexOf("厢式") != -1) {
					return "H52"; //低速厢式货车
				} else if (carModelName.indexOf("罐式") != -1) {
					return "H53"; //低速罐式货车
				} else if (carModelName.indexOf("自卸") != -1) {
					return "H54"; //低速自卸货车
				} else
					"H51"; break   //低速载货汽车 => 低速普通货车
			case 4:
				if (carModelName.indexOf("厢式") != -1) {
					return "G22"; //中型厢式全挂车
				} else if (carModelName.indexOf("罐式") != -1) {
					return "G23"; //中型罐式全挂车
				} else if (carModelName.indexOf("平板") != -1) {
					return "G24"; //中型平板全挂车
				} else if (carModelName.indexOf("集装厢") != -1 || carModelName.indexOf("集装箱") != -1) {
					return "G25"; //中型集装厢全挂车
				} else if (carModelName.indexOf("自卸") != -1) {
					return "G26"; //中型自卸全挂车
				} else
					"G21"; break   //挂车 => 中型普通全挂车
			case 5: "H4"; break    //客货两用车
			case 6: "H26"; break   //集装箱 => 中型集装厢车
			case 7: "S"; break     //特种车 => 特种作业专用车
			case 8: "T22"; break   //拖拉机 => 手扶拖拉机
			case 9: "M21"; break   //摩托车 => 普通二轮摩托车
			case 10: "M22"; break  //轻型摩托车 => 轻便二轮摩托车
			case 11: "M15"; break  //侧三轮 => 侧三轮摩托车
			case 12: "T21"; break  //运输型拖拉机 => 小型轮式拖拉机
			case 13: "K33"; break  //轿车
			case 14: "K32"; break  //越野车 => 小型越野客车
			case 15: "H27"; break  //自卸汽车 => 中型自卸货车
			case 16: "Q11"; break  //牵引汽车 => 重型半挂牵引车
			case 17:
				if (carModelName.indexOf("双层") != -1) {
					return "K12"; //大型双层客车
				} else if (carModelName.indexOf("卧铺") != -1) {
					return "K13"; //大型卧铺客车
				} else if (carModelName.indexOf("铰接") != -1) {
					return "K14"; //大型铰接客车
				} else if (carModelName.indexOf("越野") != -1) {
					return "K15"; //大型越野客车
				} else
					"K11"; break  //大型客车 => 大型普通客车
			case 18:
				if (carModelName.indexOf("双层") != -1) {
					return "K22"; //中型双层客车
				} else if (carModelName.indexOf("卧铺") != -1) {
					return "K23"; //中型卧铺客车
				} else if (carModelName.indexOf("铰接") != -1) {
					return "K24"; //中型铰接客车
				} else if (carModelName.indexOf("越野") != -1) {
					return "K25"; //中型越野客车
				} else
					"K21"; break  //中型客车 => 中型普通客车
			case 19: "K31"; break  //小型客车 => 小型普通客车
			case 20: "K33"; break  //出租轿车 => 轿车
			case 21:
				if (carModelName.indexOf("越野") != -1) {
					return "K42"; //微型越野客车
				} else if (carModelName.indexOf("轿车") != -1) {
					return "K43"; //微型轿车
				} else
					"K41"; break  //微型车 => 微型普通客车
			case 22: "N11"; break  //三轮汽车 => 三轮汽车
			case 23: "Z21"; break  //专项作业车 => 中型专项作业车
			case 24:
				if (carModelName.indexOf("挖掘") != -1) {
					return "J12"; //轮式挖掘机械
				} else if (carModelName.indexOf("平地") != -1) {
					return "J13"; //轮式平地机械
				} else
					"J11"; break  //轮式专用机械 => 轮式装载机械
			case 29: "K11"; break  //公交车辆
			case 30: "X99"; break  //其他
			case 31:
				if (carModelName.indexOf("厢式") != -1) {
					return "H32"; //轻型厢式货车
				} else if (carModelName.indexOf("封闭") != -1) {
					return "H33"; //轻型封闭货车
				} else if (carModelName.indexOf("罐式") != -1) {
					return "H34"; //轻型罐式货车
				} else if (carModelName.indexOf("平板") != -1) {
					return "H35"; //轻型平板货车
				} else if (carModelName.indexOf("自卸") != -1) {
					return "H37"; //轻型自卸货车
				} else if (carModelName.indexOf("特殊结构") != -1) {
					return "H38"; //轻型特殊结构货车
				} else
					"H31"; break  //轻型普通货车
			case 32:
				if (carModelName.indexOf("厢式") != -1) {
					return "H22"; //中型厢式货车
				} else if (carModelName.indexOf("封闭") != -1) {
					return "H23"; //中型封闭货车
				} else if (carModelName.indexOf("罐式") != -1) {
					return "H24"; //中型罐式货车
				} else if (carModelName.indexOf("平板") != -1) {
					return "H25"; //中型平板货车
				} else if (carModelName.indexOf("集装厢") != -1 || carModelName.indexOf("集装箱") != -1) {
					return "H26"; //中型集装厢货车
				} else if (carModelName.indexOf("自卸") != -1) {
					return "H27"; //中型自卸货车
				} else if (carModelName.indexOf("特殊结构") != -1) {
					return "H28"; //中型特殊结构货车
				} else
					"H21"; break  //中型普通货车
			case 33:
				if (carModelName.indexOf("厢式") != -1) {
					return "H12"; //重型厢式货车
				} else if (carModelName.indexOf("封闭") != -1) {
					return "H13"; //重型封闭货车
				} else if (carModelName.indexOf("罐式") != -1) {
					return "H14"; //重型罐式货车
				} else if (carModelName.indexOf("平板") != -1) {
					return "H15"; //重型平板货车
				} else if (carModelName.indexOf("集装厢") != -1 || carModelName.indexOf("集装箱") != -1) {
					return "H16"; //重型集装厢货车
				} else if (carModelName.indexOf("自卸") != -1) {
					return "H17"; //重型自卸货车
				} else if (carModelName.indexOf("特殊结构") != -1) {
					return "H18"; //重型特殊结构货车
				} else
					"H11"; break  //重型普通货车
			default: "X99"
		}
	}

	static private identityIn2Out(Integer idCardType) {
		if (idCardType == null) return 9
		[0 : 1,
		 1 : 6,
		 2 : 10,
		 3 : 7,
		 4 : 2,
		 5 : 15,
		 6 : 11,
		 7 : 10,
		 8 : 13,
		 9 : 10,
		 10: 9,
		][idCardType] ?: 9
	}

	static private usePropsIn2Out(String inCode) {
		[
			'0' : "000",
			"1" : "210",
			"7" : "100",
			"2" : "101",
			"3" : "102",
			"4" : "103",
			'6' : "104",
			"5" : "105",
			'8' : "200",
			"9" : "210",
			"10": "220",
			"11": "230",
			"12": "240",
			"13": "250",
			"14": "320",
		][inCode] ?: "000"
	}

	private static String getAreaCode(String plateNum) {
		["京": "110000", "冀A": "130100", "冀B": "130200", "冀C": "130300", "冀D": "130400", "冀E": "130500", "冀F": "130600", "冀G": "130700", "冀H": "130800", "冀J": "130900", "冀R": "131000", "冀T": "131100", "晋A": "140100", "晋B": "140200", "晋C": "140300", "晋D": "140400", "晋E": "140500", "晋F": "140600", "晋K": "140700", "晋M": "140800", "晋H": "140900", "晋L": "141000", "晋J": "141100", "蒙A": "150100", "蒙B": "150200", "蒙C": "150300", "蒙D": "150400", "蒙G": "150500", "蒙K": "150600", "蒙E": "150700", "蒙L": "150800", "蒙J": "150900", "蒙F": "152200", "蒙H": "152500", "蒙M": "152900", "辽A": "210100", "辽B": "210200", "辽C": "210300", "辽D": "210400", "辽E": "210500", "辽F": "210600", "辽G": "210700", "辽H": "210800", "辽J": "210900", "辽K": "211000", "辽L": "211100", "辽M": "211200", "辽N": "211300", "辽P": "211400", "吉A": "220100", "吉B": "220200", "吉C": "220300", "吉D": "220400", "吉E": "220500", "吉F": "220600", "吉J": "220700", "吉G": "220800", "吉H": "222400", "黑A": "230100", "黑B": "230200", "黑G": "230300", "黑H": "230400", "黑J": "230500", "黑E": "230600", "黑F": "230700", "黑D": "230800", "黑K": "230900", "黑C": "231000", "黑N": "231100", "黑M": "231200", "黑P": "232700", "沪": "310000", "苏A": "320100", "苏B": "320200", "苏C": "320300", "苏D": "320400", "苏E": "320500", "苏F": "320600", "苏G": "320700", "苏H": "320800", "苏J": "320900", "苏K": "321000", "苏L": "321100", "苏M": "321200", "苏N": "321300", "浙A": "330100", "浙B": "330200", "浙C": "330300", "浙F": "330400", "浙E": "330500", "浙D": "330600", "浙G": "330700", "浙H": "330800", "浙L": "330900", "浙J": "331000", "浙K": "331100", "皖A": "340100", "皖B": "340200", "皖C": "340300", "皖D": "340400", "皖E": "340500", "皖F": "340600", "皖G": "340700", "皖H": "340800", "皖J": "341000", "皖M": "341100", "皖K": "341200", "皖L": "341300", "皖N": "341500", "皖S": "341600", "皖R": "341700", "皖P": "341800", "闽D": "350200", "闽B": "350300", "闽G": "350400", "闽C": "350500", "闽E": "350600", "闽H": "350700", "闽F": "350800", "闽J": "350900", "赣A": "360100", "赣H": "360200", "赣J": "360300", "赣G": "360400", "赣K": "360500", "赣L": "360600", "赣B": "360700", "赣D": "360800", "赣C": "360900", "赣F": "361000", "赣E": "361100", "鲁A": "370100", "鲁W": "370100", "鲁B": "370200", "鲁U": "370200", "鲁C": "370300", "鲁D": "370400", "鲁E": "370500", "鲁F": "370600", "鲁Y": "370600", "鲁G": "370700", "鲁V": "370700", "鲁H": "370800", "鲁J": "370900", "鲁K": "371000", "鲁L": "371100", "鲁S": "371200", "鲁Q": "371300", "鲁N": "371400", "鲁P": "371500", "鲁M": "371600", "鲁R": "371700", "豫A": "410100", "豫B": "410200", "豫C": "410300", "豫D": "410400", "豫E": "410500", "豫F": "410600", "豫G": "410700", "豫H": "410800", "豫J": "410900", "豫K": "411000", "豫L": "411100", "豫M": "411200", "豫R": "411300", "豫N": "411400", "豫S": "411500", "豫P": "411600", "豫Q": "411700", "鄂A": "420100", "鄂B": "420200", "鄂C": "420300", "鄂E": "420500", "鄂F": "420600", "鄂G": "420700", "鄂H": "420800", "鄂K": "420900", "鄂D": "421000", "鄂J": "421100", "鄂L": "421200", "鄂S": "421300", "鄂Q": "422800", "湘A": "430100", "湘B": "430200", "湘C": "430300", "湘D": "430400", "湘E": "430500", "湘F": "430600", "湘J": "430700", "湘G": "430800", "湘H": "430900", "湘L": "431000", "湘M": "431100", "湘N": "431200", "湘K": "431300", "湘U": "433100", "粤A": "440100", "粤F": "440200", "粤B": "440300", "粤C": "440400", "粤D": "440500", "粤E": "440600", "粤X": "440600", "粤Y": "440600", "粤J": "440700", "粤G": "440800", "粤K": "440900", "粤H": "441200", "粤L": "441300", "粤M": "441400", "粤N": "441500", "粤P": "441600", "粤Q": "441700", "粤R": "441800", "粤S": "441900", "粤T": "442000", "粤U": "445100", "粤V": "445200", "粤W": "445300", "桂A": "450100", "桂B": "450200", "桂C": "450300", "桂D": "450400", "桂E": "450500", "桂P": "450600", "桂N": "450700", "桂R": "450800", "桂K": "450900", "桂L": "451000", "桂J": "451100", "桂M": "451200", "桂G": "451300", "桂F": "451400", "琼A": "460100", "琼B": "460200", "琼F": "460300", "渝": "500000", "川A": "510100", "川C": "510300", "川D": "510400", "川E": "510500", "川F": "510600", "川G": "510700", "川H": "510800", "川J": "510900", "川K": "511000", "川L": "511100", "川R": "511300", "川Z": "511400", "川Q": "511500", "川X": "511600", "川S": "511700", "川T": "511800", "川Y": "511900", "川M": "512000", "川U": "513200", "川V": "513300", "川W": "513400", "贵A": "520100", "贵B": "520200", "贵C": "520300", "贵G": "520400", "贵F": "520500", "贵D": "520600", "贵E": "522300", "贵H": "522600", "贵J": "522700", "云A": "530100", "云D": "530300", "云F": "530400", "云M": "530500", "云C": "530600", "云P": "530700", "云J": "530800", "云S": "530900", "云E": "532300", "云G": "532500", "云H": "532600", "云K": "532800", "云L": "532900", "云N": "533100", "云Q": "533300", "云R": "533400", "藏A": "540100", "藏B": "540300", "藏G": "540400", "藏C": "542200", "藏D": "542300", "藏E": "542400", "藏F": "542500", "陕A": "610100", "陕B": "610200", "陕C": "610300", "陕D": "610400", "陕E": "610500", "陕J": "610600", "陕F": "610700", "陕K": "610800", "陕G": "610900", "陕H": "611000", "甘A": "620100", "甘B": "620200", "甘C": "620300", "甘D": "620400", "甘E": "620500", "甘H": "620600", "甘G": "620700", "甘L": "620800", "甘F": "620900", "甘M": "621000", "甘J": "621100", "甘K": "621200", "甘N": "622900", "甘P": "623000", "青A": "630100", "青B": "630200", "青C": "632200", "青D": "632300", "青E": "632500", "青F": "632600", "青G": "632700", "青H": "632800", "宁A": "640100", "宁B": "640200", "宁C": "640300", "宁D": "640400", "宁E": "640500", "新A": "650100", "新J": "650200", "新K": "652100", "新L": "652200", "新B": "652300", "新E": "652700", "新M": "652800", "新N": "652900", "新P": "653000", "新Q": "653100", "新R": "653200", "新F": "654000", "新D": "654003", "新G": "654200", "新H": "654300", "新C": "659001"].find { key, value -> plateNum.contains(key) }?.value
	}

}
