apply from: rootProject.file('gradle/publish.gradle')

dependencies {

    implementation fileTree(dir: '../libs', includes: ['*.jar'])

    api "org.springframework.boot:spring-boot-starter-data-redis"
    api "org.springframework.boot:spring-boot-starter-jdbc"
    api "org.springframework.boot:spring-boot-starter-data-mongodb"
    api "org.springframework.session:spring-session-data-redis"

    api "jakarta.servlet:jakarta.servlet-api:$jakarta_servlet_api_version"
    api "jakarta.validation:jakarta.validation-api:$jakarta_validation_api_version"

    api "com.zhongan:za-scorpoin-share:$zhongan_version"

    api "org.apache.commons:commons-pool2:$apache_common_pool_version"
    api "commons-beanutils:commons-beanutils:$apache_commons_beanutils_version"
    api "commons-io:commons-io:$apache_commons_io_version"
    api "com.monitorjbl:xlsx-streamer:$xlsx_streamer_version"
    api "org.apache.commons:commons-math3:$apache_commons_math3_version"
    api "com.alibaba:fastjson:$alibaba_fastjson_version"
    api "org.apache.httpcomponents:httpclient:$apache_httpclient_version"
    api "com.baomidou:mybatis-plus-spring-boot3-starter:$mybatis_plus_version"
    api "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    api "QRCode:QRCode:$QRCode_version"
    api "dom4j:dom4j:$dom4j_version"
    api "org.jsoup:jsoup:$jsoup_version"
    api "org.apache.cxf:cxf-rt-frontend-jaxws:$apache_cxf_version"
    api "org.apache.cxf:cxf-rt-transports-http:$apache_cxf_version"
    api "joda-time:joda-time:$joda_time_version"
    api "com.google.zxing:javase:$google_zxing_version"
    api "org.apache.pdfbox:pdfbox:$apache_pdfbox_version"
    api "org.bouncycastle:bcprov-jdk15on:$bouncycastle_bcprov_jdk15on_version"
    api "commons-net:commons-net:$commons_net_version"
    api "com.jcraft:jsch:$jcraft_jsch_version"
    api "net.lingala.zip4j:zip4j:$lingala_zip4j_version"
    api "cn.hutool:hutool-all:$hutool_version"
    api ("org.apache.axis2:axis2-kernel:$apache_axis2_version") { exclude module:'httpcore'}
    api ("org.apache.axis2:axis2-adb:$apache_axis2_version") { exclude module:'httpcore'}
    api ("org.apache.axis2:axis2-transport-local:$apache_axis2_version") { exclude module:'httpcore'}
    api ("org.apache.axis2:axis2-transport-http:$apache_axis2_version") { exclude module:'httpcore'}
    api "org.apache.httpcomponents:httpmime:$apache_httpclient_version"
    api ("com.qcloud:cos_api:$cos_api_version") { exclude group:'org.slf4j'}
    api "com.cheche365.common2:common-signature:$cheche_signature_version"
    api "org.apache.skywalking:apm-toolkit-logback-1.x:$apache_skywalking_version"
    api "org.apache.skywalking:apm-toolkit-trace:$apache_skywalking_version"
    api "com.sun.mail:jakarta.mail:$jakarta_mail_version"
    api "de.ruedigermoeller:fst:$ruedigermoeller_fst_version"
    api "xml-apis:xml-apis:$xml_apis_version"
    api "org.codehaus.groovy.modules.http-builder:http-builder:$groovy_http_version"
    api "com.squareup.okhttp3:okhttp:$okhttp_version"
    api "org.openjdk.nashorn:nashorn-core:$nashorn_core_version"
    api "com.github.rholder:guava-retrying:$guava_retrying_version"
}


