package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.FieldDoc;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

/**
 * 险种价格对象
 * <AUTHOR>
 * @Created by austinChen on 2016/12/15 23:24.
 */
@Getter
@Setter
@XmlRootElement
public class SuitePriceInfo {

    /**原始保费*/
    @FieldDoc(des="原始保费",remark = "报价成功后必有")
    private BigDecimal orgCharge;
    /**折后保费*/
    @FieldDoc(des="折后保费",remark = "报价成功后必有")
    private BigDecimal discountCharge;
    /**折扣率*/
    @FieldDoc(des="折扣率",remark = "报价成功后必有")
    private BigDecimal discountRate;

    /**期望折扣*/
    @FieldDoc(des="期望折扣",remark = "报价前过规则后算出")
    private BigDecimal hopeRate;

    /**税率*/
    @FieldDoc(des="税率",remark = "报价成功后必有")
    private BigDecimal taxRate;

    /**
     * 折后不含税保费
     */
    @FieldDoc(des="折后不含税保费",remark = "报价成功后必有")
    private BigDecimal discountNoTaxCharge;

    /**
     * 折后保费的税额
     */
    @FieldDoc(des="折后保费的税额",remark = "报价成功后必有")
    private BigDecimal discountTaxCharge;

}
