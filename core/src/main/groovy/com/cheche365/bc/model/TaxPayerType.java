package com.cheche365.bc.model;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * 增值税纳税人代码
 * Created by austin on 16/5/5.
 */
@ClassDoc(remark="增值税纳税人")
public enum TaxPayerType {

    /**
     * 个人
     */
    @FieldDoc(des="0个人")
    Person(0,"个人"),
    /**
     * 一般纳税人
     */
    @FieldDoc(des="1一般纳税人")
    NormalCompany(1,"一般纳税人"),
    /**
     * 小规模纳税人
     */
    @FieldDoc(des="2小规模纳税人")
    SmallCompany(2,"小规模纳税人");

    TaxPayerType(int code,String value)
    {
        this.code=code;
        this.value=value;
    }
    @FieldDoc(des="纳税人代码")
    private int code;
    @FieldDoc(des="纳税人名称")
    private String value;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
