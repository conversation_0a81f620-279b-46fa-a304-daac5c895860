package com.cheche365.bc.model.car;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付渠道
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
//@ClassDoc(remark="支付渠道")
@Getter
@Setter
@XmlRootElement
public class PayChannel  implements Serializable{
    /**支付渠道类型*/
    private String payChannelType;
    private Integer weight;
    /**图标*/
    private String logo;
    /**服务金额*/
    private BigDecimal serviceCharges;

}
