package com.cheche365.bc.model;

import com.cheche365.bc.model.car.PersonInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * 代理人信息
 * Created by austinChen on 2015/9/18.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@XmlRootElement
public class AgentInfo extends PersonInfo implements Serializable {

    /**从业资格证*/
    private String certNumber;
    /**机构代码*/
    private String orgCode;
    /**机构名称*/
    private String orgName;
    /**团队代码*/
    private String teamCode;
    /**团队名称*/
    private String teamName;

    /**代理人常用出单网点*/
    private String issueCode;

    /**代理人常用出单网点名称*/
    private String issueName;

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Boolean getOfficial() {
        return isOfficial;
    }

    public void setOfficial(Boolean official) {
        isOfficial = official;
    }

    /**挂在哪个代理人名下*/
    private String parentId;

    /**
    是否正式用户
    */
    private Boolean isOfficial;

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }



    public String getIssueName() {
        return issueName;
    }

    public void setIssueName(String issueName) {
        this.issueName = issueName;
    }

    public String getIssueCode() {
        return issueCode;
    }

    public void setIssueCode(String issueCode) {
        this.issueCode = issueCode;
    }
}
