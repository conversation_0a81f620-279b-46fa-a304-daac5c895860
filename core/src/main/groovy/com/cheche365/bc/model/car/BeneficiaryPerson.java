package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

/**
 * 受益人
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "受益人信息")
@Getter
@Setter
@XmlRootElement
public class BeneficiaryPerson extends InsurePerson {

    /**
     * 是否法定
     */
    @FieldDoc(des = "是否法定", remark = "默认为false")
    private Boolean isLegal;
    /**
     * 受益顺序
     */
    @FieldDoc(des = "收益顺序", need = true, remark = "1,2,3,排序")
    private Integer orderNum;
    /**
     * 受益比例
     */
    @FieldDoc(des = "收益比例", need = true, remark = "0.30这样")
    private BigDecimal ratio;

}
