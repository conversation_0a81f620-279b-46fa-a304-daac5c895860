package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据库对应的车型信息
 * Created by austin on 16/6/20.
 */
@ClassDoc(remark = "车型信息")
@Getter
@Setter
@XmlRootElement
public class CarModelInfo implements Serializable {

    @FieldDoc(des = "车型代码", need = true, remark = "")
    private String code;
    @FieldDoc(des = "车价", need = true, remark = "")
    private BigDecimal price;
    @FieldDoc(des = "含税价", remark = "")
    private BigDecimal taxPrice;
    @FieldDoc(des = "排气量", remark = "")
    private BigDecimal displacement;
    private String brandName;
    private String familyName;
    @FieldDoc(des = "座位数", need = true, remark = "")
    private int seat;
    @FieldDoc(des = "Vin模板", remark = "")
    private String vinTemplate;
    @FieldDoc(des = "交管车辆类型", remark = "")
    private String jgType;
    private String factoryName;
    private String standardName;
    private String standardFullName;
    @FieldDoc(des = "载质量", remark = "")
    private BigDecimal loads;
    @FieldDoc(des = "车重量", remark = "")
    private BigDecimal fullWeight;
    @FieldDoc(des = "年款", remark = "")
    private Date markDate;
}
