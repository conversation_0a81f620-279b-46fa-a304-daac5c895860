package com.cheche365.bc.model.car; /**
 *
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.model.DeliveType;
import com.cheche365.bc.model.ReceiveDayType;
import com.cheche365.bc.model.ReceiveTimeType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

/**
 * 配送信息
 *
 * <AUTHOR>
 *         created at 2015年6月15日 下午1:55:40
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "配送信息")
@Getter
@Setter
@XmlRootElement
public class DeliverInfo extends PersonInfo {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = -2839412495057793506L;

    /**
     * 省份
     */
    @FieldDoc(des = "省份", need = true, remark = "")
    private String province;
    /**
     * 城市
     */
    @FieldDoc(des = "城市", need = true, remark = "")
    private String city;
    /**
     * 地区
     */
    @FieldDoc(des = "地区", need = true, remark = "")
    private String area;

    /**
     * 签收时间-工作日/休息日/任何时间
     */
    @FieldDoc(des = "签收日期类型", need = true, remark = "", relatTypes = {ReceiveDayType.class})
    private Integer receiveDayType;
    /**
     * 签收时间-早/中/晚/全天候
     */
    @FieldDoc(des = "签收时间类型", need = true, remark = "", relatTypes = {ReceiveTimeType.class})
    private Integer receiveTimeType;

    /**
     * 备注
     */
    @FieldDoc(des = "客户备注", remark = "")
    private String remark;
    /**
     * 配送方式
     */
    @FieldDoc(des = "配送方式", need = true, remark = "", relatTypes = {DeliveType.class})
    private Integer deliveType;

    /**
     * 配送费
     */
    @FieldDoc(des = "配送费用", remark = "")
    private BigDecimal fee;
    /**
     * 运单号
     */
    @FieldDoc(des = "运单号", remark = "")
    private String traceNumber;
    /**
     * 供应商id
     */
    @FieldDoc(des = "配送商ID", remark = "")
    private String providerId;
    /**
     * 供应商名称
     */
    @FieldDoc(des = "配送商名称", remark = "")
    private String providerName;
}
