package com.cheche365.bc.model;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.util.Date;

/**
 * 车的影像定义
 * Created by austin on 16/4/22.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "影像信息")
@XmlRootElement
public class ImgInfo {

    private String id;
    /**
     * 影像关联的业务Id
     */
    @FieldDoc(des="关联业务id",need=true)
    private String busId;
    /**
     * 影像类型
     */
    @FieldDoc(des="影像类型",need=true)
    private ImgType imgType;
    /**
     * 影像原始地址
     */
    @FieldDoc(des="影像原始地址",need=true)
    private String orgUrl;

    /**
     * 影像缩略图地址
     */
    @FieldDoc(des="影像缩略图地址")
    private String thumbUrl;
    /**
     * 影像创建时间
     */
    @FieldDoc(des="影像创建时间")
    private Date created;
    /**
     * 影像版本号
     */
    @FieldDoc(des="影像版本号")
    private int version;

    public ImgType getImgType() {
        return imgType;
    }

    public void setImgType(ImgType imgType) {
        this.imgType = imgType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBusId() {
        return busId;
    }

    public void setBusId(String busId) {
        this.busId = busId;
    }

    public String getOrgUrl() {
        return orgUrl;
    }

    public void setOrgUrl(String orgUrl) {
        this.orgUrl = orgUrl;
    }

    public String getThumbUrl() {
        return thumbUrl;
    }

    public void setThumbUrl(String thumbUrl) {
        this.thumbUrl = thumbUrl;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

}
