package com.cheche365.bc.model;

/**
 * Created by wjl on 2015/9/18.
 */
public enum JgVehicleType {

    /**
     * 0载客汽车
     */
    PassengerCar,
    /**
     * 1载货汽车
     */
    FreightCar,
    /**
     * 2半挂牵引车
     */
    HalfHangedCar,
    /**
     * 3低速载货汽车
     */
    LowSpeedCarryingCar,
    /**
     * 4挂车
     */
    Trailer,
    /**
     * 5客货两用车
     */
    Van,
    /**
     * 6集装箱
     */
    Container,
    /**
     * 7特种车
     */
    Special,
    /**
     * 8拖拉机
     */
    Tractor,
    /**
     * 9摩托车
     */
    AutoBicycle,
    /**
     * 10轻型摩托车
     */
    LightMotorcycle,
    /**
     * 11侧三轮
     */
    SideCar,
    /**
     * 12运输型拖拉机
     */
    TransportTractor,
    /**
     * 13轿车
     */
    Car,
    /**
     * 14越野车
     */
    SUV,
    /**
     * 15自卸汽车
     */
    AutoDumper,
    /**
     * 16牵引汽车
     */
    TowingTruck,
    /**
     * 17大型客车
     */
    LargeBus,
    /**
     * 18中型客车
     */
    MediumBus,
    /**
     * 19小型客车
     */
    LightBus,
    /**
     * 20出租轿车
     */
    Taxi,
    /**
     * 21微型车
     */
    Microcar,
    /**
     * 22三轮汽车
     */
    Tricar,
    /**
     * 23专项作业车
     */
    SpecialAssignmentsCar,
    /**
     * 24轮式专用机械
     */
    WheeledMachinery,
    /**
     * 25军用车辆
     */
    militaryVehicle,
    /**
     * 26武警车辆
     */
    ArmedPoliceVehicles,
    /**
     * 27警用车辆
     */
    PoliceVehicles,
    /**
     * 28外交车辆
     */
    DiplomaticVehicle,
    /**
     * 29公交车辆
     */
    PublicTransport,
    /**
     * 30其他
     */
    Other,
    /**
     * 31轻型载货汽车
     */
    LightCarryingCar,
    /**
     * 32中型载货汽车
     */
    MiddleCarryingCar,
    /**
     * 33重型载货汽车
     */
    HeavyCarryingCar
}
