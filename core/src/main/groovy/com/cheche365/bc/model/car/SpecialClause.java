package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.util.Map;

/**
 * 特别约定
 * <AUTHOR>
 *  by austinChen on 2016/3/15 10:47.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="特别约定")
@Getter
@Setter
@XmlRootElement
public final class SpecialClause {

    /**
     * 特约代码
     */
    @FieldDoc(des="特约代码",need=true,remark = "每个保险公司有自己的特约代码")
    private String code;
    /** 标志字段 */
    @FieldDoc(des="标志字段",need=true,remark = "标志字段")
    private String flag;
    /** 特约条款文字结果描述 */
    @FieldDoc(des="特约条款文字结果描述",need=true,remark = "特约条款文字结果描述")
    private String clauses;
    /**
     * 特约标题
     */
    @FieldDoc(des="特约标题")
    private String title;
    /**
     * 特约描述
     */
    @FieldDoc(des="特约描述")
    private String des;
    /**
     * 特约参数
     */
    @FieldDoc(des="特约参数")
    private Map<String,String> params=null;

}
