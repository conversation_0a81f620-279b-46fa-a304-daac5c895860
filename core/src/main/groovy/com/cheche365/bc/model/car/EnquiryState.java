package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.util.Map;

/**
 * Created by austin on 16/4/11.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "单方状态")
@XmlRootElement
public final  class EnquiryState {
    @FieldDoc(des="报价中")
    public static final String inQuote="inQuote";
    @FieldDoc(des="核保中")
    public static final String inInsure="inInsure";
    @FieldDoc(des="重新报价")
    public static final String reQuote="reQuote";
    @FieldDoc(des="重新核保")
    public static final String reInsure="reInsure";
    @FieldDoc(des="报价成功")
    public static final String quoteSuccess="quoteSuccess";
    @FieldDoc(des="报价失败")
    public static final String quoteFail="quoteFail";
    @FieldDoc(des="核保成功")
    public static final String insureSuccess="insureSuccess";
    @FieldDoc(des="核保失败")
    public static final String insureFail="insureFail";
    @FieldDoc(des="待支付")
    public static final String inPay="inPay";
    @FieldDoc(des="支付成功")
    public static final String paySuccess="paySuccess";
    @FieldDoc(des="承保中")
    public static final String inApproved="inApproved";
    @FieldDoc(des="承保成功")
    public static final String approvedSuccess="approvedSuccess";
    @FieldDoc(des="保单打印中")
    public static final String inPrint="inPrint";
    @FieldDoc(des="保单打印完成")
    public static final String printComplete="printSuccess";

    @FieldDoc(des="配送中")
    public static final String inDeliver="inDeliver";
    @FieldDoc(des="配送成功")
    public static final String deliverSuccess="deliverSuccess";
    @FieldDoc(des="配送失败")
    public static final String deliverFail="deliverFail";

    @FieldDoc(des="无法承保")
    public static final String reject="reject";
    @FieldDoc(des="订单成功结束")
    public static final String overSuccess="overSuccess";

    public static  Map<String,Map<String,String>> statesActionMap=null;
    public static Map<String,String> statesCnMap=null;

    static{
//        statesActionMap=new HashMap<String,List<String>>();
//        statesActionMap.put(inQuote, );
//        statesActionMap.put(quoteSuccess, Arrays.asList(new String[]{inInsure,reQuote,reject}));
//        statesActionMap.put(quoteFail, Arrays.asList(new String[]{reQuote,quoteSuccess,reject}));
//        statesActionMap.put(inInsure, Arrays.asList(new String[]{insureSuccess,insureFail}));
//        statesActionMap.put(insureSuccess, Arrays.asList(new String[]{inPay,quoteFail}));
//        statesActionMap.put(inQuote, Arrays.asList(new String[]{quoteSuccess,quoteFail}));
//        statesActionMap.put(inQuote, Arrays.asList(new String[]{quoteSuccess,quoteFail}));
//        statesActionMap.put(inQuote, Arrays.asList(new String[]{quoteSuccess,quoteFail}));
    }




}
