package com.cheche365.bc.model.car; /**
 * 
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * 供应商信息。可附加上供应商能力描述
 * <AUTHOR>
 * created at 2015年6月12日 下午1:58:10
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="供应商信息")
@Getter
@Setter
@XmlRootElement
public class ProviderInfo implements Serializable {
	/**
	 * 序列化编号
	 */
	private static final long serialVersionUID = 1021786890818634594L;

    @FieldDoc(des="供应商id",need=true,remark = "")
	private String id;
    /*昵称*/
    @FieldDoc(des="简称",need=true,remark = "")
	private String nickName;
    /*公司ID*/
    @FieldDoc(des="公司ID",need=true,remark = "")
	private String comId;


    @Override
    public String toString() {
        return "ProviderInfo{" +
                "comId='" + comId + '\'' +
                ", id='" + id + '\'' +
                ", nickName='" + nickName + '\'' +
                '}';
    }
}
