package com.cheche365.bc.model;


import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * 影像类型,存储在数据库中,启动的时候从数据库读取到服务内存中使用
 * Created by austin on 16/4/22.
 */
@ClassDoc(remark = "影像类型")
public class ImgType {

    /**
     * 影像代码
     */
    @FieldDoc(des="影像代码",need=true)
    private int code;
    /**
     * 影像描述
     */
    @FieldDoc(des="影像描述")
    private String des;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
