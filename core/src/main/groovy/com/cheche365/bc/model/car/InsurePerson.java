package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 保险人使用的对象
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "保险人信息")
@Getter
@Setter
@XmlRootElement
public class InsurePerson extends PersonInfo {


    /**
     * 与车主关系
     */
    @FieldDoc(des = "与车主关系")
    private String relation;

}
