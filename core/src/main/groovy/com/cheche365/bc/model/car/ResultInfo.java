package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="结果信息")
@Getter
@Setter
@XmlRootElement
public class ResultInfo implements Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	/**错误编码，从0开始*/
	@FieldDoc(des="结果代码",need=true,remark = "从码表中获取结果代码")
	private Integer code;

	/**错误描述*/
	@FieldDoc(des="结果描述",need=true,remark = "从码表中获取结果描述")
	private String des;
	/**
	 * 处理系统或者处理人
	 */
	@FieldDoc(des="处理人",need=true,remark = "处理系统或者处理人")
    private String processor;
	@FieldDoc(des="处理时间",need=true,remark = "")
	private Date processDate;

}
