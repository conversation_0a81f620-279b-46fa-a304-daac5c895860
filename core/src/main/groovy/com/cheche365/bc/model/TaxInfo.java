package com.cheche365.bc.model;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

/**
 * 营改增税务信息
 * Created by austin on 16/5/5.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="营改增税务信息")
@XmlRootElement
public class TaxInfo {

    public boolean isInvoice() {
        return invoice;
    }

    public void setInvoice(boolean invoice) {
        this.invoice = invoice;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(int idCardType) {
        this.idCardType = idCardType;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public int getTaxPayerType() {
        return taxPayerType;
    }

    public void setTaxPayerType(int taxPayerType) {
        this.taxPayerType = taxPayerType;
    }

    public int getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(int invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }


    /**
     * 是否开票
     */
    @FieldDoc(des="是否开票",need = true)
    private boolean invoice;
    /**
     * 开票的名字
     */
    @FieldDoc(des="开票的名字")
    private String name;
    /**
     * 开票的证件类型
     */
    @FieldDoc(des="开票的证件类型")
    private int idCardType;
    /**
     * 开票的证件号
     */
    @FieldDoc(des="开票的证件号")
    private String idCard;
    /**
     * 开票的地址
     */
    @FieldDoc(des="开票的地址")
    private String address;
    /**
     * 手机号码
     */
    @FieldDoc(des="开票的地址")
    private String mobile;
    /**
     * 银行开户行
     */
    @FieldDoc(des="银行开户行")
    private String bankName;
    /**
     * 银行开户账号
     */
    @FieldDoc(des="银行开户账号")
    private String bankAccount;

    /**
     * 纳税人类型,个人或者小规模纳税人,可选择增值税普通发票(电子),纸质,默认电子,一般纳税人,不默认,请选择
     */
    @FieldDoc(des="纳税人类型",relatTypes = {TaxPayerType.class})
    private int taxPayerType;
    /**
     * 发票类型
     */
    @FieldDoc(des="发票类型",relatTypes = {InvoiceType.class})
    private int invoiceType;
    /**
     * 税率
     */
    @FieldDoc(des="税率")
    private BigDecimal taxRate;

    /**
     * 如果是增值税电子发票.邮箱会用上
     */
    @FieldDoc(des="邮箱地址",remark = "如果是增值税电子发票.邮箱会用上")
    private String email;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
