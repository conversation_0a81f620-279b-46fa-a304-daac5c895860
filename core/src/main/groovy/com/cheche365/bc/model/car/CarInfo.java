package com.cheche365.bc.model.car; /**
 *
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 车辆信息对象
 *
 * <AUTHOR>
 *         created at 2015年6月12日 下午5:11:52
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "车辆信息")
@Getter
@Setter
@XmlRootElement
public class CarInfo implements Serializable {

    /**
     * 车辆的ID
     */
    private String id;
    /**新车标志*/
    @FieldDoc(des="是否新车标志",remark = "新车true，非新车false")
    private Boolean isNew;
    /**未上牌标记*/
    @FieldDoc(des="是否未上牌标志",remark = "未上牌true，上牌false")
    private Boolean noLicenseFlag;
    /**车牌号*/
    @FieldDoc(des="车牌号码",need=true,remark = "")
    private String plateNum;
    /**号牌颜色*/
    @FieldDoc(des="号牌颜色", need=true)
    private Integer plateColor;
    /**号牌种类*/
    @FieldDoc(des="号牌种类", need=true)
    private Integer plateType;
    /**车系名称*/
        @FieldDoc(des="车系名称")
    private String familyName;
    /**玻璃类型*/
    @FieldDoc(des="玻璃类型",remark="国产为0，进口为1，参见GlassType对象")
    private Integer glassType;
    /**车架号*/
    @FieldDoc(des="车架号",need=true,remark = "")
    private String vin;
    /**发动机号*/
    @FieldDoc(des="发动机号",need=true,remark = "")
    private String engineNum;
    /**交管车辆类型*/
    @FieldDoc(des="交管车辆类型",remark = "参见JgVehicleType", need=true)
    private Integer jgVehicleType;
    //车辆种类,包含特种车信息
    @FieldDoc(des="车辆种类名称,包含特种车信息",remark = "无")
    private String syvehicletypename;
    //车辆种类,包含特种车信息
    @FieldDoc(des="车辆种类代码,包含特种车信息",remark = "无", need=true)
    private String syvehicletypecode;
    /**使用性质*/
    @FieldDoc(des="使用性质",need = true,remark = "参见UseProps")
    private Integer useProps;
    /**车辆用户类型*/
    @FieldDoc(des="车辆用户类型",remark = "参见CarUserType")
    private Integer carUserType;
    /**行驶证上的初登日期*/
    @FieldDoc(des="行驶证上的初登日期",need=true,remark = "yyyy-MM-dd")
    private Date firstRegDate;

    /**是过户车*/
    @FieldDoc(des="是否过户车",need = true,remark = "是的时候为true，其他为false")
    private Boolean isTransfer;
    /**是否外地车辆*/
    @FieldDoc(des="是否外地车辆",remark = "是的时候为true，其他为false")
    private Boolean isNonLocal;
    /**过户转移登记日期*/
    @FieldDoc(des="过户转移登记日期",remark = "当isTransfer为true的时候必填")
    private Date transferDate;
    /**车型品牌名称*/
    @FieldDoc(des="车型品牌名称",remark = "")
    private String carBrandName;
        /**车型名称*/
    @FieldDoc(des="车型名称",need = true,remark = "")
    private String carModelName;
    /**人保车型名称*/
    private String rbCarModelName;
    /**精友车型名称*/
    private String jyCarModelName;
    /**内部车型code*/
    private String bwCode;
    /**精友车型code*/
    private String jyCode;
    /**人保车型code*/
    private String rbCode;
    /**是否人保code匹配*/
    private Boolean isRbMatch;
    /**是否精友code匹配*/
    private Boolean isJyMatch;
    /**保险公司车型Code*/
    private String insuranceCode;
    /**
     * 车价
     */
    @FieldDoc(des="车价",need = true,remark = "新车购置价的时候必填发票价")
    private BigDecimal price;

    /**
     * 车船税折扣比例
     */
    private BigDecimal taxRate;
    /**
     * 车船税完税标志
     */
    @FieldDoc(des="完税标志",remark = "车船税完税标志,完税为:1,未完税为0或空")
    private int paidtaxes;

    /**
     * 新车购置价
     */
    @FieldDoc(des="新车购置价",remark = "新车购置价的时候必填发票价")
    private BigDecimal taxPrice;
    /**含税类比价*/
    @FieldDoc(des="含税类比价",remark = "")
    private BigDecimal taxAnalogyPrice;
    /**不含税类比价*/
    @FieldDoc(des="不含税类比价",remark = "")
    private BigDecimal analogyPrice;

    /**
     * 排气量
     */
    @FieldDoc(des="排气量",need = true,remark = "")
    private BigDecimal displacement;

    /**
     * 载重量
     */
    @FieldDoc(des="载重量",remark = "")
    private BigDecimal modelLoad;

    /**
     * 车身自重
     */
    @FieldDoc(des="车身自重",remark = "")
    private BigDecimal fullLoad;
    /**座位数*/
    @FieldDoc(des="座位数",need = true,remark = "")
    private Integer seatCnt;
    /**核定载客人数*/
    @FieldDoc(des="核定载客人数",remark = "")
    private Integer ratedPassengerCapacity;
    /**准牵引总质量*/
    private BigDecimal haulage;
    /**杂项*/
    private Map<String, String> misc = new HashMap<String, String>();
    /**机动车燃料种类**/
    @FieldDoc(des="机动车燃料种类",remark = "")
    private Integer fuleType;

    /**
     * 创建时间
     */
    private Date created;
    /**
     * 更新时间
     */
    private Date updated;
    /**车价选择，0-最低，1-最高，2-自定义*/
    @FieldDoc(des="车价选择", remark = "0-最低，1-最高，2-自定义")
    private int carPriceType;

    /**
     * 自定义的车损保额
     */
    /*自定义车价，如果车价选择为2的时候该字段为自定义的车价，其它的车价选择，该字段为0*/
    private BigDecimal definedCarPrice;

    /**
     * 车主信息
     */
    private CarOwnerInfo carOwnerInfo;

    /**
     * 中文品牌型号
     */
    private String carCnModel;
    /**
     * 英文品牌型号
     */
    private String carEnModel;

    /**
     * 第三方的使用性质
     */
    private String thirdUseProp;

    /***
    * 外形尺寸
    */
    private String vehicleSize;

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = -1944220078598289985L;

}
