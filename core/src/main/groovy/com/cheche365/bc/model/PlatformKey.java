package com.cheche365.bc.model;

import java.util.List;

/**
 * Created by Co on 2016/9/5.
 */
public class PlatformKey {
    /*整体信息存储在中间变量的key*/
    public static final String platformBack = "platformBack";
    /*车辆信息节点key*/
    public static final String task = "task";
    /*品牌名称*/
    public static final String carBrandName = "carBrandName";
    /*新车购置价*/
    public static final String price = "price";
    /*新车购置价(含税)*/
    public static final String taxPrice = "taxPrice";
    /*类比价*/
    public static final String analogyPrice = "analogyPrice";
    /*类比价(含税)*/
    public static final String analogyTaxPrice = "analogyTaxPrice";
    /*上市年份*/
    public static final String carModelDate = "carModelDate";
    /*核定载客(框架拿)*/
    public static final String seatCnt = "seatCnt";
    /*核定载质量(框架拿)*/
    public static final String modelLoad = "modelLoad";
    /*整备质量*/
    public static final String fullLoad = "fullLoad";
    /*排气量*/
    public static final String displacement = "displacement";
    /*车型编码*/
    public static final String modelCode = "modelCode";
    /*行业车型编码*/
    public static final String vehicleInfo_tradeModelCode = "vehicleInfo.tradeModelCode";
    /*自主核保系数*/
    public static final String car_specific_selfInsureRate = "car.specific.selfInsureRate";
    /*自主渠道系数*/
    public static final String car_specific_selfChannelRate = "car.specific.selfChannelRate";
    /*NCD系数*/
    public static final String car_specific_NcdRate = "car.specific.NcdRate";
    /*车辆种类*/
    public static final String vehicleInfo_vehicleType = "vehicleInfo.vehicleType";
    /*机动车车损纯风险基准保费*/
    public static final String quoteItems_ruleItem_basicRiskPremium = "quoteItems_ruleItem.basicRiskPremium";
    /*上年承保公司名称*/
    public static final String application_lastInsureCo = "application.lastInsureCo";


    /*查询公司名称*/
    public static final String plateform_InsureCo = "plateform.InsureCo";
    /*无赔款优待系数*/
    public static final String noClaimDiscountCoefficient = "noClaimDiscountCoefficient";
    /*自主定价系数*/
    public static final String selfRate = "selfRate";
    /*商业险连续承保年数*/
    public static final String bizContinuityInsureYears = "bizContinuityInsureYears";
    /*无赔款折扣浮动原因*/
    public static final String noClaimDiscountCoefficientReasons = "noClaimDiscountCoefficientReasons";
    /*无赔款折扣不浮动原因*/
    public static final String loyaltyReasons = "loyaltyReasons";
    /*交通违法系数*/
    public static final String trafficOffenceDiscount = "trafficOffenceDiscount";
    /*交强险理赔系数*/
    public static final String compulsoryClaimRate = "compulsoryClaimRate";
    /*交强险浮动原因*/
    public static final String compulsoryClaimRateReasons = "compulsoryClaimRateReasons";
    /*投保类型(框架算、不用回)*/
    public static final String firstInsureType = "firstInsureType";
    /*上年商业险理赔次数(框架算、不用回)*/
    public static final String bwCommercialClaimTimes = "bwCommercialClaimTimes";
    /*上年商业险理赔金额*/
    public static final String bwLastClaimSum = "bwLastClaimSum";
    /*上年交强险理赔次数(框架算、不用回)*/
    public static final String bwCompulsoryClaimTimes = "bwCompulsoryClaimTimes";
    /*上年交强险理赔金额*/
    public static final String bwLastCompulsoryClaimSum = "bwLastCompulsoryClaimSum";
    /*平台商业险理赔次数*/
    public static final String claimTimes = "claimTimes";
    /*平台交强险理赔次数*/
    public static final String compulsoryClaimTimes = "compulsoryClaimTimes";
    /*平台商业险理赔金额*/
    public static final String lastClaimSum = "lastClaimSum";
    /*商业险起保标识*/
    public static final String SYendMark = "SYendMark";
    /*商业险起保提示语*/
    public static final String errorMsgSY = "errorMsgSY";
    /*交强险起保标识*/
    public static final String JQendMark = "JQendMark";
    /*交强险起保提示语*/
    public static final String errorMsgJQ = "errorMsgJQ";
    /*车牌(框架算、不用回)*/
    public static final String licenseNo = "licenseNo";
    /*发动机(框架算、不用回)*/
    public static final String engineNo = "engineNo";
    /*vin(框架算、不用回)*/
    public static final String vinNo = "vinNo";
    /*初登日期(框架算、不用回)*/
    public static final String enrollDate = "enrollDate";
    /*成功标识succed(框架算、不用回)*/
    public static final String succeed = "succeed";
    /*交强险折扣保费*/
    public static final String efcDiscount = "efcDiscount";
    /*车船税滞纳金*/
    public static final String vehicleTaxOverdueFine = "vehicleTaxOverdueFine";
    /*风险类别*/
    public static final String riskClass = "riskClass";
    /*是否通过纯电销投保*/
    public static final String pureESale = "pureESale";
    /*上年商业险赔付率*/
    public static final String bwCommercialClaimRate = "bwCommercialClaimRate";
    /*往年补缴税额*/
    public static final String lwArrearsTax = "lwArrearsTax";
    /*平台车价*/
    public static final String platformCarPrice = "platformCarPrice";
    /*酒驾系数*/
    public static final String DrunkDrivingRate = "DrunkDrivingRate";
    /*实际价值*/
    public static final String rateCarPrice = "rateCarPrice";
    /*车龄*/
    public static final String carAge = "carAge";

    //商业上年投保信息key
    public static final String bizPolicies = "bizPolicies";
    //交强上年投保信息key
    public static final String efcPolicies = "efcPolicies";
    //商业上年理赔信息key
    public static final String bizClaims = "bizClaims";
    //交强上年理赔信息key
    public static final String efcClaims = "efcClaims";

    /*保险公司ID*/
    public static final String insCorpId = "insCorpId";
    /*保险公司代码*/
    public static final String insCorpCode = "insCorpCode";
    /*保险公司名称*/
    public static final String insCorpName = "insCorpName";
    /*起保时间*/
    public static final String policyStartTime = "policyStartTime";
    /*终保时间*/
    public static final String policyEndTime = "policyEndTime";
    /*保单号*/
    public static final String policyId = "policyId";
    /*出险时间*/
    public static final String caseStartTime = "caseStartTime";
    /*结案时间*/
    public static final String caseEndTime = "caseEndTime";
    /*理赔金额*/
    public static final String claimAmount = "claimAmount";

    //商业险手续费比例
    public static final String bizBrokerageRate = "geniusItem.commercialBrokerageRate";
    //交强险手续费比例
    public static final String efcBrokerageRate = "geniusItem.compulsoryBrokerageRate";
    //商业险风险等级1
    public static final String bizRiskLevel1 = "geniusItem.commercialRiskLevel1";
    //商业险风险等级2
    public static final String bizRiskLevel2 = "geniusItem.commercialRiskLevel2";
    //交强险风险等级1
    public static final String efcRiskLevel1 = "geniusItem.compulsoryRiskLevel1";
    //交强险风险等级2
    public static final String efcRiskLevel2 = "geniusItem.compulsoryRiskLevel2";
    //项目代码
    public static final String itemCode = "geniusItem.itemCode";
    //商业险业务评分
    public static final String bizScore = "application.bizScore";
    //机构名称
    public static final String deptChineseName = "application.deptChineseName";
    /**
     * 交强机构名称
     */
    public static final String DEPT_JQ_CHINESE_NAME = "application.deptJQChineseName";
    /**
     * 交强险业务评分
     */
    public static final String TRAFFIC_SCORE = "application.trafficScore";
    /**
     * 中华用到的 服务代码
     */
    public static final String SERVICE_CODE = "application.serviceCode";
    //商业险预期赔付率
    public static final String application_expectLossRatio = "application.expectLossRatio";
    //商业险预期赔付率标签
    public static final String application_expectLossRatioTag = "application.expectLossRatioTag";
    //交强险预期赔付率
    public static final String application_expectTrafficLossRatio = "application.expectTrafficLossRatio";
    //交商合计预期赔付率
    public static final String application_expectMixedRatio = "application.expectMixedRatio";
    //客户忠诚度
    public static final String application_loyalty = "application.loyalty";
    //交通违章次数
    public static final String application_trafficOffence = "application.trafficOffence";
    //酒驾系数
    public static final String drunkenCoefficient = "drunkenCoefficient";
    //交强险业务评级
    public static final String application_trafficRate = "application.trafficRate";
    //商业险业务评级
    public static final String application_bizRate = "application.bizRate";
    //商业险预测评分
    public static final String application_syForecastScore = "application.syForecastScore";
    //交强险预测评分
    public static final String application_jqForecastScore = "application.jqForecastScore";

    public static final List<String> PLATFORM_KEY_LIST = List.of(bwLastClaimSum, bwLastCompulsoryClaimSum, trafficOffenceDiscount, application_lastInsureCo, insCorpName, noClaimDiscountCoefficient, bwCompulsoryClaimTimes, application_loyalty, firstInsureType, bizContinuityInsureYears, bizPolicies, efcPolicies, bwCommercialClaimTimes);


}
