package com.cheche365.bc.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * author:<PERSON>Zhao<PERSON><PERSON>
 * Date:2019/10/31 16:54
 */
@Data
public class ExportFeeModel {

    /**
     * 代理公司名称
     */
    private String orgName;

    /**
     * 保险公司名称
     */
    private String insName;

    /**
     * 结算批次号
     */
    private String actionNo;

    /**
     * 总计：总含税保费
     */
    private BigDecimal premium;

    /**
     * 总计：总净保费
     */
    private BigDecimal netPremium;

    /**
     * 总计：总直接手续费
     */
    private BigDecimal directFee;

    /**
     * 总计：总税金（直接）
     */
    private BigDecimal directFeeTax;

    /**
     * 总计：总间接手续费
     */
    private BigDecimal indirectFee;

    /**
     * 总计：总税金（间接）
     */
    private BigDecimal indirectFeeTax;

    /**
     * 总计：总手续费（直接+间接）
     */
    private BigDecimal totalFee;

    /**
     * 总计：总税金
     */
    private BigDecimal totalTax;

}
