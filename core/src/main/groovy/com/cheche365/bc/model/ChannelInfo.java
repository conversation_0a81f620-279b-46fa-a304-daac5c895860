package com.cheche365.bc.model; /**
 * 
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.model.car.PersonInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 渠道信息描述，包含渠道能力描述
 * <AUTHOR>
 * created at 2015年6月12日 下午1:59:12
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "渠道信息")
@XmlRootElement
public class ChannelInfo extends PersonInfo {

	/**
	 * 序列化ID
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 允许渠道接入IP
	 */
	@FieldDoc(des="允许渠道接入IP",remark = "")
	private String allowIps;
	/**
	 * desKey 加密和解密的key
	 */
	@FieldDoc(des="加密和解密的key",remark = "")
	private String desKey;
	/**
	 * 剩余的车辆查询次数
	 */
	private int leftQueryCarTimes;
	/**
	 * 总共的车辆查询次数
	 */
	private int totalQueryCarTimes;
	/**
	 * 已使用的车辆查询次数
	 */
	private int usedQueryCarTimes;

	/**
	 * 剩余的平台查询次数
	 */
	private int leftQueryPlatTimes;
	/**
	 * 剩余的保单查询次数
	 */
	private int leftQueryPolicyTimes;
	/**
	 * 剩余的车型查询次数
	 */
	private int leftQueryCarModelPolicyTimes;

	/**
	 * 是否测试阶段
	 */
	@FieldDoc(des="是否测试阶段",remark = "测试阶段为true")
	private boolean testFlag;



	//private int 
	
//	{
//	    "appKey": "testChannel",
//	    "channel": "mobile",
//	    "callBack": "",
//	    "version": "V1.0",
//	    "appId": "testChannel"
//	  }
	
	/**
	 * 渠道接入商的应用appId
	 */
	@FieldDoc(des="渠道接入商的应用appId",need=true,remark = "")
	private String appId="";
	/**
	 * 渠道接入商的应用appKey
	 */
	@FieldDoc(des="渠道接入商的应用appKey",remark = "")
	private String appKey="";
	/**
	 * 渠道的接入方式
	 */
	@FieldDoc(des="渠道名称",need=true,remark = "")
	private String channel="";
	/**
	 * 报价，核保，承保，支付结果通知回调渠道的url
	 */
	@FieldDoc(des="回调地址",remark = "")
	private String callBack="";
	/**
	 * 协议版本号
	 */
	@FieldDoc(des="协议版本号",remark = "")
	private String version="V1.0";

	/**
	 * 是否校验车主姓名
	 */
	@FieldDoc(des="是否校验车主姓名",remark = "")
	private boolean checkNameFlag=false;
	
	
	public String getAppKey() {
		return appKey;
	}
	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getCallBack() {
		return callBack;
	}
	public void setCallBack(String callBack) {
		this.callBack = callBack;
	}


	public int getLeftQueryCarTimes() {
		return leftQueryCarTimes;
	}

	public void setLeftQueryCarTimes(int leftQueryCarTimes) {
		this.leftQueryCarTimes = leftQueryCarTimes;
	}

	public String getAllowIps() {
		return allowIps;
	}

	public void setAllowIps(String allowIps) {
		this.allowIps = allowIps;
	}

	public boolean isTestFlag() {
		return testFlag;
	}

	public void setTestFlag(boolean testFlag) {
		this.testFlag = testFlag;
	}

	public int getLeftQueryCarModelPolicyTimes() {
		return leftQueryCarModelPolicyTimes;
	}

	public void setLeftQueryCarModelPolicyTimes(int leftQueryCarModelPolicyTimes) {
		this.leftQueryCarModelPolicyTimes = leftQueryCarModelPolicyTimes;
	}

	public int getLeftQueryPolicyTimes() {
		return leftQueryPolicyTimes;
	}

	public void setLeftQueryPolicyTimes(int leftQueryPolicyTimes) {
		this.leftQueryPolicyTimes = leftQueryPolicyTimes;
	}

	public int getLeftQueryPlatTimes() {
		return leftQueryPlatTimes;
	}

	public void setLeftQueryPlatTimes(int leftQueryPlatTimes) {
		this.leftQueryPlatTimes = leftQueryPlatTimes;
	}

	public String getDesKey() {
		return desKey;
	}

	public void setDesKey(String desKey) {
		this.desKey = desKey;
	}

	public int getTotalQueryCarTimes() {
		return totalQueryCarTimes;
	}

	public void setTotalQueryCarTimes(int totalQueryCarTimes) {
		this.totalQueryCarTimes = totalQueryCarTimes;
	}

	public int getUsedQueryCarTimes() {
		return usedQueryCarTimes;
	}

	public void setUsedQueryCarTimes(int usedQueryCarTimes) {
		this.usedQueryCarTimes = usedQueryCarTimes;
	}

	public boolean isCheckNameFlag() {
		return checkNameFlag;
	}

	public void setCheckNameFlag(boolean checkNameFlag) {
		this.checkNameFlag = checkNameFlag;
	}
}
