package com.cheche365.bc.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by ya<PERSON><PERSON> on 2019/4/8 22:02
 */
@Data
public class CheckModel<T> {
    /**
     * 是否开启验证
     */
    private boolean enableValidate = true;

    /**
     * 是否有出单信息
     */
    private boolean haveBusinessInfo = false;

    /**
     * 验证列
     */
    private Set<String> checkSet;
    /**
     * 列映射
     */
    private Map<String, String> columnReference;
    /**
     * 结果
     */
    private List<T> result = new ArrayList<>();

    public CheckModel(Map<String, String> columnReference, Set<String> checkSet) {
        this.columnReference = columnReference;
        this.checkSet = checkSet;
    }

    public CheckModel(boolean enableValidate, Map<String, String> columnReference, Set<String> checkSet) {
        this.enableValidate = enableValidate;
        this.columnReference = columnReference;
        this.checkSet = checkSet;
    }

    public CheckModel(boolean enableValidate, boolean haveBusinessInfo, Map<String, String> columnReference, Set<String> checkSet) {
        this.enableValidate = enableValidate;
        this.haveBusinessInfo = haveBusinessInfo;
        this.columnReference = columnReference;
        this.checkSet = checkSet;
    }
}
