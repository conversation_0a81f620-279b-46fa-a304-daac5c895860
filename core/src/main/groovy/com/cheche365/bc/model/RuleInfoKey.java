package com.cheche365.bc.model;

/**
 * Created by Co on 2017/3/8.
 */
public class RuleInfoKey {

    /**
     * 请求参数部分
     */
    //规则name
    public static final String interface_name = "interface.name";
    //规则ID(Long)
    public static final String ruleItem_ruleID = "ruleItem.ruleID";
    //规则ID(Long) (适配新规则引擎)
    public static final String ruleItem_ruleGroupId = "ruleItem.ruleGroupId";
    //规则日志编号(String)
    public static final String api_log_enquiry_id = "api.log.enquiry.id";
    //保险公司出单系统账号(String)
    public static final String application_insureCompanyCode = "application.insureCompanyCode";
    //代理点编号(String)
    public static final String application_agentOrgCode = "application.agentOrgCode";
    //出单网点编码(String)
    public static final String application_deptCode = "application.deptCode";
    //业务归属代码(String)
    public static final String application_affiliationCode = "application.affiliationCode";
    //理赔金额(Double)
    public static final String application_lastCommercialClaimSum = "application.lastCommercialClaimSum";
    //所属性质(Integer)
    public static final String car_specific_userType = "car.specific.userType";
    //车辆使用性质(Long)
    public static final String car_specific_useProps = "car.specific.useProps";
    //上年承保公司(String)
    public static final String application_lastInsureCo="application.lastInsureCo";
    //上年商业理赔次数(Integer)
    public static final String application_commercialClaimTimes = "application.commercialClaimTimes";
    //投保类型(Integer)
    public static final String application_firstInsureType = "application.firstInsureType";
    //实际价值(Double)
    public static final String car_specific_depPrice = "car.specific.depPrice";
    //新车购置价(Double)
    public static final String car_specific_taxPrice = "car.specific.taxPrice";

    //新车购置价不含税(Double)
    public static final String car_specific_Price = "car.specific.price";
    //车龄(Integer)ps:规则那边另做计算
    public static final String car_specific_useMonth = "car.specific.useMonth";
    //车型品牌(String)
    public static final String car_model_brandName = "car.model.brandName";
    //车型名称(String)
    public static final String car_model_modelName = "car.model.modelName";
    //车型系列名称(String)
    public static final String car_model_familyName = "car.model.familyName";
    //地区编号(Long)
    public static final String application_insureArea = "application.insureArea";
    //车牌号码(String)
    public static final String car_specific_license = "car.specific.license";
    //载重量(Double)
    public static final String car_model_modelLoad = "car.model.modelLoad";
    //座位数(Integer)
    public static final String car_model_seats = "car.model.seats";
    //投保交强险
    public static final String insureItem_vehicleCompulsoryIns_isInsured = "insureItem.vehicleCompulsoryIns.isInsured";
    //排气量(Double)
    public static final String car_model_displacement = "car.model.displacement";
    //玻璃类型(Integer)
    public static final String car_specific_glassType = "car.specific.glassType";
    //车辆初登日期(Date)格式:yyyy-M-d
    public static final String car_specific_regDate = "car.specific.regDate";
    //起保日期(Date)格式:yyyy-M-d
    public static final String application_commercialEffectiveDate = "application.commercialEffectiveDate";
    //商业险终止日期(Date)格式:yyyy-M-d
    public static final String application_commercialExpiryDate = "application.commercialExpiryDate";
    //上年交强理赔次数(Integer)
    public static final String application_compulsoryClaimTimes = "application.compulsoryClaimTimes";
    //是否过户车(Boolean)
    public static final String car_specific_isTransferCar = "car.specific.isTransferCar";
    //车辆产地类型(Integer)
    public static final String car_model_vehicleOrigin = "car.model.vehicleOrigin";
    //被保险人姓名(String)
    public static final String insured_name = "insured.name";
    //被保险人年龄(String)
    public static final String insured_age = "insured.age";
    //投保人姓名(String)
    public static final String proposer_name = "proposer.name";
    //投保人年龄(String)
    public static final String proposer_age = "proposer.age";

    //车主姓名(String)
    public static final String car_owner_name = "car.owner.name";
    //被保险人证件类型(Integer)
    public static final String insured_certificate = "insured.certificate";
    //投保人证件类型(Integer)
    public static final String proposer_certificate = "proposer.certificate";
    //车主证件类型(Integer)
    public static final String car_owner_certificate = "car.owner.certificate";
    //特种车类型(Long)
    public static final String car_specific_specialVehicleType = "car.specific.specialVehicleType";
    //无赔款折扣不浮动原因(Integer)
    public static final String ruleItem_noRenewalDiscountReason = "ruleItem.noRenewalDiscountReason";
    //无赔款优待系数
    public static final String ruleItem_noCompensationDiscount = "ruleItem.noCompensationDiscount";
    //业务评分
    public static final String application_bizScore = "application.bizScore";
    /**
     * 业务分类
     */
    public static final String BIZ_RATE = "application.bizRate";
    /**交强险业务评分*/
    public static final String TRAFFIC_SCORE = "application.trafficScore";
    /**
     * 交强险业务分类
     */
    public static final String TRAFFIC_RATE = "application.trafficRate";
    /**服务代码*/
    public static final String SERVICE_CODE = "application.serviceCode";

    /**
     * 返回结果部分
     */
    //自主核保系数
    public static final String geniusItem_insureDiscount = "geniusItem.insureDiscount";
    //自主渠道系数
    public static final String geniusItem_channelDiscount = "geniusItem.channelDiscount";
    //整单折扣
    public static final String geniusItem_policyDiscount = "geniusItem.policyDiscount";
    //自主系数合计
    public static final String geniusItem_totalDiscount = "geniusItem.totalDiscount";
    //平均行驶里程
    public static final String geniusItem_avgMileage = "geniusItem.avgMileage";
    //多险种投保优惠
    public static final String geniusItem_multipleInsuranceDiscount = "geniusItem.multipleInsuranceDiscount";
    //预期经验赔付率
    public static final String geniusItem_cexpectClaimRate = "geniusItem.cexpectClaimRate";
    //交强险预期经验赔付率
    public static final String geniusItem_compulsoryCexpectClaimRate = "geniusItem.compulsoryCexpectClaimRate";
    //赔付率系数
    public static final String geniusItem_claimRateDiscount = "geniusItem.claimRateDiscount";
    //行驶区域
    public static final String geniusItem_drivingArea = "geniusItem.drivingArea";
    //承保数量
    public static final String geniusItem_acceptInsuranceAmount = "geniusItem.acceptInsuranceAmount";
    //承保系数
    public static final String geniusItem_acceptInsuranceDiscount = "geniusItem.acceptInsuranceDiscount";
    //是否安装OBD
    public static final String geniusItem_installedOBD = "geniusItem.installedOBD";
    //是否安装行车记录仪
    public static final String geniusItem_installedTachograph = "geniusItem.installedTachograph";
    //是否过户
    public static final String geniusItem_isTransferCar = "geniusItem.isTransferCar";
    //指定专修厂费率
    public static final String geniusItem_repairDiscount = "geniusItem.repairDiscount";
    //车损保额调整系数
    public static final String geniusItem_vehicleDemageIns_coverageDiscount = "geniusItem.vehicleDemageIns.coverageDiscount";
    //盗抢保额调整系数
    public static final String geniusItem_theftIns_coverageDiscount = "geniusItem.theftIns.coverageDiscount";
    //自燃保额调整系数
    public static final String geniusItem_combustionIns_coverageDiscount = "geniusItem.combustionIns.coverageDiscount";
    //触发的规则
    public static final String ruleItem_runningRecord = "ruleItem.runningRecord";
    //触发的规则
    public static final String RULE_ITEM_TAX_DERATE_TYPE = "ruleItem.taxDerateType";
    //不确定因子
    public static final String ruleItem_uncertaintyFactorUseNumber = "geniusItem.uncertaintyFactorUseNumber";
    //断保天数
    public static final String RULE_BROKEN_INSURE_DAYS = "ruleItem.brokenInsureDays";
    //商业险连续承保年数
    public static final String ruleItem_bizContinuityInsureYears = "ruleItem.bizContinuityInsureYears";


    //光博业务分类和天平业务评分
    public static final String RULE_Biz_Rate = "application.bizRate";
    //投保类型 单商:1,单交:2,混双:3
    public static final String INSURETYPE = "application.insureType";



}
