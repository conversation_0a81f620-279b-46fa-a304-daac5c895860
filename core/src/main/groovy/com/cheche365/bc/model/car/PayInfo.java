package com.cheche365.bc.model.car; /**
 * 
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付信息
 * <AUTHOR>
 * created at 2015年6月15日 下午3:12:14
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="支付信息")
@Getter
@Setter
@XmlRootElement
public class PayInfo implements Serializable {
	
	private String id;
	/**
	 * 序列化ID
	 */
	private static final long serialVersionUID = 7977188660505923386L;

    /*支付渠道ID*/
    @FieldDoc(des="支付通道ID",need=true)
	private String payChannelId;
    /*支付渠道类型*/
    @FieldDoc(des="支付通道类型",need=true)
	private String payChannelType;
    /*支付方式*/
	private String  payStyle;
    /*支付平台生成的交易跟踪号*/
    @FieldDoc(des="支付平台跟踪号",need=true)
	private String payPlatNo;
    /*保险公司生成的支付跟踪号*/
    @FieldDoc(des="保险公司跟踪号",need=true)
	private String insureCoNo;
    /*杂项*/
    @FieldDoc(des="支付杂项")
	private Map<String,String> misc=new HashMap<String,String>();
    /*支付链接*/
    @FieldDoc(des="支付URL")
	private String payUrl;
    /*支付金额*/
    @FieldDoc(des="金额",need = true)
	private BigDecimal amount;
    /*支付时间*/
    @FieldDoc(des="支付时间",need = true)
	private Date payTime;
    /**
     * 支付结果标志 成功为true，否则false
     */
    @FieldDoc(des="支付结果标志",need=true,remark = "成功为true，否则false")
	private Boolean successFlag;
    /**
     * 支付结果备注
     */
    @FieldDoc(des="支付结果备注")
    private String payRemark;

    /**
     * 支付货币类型
     */
    @FieldDoc(des="货币类型")
	private String  currencyCode;

    /**
     * 收款商户号
     */
    @FieldDoc(des="收款商户号")
	private String merchantId;
    /**
     * 收款商户名称
     */
    @FieldDoc(des="收款商户名称",need = true)
	private String merchantName;

}
