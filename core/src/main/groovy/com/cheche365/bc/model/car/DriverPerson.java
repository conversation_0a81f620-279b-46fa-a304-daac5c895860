package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 驾驶人列表
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "驾驶人信息")
@Getter
@Setter
@XmlRootElement
public class DriverPerson extends InsurePerson {

    /**
     * 驾照类型
     */
    @FieldDoc(des = "驾照类型", need = true, remark = "")
    private String licenseType;
    /**
     * 证件号码
     */
    @FieldDoc(des = "证件号码", need = true, remark = "")
    private String licenseNo;
    /**
     * 证件登记日期
     */
    @FieldDoc(des = "证件登记日期", remark = "")
    private Date firstRegDate;
    /**
     * 是否主驾驶员
     */
    @FieldDoc(des = "是否主驾驶员", need = true, remark = "")
    private Boolean isPrimary;
    /**
     * 驾驶习惯
     */
    @FieldDoc(des = "驾驶习惯", need = false, remark = "")
    private Map<String, String> habits = new HashMap<String, String>();

}
