package com.cheche365.bc.model;


import com.cheche365.bc.annotation.ClassDoc;

/**
 * 车身颜色
 * Created by austinChen on 2015/9/18.
 */
@ClassDoc(remark="车身颜色枚举")
public enum CarBodyColor {

     <PERSON>(1,"白色"),
     <PERSON>(2,"灰"),
     <PERSON>(3,"黄") ,
     <PERSON><PERSON><PERSON>(4,"粉"),
     <PERSON> (5,"红"),
     <PERSON>(6,"紫") ,
     <PERSON> (7,"绿"),
     <PERSON> (8,"蓝"),
     <PERSON> (9,"棕"),
     <PERSON> (10,"黑"),
     <PERSON>(11,"其他"),
     <PERSON><PERSON> (12,"银白"),
     <PERSON><PERSON><PERSON>(13,"银灰") ,
     <PERSON><PERSON><PERSON> (14,"墨绿"),
     <PERSON><PERSON> (15,"天蓝"),
     <PERSON><PERSON><PERSON><PERSON>(16,"深棕") ,
     <PERSON><PERSON>(17,"乳白");
    CarBodyColor(int value,String name){
        this.value = value;
        this.name = name;
    }
    private int value;
    private String name;

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
