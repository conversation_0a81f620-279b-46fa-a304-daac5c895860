package com.cheche365.bc.refresh;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.RedisUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import static com.cheche365.bc.constants.Constants.SESSION_CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class YangGuangRefreshHandler implements BaseRefreshHandler {


    @Override
    public boolean isSpecial(String companyId) {
        return String.valueOf(InsCompanyEnum.SMIC.getCode()).equals(companyId);
    }

    @Override
    public String refresh(String url, CloseableHttpClient httpClient, RequestConfig requestConfig, String login) throws Exception {
        String token = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.SMIC.getCode() + ":" + login);

        if (StringUtils.isBlank(token)) {
            return "";
        }

        JSONObject request = new JSONObject();
        request.put("token", token);
        request.put("riskCode", "0507");
        request.put("comCode", "02120000");
        request.put("systemCode", "prpallcar");
        String response = HttpSender.doPostWithRepHeaders(httpClient, true, url, request.toString(), null,
            null, "utf-8", requestConfig, null, null);

        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            String paramKey = Constants.SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.SMIC.getCode() + ":" + login;
            String data = jsonObject.getString("data");
            if (StringUtils.isNotBlank(data)) {
                RedisUtil.set(paramKey, data, 60 * 60);
            } else {
                log.error("阳光精灵机构{}返回 data 为空，发送为：{}，结果为：{}，", login, request, response);
            }

        } catch (Exception e) {
            log.error("阳光精灵机构{}保持登录失败，发送为：{}，结果为：{}，", login, request, response);
        }

        return response;
    }
}
