package com.cheche365.bc.refresh;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.RedisUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.cheche365.bc.constants.Constants.SESSION_CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 */
@Component
public class ZhongChengRefreshHandler implements BaseRefreshHandler {


    @Override
    public boolean isSpecial(String companyId) {
        return String.valueOf(InsCompanyEnum.UTIC.getCode()).equals(companyId);
    }

    @Override
    public String refresh(String url, CloseableHttpClient httpClient, RequestConfig requestConfig, String login) throws Exception {
        String authorToken = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.UTIC.getCode() + ":" + login);
        Map<String, String> headerMap = buildHeaderMap(authorToken);
        return (String) HttpSender.doGet(httpClient, url, headerMap, null, "UTF-8", requestConfig, false);


    }

    private Map<String, String> buildHeaderMap(String authorToken) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("AUTHOR-TOKEN", authorToken);
        headerMap.put(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        return headerMap;
    }
}
