// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) fieldsfirst ansi 
// Source File Name:   Head.java

package com.cheche365.bc.utils.youbaolian.model;

import java.util.Objects;

public class Head {

    private String partnerNo;
    private String timestamp;
    private String requestId;
    private String version;
    private String sign;
    private String message;
    private Integer resultCode;

    public Head() {
        resultCode = Integer.valueOf(1);
    }

    public String getPartnerNo() {
        return partnerNo;
    }

    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getResultCode() {
        return resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof Head)) {
            return false;
        } else {
            Head head = (Head) o;
            return Objects.equals(getPartnerNo(), head.getPartnerNo()) && Objects.equals(getTimestamp(), head.getTimestamp()) && Objects.equals(getRequestId(), head.getRequestId()) && Objects.equals(getVersion(), head.getVersion()) && Objects.equals(getSign(), head.getSign()) && Objects.equals(getMessage(), head.getMessage()) && Objects.equals(getResultCode(), head.getResultCode());
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{
                getPartnerNo(), getTimestamp(), getRequestId(), getVersion(), getSign(), getMessage(), getResultCode()
        });
    }

    public String toString() {
        return (new StringBuilder()).append("Head{partnerNo='").append(partnerNo).append('\'').append(", timestamp='").append(timestamp).append('\'').append(", requestId='").append(requestId).append('\'').append(", version='").append(version).append('\'').append(", sign='").append(sign).append('\'').append(", message='").append(message).append('\'').append(", resultCode=").append(resultCode).append('}').toString();
    }
}
