package com.cheche365.bc.utils.dama;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.tools.DateUtil;
import com.cheche365.bc.tools.FileUtil;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.DataUtil;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
public class Constant {


    /**
     * 产生指定范围内的随机数
     */
    public static int intNum(int start, int end) {
        return (int) (Math.random() * (end - start));
    }

    /**
     * 随机产生手机号码
     */
    public static String getMobile() {
        return StringUtil.getMobile();
    }

    public static Double getTrueValue(Map carInfo) throws Exception {
        String insureStart = DateUtil.d2S(new Date());
        return getTrueValue(carInfo, "不含税价", true, insureStart);
    }

    public static Double getTrueValue(Map carInfo, boolean moreDayAddOne) throws Exception {
        String insureStart = DateUtil.d2S(new Date());
        return getTrueValue(carInfo, "不含税价", moreDayAddOne, insureStart);
    }

    public static Double getTrueValue(Map carInfo, String carPriceType, boolean moreDayAddOne, String insureStart) throws Exception {
        return getTrueValue(carInfo, carPriceType, moreDayAddOne ? 0 : 32, insureStart);
    }

    public static Double getTrueValue(Map carInfo, String carPriceType, int moreDaysAddOne, String insureStart) throws Exception {
        int seatCnt = 5;
        double modelLoad = 0;
        //默认 家庭自用
        int useProps = 1;
        //默认 轿车
        int jgVehicleType = 13;
        //默认 0.006的折旧率系数
        double ratio;
        //车辆号牌
        String plateNum = "";
        if (carInfo.containsKey("seatCnt")) {
            seatCnt = (int) (DataUtil.get("seatCnt", carInfo));
        }
        if (carInfo.containsKey("jgVehicleType")) {
            jgVehicleType = (int) (DataUtil.get("jgVehicleType", carInfo));
        }
        if (carInfo.containsKey("useProps")) {
            useProps = (int) (DataUtil.get("useProps", carInfo));
        }
        if (carInfo.containsKey("modelLoad")) {
            modelLoad = Double.parseDouble(DataUtil.get("modelLoad", carInfo).toString());
        }
        if (carInfo.containsKey("plateNum")) {
            plateNum = DataUtil.get("plateNum", carInfo).toString();
        }
        int modelLoadInt = (int) modelLoad;
        switch (useProps) {
            case 1: {
                if (seatCnt <= 9) {
                    ratio = 0.006;
                } else {
                    ratio = 0.009;
                }
                break;
            }
            case 9:
            case 10:
            case 11:
            case 12:
            case 8: {
                if (seatCnt <= 9 && modelLoadInt == 0) {
                    ratio = 0.006;
                } else if (seatCnt > 10) {
                    ratio = 0.009;
                } else if (jgVehicleType == 3 || jgVehicleType == 22) {
                    ratio = 0.011;
                } else {
                    ratio = 0.009;
                }
                break;
            }
            case 2: {
                if (jgVehicleType == 3 || jgVehicleType == 22) {
                    ratio = 0.014;
                } else {
                    ratio = 0.011;
                }
                break;
            }
            case 6: {
                ratio = 0.011;
                break;
            }
            case 3:
            case 4:
            case 5:
            case 7: {
                if (modelLoadInt == 0 && seatCnt >= 5) {
                    ratio = 0.009;
                } else if (jgVehicleType == 3 || jgVehicleType == 22) {
                    ratio = 0.014;
                } else {
                    ratio = 0.010;
                }
                break;
            }
            case 15:
            case 16: {
                ratio = 0.009;
                break;
            }
            default: {
                ratio = 0.006;
            }
        }
        if (plateNum.contains("挂")) {
            ratio = 0.009;
        }
        Double taxPrice = getPrice(carInfo, carPriceType);
        String firstReg = carInfo.get("firstRegDate").toString();
        BigDecimal tax = new BigDecimal(taxPrice);

        int months = DateUtil.diffMonth(firstReg.substring(0, 10) + " 00:00:00", insureStart, false);
        int days = diffDays4CurrentMonth(firstReg.substring(0, 10) + " 00:00:00", insureStart);
        if (carInfo.containsKey("carPriceType") && (Integer.parseInt(carInfo.get("carPriceType").toString())) == 2) {
            return Double.parseDouble(carInfo.get("definedCarPrice").toString());
        }
        if (months < 9 || (months == 9 && days == 0)) {
            return taxPrice;
        }
        if (days >= moreDaysAddOne) {
            months += 1;
        }
        //盗抢保额=新车购置价-新车购置价*折旧月数*0.006
        BigDecimal tt = tax.multiply(BigDecimal.valueOf(months).multiply(new BigDecimal(ratio)));
        BigDecimal big = tax.subtract(tt);
        BigDecimal lowRate = BigDecimal.valueOf(0.2);// new BigDecimal(0.2);
        double lowPrice = tax.multiply(lowRate).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        double computePrice = big.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        if (computePrice < lowPrice)
            return lowPrice;
        else {
            return computePrice;
        }
    }

    public static Double getPrice(Map carInfo, String carPriceType) {
        Map enquiry = new HashMap();
        enquiry.put("carInfo", carInfo);
        enquiry.put("carPriceType", carPriceType);
        return getPrice(enquiry);
    }

    public static Double getPrice(Map enquiry) {
        Map carInfo = (Map) enquiry.get("carInfo");
        Double price;
        String carPriceType = enquiry.containsKey("carPriceType") ? (String) enquiry.get("carPriceType") : "不含税价";
        if (DataUtil.containsKey(enquiry, "carInfo.carPriceType") && (Integer.parseInt(carInfo.get("carPriceType").toString())) == 2) {
            price = Double.parseDouble(carInfo.get("definedCarPrice").toString());
        } else {
            switch (carPriceType) {
                case "不含税价":
                    price = Double.parseDouble(carInfo.get("price").toString());
                    break;
                case "不含税类比价":
                    price = Double.parseDouble(carInfo.get("analogyPrice").toString());
                    break;
                case "含税价":
                    price = Double.parseDouble(carInfo.get("taxPrice").toString());
                    break;
                case "含税类比价":
                    price = Double.parseDouble(carInfo.get("taxAnalogyPrice").toString());
                    break;
                default:
                    price = Double.parseDouble(carInfo.get("price").toString());
                    break;
            }
        }
        return price;
    }

    private static ThreadLocal<DateFormat> dateThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        }
    };

    public static int diffDays4CurrentMonth(String begin, String end) throws ParseException {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        c1.setTime(dateThreadLocal.get().parse(begin));
        c2.setTime(dateThreadLocal.get().parse(end));
        int days = c2.get(Calendar.DAY_OF_MONTH) - c1.get(Calendar.DAY_OF_MONTH);
        return days;
    }

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger("Constant");

    private static Properties props = new Properties();
    private static final int MAX_PROCESS_NUM = 100;

    public static String doMain;
    public static int maxProcessNum;



    @Setter
    public static String APP_ID;
    @Setter
    public static String bucketName;
    @Setter
    public static String cosSecretID;
    @Setter
    public static String cosSecretKey;


    @Setter
    public static String FF_APP_ID = "333309";
    @Setter
    public static String FF_APP_KEY = "ULUbai38NnYCyhXNeRbdofWE8ukCOrtN";
    @Setter
    public static String FF_PD_ID = "133309";
    @Setter
    public static String FF_PD_KEY = "B7YIyxxhC5i2TShXo+3AlmKZUJrVOXqH";
    public static String CJY_DAMA = "超级鹰打码";
    @Setter
    public static String CJY_USERNAME = "chechekeji";
    @Setter
    public static String CJY_PASSWORD = "Cwq4ejW26RCg5Sf";
    @Setter
    public static String CJY_SOFTID = "929083";
    public static String DAMA_MODE = CJY_DAMA;

    public static boolean isEnablePressureTest = false;
    public static JSONObject msgParseRuleJson;
    private String dataTransFormPath;
    public static int defaultConnectTimeout = 90000;
    public static int defaultReadTimeout = 90000;

    private static final String defaultHttpClientSocketTimeoutKey = "defaultHttpClientSocketTimeout";
    public static int defaultHttpClientSocketTimeout = 15000;
    private static final String defaultHttpClientConnectTimeoutKey = "defaultHttpClientConnectTimeout";
    public static int defaultHttpClientConnectTimeout = 15000;
    private static final String defaultHttpClientConnectionRequestTimeoutKey = "defaultHttpClientConnectionRequestTimeout";
    public static int defaultHttpClientConnectionRequestTimeout = 15000;

    private static String getConfig(String key, String defaultValue) {

        if (StringUtils.isEmpty(props.getProperty(key))) {
            return defaultValue;
        } else {
            return props.getProperty(key);
        }
    }

    public void init() {
        try {
            logger.info("开始初始化Constant");
            props = new Properties();
            doMain = props.getProperty("domain");
            maxProcessNum = Integer.parseInt(getConfig("system.maxProcessNum", String.valueOf(MAX_PROCESS_NUM)));
            isEnablePressureTest = Boolean.parseBoolean(getConfig("system.isEnablePressureTest", String.valueOf(isEnablePressureTest)));
            defaultConnectTimeout = Integer.parseInt(getConfig("sun.net.client.defaultConnectTimeout", "30000"));
            defaultReadTimeout = Integer.parseInt(getConfig("sun.net.client.defaultReadTimeout", "30000"));
            defaultHttpClientConnectTimeout = Integer.parseInt(getConfig(defaultHttpClientConnectTimeoutKey, "15000"));
            defaultHttpClientConnectionRequestTimeout = Integer.parseInt(getConfig(defaultHttpClientConnectionRequestTimeoutKey, "15000"));
            defaultHttpClientSocketTimeout = Integer.parseInt(getConfig(defaultHttpClientSocketTimeoutKey, "15000"));

        } catch (Exception e) {
            logger.error("配置读取异常：", e);
        }
        try {
            logger.info("载入数据转换文件...");
            msgParseRuleJson = load(dataTransFormPath);
        } catch (Exception e) {
            logger.error("msgParseRule.json文件初始化报错：", e);
        }
    }

    /**
     * 刷新配置文件
     */
    public void refresh() {
        logger.info("...刷新系统配置文件...");
        init();
    }

    public static JSONObject load(final String file) {
        logger.info("加载系统配置文件->{}", file);
        final String str = FileUtil.getResourceFromStream(file, "utf-8");
        logger.info("加载Json 配置文件成功");
        return JSON.parseObject(str);
    }

    public void setDataTransFormPath(String dataTransFormPath) {
        this.dataTransFormPath = dataTransFormPath;
    }

}
