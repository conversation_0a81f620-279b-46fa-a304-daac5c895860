package com.cheche365.bc.utils.encrypt.SM4;

/**
 * 常量类
 */
public class Constant {
    /**
     * sm4加解密相关
     */
    public static final int ZERO = 0;
    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int EIGHT = 8;
    public static final int TEN = 10;
    public static final int TWELVE = 12;
    public static final int THIRTEEN = 13;
    public static final int SIXTEEN = 16;
    public static final int EIGHTEEN = 18;
    public static final int TWO_THREE = 23;
    public static final int TWO_FOUR = 24;
    public static final int THREE_ONE = 31;
    public static final int THREE_TWO = 32;
    public static final int THREE_THREE = 33;
    public static final int THREE_FOUR = 34;
    public static final int THREE_FIVE = 35;
    public static final int THREE_SIX = 36;
}
