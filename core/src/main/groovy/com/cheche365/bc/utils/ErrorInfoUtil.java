package com.cheche365.bc.utils;

import com.google.common.collect.Maps;

import java.util.Map;

import static com.cheche365.bc.task.ErrorInfoKeys.ERROR_CODE;
import static com.cheche365.bc.task.ErrorInfoKeys.ERROR_DESC;

/**
 *
 */
public class ErrorInfoUtil {

    public static Map<String, Object> build(Integer errorCode, String errorDesc) {
        Map<String, Object> errorInfo = Maps.newHashMapWithExpectedSize(2);
        errorInfo.put(ERROR_CODE, errorCode);
        errorInfo.put(ERROR_DESC, errorDesc);
        return errorInfo;
    }

}
