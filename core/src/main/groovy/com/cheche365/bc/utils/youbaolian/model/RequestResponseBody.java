// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) fieldsfirst ansi 
// Source File Name:   RequestResponseBody.java

package com.cheche365.bc.utils.youbaolian.model;

import java.io.Serializable;
import java.util.Objects;


public class RequestResponseBody implements Serializable {

    private String body;
    private Head head;

    public RequestResponseBody() {
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Head getHead() {
        return head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof RequestResponseBody)) {
            return false;
        } else {
            RequestResponseBody that = (RequestResponseBody) o;
            return Objects.equals(getBody(), that.getBody()) && Objects.equals(getHead(), that.getHead());
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{
                getBody(), getHead()
        });
    }

    public String toString() {
        return (new StringBuilder()).append("RequestResponseBody{body='").append(body).append('\'').append(", head=").append(head).append('}').toString();
    }
}
