package com.cheche365.bc.utils.dama;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.utils.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.Base64.Encoder;


@Slf4j
public class TuJian implements DamaHandler {

    private static final String API_URL = "http://api.ttshitu.com/predict";
    private static final String USERNAME = "chechekeji";
    private static final String PASSWORD = "Vosbj#5zkKjh";
    private int sequence;

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public int getSequence() {
        return sequence;
    }

    public TuJian(int sequence) {
        this.sequence = sequence;
    }

    public static String getImageStr(String imgFile) {
        /**
         * @Description: 根据图片地址转换为base64编码字符串
         * @return String
         */
        FileInputStream inputStream = null;
        byte[] data = null;
        try {
            inputStream = new FileInputStream(imgFile);
            data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Encoder encoder = Base64.getEncoder();
        String encodedata = encoder.encodeToString(data);
        String encode = "";
        try {
            encode = URLEncoder.encode(encodedata, "UTF-8");
        } catch (Exception e) {
            System.out.println("转码异常!" + e);
            e.printStackTrace();
        }
        return encode;
    }

    public static String getAuth(String captchaData, String type) {

        JSONObject param = new JSONObject();
        param.put("username", USERNAME);
        param.put("password", PASSWORD);
        param.put("image", captchaData);
        param.put("typeid", type);
        PrintWriter out = null;
        BufferedReader in = null;
        String result = null;
        String line;
        StringBuffer sb = new StringBuffer();
        try {
            URL realUrl = new URL(API_URL);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性 设置请求格式
            conn.setRequestProperty("contentType", "UTF-8");
            conn.setRequestProperty("content-type", "application/json");
            //设置超时时间
            conn.setConnectTimeout(60 * 1000);
            conn.setReadTimeout(60 * 1000);

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), "UTF-8"));
            // 发送请求参数
            out.print(param.toJSONString());
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应    设置接收格式
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            String response = sb.toString();
            log.info("图鉴打码返回: {}", response);
            JSONObject obj = JSON.parseObject(response);
            if ("0".equals(obj.getString("code"))) {
                result = obj.getJSONObject("data").getString("result");
            } else {
                log.error("图鉴打码返回错误: {}", response);
            }
        } catch (Exception e) {
            log.error("图鉴打码失败: {}", Util.getStackTrace(e));
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    @Override
    public String execute(String base64, String type) {
        try {
            String auth = getAuth(base64, type);
            if (StringUtils.isNotBlank(auth))
                return auth;
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
