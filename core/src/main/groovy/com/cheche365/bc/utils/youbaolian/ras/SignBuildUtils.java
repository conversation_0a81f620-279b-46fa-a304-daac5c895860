package com.cheche365.bc.utils.youbaolian.ras;

import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.youbaolian.model.Head;
import com.cheche365.bc.utils.youbaolian.model.RequestResponseBody;

import java.util.*;

public class SignBuildUtils {

    public static String PARTNERNO = "partnerNo";
    public static String REQUESTID = "requestId";
    public static String TIMESTAMP = "timestamp";
    public static String VERSION = "version";
    public static String SIGNKEY = "signKey";
    public static String BODY = "body";

    public static String signBuild(String input, int length) {
        String sign = null;
        if (length == 0)
            sign = Hex.encodeToString(input);
        else
            sign = Hex.encodeToString(input).substring(0, length);
        return sign;
    }

    public static String sign(RequestResponseBody responseBody, String signKey) {
        Map params = new HashMap();
        params.put(PARTNERNO, responseBody.getHead().getPartnerNo());
        params.put(REQUESTID, responseBody.getHead().getRequestId());
        params.put(TIMESTAMP, responseBody.getHead().getTimestamp());
        params.put(VERSION, responseBody.getHead().getVersion());
        params.put(SIGNKEY, signKey);
        params.put(BODY, responseBody.getBody());
        return signBuild(getSignContent(params), 0);
    }

    public static String sign(Head head, String body, String signKey) {
        Map params = new HashMap();
        params.put(PARTNERNO, head.getPartnerNo());
        params.put(REQUESTID, head.getRequestId());
        params.put(TIMESTAMP, head.getTimestamp());
        params.put(VERSION, head.getVersion());
        params.put(SIGNKEY, signKey);
        params.put(BODY, body);
        return signBuild(getSignContent(params), 0);
    }

    public static String getSignContent(Map sortedParam) {
        LinkedHashMap map = new LinkedHashMap();
        List keys = new ArrayList(sortedParam.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = (String) keys.get(i);
            String value = sortedParam.get(key).toString();
            map.put(key, value);
        }

        return map.toString();
    }
}
