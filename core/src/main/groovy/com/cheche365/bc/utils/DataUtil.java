package com.cheche365.bc.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cheche365.bc.tools.MapUtil;
import com.cheche365.bc.utils.dama.Constant;
import com.google.common.base.Strings;
import groovy.util.XmlSlurper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Component;
import org.xml.sax.SAXParseException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 报文处理工具
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DataUtil {

    private static final Pattern REG_PATTERN = Pattern.compile("[(\\w+=\\w+&)|(\\w+=[\\u4e00-\\u9fa5]+&)]+");

    /**
     * 解析返回报文(Json,Xml)
     *
     * @param data
     * @return
     */
    public static Object parse(String data) throws Exception {
        if (data == null) {
            return null;
        }
        Object obj = null;
        try {
            final String dataTrim = data.trim();
            if (dataTrim.contains("<html") || dataTrim.contains("<HTML")) {
                obj = Jsoup.parse(dataTrim);
            } else if (dataTrim.startsWith("<")) {
                try {
                    obj = new XmlSlurper().parseText(dataTrim);
                } catch (SAXParseException e) {
                    obj = Jsoup.parse(dataTrim);
                }
            } else if (dataTrim.startsWith("{")) {
                obj = JSON.parseObject(dataTrim);
            } else if (dataTrim.contains("=") && REG_PATTERN.matcher(dataTrim).find()) {
                if (dataTrim.startsWith("[")) {
                    obj = dataTrim;
                } else {
                    String[] fields = dataTrim.split("&");
                    for (String field : fields) {
                        if (obj == null) {
                            obj = new HashMap<String, Object>();
                        }
                        if (field.startsWith("<")) {
                            ((Map) obj).put("request", new XmlSlurper().parseText(field));
                        } else if (field.startsWith("{")) {
                            ((Map) obj).put("request", JSON.parseObject(field));
                        } else {
                            ((Map) obj).put(field.split("=")[0], field.split("=")[1]);
                        }
                    }
                }
            } else {
                obj = data;
            }
        } catch (Exception e) {
            log.error("返回报文解析数据异常", e);
            return data;
        }
        return obj;
    }

    /**
     * 按路径取Json
     *
     * @param path
     * @param object
     * @return Map, List
     */
    public static Object get(String path, Object object) {
        if (object instanceof String) {
            object = JSONObject.parseObject(object.toString());
        } else if (object instanceof Map) {
            return getValue(path, (Map) object);
        }
        for (String node : path.split("\\.")) {
            if (object instanceof JSONArray) {
                break;
            }
            for (Map.Entry<String, Object> param : ((JSONObject) object).entrySet()) {
                if (param.getKey().equals(node)) {
                    object = param.getValue();
                    break;
                }
            }
        }
        if (object instanceof JSONObject) {
            object = toMap((JSONObject) object);
        } else if (object instanceof JSONArray) {
            object = toList((JSONArray) object);
        }
        return object;
    }

    public static Boolean containsKey(Map data, String keyPath) {
        if (data == null || data.isEmpty() || StringUtils.isEmpty(keyPath)) {
            return false;
        }
        if (!keyPath.contains(".")) {
            return data.containsKey(keyPath);
        } else {
            String[] keys = keyPath.split("\\.");
            if (data.get(keys[0]) instanceof Map) {
                return containsKey((Map) data.get(keys[0]), keyPath.replace(keys[0] + ".", ""));
            } else if (data.get(keys[0]) instanceof JSONObject) {
                return containsKey((JSONObject) data.get(keys[0]), keyPath.replace(keys[0] + ".", ""));
            }
        }
        return false;
    }

    public static Boolean isNotEmptyValue(Map data, String keyPath) {
        Object value = get(keyPath, data);
        return value != null;
    }

    public static Boolean containsKey(JSONObject data, String keyPath) {
        return containsKey(JSONObject.toJavaObject(data, Map.class), keyPath);
    }

    public static Boolean containsKeySet(Map data, List<String> keyPaths) {
        for (String keyPath : keyPaths) {
            if (!containsKey(data, keyPath)) {
                return false;
            }
        }
        return true;
    }

    public static Boolean containsKeySet(JSONObject data, List<String> keyPaths) {
        return containsKeySet(JSONObject.toJavaObject(data, Map.class), keyPaths);
    }

    /*
     * conditions key ,中文备注  ["userName@用户名","password@密码","agreeId@协议号"]
     *
     * */
    public static void checkData(Map data, List<String> conditions) throws Exception {
        if (data == null || data.isEmpty()) {
            throw new Exception("参数配置集不能为空！");
        }
        StringBuffer sb = new StringBuffer();
        for (String condition : conditions) {
            if (StringUtils.isEmpty(condition)) {
                throw new IllegalArgumentException("非法的传入参数");
            }
            String[] keyDesc = condition.split("@");
            if (keyDesc.length == 1 || keyDesc.length > 2) {
                throw new Exception(String.format("传入的参数个数与约定的不匹配,参数项:%s", keyDesc[0]));
            }
            if (StringUtils.isNotEmpty(keyDesc[0]) && !containsKey(data, keyDesc[0])) {
                sb.append(String.format("配置参数项 %s(%s) 不能为空;", keyDesc[0], keyDesc[1]));
            }
        }
        if (sb.length() > 0) {
            throw new Exception(sb.toString());
        }
    }


    public static Object getValue(String path, Map map) {
        if (StringUtils.isEmpty(path) || map == null || map.isEmpty()) {
            return null;
        }
        if (!path.contains(".")) {
            if (path.contains("[")) {
                String key = path.contains("[") ? path.substring(0, path.indexOf("[")) : path;
                int index = Integer.parseInt(path.substring(path.indexOf("[") + 1, path.indexOf("]")));
                if (null == map.get(key) || ((List) map.get(key)).size() - 1 < index)
                    return null;
                if (map.containsKey(key)) {
                    return ((List) (map.get(key))).get(index);
                } else {
                    return null;
                }
            }
            if (map.containsKey(path)) {
                return map.get(path);
            } else {
                return null;
            }
        }
        String[] paths = path.split("\\.");
        String key = paths[0].contains("[") ? paths[0].substring(0, paths[0].indexOf("[")) : paths[0];
        if (!map.containsKey(key)) {
            return null;
        }
        if (paths[0].contains("[")) {
            int index = Integer.parseInt(paths[0].substring(paths[0].indexOf("[") + 1, paths[0].indexOf("]")));
            if (null == map.get(key) || ((List) map.get(key)).size() - 1 < index)
                return null;
            return getValue(path.replace(paths[0] + ".", ""), (Map) (((List) (map.get(key))).get(index)));
        } else {
            return getValue(path.replace(paths[0] + ".", ""), (Map) (map.get(key)));
        }
    }

    /**
     * JSONObject转换成Map
     *
     * @param jsonObject
     * @return
     */
    public static Map<String, Object> toMap(JSONObject jsonObject) {
        Map<String, Object> map = new HashMap<String, Object>();
        for (Map.Entry<String, Object> param : jsonObject.entrySet()) {
            if (param.getValue() instanceof JSONObject) {
                map.put(param.getKey(), toMap((JSONObject) param.getValue()));
            } else if (param.getValue() instanceof JSONArray) {
                map.put(param.getKey(), toList((JSONArray) param.getValue()));
            } else {
                map.put(param.getKey(), JSONObject.toJSON(param.getValue()));
            }
        }
        return map;
    }

    /*删除指定的key*/
    public static void removeContainsKey(Map data, String keyPath) {
        if (data == null || data.isEmpty() || StringUtils.isEmpty(keyPath)) {
            return;
        }
        if (!keyPath.contains(".")) {
            data.remove(keyPath);
        } else {
            String[] keys = keyPath.split("\\.");
            if (data.get(keys[0]) instanceof Map) {
                removeContainsKey((Map) data.get(keys[0]), keyPath.replace(keys[0] + ".", ""));
            } else if (data.get(keys[0]) instanceof JSONObject) {
                removeContainsKey((JSONObject) data.get(keys[0]), keyPath.replace(keys[0] + ".", ""));
            }
        }
    }

    public static void removeContainsKey(JSONObject data, String keyPath) {
        removeContainsKey(JSON.toJavaObject(data, Map.class), keyPath);
    }

    /*判断map是否为null或者没有key值*/
    public static boolean isEmpty(final Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * JSONArray转换成List
     *
     * @param jsonArray
     * @return
     */
    public static List<Object> toList(JSONArray jsonArray) {
        List<Object> list = new ArrayList<Object>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Object object = jsonArray.get(i);
            if (object instanceof JSONObject) {
                object = toMap((JSONObject) object);
            } else if (object instanceof JSONArray) {
                object = toList((JSONArray) object);
            } else {
                object = JSONObject.toJSONString(object, SerializerFeature.WriteClassName);
            }
            list.add(object);
        }
        return list;
    }

    /**
     * 将报文转换为EDI的报文格式
     * 该函数根据替换规则msgParseRuleJson仅对要转换的报文做键名的替换（仅对enquiry键值做转化且会根据情况添加和删去enquiry键）
     * 替换规则由文件msgParseRule.json生成
     *
     * @param msgBody 要转换的报文
     * @return
     */
    public static Map parseInEdi(String msgBody) {
        if (msgBody == null) {
            return null;
        }
        StringBuffer msgBodySB = new StringBuffer(msgBody);

        //从掌中宝json转换为edi json
        String replaceOldKey = "key";
        String replaceNewKey = "keyEdi";

        JSONArray ruleArray = Constant.msgParseRuleJson.getJSONArray("keyReplaceRule");
        //
        for (int i = 0; i < ruleArray.size(); i++) {
            //获取规则
            JSONObject tmpRule = ruleArray.getJSONObject(i);
            //替换key值
            int fromIndex = 0;
            String oldKeyStr = "\"" + tmpRule.getString(replaceOldKey) + "\"";
            String newKeyStr = "\"" + tmpRule.getString(replaceNewKey) + "\"";
            while (msgBodySB.indexOf(oldKeyStr, fromIndex) != -1) {
                fromIndex = msgBodySB.indexOf(oldKeyStr, fromIndex);
                msgBodySB.replace(fromIndex, fromIndex + oldKeyStr.length(),
                        newKeyStr);
            }

        }
        return JSON.parseObject(msgBodySB.toString(), Map.class);
    }


    /**
     * 转换EDI报文前特殊规则预处理
     *
     * @param msgBody
     * @return
     */
    public static Map preParseInEdi(Map msgBody) {
        //从掌中宝json转换为edi json
        String key = "key";
        String keyEdi = "keyEdi";

        JSONArray ruleArray = Constant.msgParseRuleJson.getJSONArray("keySpecialRule");
        for (int i = 0; i < ruleArray.size(); i++) {
            //获取规则
            JSONObject tmpRule = ruleArray.getJSONObject(i);

            if ("listToObject".equals(tmpRule.get("changeTypeToEdi"))) {
                if (msgBody.get(tmpRule.get("key")) instanceof JSONArray) {
                    JSONArray array = (JSONArray) msgBody.get(tmpRule.get("key"));

                    //对特殊规则 由list到object进行预处理
                    //处理规则为 默认取list的第一个object
                    if (CollUtil.isNotEmpty(array)) {
                        //抹掉原来的list
                        msgBody.remove(tmpRule.get(key));

                        String listItemKey = (String) tmpRule.get("listItemKey");
                        if (!StringUtils.isEmpty(listItemKey)) {
                            //加入object
                            msgBody.put(tmpRule.get(keyEdi), ((JSONObject) (array.get(0))).get(listItemKey));

                        } else {
                            //加入object
                            msgBody.put(tmpRule.get(keyEdi), (array.get(0)));

                        }
                    }
                }

            }

            if ("stringToMap".equals(tmpRule.get("changeTypeToEdi"))) {
                //将String转化为Map
                String[] keyArray = ((String) tmpRule.get("key")).split("\\.");
                if (keyArray.length > 1) {
                    //层次不止一层
                    Object tmpValue = msgBody;
                    for (String tmpKey : keyArray) {
                        tmpValue = ((Map) tmpValue).get(tmpKey);
                        if (tmpValue == null) {
                            break;
                        }

                    }

                    if (tmpValue instanceof String) {
                        //只处理这种情况了(层级为2)
                        ((Map) msgBody.get(keyArray[0])).put(keyArray[1], JSON.parseObject((String) tmpValue, Map.class));

                    }

                }


            }

            if ("renewalKeyChange".equals(tmpRule.get("changeTypeToEdi"))) {
                //按照规则，将续保条件转换为专用数据
                if (((String) tmpRule.get("key")).split("\\.").length > 1) {
                    String[] keyArray = ((String) tmpRule.get("key")).split("\\.");
                    //层次不止一层
                    Object tmpValue = msgBody;
                    //renewalquoteitem的数据结构相对固定
                    for (String tmpKey : keyArray) {
                        if (tmpValue instanceof JSONArray) {
                            for (int i1 = 0; i1 < ((JSONArray) tmpValue).size(); i1++) {
                                if (((JSONObject) ((JSONArray) tmpValue).get(i1)).get("itemcode").equals(tmpKey)) {
                                    tmpValue = ((JSONObject) ((JSONArray) tmpValue).get(i1)).get("itemvalue");
                                    break;
                                }
                            }

                        } else if (tmpValue instanceof Map) {
                            tmpValue = ((Map) tmpValue).get(tmpKey);
                        } else if (tmpValue == null) {
                            break;
                        }

                    }
                    //取到值后赋值，tmpValue这个instanceof需要根据itemvalue来设
                    if (((String) tmpRule.get("keyEdi")).split("\\.").length > 1 && tmpValue instanceof String) {
                        String[] keyEdiArray = ((String) tmpRule.get("keyEdi")).split("\\.");
//                        Object[] tmpNodeObj = new Object[10];
                        //层次不止一层
                        Object tmpName = msgBody;
                        for (int ediI = 0; ediI < keyEdiArray.length - 1; ediI++) {
                            String ediIKey = "";
                            if (keyEdiArray[ediI].contains("[")) {
                                ediIKey = keyEdiArray[ediI].substring(0, keyEdiArray[ediI].indexOf("["));
                            } else {
                                ediIKey = keyEdiArray[ediI];
                            }

                            if (tmpName instanceof Map) {
                                //这有东西要处理
                                tmpName = ((Map) tmpName).get(ediIKey);
//                                tmpNodeObj[ediI] = tmpName;
                            } else if (tmpName instanceof JSONArray) {
                                if (((JSONArray) tmpName).size() > 0) {
                                    //直接取第一个了
                                    if (((JSONObject) ((JSONArray) tmpName).get(0)).containsKey(ediIKey)) {
                                        tmpName = ((JSONObject) ((JSONArray) tmpName).get(0));
//                                        tmpNodeObj[ediI] = tmpName;
                                    } else {
                                        //没有那个值，其实这个设值是一个异常
                                        tmpName = "-1";
                                        break;
                                    }
                                }

                            } else if (tmpName instanceof JSONObject) {
                                //理论上来讲，到了这里就可以停了，接下去取值的话，不可能得到一个可以设值的对象了，其实这个设值是一个异常
                                tmpName = "-1";
                                break;
                            }
                        }

                        if (tmpName instanceof JSONObject) {
                            ((JSONObject) tmpName).put(keyEdiArray[keyEdiArray.length - 1], tmpValue);
                        } else if (tmpName instanceof JSONArray) {
                            if (((JSONArray) tmpName).size() > 0) {
                                ((JSONObject) ((JSONArray) tmpName).get(0)).put(keyEdiArray[keyEdiArray.length - 1], tmpValue);
                            } else {
                                JSONObject item = new JSONObject();
                                item.put(keyEdiArray[keyEdiArray.length - 1], tmpValue);
                                ((JSONArray) tmpName).add(0, item);
                            }
                        }
                    }
                }
            }
        }

        return msgBody;
    }

    /**
     * 命名转换
     *
     * @param msgBodyStr
     * @return
     */
    public static Map parseInEdiInterface(String msgBodyStr) {
        //处理列表类型问题
        Map msgBody = JSON.parseObject(msgBodyStr, Map.class);
        return parseInEdi(JSONObject.toJSONString(preParseInEdi(msgBody)));
    }

    public static void supplyParamConversion(Map dataSource) throws Exception {
        String taskId = (String) get("businessId", dataSource);
        log.info("{}开始填写补充数据项", taskId);
        Map<String, Object> insuredPerson;
        Map<String, Object> insurePerson;
        Map<String, Object> carOwnerInfo;
        List<Map<String, String>> supplyParams = (List<Map<String, String>>) getValue("supplyParam", dataSource);
        if (supplyParams == null || supplyParams.isEmpty()) {
            log.warn("{}:补充数据项内容为空!", taskId);
        } else {
            //被投保人
            try {
                insuredPerson = (Map) getValue("insuredPersonInfoList[0]", dataSource);
            } catch (Exception e) {
                insuredPerson = new HashMap<>();
            }
            try {
                insurePerson = (Map) getValue("applicantPersonInfo", dataSource);
            } catch (Exception e) {
                insurePerson = new HashMap<>();
            }
            try {
                carOwnerInfo = (Map) getValue("carOwnerInfo", dataSource);
            } catch (Exception e) {
                carOwnerInfo = new HashMap<>();
            }
            Map<String, String> tempMap = new HashMap<>();
            supplyParams.stream()
                .filter(param -> StringUtils.isNotEmpty(param.get("itemvalue")) && StringUtils.isNotEmpty(param.get("itemcode")))
                .filter(param -> !param.get("itemvalue").contains("null"))
                .forEach(param -> tempMap.put(param.get("itemcode"), param.get("itemvalue")));
        MapUtil.putMap(dataSource, "misc/supplyParam", tempMap);


            log.info("{}需要补充的数据项目为{}", taskId, JSON.toJSONString(tempMap));
            //手机号录入逻辑
            if (tempMap.containsKey(SupplyParamKey.INSURED_MOBILE)) {
                String tmpStr = tempMap.get(SupplyParamKey.INSURED_MOBILE);
                log.info("{}补充被保人数据:{}", taskId, tmpStr);
                insuredPerson.put("mobile", tmpStr);
            }
            String appMobile = tempMap.get(SupplyParamKey.APPLICANT_MOBILE);
            String isdMobile = tempMap.get(SupplyParamKey.INSURED_MOBILE);
            if (!Strings.isNullOrEmpty(appMobile) && !Strings.isNullOrEmpty(isdMobile)) {
                //当前端传两个及以上手机号码（投保人及被保人）时
                insuredPerson.put("mobile", isdMobile);
                insurePerson.put("mobile", appMobile);
                carOwnerInfo.put("mobile", appMobile);
            } else if (!Strings.isNullOrEmpty(appMobile)) {
                //当前端只传一个手机号码（投保人或被保人）时
                insuredPerson.put("mobile", appMobile);
                insurePerson.put("mobile", appMobile);
                carOwnerInfo.put("mobile", appMobile);
            } else if (!Strings.isNullOrEmpty(isdMobile)) {
                //当前端只传一个手机号码（投保人或被保人）时
                insuredPerson.put("mobile", isdMobile);
                insurePerson.put("mobile", isdMobile);
                carOwnerInfo.put("mobile", isdMobile);
            }
            if (tempMap.containsKey(SupplyParamKey.APPLICANT_MOBILE)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/mobile", tempMap.get(SupplyParamKey.APPLICANT_MOBILE));
            }
            if (tempMap.containsKey(SupplyParamKey.OWNER_MOBILE)) {
                MapUtil.putMap(dataSource, "carOwnerInfo/mobile", tempMap.get(SupplyParamKey.OWNER_MOBILE));
            }
            //邮箱录入逻辑
            if (tempMap.containsKey(SupplyParamKey.INSURED_EMAIL) && tempMap.containsKey(SupplyParamKey.APPLICANT_EMAIL)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/email", tempMap.get(SupplyParamKey.APPLICANT_EMAIL));
                insuredPerson.put("email", tempMap.get(SupplyParamKey.INSURED_EMAIL));
                if (tempMap.containsKey(SupplyParamKey.OWNER_EMAIL)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.INSURED_EMAIL));
                }
            } else if (tempMap.containsKey(SupplyParamKey.INSURED_EMAIL)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/email", tempMap.get(SupplyParamKey.INSURED_EMAIL));
                insuredPerson.put("email", tempMap.get(SupplyParamKey.INSURED_EMAIL));
                if (tempMap.containsKey(SupplyParamKey.OWNER_EMAIL)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.INSURED_EMAIL));
                }
            } else if (tempMap.containsKey(SupplyParamKey.APPLICANT_EMAIL)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/email", tempMap.get(SupplyParamKey.APPLICANT_EMAIL));
                insuredPerson.put("email", tempMap.get(SupplyParamKey.APPLICANT_EMAIL));
                if (tempMap.containsKey(SupplyParamKey.OWNER_EMAIL)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.APPLICANT_EMAIL));
                }
            } else if (tempMap.containsKey(SupplyParamKey.OWNER_EMAIL)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
                insuredPerson.put("email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
                MapUtil.putMap(dataSource, "carOwnerInfo/email", tempMap.get(SupplyParamKey.OWNER_EMAIL));
            }
            //地址录入逻辑
            if (tempMap.containsKey(SupplyParamKey.INSURED_ADDRESS) && tempMap.containsKey(SupplyParamKey.APPLICANT_ADDRESS)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/address", tempMap.get(SupplyParamKey.APPLICANT_ADDRESS));
                insuredPerson.put("address", tempMap.get(SupplyParamKey.INSURED_ADDRESS));
                if (tempMap.containsKey(SupplyParamKey.OWNER_ADDRESS)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.INSURED_ADDRESS));
                }
            } else if (tempMap.containsKey(SupplyParamKey.INSURED_ADDRESS)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/address", tempMap.get(SupplyParamKey.INSURED_ADDRESS));
                insuredPerson.put("address", tempMap.get(SupplyParamKey.INSURED_ADDRESS));
                if (tempMap.containsKey(SupplyParamKey.OWNER_ADDRESS)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.INSURED_ADDRESS));
                }
            } else if (tempMap.containsKey(SupplyParamKey.APPLICANT_ADDRESS)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/address", tempMap.get(SupplyParamKey.APPLICANT_ADDRESS));
                insuredPerson.put("address", tempMap.get(SupplyParamKey.APPLICANT_ADDRESS));
                if (tempMap.containsKey(SupplyParamKey.OWNER_ADDRESS)) {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
                } else {
                    MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.APPLICANT_ADDRESS));
                }
            } else if (tempMap.containsKey(SupplyParamKey.OWNER_ADDRESS)) {
                MapUtil.putMap(dataSource, "applicantPersonInfo/address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
                insuredPerson.put("address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
                MapUtil.putMap(dataSource, "carOwnerInfo/address", tempMap.get(SupplyParamKey.OWNER_ADDRESS));
            }
            //权益索赔人录入逻辑
            Object beneficiaryPersons = get("beneficiaryPersonList", dataSource);
            Map beneficiary;
            if (beneficiaryPersons != null && ((List) beneficiaryPersons).size() > 0) {
                beneficiary = (Map) ((List) beneficiaryPersons).get(0);
            } else {
                beneficiary = new HashMap();
                List<Map> bList = new ArrayList<>();
                dataSource.put("beneficiaryPersonList", bList);
                bList.add(beneficiary);
            }
            if (tempMap.containsKey(SupplyParamKey.CLAIMANT_NAME)) {
                beneficiary.put("name", tempMap.get(SupplyParamKey.CLAIMANT_NAME));
            }
            //权益索赔人证件类型
            if (tempMap.containsKey(SupplyParamKey.CLAIMANT_DOCUMENT_TYPE)) {
                beneficiary.put("idCardType", tempMap.get(SupplyParamKey.CLAIMANT_DOCUMENT_TYPE));
            }
            //权益索赔人证件号码
            if (tempMap.containsKey(SupplyParamKey.CLAIMANT_DOCUMENT_NUMBER)) {
                beneficiary.put("idCard", tempMap.get(SupplyParamKey.CLAIMANT_DOCUMENT_NUMBER));
            }
            //权益索赔人手机号码
            if (tempMap.containsKey(SupplyParamKey.CLAIMANT_MOBILE)) {
                beneficiary.put("mobile", tempMap.get(SupplyParamKey.CLAIMANT_MOBILE));
                beneficiary.put("phone", tempMap.get(SupplyParamKey.CLAIMANT_MOBILE));
            }
            //权益索赔人邮箱
            if (tempMap.containsKey(SupplyParamKey.CLAIMANT_EMAIL)) {
                beneficiary.put("email", tempMap.get(SupplyParamKey.CLAIMANT_EMAIL));
            }
            //身份证有效止期录入2017/11/21
            if (tempMap.containsKey(SupplyParamKey.APPLICANT_VALID_DATE)) {
                insurePerson.put("validDate", tempMap.get(SupplyParamKey.APPLICANT_VALID_DATE));
            }
            if (tempMap.containsKey(SupplyParamKey.INSURED_VALID_DATE)) {
                insuredPerson.put("validDate", tempMap.get(SupplyParamKey.INSURED_VALID_DATE));
            }
            if (tempMap.containsKey(SupplyParamKey.OWNER_VALID_DATE)) {
                carOwnerInfo.put("validDate", tempMap.get(SupplyParamKey.OWNER_VALID_DATE));
            }
            if (tempMap.containsKey(SupplyParamKey.APPLICANT_REG_DATE)) {
                insurePerson.put("regDate", tempMap.get(SupplyParamKey.APPLICANT_REG_DATE));
            }
            if (tempMap.containsKey(SupplyParamKey.INSURED_REG_DATE)) {
                insuredPerson.put("regDate", tempMap.get(SupplyParamKey.INSURED_REG_DATE));
            }
            if (tempMap.containsKey(SupplyParamKey.OWNER_REG_DATE)) {
                carOwnerInfo.put("regDate", tempMap.get(SupplyParamKey.OWNER_REG_DATE));
            }
            //被保人身份证签发机关2017/12/27
            if (tempMap.containsKey(SupplyParamKey.INSURED_ID_CARD_PUBLISH_DEPT)) {
                insuredPerson.put("iDCardIssuingAuthority", tempMap.get(SupplyParamKey.INSURED_ID_CARD_PUBLISH_DEPT));
            }
            //投保人身份证签发机关2017/12/27
            if (tempMap.containsKey(SupplyParamKey.APPLICANT_ID_CARD_PUBLISH_DEPT)) {
                insuredPerson.put("iDCardIssuingAuthority", tempMap.get(SupplyParamKey.APPLICANT_ID_CARD_PUBLISH_DEPT));
            }
            //车主身份证签发机关2017/12/27
            if (tempMap.containsKey(SupplyParamKey.OWNER_ID_CARD_PUBLISH_DEPT)) {
                insuredPerson.put("iDCardIssuingAuthority", tempMap.get(SupplyParamKey.OWNER_ID_CARD_PUBLISH_DEPT));
            }
        }
    }

    final class SupplyParamKey {
        static final String OWNER_MOBILE = "ownerMobile";
        static final String OWNER_EMAIL = "ownerEmail";
        static final String OWNER_ADDRESS = "ownerAddress";
        static final String DRIVING_LICENSE_ADDRESS = "drivingLicenseAddress";
        static final String INSURED_MOBILE = "insuredMobile";
        static final String INSURED_EMAIL = "insuredEmail";
        static final String INSURED_ADDRESS = "insuredAddress";
        static final String APPLICANT_MOBILE = "applicantMobile";
        static final String APPLICANT_EMAIL = "applicantEmail";
        static final String APPLICANT_ADDRESS = "applicantAddress";
        static final String CLAIMANT_NAME = "claimantName";
        static final String CLAIMANT_DOCUMENT_TYPE = "claimantDocumentType";
        static final String CLAIMANT_DOCUMENT_NUMBER = "claimantDocumentNumber";
        static final String CLAIMANT_MOBILE = "claimantMobile";
        static final String CLAIMANT_EMAIL = "claimantEmail";
        /**
         * 投保人身份证有效止期
         */
        static final String APPLICANT_REG_DATE = "applicantIDCardRegistrationDate";
        /**
         * 车主身份证有效止期
         */
        static final String OWNER_REG_DATE = "ownerIDCardRegistrationDate";
        /**
         * 被保人身份证有效止期
         */
        static final String INSURED_REG_DATE = "insuredIDCardRegistrationDate";
        /**
         * 投保人身份证有效止期
         */
        static final String APPLICANT_VALID_DATE = "applicantIDCardValidDate";
        /**
         * 车主身份证有效止期
         */
        static final String OWNER_VALID_DATE = "ownerIDCardValidDate";
        /**
         * 被保人身份证有效止期
         */
        static final String INSURED_VALID_DATE = "insuredIDCardValidDate";
        /**
         * 被保人身份证签发机关
         */
        static final String OWNER_ID_CARD_PUBLISH_DEPT = "ownerIDCardIssuingAuthority";
        /**
         * 被保人身份证签发机关
         */
        static final String APPLICANT_ID_CARD_PUBLISH_DEPT = "applicantIDCardIssuingAuthority";
        /**
         * 被保人身份证签发机关
         */
        static final String INSURED_ID_CARD_PUBLISH_DEPT = "insuredIDCardIssuingAuthority";
    }

    /**
     * 转换掌中宝报文特殊规则预处理
     *
     * @param originalMsgBody
     * @return
     */
    public static Map preParseOutEdi(Map originalMsgBody) throws Exception {
        Map msgBody = (Map) originalMsgBody.get("enquiry");
        if (msgBody == null) {
            log.warn("要转换的EDI报文中缺失enquiry信息");
            return originalMsgBody;
        }
        JSONArray ruleArray = Constant.msgParseRuleJson.getJSONArray("keySpecialRule");
        for (int i = 0; i < ruleArray.size(); i++) {
            //获取规则
            JSONObject tmpRule = ruleArray.getJSONObject(i);
            if ("listToObject".equals(tmpRule.get("changeTypeToEdi"))) {
                if (msgBody.get(tmpRule.get("keyEdi")) != null) {
                    JSONObject keyJsonObj = new JSONObject();
                    String listItemKey = (String) tmpRule.get("listItemKey");
                    if (!StringUtils.isEmpty(listItemKey)) {
                        keyJsonObj.put(listItemKey, msgBody.get(tmpRule.get("keyEdi")));
                    }
                    msgBody.remove(tmpRule.get("keyEdi"));
                    JSONArray array = new JSONArray();
                    array.set(0, keyJsonObj);

                    msgBody.put((String) tmpRule.get("key"), array);
                }
            }

        }

        return msgBody;
    }


    public static Map parseOutEdiInterface(Map msgBodyStr) throws Exception {
        return parseOutEdi(JSONObject.toJSONString(preParseOutEdi(msgBodyStr)));
    }

    /**
     * 将EDI格式报文转化为其他格式
     * 该函数根据替换规则msgParseRuleJson仅对要转换的报文做键名的替换（仅对enquiry键值做转化且会根据情况添加和删去enquiry键）
     * 替换规则由文件msgParseRule.json生成
     *
     * @param msgBody 要转换的报文
     * @return
     */
    public static JSONObject parseOutEdi(String msgBody) throws Exception {
        StringBuffer msgBodySB = new StringBuffer(msgBody);
        //从edi json转换为掌中宝json
        String replaceOldKey = "keyEdi";
        String replaceNewKey = "key";
        msgBodySB = new StringBuffer(msgBody);

        JSONArray ruleArray = Constant.msgParseRuleJson.getJSONArray("keyReplaceRule");
        for (int i = 0; i < ruleArray.size(); i++) {
            //获取规则
            JSONObject tmpRule = ruleArray.getJSONObject(i);
            //替换key值
            int fromIndex = 0;
            String oldKeyStr = "\"" + tmpRule.getString(replaceOldKey) + "\"";
            String newKeyStr = "\"" + tmpRule.getString(replaceNewKey) + "\"";
            while (msgBodySB.indexOf(oldKeyStr, fromIndex) != -1) {
                fromIndex = msgBodySB.indexOf(oldKeyStr, fromIndex);
                msgBodySB.replace(fromIndex, fromIndex + oldKeyStr.length(),
                        newKeyStr);
            }
        }

        return JSON.parseObject(msgBodySB.toString());
    }

}
