package com.cheche365.bc.utils.encrypt.hulianhutong;


import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.encrypt.hulianhutong.vo.EnDecryptVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 地方性系统通用DES加解密工具类
 * 由于非对称加密速度极其缓慢，一般文件不使用它来加密而是使用对称加密，
 * 非对称加密算法可以用来对对称加密的密钥加密，这样保证密钥的安全也就保证了数据的安全
 */
@Slf4j
public class UniversalFundation {


    /**
     * 加密 --使用DES加密数据,DESkey使用RSA加密传输给对方
     *
     * @param enDecryptVo ->必须要含有的DES未加密的KEY，未加密的数据，RSA加密的公钥
     * @return 加密处理好的的对象
     */
    public EnDecryptVo EncryptData(EnDecryptVo enDecryptVo) throws Exception {
        EnDecryptVo decryptVo = new EnDecryptVo();
        BeanUtils.copyProperties(enDecryptVo, decryptVo);
        byte[] keyBytes = new byte[8];
        System.arraycopy(enDecryptVo.getRecord().getBytes(StandardCharsets.UTF_8),0,keyBytes,0,enDecryptVo.getRecord().length());
        AesDesEncryption des = AesDesEncryption.builder()
                .key(new String(new byte[8]))
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .ivKey("12345678")
                .ivKeyFormat(EncryptEnum.IvKeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.DES_CBC_PKCS5Padding)
                .build();
        String encryptRecord = des.encrypt(enDecryptVo.getRecord());
        // encryptRecord-》DES加密后的数据
        decryptVo.setDESRecord(encryptRecord);
        byte[] input = enDecryptVo.getDESPwd().getBytes(StandardCharsets.UTF_8);
        RSA rsa = RSA.builder().publicKey(enDecryptVo.getPubKey())
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA_ECB_PKCS1PADDING).build();
        // encryptPwd-》RSA公钥加密后的DES KEY
        decryptVo.setRSAPwd(rsa.encrypt(input).getBytes());
        return decryptVo;
    }


    /**
     * 解密 --使用RSA私钥解密DES KEY，使用DES对加密数据进行解密
     *
     * @param enDecryptVo ->必须要含有DES被加密的KEY、RSA解密的私钥、加密后的数据
     * @return 解密处理好的的对象
     */
    public EnDecryptVo DecryptData(EnDecryptVo enDecryptVo) throws Exception {
        EnDecryptVo decryptVo = new EnDecryptVo();
        BeanUtils.copyProperties(enDecryptVo, decryptVo);
        byte[] input = enDecryptVo.getRSAPwd();
        RSA rsa = RSA.builder().privateKey(enDecryptVo.getPriKey())
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA_ECB_PKCS1PADDING).build();
        String decryptKeyStr = rsa.decrypt(input);
//      decryptKey-》解密后的DES KEY
        decryptVo.setDESPwd(decryptKeyStr);
        byte[] keyBytes = new byte[8];
        System.arraycopy(decryptKeyStr.getBytes(StandardCharsets.UTF_8),0,keyBytes,0,8);
        AesDesEncryption des = AesDesEncryption.builder()
                .key(new String(keyBytes))
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .ivKey("12345678")
                .ivKeyFormat(EncryptEnum.IvKeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.DES_CBC_PKCS5Padding)
                .build();
        String decryptRecord=des.decrypt(enDecryptVo.getDESRecord());
        // decryptRecord-》DES解密后的明文数据
        decryptVo.setRecord(decryptRecord);
        return decryptVo;
    }
}
