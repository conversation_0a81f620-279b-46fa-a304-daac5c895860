package com.cheche365.bc.utils;

import com.cheche365.bc.cache.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description:
 * @Date: 2021/1/7 14:28
 */
@Component
@Slf4j
public class RedisUtil {

    private static StringRedisTemplate redisTemplate = RedisCache.getStringRedis();

    /**
     * 判断 redis 中是否存在指定的 key
     *
     * @param key 键，不能为null
     * @return true表示存在，false表示不存在
     */
    public static boolean exists(String key) {
        boolean result = false;

        try {
            if (StringUtils.isNotBlank(key)) {
                result = redisTemplate.hasKey(key);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return result;
    }

    /**
     * 从 redis 中获取指定 key 对应的 string 数据
     *
     * @param key 键，不能为null
     * @return key 对应的字符串数据
     */
    public static String get(String key) {
        String t = null;

        try {
            if (StringUtils.isNotBlank(key)) {
                t = redisTemplate.opsForValue().get(key);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return t;
    }

    /**
     * 将指定的 key, value 放到 redis 中，并设置过期时间
     *
     * @param key   键，不能为null
     * @param value 值，不能为null
     * @param time  时间（秒），time要大于0，如果time小于等于0，将设置无限期
     * @return true表示成功，false表示失败
     */
    public static boolean set(String key, String value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 将指定的 key, value 放到 redis 中
     *
     * @param key   键，不能为null
     * @param value 值，不能为null
     * @return true表示成功，false表示失败
     */
    public static boolean set(String key, String value) {
        boolean result = false;

        try {
            if (StringUtils.isNotBlank(key)) {
                redisTemplate.opsForValue().set(key, value);

                result = true;
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return result;
    }
    /**
     * 移除key
     */
    public static boolean delete(String key) {
        boolean result = false;

        try {
            if (StringUtils.isNotBlank(key)) {
                result = redisTemplate.delete(key);
            }
        } catch (Exception e) {
            log.error("移除key：{}，失败：{}", key, ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    /**
     * 获取key剩余TTL
     * @param key
     */
    public static long getKeyExpire(String key) {
        long t = 0;
        try {
            if (StringUtils.isNotBlank(key)) {
                t = redisTemplate.getExpire(key);
            }
        } catch (Exception e) {
            log.error("获取key剩余TTL：{}，失败：{}", key, ExceptionUtils.getStackTrace(e));
        }
        return t;
    }

    public static long getKeyExpire(String key, TimeUnit timeUnit) {
        long t = 0;
        try {
            if (StringUtils.isNotBlank(key)) {
                t = redisTemplate.getExpire(key, timeUnit);
            }
        } catch (Exception e) {
            log.error("获取key剩余TTL：{}，失败：{}", key, ExceptionUtils.getStackTrace(e));
        }
        return t;
    }
    public static void addToSet(String key, String... values) {
        redisTemplate.opsForSet().add(key, values);
    }
    public static Set<String> getMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    public static Boolean isMember(String key, String value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }
    public static Long getSetSize(String key) {
        return redisTemplate.opsForSet().size(key);
    }
}
