package com.cheche365.bc.utils.encrypt.SM4;

import java.util.Base64;


public class Sm4Utils {
    public static String secretKey = "";

    public Sm4Utils() {
    }

    /**
     * 加密
     *
     * @param plainText
     * @return
     */
    public static String encryptDataEcb(String plainText) {
        try {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_ENCRYPT;

            byte[] keyBytes;
            keyBytes = secretKey.getBytes();
            Sm4 sm4 = new Sm4();
            sm4.sm4SetkeyEnc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4CryptEcb(ctx, plainText.getBytes("UTF-8"));
            String cipherText = Base64.getEncoder().encodeToString(encrypted);
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解密
     *
     * @param cipherText
     * @return
     */
    public static String decryptDataEcb(String cipherText) {
        try {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_DECRYPT;

            byte[] keyBytes;
            keyBytes = secretKey.getBytes();
            Sm4 sm4 = new Sm4();
            sm4.sm4SetKeyDec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4CryptEcb(ctx, Base64.getDecoder().decode(cipherText));
            return new String(decrypted, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}