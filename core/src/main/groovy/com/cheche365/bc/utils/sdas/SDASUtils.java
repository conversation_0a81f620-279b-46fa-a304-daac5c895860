package com.cheche365.bc.utils.sdas;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.utils.sender.HttpSender;
import com.cheche365.cheche.signature.APISignature;
import com.cheche365.cheche.signature.Parameters;
import com.cheche365.cheche.signature.Secrets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description:
 * @Date: 2020/12/30 16:30
 */
@Component
@Slf4j
public class SDASUtils {

    //生产
    private static String APP_KEY = "48292168-c59c-4e3d-940a-79b8fbdd0d37";
    private static String SECRET_KEY = "ce051956-a839-451d-ab9c-532735023d1b";
    //生产地址
    private static String SDAS_URL_DEV = "http://sdas.bedrock.chetimes.com/api/assets";
    //签名版本
    private static final String VERSION = "1.0";
    //签名算法
    private static final String SIGNATURE_METHOD = "HMAC-SHA1";
    private static final String NEW_ASSET_SOURCE = "X-CheChe365-NewAssetSource";
    private static final String DERIVED_PRE = "derived;pre";
    private static final Map<String, Object> PERSISTENT_CONFIG_MAP = new HashMap<>();
    @Value("${sdas.url}")
    private String sdasUrl;
    @Value("${sdas.app.key}")
    private String appKey;
    @Value("${sdas.secret.key}")
    private String secretKey;

    @PostConstruct
    public void init() {
        SDAS_URL_DEV = sdasUrl;
        APP_KEY = appKey;
        SECRET_KEY = secretKey;
        System.out.println(SDAS_URL_DEV);
    }

    static {
        PERSISTENT_CONFIG_MAP.put("sourceStorageType", 3);
        PERSISTENT_CONFIG_MAP.put("targetStorageType", 3);
        PERSISTENT_CONFIG_MAP.put("sourceDataType", 0);
        PERSISTENT_CONFIG_MAP.put("targetDataType", 1);
        PERSISTENT_CONFIG_MAP.put("obfuscatedName", false);
    }

    /**
     * 请求前先签名
     *
     * @param requestParamString 请求参数json化
     * @return 签名后 Authorization字段
     */
    public static String preRequestSign(String url, String requestParamString, String requestMethod, String appKey, String secretKey) {
        ApiRequest request = new ApiRequest();
        request.setRequestMethod(requestMethod);
        request.setEntity(requestParamString);
        request.setRequestURL(url);
        Parameters params = new Parameters();
        params.setAppId(appKey);
        params.setSignatureMethod(SIGNATURE_METHOD);
        params.setVersion(VERSION);
        Secrets secrets = new Secrets();
        secrets.setAppSecret(secretKey);
        try {
            //使用工具类签名并将签名相关信息写到request header里
            APISignature.sign(request, params, secrets);
        } catch (Exception e) {
            log.error("访问sdas接口API签名失败, message:{}", ExceptionUtils.getStackTrace(e));
        }
        List<String> header = request.getHeaderValues(Parameters.AUTHORIZATION_HEADER);
        String signString = header.get(0);
        return signString;
    }

    /**
     * 为上传单一资源获取sdas位置
     *
     * @param contentType 资产的内容类型
     * @param name        资产名称
     * @param data        该字段的含义是资产位置 此值由业务方自行保证唯一性
     * @return payload.assets*.id，资产的唯一标识，需要业务方自行保存，以便后续获取资产
     * payload.assets*.location，是一个仅接受HTTP PUT method的临时授权访问URL，用于上传资产的实际内容
     */
    public static JSONObject assetOccupancy(String contentType, String name, String data) {
        JSONObject resultJsonObject = new JSONObject();
        Map<String, Object> params = SDASUtils.makeParams(resultJsonObject, contentType, name, data);
        JSONObject asset = new JSONObject(params);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(asset);
        JSONObject requestParam = new JSONObject();
        requestParam.put("assets", jsonArray);
        String requestParamString = requestParam.toJSONString();
        return SDASUtils.makeHeaders(requestParamString, resultJsonObject);
    }

    /**
     * 为上传多个资源获取sdas位置
     *
     * @param assetList 资源集合
     *                  contentType 资产的内容类型
     *                  name 资产名称
     *                  data 该字段的含义是资产位置 此值由业务方自行保证唯一性
     * @return 多条资源 id location
     */
    public static JSONObject assetOccupancy(List<Map<String, String>> assetList) {
        JSONObject resultJsonObject = new JSONObject();
        if (CollectionUtils.isEmpty(assetList)) {
            JSONArray jsonArray = new JSONArray();
            JSONObject requestParam = new JSONObject();
            assetList.forEach(map -> {
                String contentType = map.get("contentType");
                String name = map.get("name");
                String data = map.get("data");
                Map<String, Object> params = SDASUtils.makeParams(resultJsonObject, contentType, name, data);
                JSONObject asset = new JSONObject(params);
                jsonArray.add(asset);
            });

            requestParam.put("assets", jsonArray);
            String requestParamString = requestParam.toJSONString();
            JSONObject jsonObject = makeHeaders(requestParamString, resultJsonObject);
            return jsonObject;
        }
        resultJsonObject.put("code", "-9999");
        return resultJsonObject;
    }

    private static JSONObject makeHeaders(String requestParamString, JSONObject resultJsonObject) {
        Map<String, String> headers = new HashMap<>();
        String sign = preRequestSign(SDAS_URL_DEV, requestParamString, "POST", APP_KEY, SECRET_KEY);
        headers.put(NEW_ASSET_SOURCE, DERIVED_PRE);
        headers.put("Authorization", sign);
        headers.put("Content-Type", "application/json");
        try {
            String resultString = HttpSender.doPost(SDAS_URL_DEV, requestParamString, null, headers, "UTF-8");
            if (StringUtils.isBlank(resultString)) {
                resultJsonObject.put("msg", "访问sdas的返回结果为空");
                resultJsonObject.put("code", "-9999");
                return resultJsonObject;
            } else {
                JSONObject jsonObject = JSONObject.parseObject(resultString);
                return jsonObject;
            }
        } catch (Exception e) {
            log.error("访问obs占位异常:{}", ExceptionUtils.getStackTrace(e));
        }
        resultJsonObject.put("code", "-9999");
        return resultJsonObject;
    }

    private static Map<String, Object> makeParams(JSONObject resultJsonObject, String contentType, String name, String data) {
        if (StringUtils.isBlank(contentType) || StringUtils.isBlank(name) || StringUtils.isBlank(data)) {
            log.error("访问sdas失败关键参数不全。 contentType:{},name:{},data:{}", contentType, name, data);
            resultJsonObject.put("msg", "访问sdas失败关键参数不全");
            resultJsonObject.put("code", "-9999");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("contentType", contentType);
        params.put("name", name);
        params.put("data", data);
        params.put("persistentConfig", PERSISTENT_CONFIG_MAP);
        return params;
    }


    /**
     * 给sdas上传文件
     *
     * @param name                 文件名
     * @param assetOccupancyResult 首次请求sdas返回结果
     * @param bis                  字节流
     */
    public static JSONObject uploadPutAsset(String name, JSONObject assetOccupancyResult, ByteArrayEntity bis) {
        JSONObject resultJsonObject = new JSONObject();
        if (assetOccupancyResult.getString("payload") == null || StringUtils.isBlank(name)) {
            log.error("访问sdas失败关键参数不全。 assetOccupancyResult:{},name:{}", assetOccupancyResult, name);
            resultJsonObject.put("msg", "访问sdas失败关键参数不全");
            resultJsonObject.put("code", "-9999");
            return resultJsonObject;
        }

        JSONArray assetsJsonArray = assetOccupancyResult.getJSONObject("payload").getJSONArray("assets");
        for (int i = 0; i < assetsJsonArray.size(); i++) {
            JSONObject asset = assetsJsonArray.getJSONObject(i);
            if (name.equals(asset.getString("name"))) {
                String location = asset.getString("location");
                String contentType = asset.getString("contentType");
                String id = asset.getString("id");
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", contentType);
                try {
                    log.info("上传obs的请求参数 url:{},headers:{}", location, headers);
                    HttpSender.doPutRep(location, bis, headers);
                    resultJsonObject.put("id", id);
                    resultJsonObject.put("name", name);
                    return resultJsonObject;
                } catch (Exception e) {
                    log.error("上传obs失败，请求参数 url:{},headers{}，异常信息：{}", location, headers, ExceptionUtils.getStackTrace(e));
                }
            }
        }
        resultJsonObject.put("code", "-9999");
        return resultJsonObject;
    }

    /**
     * PUT请求后   需要使用get请求访问sdas资源id去  获取资源地址
     *
     * @param id 资源id
     * @return
     */
    public static JSONObject getAsset(String id) {
        JSONObject resultJsonObject = new JSONObject();
        String getParam = "";
        String url = SDAS_URL_DEV + "/" + id;
        String sign = SDASUtils.preRequestSign(url, getParam, "GET", APP_KEY, SECRET_KEY);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", sign);
        try {
            Object o = HttpSender.doGet(HttpClients.createDefault(), url, headers, null, "UTF-8", null, false);
            if (o != null) {
                JSONObject assets = JSONObject.parseObject(o.toString());
                if ("0".equals(assets.get("code").toString())) {
                    //只有拿到成功的响应 才返回数据 否则返回错误代码 -9999
                    return JSONObject.parseObject(o.toString());
                }
            }
        } catch (Exception e) {
            log.error("根据资源id:{}, 获取资源url异常：{}", id, ExceptionUtils.getStackTrace(e));
        }
        resultJsonObject.put("code", "-9999");
        return resultJsonObject;
    }

    public static String uploadOBS(String fileName, byte[] bytes, String contentType, String path) {
        JSONObject assetResult = SDASUtils.assetOccupancy(contentType, fileName, path);
        if (Objects.nonNull(assetResult) && assetResult.getInteger("code") == 0) {
            JSONObject uploadResult = SDASUtils.uploadPutAsset(fileName, assetResult, new ByteArrayEntity(bytes));
            if (!"9999".equals(uploadResult.get("code"))) {
                return uploadResult.getString("id");
            }
        }
        return null;
    }
    public static String uploadOBS(String fileName, byte[] bytes, String path) {
        return uploadOBS(fileName, bytes, Constants.PDF_CONTENT_TYPE, path);
    }

    public static String getLocationById(String id) {
        JSONObject jsonObject = getAsset(id);
        if (jsonObject.getInteger("code") == 0) {
            JSONArray jsonArray = jsonObject.getJSONObject("payload").getJSONArray("assets");
            JSONObject json = (JSONObject) jsonArray.get(0);
            return json.getString("location");
        }
        return null;
    }
}
