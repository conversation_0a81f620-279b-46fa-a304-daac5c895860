package com.cheche365.bc.utils;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
/**
 * <AUTHOR> zhangying
 * @date 2022-03-18
 * @descript :sftp连接工具类
 */
@Slf4j
public class SftpUtils {

    // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
    private static final String SESSION_CONFIG_STRICT_HOST_KEY_CHECKING = "StrictHostKeyChecking";

    /**
     * 创建SFTP连接
     *
     * @return
     * @throws Exception
     */
    public ChannelSftp createSftp(String username, String host, String password, int port, int sessionConnectTimeout, String protocol, int channelConnectedTimeout) throws Exception {
        JSch jsch = new JSch();
        log.info("连接sftp->username：{},host：{},pass：{}", username, host, password);
        Session session = createSession(jsch, host, username, port);
        session.setPassword(password);
        session.connect(sessionConnectTimeout);
        log.info("Session 连接 {}.", host);
        Channel channel = session.openChannel(protocol);
        channel.connect(channelConnectedTimeout);
        log.info("Channel 创建 {}.", host);
        return (ChannelSftp) channel;
    }

    /**
     * 创建session
     *
     * @param jsch
     * @param host
     * @param username
     * @param port
     * @return
     * @throws Exception
     */
    public Session createSession(JSch jsch, String host, String username, Integer port) throws Exception {
        Session session = null;
        if (port <= 0) {
            session = jsch.getSession(username, host);
        } else {
            session = jsch.getSession(username, host, port);
        }
        if (session == null) {
            throw new Exception(host + " session is null");
        }
        session.setConfig(SESSION_CONFIG_STRICT_HOST_KEY_CHECKING, "no");
        return session;
    }

    /**
     * 加密秘钥方式登陆
     *
     * @return
     */
    public ChannelSftp connectByKey(String username, String host, int port, int sessionConnectTimeout, String protocol, int channelConnectedTimeout, String privateKey, String passphrase) throws Exception {
        JSch jsch = new JSch();
        // 设置密钥和密码 ,支持密钥的方式登陆
        if (StringUtils.isNotBlank(privateKey)) {
            if (StringUtils.isNotBlank(passphrase)) {
        // 设置带口令的密钥
                jsch.addIdentity(privateKey, passphrase);
            } else {
        // 设置不带口令的密钥
                jsch.addIdentity(privateKey);
            }
        }
        Session session = createSession(jsch, host, username, port);
        // 设置登陆超时时间
        session.connect(sessionConnectTimeout);
        // 创建sftp通信通道
        Channel channel = session.openChannel(protocol);
        channel.connect(channelConnectedTimeout);
        return (ChannelSftp) channel;
    }

    /**
     * 关闭连接
     *
     * @param sftp
     */
    public void disconnect(ChannelSftp sftp) {
        try {
            if (sftp != null) {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                } else if (sftp.isClosed()) {
                    log.info("sftp is closed already");
                }
                if (null != sftp.getSession()) {
                    sftp.getSession().disconnect();
                }
            }
        } catch (JSchException e) {
            e.printStackTrace();
        }
    }

}
