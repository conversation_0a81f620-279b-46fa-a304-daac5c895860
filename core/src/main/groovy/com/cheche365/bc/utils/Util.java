package com.cheche365.bc.utils;

import com.cheche365.bc.tools.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 通用工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class Util {

    private Util() {

    }

    //产生指定范围内的随机数
    public static int intNum(int start, int end) {
        return (int) (Math.random() * (end - start));
    }

    //随机产生手机号码
    public static String getMobile() {
        return StringUtil.getMobile();
    }

    /**
     * 判断字符串是否为空
     *
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        return !(str != null && str.trim().length() != 0 && str.length() != 0 && !"".equals(str) && !"null".equalsIgnoreCase(str));
    }

    /**
     * 判断字符串是否不为空
     *
     * @param str
     * @return
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 判断字符串是否都不为空
     *
     * @param strs
     * @return
     */
    public static boolean areNotEmpty(String... strs) {
        for (final String val : strs) {
            if (isEmpty(val)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断map是否为空
     *
     * @param map
     * @param key
     * @return
     */
    public static boolean isEmpty(Map map, String key) {
        return !map.containsKey(key) || map.get(key) == null || "".equals(map.get(key));
    }

    public static String readResource(String path) {
        return readResource(path, "utf-8");
    }

    public static String readResource(String path, String charSet) {
        try {
            log.debug("读取资源，相对路径{}", path);
            String realPath = Util.class.getResource(path).getPath();
            log.debug("读取资源，绝对路径{}", realPath);
            return read(realPath, charSet);
        } catch (Exception e) {
            log.error("路径" + path + "I/O流读取异常：", e);
            return "";
        }

    }

    /**
     * 读取IO文件
     *
     * @param filePath
     * @return
     */
    public static String read(String filePath, String charSet) {
        try {
            String fileContent;
            final File file = new File(filePath);
            final Long size = file.length();
            byte[] buff = new byte[size.intValue()];
            final FileInputStream fs = new FileInputStream(file);
            fs.read(buff);
            fs.close();
            fileContent = new String(buff, charSet);
            return fileContent;
        } catch (Exception e) {
            log.error("I/O流读取异常：", e);
            return "";
        }
    }

    /**
     * 按指定格式生成当前时间
     *
     * @param pattern
     * @return
     */
    public static String now(String pattern) {
        return dateThreadLocal(pattern).get().format(new Date());
    }

    /**
     * 返回一个线程安全的SimpleDateFormat实例
     *
     * @param pattern
     * @return
     */
    public static ThreadLocal<DateFormat> dateThreadLocal(final String pattern) {
        return new ThreadLocal<DateFormat>() {
            @Override
            protected DateFormat initialValue() {
                return new SimpleDateFormat(pattern);
            }
        };
    }

    /**
     * 计算两个日期相差天数
     *
     * @param start 起始日期
     * @param end   终止日期
     * @return
     */
    public static long diffDays(Date start, Date end) {
        return (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
    }

    /**
     * 计算两个日期相差月份
     *
     * @param start 起始日期
     * @param end   终止日期
     * @return
     */
    public static long diffMonths(Date start, Date end) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        int sYear = calendar.get(Calendar.YEAR);
        int sMonth = calendar.get(Calendar.MONTH);
        calendar.setTime(end);
        int eYear = calendar.get(Calendar.YEAR);
        int eMonth = calendar.get(Calendar.MONTH);
        return (long) ((eYear - sYear) * 12 + (eMonth - sMonth));
    }

    public static String getStackTrace(Throwable e) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            e.printStackTrace(pw);
            return sw.toString();
        }
    }

    /***
    * @Description: 需求 11810
    * @Date: 2025/3/12
    * @Author: wangj
    * @Param: [carModelName, plateNum]
    * @Return: boolean
    */
    public static boolean isNewEnergy(String carModelName, String plateNum) {
        if (StringUtils.isBlank(carModelName) || StringUtils.isBlank(plateNum)) {
            return false;
        }
        if (carModelName.contains("纯电") || carModelName.contains("插电")) {
            return true;
        }
        if (plateNum.length() == 8) {
            String plateSecond = plateNum.substring(2, 3);
            String[] codes = {"A", "B", "C", "D", "E", "F", "G", "H", "J", "K"};
            return Arrays.asList(codes).contains(plateSecond);
        }
        return false;
    }

    /***
     * @Description: 检查身份证合法
     * @Date: 2025/4/15
     * @Author: wangj
     * @Param: [idCard]
     * @Return: boolean
     */
    public static boolean checkIdCard(String idCard) {
        if(StringUtils.isBlank(idCard)) {
            return false;
        }
        Pattern ID_PATTERN = Pattern.compile("^\\d{6}(19|20)\\d{9}[\\dXx]$");
        return ID_PATTERN.matcher(idCard).matches();
    }
}

