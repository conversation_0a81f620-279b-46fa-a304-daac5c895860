package com.cheche365.bc.utils.tai_bao;

import cn.hutool.crypto.asymmetric.KeyType;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import org.apache.commons.collections4.map.LinkedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 签名工具
 *
 * <AUTHOR>
 */
public class SignatureUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger("SignatureUtils");

    /**
     * 验签并解密
     * <p/>
     * 对于<b>合作伙伴</b>，publicKey是指保险公司的公钥，privateKey是指合作伙伴的私钥<br>
     * 对于<b>保险公司</b>，publicKey是指合作伙伴的公钥，privateKey是指保险公司自己的私钥<br>
     *
     * @param request     原始报文(JSON字符串)
     * @param publicKey   公钥
     * @param privateKey  私钥
     * @param isCheckSign 是否验签
     * @param isDecrypt   是否解密
     * @return 解密后的明文，验签失败则异常抛出
     * @throws Exception
     */
    public static String checkSignAndDecrypt(String request, String publicKey, String privateKey,
                                             boolean isCheckSign, boolean isDecrypt) throws Exception {

        RSA build = RSA.builder()
                .publicKey(publicKey)
                .privateKey(privateKey)
                .signAlgorithm(EncryptEnum.SignAlgorithmEnum.MD5withRSA)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .build();

        boolean verifyResult = false;
        JSONObject requestJSONObject = JSONObject.parseObject(request);
        String sign = requestJSONObject.getString(MessageConstants.SIGN_CONTENT);//获取的签文
        String bizContent = requestJSONObject.getString(MessageConstants.BIZ_CONTENT);//获取的业务数据密文
        requestJSONObject.remove(MessageConstants.SIGN_CONTENT);//要排序先去掉signContent（签文）
        String signContent = getSignContent(requestJSONObject);//所有key排序后放入LingkedMap后map.toString()
        if (isCheckSign) {
            verifyResult = build.verify(signContent, sign);
        }
        if (!verifyResult)
            throw new Exception("验签失败！");
        if (isDecrypt && verifyResult) {
            return build.decrypt(bizContent);
        }
        return null;
    }

    /**
     * 加密并签名
     * <p/>
     * 对于<b>合作伙伴</b>，publicKey是指保险公司的公钥，privateKey是指合作伙伴的私钥<br>
     * 对于<b>保险公司</b>，publicKey是指合作伙伴的公钥，privateKey是指保险公司自己的私钥<br>
     *
     * @param bizContent  返回报文原文
     * @param publicKey
     * @param privateKey
     * @param isCheckSign
     * @param isDecrypt
     * @return 加密加签后的返回报文
     * @throws Exception
     */
    public static String encryptAndSign(String bizContent, String publicKey, String privateKey, String request,
                                        boolean isCheckSign, boolean isDecrypt) throws Exception {

        RSA build = RSA.builder()
                .publicKey(publicKey)
                .privateKey(privateKey)
                .signAlgorithm(EncryptEnum.SignAlgorithmEnum.MD5withRSA)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .build();

        JSONObject responseJSONObject = JSONObject.parseObject(request);
        responseJSONObject.remove(MessageConstants.SIGN_CONTENT);//去掉signContent,之后加签添加
        if (isDecrypt) {
            String encryptBizContent = build.encrypt(bizContent);
            LOGGER.debug("加密后业务数据：" + encryptBizContent);
            responseJSONObject.put(MessageConstants.BIZ_CONTENT, encryptBizContent);
            if (isCheckSign) {
                String signContent = getSignContent(responseJSONObject);//所有key排序后放入LingkedMap后map.toString()
                LOGGER.debug("待签名字符串为：" + signContent);
                String sign = build.sign(signContent);
                responseJSONObject.put(MessageConstants.SIGN_CONTENT, sign);
            }
        } else if (isCheckSign) {//只加签、不加密
            String signContent = getSignContent(responseJSONObject);//所有key排序后放入LingkedMap后map.toString()
            LOGGER.debug("待签名字符串为：" + signContent);
            String sign = build.sign(signContent);
            responseJSONObject.put(MessageConstants.SIGN_CONTENT, sign);
        }
        return responseJSONObject.toJSONString();
    }


    /**
     * 封装待验签的内容
     *
     * @param sortedParam
     * @return
     */
    public static String getSignContent(Map<String, Object> sortedParam) {
        LinkedMap map = new LinkedMap();
        List<String> keys = new ArrayList<String>(sortedParam.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParam.get(key).toString();
            map.put(key, value);
        }
        LOGGER.debug("map = " + map.toString());
        return map.toString();
    }


    public static String encryptByBody(String requestBody, String publicKey) {

        JSONObject requestObject = JSONObject.parseObject(requestBody);
        String requsetObjectString = requestObject.getString(MessageConstants.SIGN_BODY);

        cn.hutool.crypto.asymmetric.RSA rsa = new cn.hutool.crypto.asymmetric.RSA(null,publicKey);
        String body = rsa.encryptHex(requsetObjectString, KeyType.PublicKey);

        LOGGER.debug("加密后业务数据：" + body);
        requestObject.replace(MessageConstants.SIGN_BODY, body);
        return requestObject.toJSONString();
    }
}
