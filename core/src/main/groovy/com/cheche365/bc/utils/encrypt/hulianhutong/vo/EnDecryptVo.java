package com.cheche365.bc.utils.encrypt.hulianhutong.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EnDecryptVo {


    /**
     * DES KEY
     */
    @JsonProperty(value = "DESPwd")
    private String DESPwd;

    /**
     * RSA公钥
     */
    @JsonProperty(value = "pubKey")
    private String pubKey;

    /**
     * RSA私钥
     */
    @JsonProperty(value = "priKey")
    private String priKey;

    /**
     * 明文数据
     */
    @JsonProperty(value = "record")
    private String record;

    /**
     * DES加密后的数据
     */
    @JsonProperty(value = "DESRecord")
    private String DESRecord;

    /**
     * RSA 公钥加密后DES KEY
     */
    @JsonProperty(value = "RSAPwd")
    private byte[] RSAPwd;

}
