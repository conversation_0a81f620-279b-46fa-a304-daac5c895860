package com.cheche365.bc.utils.youbaolian.ras;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.youbaolian.model.Head;
import com.cheche365.bc.utils.youbaolian.model.RequestResponseBody;
import com.cheche365.bc.utils.youbaolian.utils.StreamUtils;

import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.apache.commons.codec.binary.Base64.decodeBase64;

public class RSAUtils {
    public RSAUtils() {
    }

    public static String checkSignAndDecrypt(RequestResponseBody requestJsonBody, String signKey, String privateKey, String charset, boolean isDecrypt, boolean isCheckSign)
            throws Exception {
        if (charset == null || "".equalsIgnoreCase(charset))
            charset = "UTF-8";
        if (isCheckSign) {
            if (requestJsonBody.getHead() == null)
                throw new RuntimeException("验签失败");
            if (!SignBuildUtils.sign(requestJsonBody, signKey).equalsIgnoreCase(requestJsonBody.getHead().getSign()))
                throw new RuntimeException("验签失败");
        }
        if (isDecrypt)
            return URLDecoder.decode(rsaDecrypt(requestJsonBody.getBody(), privateKey, charset), "UTF-8");
        else
            return requestJsonBody.getBody();
    }

    public static Map encryptAndSign(Head head, String body, String signKey, String publicKey, String charset, boolean isEncrypt, boolean isSign)
            throws Exception {
        if (charset == null || "".equalsIgnoreCase(charset))
            charset = "UTF-8";
        body = URLEncoder.encode(body, "UTF-8");
        Map map = new HashMap();
        if (isEncrypt) {
            String encrypted = rsaEncrypt(body, publicKey, charset);
            map.put("CONTENT", encrypted);
            if (isSign) {
                String sign = SignBuildUtils.sign(head, body, signKey);
                map.put("SIGN", sign);
            }
        } else if (isSign) {
            map.put("CONTENT", body);
            String sign = SignBuildUtils.sign(head, body, signKey);
            map.put("SIGN", sign);
        }
        return map;
    }

    public static String rsaEncrypt(String content, String publicKey, String charset) throws Exception {

        RSA build = RSA.builder()
                .publicKey(publicKey)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .build();
        return build.encrypt(content.getBytes(charset));

    }

    public static String rsaDecrypt(String content, String privateKey, String charset) throws Exception {
        try {

            byte[] encryptedData = isEmpty(charset) ? decodeBase64(content.getBytes()) : decodeBase64(content.getBytes(charset));

            RSA build = RSA.builder()
                    .privateKey(privateKey)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                    .build();
            return build.decrypt(encryptedData);
        } catch (Exception e) {
            throw new Exception("EncodeContent = " + content + ",charset = " + charset, e);
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins)
            throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        StringWriter writer = new StringWriter();
        StreamUtils.io(new InputStreamReader(ins), writer);
        byte encodedKey[] = writer.toString().getBytes();
        encodedKey = Base64.getDecoder().decode(encodedKey);
        return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
    }

    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream ins)
            throws Exception {
        if (ins == null || isEmpty(algorithm)) {
            return null;
        } else {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            byte encodedKey[] = StreamUtils.readText(ins).getBytes();
            encodedKey = Base64.getDecoder().decode(encodedKey);
            return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        }
    }

    public static boolean isEmpty(String value) {
        return null == value || value.trim().equals("");
    }


    /**
     * 验签并加密
     *
     * @Param body 原始参数
     * @Param partnerNo 保险公司编码
     * @Param version 版本号
     * @Param timeStamp 时间
     * @Param publicKey 公钥
     * @Param signKey 签名key
     */
    public static String encrypt(String body, String partnerNo, String version, String timeStamp, String publicKey, String signKey) {
        String result = "";
        RequestResponseBody requestJsonBody = new RequestResponseBody();
        requestJsonBody.setBody(body);
        Head head = new Head();
        head.setPartnerNo(partnerNo);
        head.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        head.setVersion(version);
        head.setTimestamp(timeStamp);
        try {
            Map stringStringMap = RSAUtils.encryptAndSign(head, requestJsonBody.getBody(), signKey, publicKey, "UTF-8", true, true);
            head.setSign((String) stringStringMap.get("SIGN"));
            requestJsonBody.setHead(head);
            requestJsonBody.setBody((String) stringStringMap.get("CONTENT"));
            System.out.println("优保联加密前参数：" + JSONObject.toJSONString(body));
            System.out.println("优保联加密信息：" + JSONObject.toJSONString(requestJsonBody));
            result = JSONObject.toJSONString(requestJsonBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 验签并解密
     *
     * @Param body 原始参数
     * @Param partnerNo 保险公司编码
     * @Param version 版本号
     * @Param timeStamp 时间
     * @Param privateKey 私钥
     * @Param signKey 签名key
     */
    public static String decode(String body, String partnerNo, String version, String timeStamp, String privateKey, String signKey) {
        String decodeBody = "";
        RequestResponseBody requestJsonBody = new RequestResponseBody();
        Head head = new Head();
        head.setPartnerNo(partnerNo);
        head.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        head.setVersion(version);
        head.setTimestamp(timeStamp);
        try {
            head.setSign(signKey);
            requestJsonBody.setHead(head);
            requestJsonBody.setBody(JSONObject.parseObject(body).getString("body"));
            decodeBody = RSAUtils.checkSignAndDecrypt(requestJsonBody, signKey, privateKey, "UTF-8", true, false);
            System.out.println("优保联解密信息：" + decodeBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decodeBody;
    }
}

