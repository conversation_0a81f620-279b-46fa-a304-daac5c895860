package com.cheche365.bc.utils.encrypt.hulianhutong.zhonghang;

import java.io.Serializable;


public class Packet implements Serializable {
    private static final long serialVersionUID = 1L;

    private Head head = new Head();
    private String body;

    public Head getHead() {
        return this.head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public String getBody() {
        return this.body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String toString() {
        return "Packet [head=" + this.head + ", body=" + this.body + "]";
    }
}
