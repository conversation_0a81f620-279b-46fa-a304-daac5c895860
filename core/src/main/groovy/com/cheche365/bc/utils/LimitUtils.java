package com.cheche365.bc.utils;

import com.cheche365.bc.cache.RedisCache;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 任务次数限制工具
 *
 * <AUTHOR>
 * Created by austinChen on 2018/10/26 17:50.
 */
@Slf4j
public class LimitUtils {

    private static StringRedisTemplate redisTemplate = RedisCache.getStringRedis();

  /**
   * 滑动窗口算法
   * @param comId     保险公司id
   * @param account   账户
   * @param taskType  报价类型
   * @param period    单位s
   * @param maxCount
   * @return boolean
   * <AUTHOR>
   */
    public static boolean isActionAllowed(String comId, String account, String taskType, int period, int maxCount) {

        final long current = System.currentTimeMillis();
        // 移除时间窗口之前的行为记录，剩下的都是时间窗口内的
        String key = "auto:account:limit:" + comId + "-" + account + ":" + taskType;
        redisTemplate.opsForZSet().removeRangeByScore(key, 0, current - period * 1000);
        // 获取窗口内的行为数量
        Long zCard = redisTemplate.opsForZSet().zCard(key);
        if (zCard < maxCount) {
            // 记录行为 score
            redisTemplate.opsForZSet().add(key, String.valueOf(current), current);
            // 设置zset过期时间，避免冷用户持续占用内存
            // 过期时间应该等于时间窗口长度，再多宽限1s
            redisTemplate.expire(key, period + 1, TimeUnit.SECONDS);
            return true;
        }
        return false;
    }

    public static void checkLimitTimes(String comId, String account, String taskType) throws Exception {
        String dateNow = DateUtil.d2SMinute();
        String checkAccount = comId + "-" + account;
        String limitStr = redisTemplate.opsForValue().get("auto:account:limit:" + checkAccount + ":" + taskType + ":" + dateNow);
        if (StringUtils.isNoneEmpty(limitStr)) {
            String key = "auto:account:limit:" + checkAccount + ":" + taskType + ":callnum:" + dateNow;
            int callNum = 0;
            int limitNum = 0;
            try {
                String callNumStr = redisTemplate.opsForValue().get(key);
                if (StringUtils.isNotBlank(callNumStr)) {
                    callNum = Integer.parseInt(callNumStr);
                    limitNum = Integer.parseInt(limitStr);
                }
            } catch (Exception ex) {
                log.error("错误",ex);
            }
            if (limitNum > 0 && callNum > 0 && callNum >= limitNum) {
                throw new UniqueException(UniqueException.TimeLimit, "1分钟内只允许账号[报价操作]" + account + "进行" + taskType + "操作" + callNum + "次！，报价次数限制。");
            }
        }
    }


    public static void inc(String comId, String account, String taskType) throws Exception {
        String dateNow = DateUtil.d2SMinute();
        String key = "auto:account:limit:" + comId + "-" + account + ":" + taskType + ":callnum:" + dateNow;
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 60, TimeUnit.SECONDS);
    }

    /**
     * 功能描述: <br>
     * 〈控制同一个账号在1分钟内的请求次数〉
     *
     * @param:[comId, account, taskType, autoTask]
     * @return:void
     * @since: 1.0.0
     * @Author:s·D·bs
     * @Date: 2019/7/25 16:54
     */
    public static void quoteLimit(String comId, String account, String taskType, AutoTask autoTask) {

        try {
            checkLimitTimes(comId, account, taskType);
        } catch (Exception e) {
            if (e instanceof UniqueException) {
                UniqueException err = (UniqueException) e;
                log.info("err.errorCode:[{}] ,err.errorMsg:[{}]", err.getCode(), err.getMessage());
                autoTask.getErrorInfo().put("errorcode", err.getCode());
                autoTask.getErrorInfo().put("errordesc", err.getMessage());
            }
            log.error("error:{}", e);
        }
    }

    /**
     * 接口调用次数控制，未考虑并发
     * @param comId
     * @param account
     * @param taskType
     * @param autoTask
     * @param limitCount
     */
    public static Boolean  taskTypeLimit(String comId, String account, String taskType, AutoTask autoTask, int limitCount) throws Exception{
        //目前不考虑按接口控制
        String key = comId + "-" + account;
        long current = System.currentTimeMillis();
        long before = current - 60000 ;
        if (redisTemplate.opsForZSet().count(key,(double) before,(double) current) > limitCount){
            return false;
        } else {
            redisTemplate.opsForZSet().add(key, String.valueOf(current), (double) current);
            redisTemplate.opsForZSet().removeRange(key, 0, current - 60001);
            return true;
        }

    }

}
