// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3) fieldsfirst ansi 
// Source File Name:   StreamUtils.java

package com.cheche365.bc.utils.youbaolian.utils;

import java.io.*;

public class StreamUtils {
    private static class SynchronizedOutputStream extends OutputStream {

        private OutputStream out;
        private Object lock;

        public void write(int datum)
                throws IOException {
            synchronized (lock) {
                out.write(datum);
            }
        }

        public void write(byte data[])
                throws IOException {
            synchronized (lock) {
                out.write(data);
            }
        }

        public void write(byte data[], int offset, int length)
                throws IOException {
            synchronized (lock) {
                out.write(data, offset, length);
            }
        }

        public void flush()
                throws IOException {
            synchronized (lock) {
                out.flush();
            }
        }

        public void close()
                throws IOException {
            synchronized (lock) {
                out.close();
            }
        }

        SynchronizedOutputStream(OutputStream out) {
            this(out, out);
        }

        SynchronizedOutputStream(OutputStream out, Object lock) {
            this.out = out;
            this.lock = lock;
        }
    }


    private static final int DEFAULT_BUFFER_SIZE = 8192;

    public StreamUtils() {
    }

    public static void io(InputStream in, OutputStream out)
            throws IOException {
        io(in, out, -1);
    }

    public static void io(InputStream in, OutputStream out, int bufferSize)
            throws IOException {
        if (bufferSize == -1)
            bufferSize = 8192;
        byte buffer[] = new byte[bufferSize];
        int amount;
        while ((amount = in.read(buffer)) >= 0)
            out.write(buffer, 0, amount);
    }

    public static void io(Reader in, Writer out)
            throws IOException {
        io(in, out, -1);
    }

    public static void io(Reader in, Writer out, int bufferSize)
            throws IOException {
        if (bufferSize == -1)
            bufferSize = 4096;
        char buffer[] = new char[bufferSize];
        int amount;
        while ((amount = in.read(buffer)) >= 0)
            out.write(buffer, 0, amount);
    }

    public static OutputStream synchronizedOutputStream(OutputStream out) {
        return new SynchronizedOutputStream(out);
    }

    public static OutputStream synchronizedOutputStream(OutputStream out, Object lock) {
        return new SynchronizedOutputStream(out, lock);
    }

    public static String readText(InputStream in)
            throws IOException {
        return readText(in, null, -1);
    }

    public static String readText(InputStream in, String encoding)
            throws IOException {
        return readText(in, encoding, -1);
    }

    public static String readText(InputStream in, String encoding, int bufferSize)
            throws IOException {
        Reader reader = encoding != null ? ((Reader) (new InputStreamReader(in, encoding))) : ((Reader) (new InputStreamReader(in)));
        return readText(reader, bufferSize);
    }

    public static String readText(Reader reader)
            throws IOException {
        return readText(reader, -1);
    }

    public static String readText(Reader reader, int bufferSize)
            throws IOException {
        StringWriter writer = new StringWriter();
        io(reader, writer, bufferSize);
        return writer.toString();
    }
}
