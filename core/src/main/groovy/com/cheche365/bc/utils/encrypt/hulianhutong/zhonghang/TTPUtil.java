package com.cheche365.bc.utils.encrypt.hulianhutong.zhonghang;


import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.MD5;
import com.cheche365.bc.utils.encrypt.RSA;
import java.io.*;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class TTPUtil {
    public static SinoKPair genKeys() {
        SinoKPair result = new SinoKPair();
        try {
            KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
            generator.initialize(1024);
            KeyPair keyPair = generator.generateKeyPair();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            result.setPrivateKey(encryptBase64(privateKey.getEncoded()));
            result.setPublicKey(encryptBase64(publicKey.getEncoded()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static Packet genSendObj(String partnerNo, String pubk, String prik, String outText) throws Exception {
        Packet pac = new Packet();
        Head head = pac.getHead();
        String enOutText = encrypt(outText, pubk, prik);
        pac.setBody(enOutText);
        head.setPartnerNo(partnerNo);
        head.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        head.setTimeStamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        signPacket(pac);
        return pac;
    }

    public static Packet genReceiveObj(Packet oriPac, String pubk, String prik) throws Exception {
        Packet dePac = new Packet();
        if (checkSignPacket(oriPac)) {
            Head deHead = dePac.getHead();
            Head oriHead = oriPac.getHead();
            deHead.setPartnerNo(oriHead.getPartnerNo());
            deHead.setRequestId(oriHead.getRequestId());
            dePac.setBody(decrypt(oriPac.getBody(), pubk, prik));
        } else {
            throw new RuntimeException("sign incorrect");
        }
        return dePac;
    }

    public static String encrypt(String bodyText, String pubk, String prik) throws Exception {
        String targetText = null;
        try {
            Map<String, String> encryptAndSignParams = encryptAndSignParams(bodyText, pubk, prik,
                    "GBK", true, false);
            targetText = encryptAndSignParams.get("biz_content");
        } catch (Exception e2) {
            e2.printStackTrace();
            throw e2;
        }
        return targetText;
    }

    public static String decrypt(String enBodyText, String pubk, String prik) throws Exception {
        String targetText = null;
        try {
            RSA rsa = RSA.builder().privateKey(prik)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA_ECB_PKCS1PADDING).build();
            byte[] decode = Base64.getDecoder().decode(enBodyText.getBytes("GBK"));
            String deBodyText = rsa.decrypt(decode);
            targetText = deBodyText;
        } catch (Exception e2) {
            e2.printStackTrace();
            throw e2;
        }
        return targetText;
    }

    private static String encryptBase64(byte[] key) throws Exception {
        return new String(Base64.getEncoder().encode(key));
    }

    //stringutils===================
    public static boolean isEmpty(String value) {
        int strLen;
        if (value == null || (strLen = value.length()) == 0)
            return true;
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(value.charAt(i)))
                return false;
        }
        return true;
    }


    public static boolean areNotEmpty(String... values) {
        int i;
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            byte b;
            int j;
            String[] arrayOfString;
            for (j = (arrayOfString = values).length, b = 0; b < j; ) {
                String value = arrayOfString[b];
                result = ((isEmpty(value) ? 0 : 1) == 0) ? false : true;
                b++;
            }
        }
        return result;
    }

    //sinonature==============
    public static String getSignContent(Map<String, String> sortedParams) {

        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParams.get(key);
            if (TTPUtil.areNotEmpty(new String[]{key, value})) {
                content.append(String.valueOf((index == 0) ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    public static String rsaSign(String content, String privateKey, String charset) throws Exception {
        try {
            RSA build = RSA.builder()
                    .privateKey(privateKey)
                    .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA1WithRSA)
                    .build();
            return build.sign(content.getBytes(charset));
        } catch (InvalidKeySpecException ie) {
            throw new Exception("RSA私钥格式不正确，请检查是否正确配置了PKCS8格式的私钥", ie);
        } catch (Exception e) {
            throw new Exception("RSAcontent = " + content + "; charset = " + charset, e);
        }
    }

    public static String rsaSign(Map<String, String> params, String privateKey, String charset) throws Exception {
        String signContent = getSignContent(params);
        return rsaSign(signContent, privateKey, charset);
    }

    public static Map<String, String> encryptAndSignParams(String bizContent, String cusPublicKey, String selPrivateKey, String charset, boolean isEncrypt, boolean isSign) throws Exception {
        Map<String, String> result = new HashMap<>();
        if (TTPUtil.isEmpty(charset))
            charset = "GBK";
        result.put("charset", charset);
        if (isEncrypt) {
            RSA rsa = RSA.builder().publicKey(cusPublicKey)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA_ECB_PKCS1PADDING).build();
            String encrypted = rsa.encrypt(bizContent);
            result.put("biz_content", encrypted);
            if (isSign) {
                result.put("sign_type", "RSA");
                String sign = rsaSign(result, selPrivateKey, charset);
                result.put("sign", sign);
            }
        } else if (isSign) {
            result.put("biz_content", bizContent);
            result.put("sign_type", "RSA");
            String sign = rsaSign(result, selPrivateKey, charset);
            result.put("sign", sign);
        } else {
            result.put("biz_content", bizContent);
        }
        return result;
    }

    public static void signPacket(Packet pac) throws Exception {
        try {
            Head head = pac.getHead();
            String body = pac.getBody();
            String partnerNo = head.getPartnerNo();
            String requestId = head.getRequestId();
            String timeStamp = head.getTimeStamp();
            String version = head.getVersion();
            String text = String.valueOf(partnerNo) + requestId + timeStamp + version + "cedon" + body;
            String sign = MD5.toHex(text).toUpperCase();
            head.setSign(sign);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("sign error!");
        }
    }

    public static boolean checkSignPacket(Packet pac) throws Exception {
        boolean result = false;
        try {
            Head head = pac.getHead();
            String body = pac.getBody();
            String partnerNo = head.getPartnerNo();
            String requestId = head.getRequestId();
            String timeStamp = head.getTimeStamp();
            String version = head.getVersion();
            String text = String.valueOf(partnerNo) + requestId + timeStamp + version + "cedon" + body;
            String sign = MD5.toHex(text).toUpperCase();
            if (sign.equals(head.getSign()))
                result = true;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("sign check error!");
        }
        return result;
    }
}
