package com.cheche365.bc.utils.encrypt.hulianhutong.zhonghang;

import java.io.Serializable;


public class Head implements Serializable {
    private static final long serialVersionUID = 1L;

    private String partnerNo;

    private String timeStamp;

    private String requestId;

    private String version = "1.0.0";

    private String sign;

    private Integer resultCode;

    private String message;

    public Head() {
    }

    public String getPartnerNo() {
        return this.partnerNo;
    }

    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    public String getTimeStamp() {
        return this.timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSign() {
        return this.sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Integer getResultCode() {
        return this.resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String toString() {
        return "Head [partnerNo=" + this.partnerNo + ", timeStamp=" + this.timeStamp + ", requestId=" + this.requestId + ", version=" +
                this.version + ", sign=" + this.sign + ", resultCode=" + this.resultCode + ", message=" + this.message + "]";
    }
}