package com.cheche365.bc.utils.sender;

import cn.hutool.core.map.MapUtil;
import com.cheche365.bc.exception.HttpStatusCodeException;
import com.cheche365.bc.tools.FileUtil;
import com.google.common.net.HttpHeaders;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.NoHttpResponseException;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.CustomHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.*;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.KeyStore;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static cn.hutool.core.util.CharsetUtil.UTF_8;
import static com.cheche365.bc.utils.sender.HttpLogUtil.logRequest;
import static com.cheche365.bc.utils.sender.HttpLogUtil.logResponse;

/**
 * HttpClient
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpSender {

    public static final String HTTP_GET_METHOD = "get";
    public static final String HTTP_POST_METHOD = "post";
    public static final String HTTP_PUT_METHOD = "put";
    public static final String HTTP_DELETE_METHOD = "delete";

    public static final String proxyHost = "proxyHost";
    public static final String proxyPort = "proxyPort";
    public static final String proxyName = "proxyName";
    public static final String proxyPass = "proxyPass";

    public static int defaultConnectTimeout = 90000;
    public static int defaultReadTimeout = 90000;
    public static int defaultHttpClientSocketTimeout = 20000;
    public static int defaultHttpClientConnectTimeout = 20000;
    public static int defaultHttpClientConnectionRequestTimeout = 20000;
    private static final PoolingHttpClientConnectionManager ccm;
    public static final List<Integer> REDIRECT_STATUS = List.of(HttpStatus.SC_MOVED_PERMANENTLY, HttpStatus.SC_MOVED_TEMPORARILY, HttpStatus.SC_SEE_OTHER, HttpStatus.SC_TEMPORARY_REDIRECT);

    public final static HashMap<String, String> JSON_HEADERS = new HashMap<>() {
        {
            put("Content-Type", "application/json;charset=UTF-8");
        }
    };

    private static final AtomicLong _id = new AtomicLong(0);

    private static LayeredConnectionSocketFactory sslSocketFactory = null;
    private static final Map<String, LayeredConnectionSocketFactory> sslSocketFactoryMap = new HashMap<>();

    @Getter
    private static RequestConfig defaultRequestConfig = null;

    static {
        try {
            System.setProperty("sun.net.client.defaultConnectTimeout", String.valueOf(defaultConnectTimeout));// （单位：毫秒）
            System.setProperty("sun.net.client.defaultReadTimeout", String.valueOf(defaultReadTimeout)); // （单位：毫秒）

            defaultRequestConfig = RequestConfig.custom()
                .setSocketTimeout(defaultHttpClientSocketTimeout)
                .setConnectTimeout(defaultHttpClientConnectTimeout)
                .setConnectionRequestTimeout(defaultHttpClientConnectionRequestTimeout)
                .build();

            ConnectionSocketFactory plainSf = PlainConnectionSocketFactory.getSocketFactory();
            //加密上下文
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{new MyX509TrustManager()}, null);
            sslSocketFactory = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"TLSv1.3", "TLSv1.2", "TLSv1.1", "TLSv1", "SSLv3", "SSLv2Hello"},
                null,
                new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;

                    }
                });
            Registry<ConnectionSocketFactory> r = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", plainSf)
                .register("https", sslSocketFactory)
                .build();

            ccm = new PoolingHttpClientConnectionManager(r);
            //将最大连接数增加到300
            ccm.setMaxTotal(10000);
            //将每个路由基础的连接增加到20
            ccm.setDefaultMaxPerRoute(2000);
            ccm.setDefaultSocketConfig(SocketConfig.custom().setSoTimeout(defaultReadTimeout).build());
            ccm.setValidateAfterInactivity(6000);
        } catch (Exception e) {
            log.error("HTTPCLIENT 初始化异常 ", e);
            throw new RuntimeException("HTTPCLIENT 初始化异常");
        }
    }

    public static LayeredConnectionSocketFactory getSSlSocketFactory() throws Exception {
        return sslSocketFactory;
    }

    public static LayeredConnectionSocketFactory getSSlSocketFactory(String sslStr, String[] supportedProtocols) throws Exception {
        return getSSlSocketFactory(sslStr, supportedProtocols, SSLConnectionSocketFactory.getDefaultHostnameVerifier());
    }

    public static LayeredConnectionSocketFactory getSSlSocketFactory(String sslStr, String[] supportedProtocols, HostnameVerifier hostnameVerifier) throws Exception {
        //加密上下文
        SSLContext sslContext = SSLContext.getInstance(sslStr);

        sslContext.init(null, new TrustManager[]{new MyX509TrustManager()}, null);
        return new SSLConnectionSocketFactory(
            sslContext,
            supportedProtocols,
            null,
            hostnameVerifier);
    }

    public static SSLConnectionSocketFactory getSSLSocketFactoryNoCheck() throws Exception {
        // 创建一个信任所有证书的 TrustManager
        SSLContext sslContext = new SSLContextBuilder()
            .loadTrustMaterial(null, (chain, authType) -> {
                return true; // 信任所有证书
            })
            .build();
        // 创建 SSLConnectionSocketFactory，使用 NoopHostnameVerifier
        return new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
    }

    public static CloseableHttpClient buildHttpClientByComId(String insComId, String password, String path) {
        try {
            if (!sslSocketFactoryMap.containsKey(insComId)) {
                StringBuilder tempPath = new StringBuilder();
                if (StringUtils.isNoneBlank(path)) {
                    tempPath.append(path);
                } else {
                    tempPath.append("/keys/").append(insComId).append(".jks");
                }
                SSLContext sslContext2 = SSLContext.getInstance("SSL");
                KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
                TrustManagerFactory tmf = TrustManagerFactory
                    .getInstance("SunX509");
                KeyStore ks = KeyStore.getInstance("JKS");
                try (InputStream inputStream = HttpSender.class.getResourceAsStream(tempPath.toString())) {
                    BufferedInputStream bf = new BufferedInputStream(inputStream);
                    bf.mark(0);
                    ks.load(bf, password.toCharArray());
                    kmf.init(ks, password.toCharArray());
                    KeyStore tks = KeyStore.getInstance("JKS");
                    bf.reset();
                    tks.load(bf, password.toCharArray());
                    tmf.init(tks);
                }
                sslContext2.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);
                LayeredConnectionSocketFactory sslSocketFactory2 = new SSLConnectionSocketFactory(
                    sslContext2);
                sslSocketFactoryMap.put(insComId, sslSocketFactory2);
            }
            return HttpClients.custom().setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE).setSSLSocketFactory(sslSocketFactoryMap.get(insComId)).build();
        } catch (Exception ex) {
            log.error("加载专用的http请求出现异常", ex);
            return HttpClients.custom().setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE).setSSLSocketFactory(sslSocketFactory).build();
        }
    }

    public static CloseableHttpClient buildHttpClientByComId(String insComId) {
        return buildHttpClientByComId(insComId, "123456", null);
    }


    public static CloseableHttpClient buildHttpClient() {
        return buildHttpClient(true);
    }

    public static CloseableHttpClient buildHttpClient(LayeredConnectionSocketFactory sslSocketFactory) {
        return HttpClients.custom().setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE).setSSLSocketFactory(sslSocketFactory).build();
    }

    public static CloseableHttpClient buildHttpClient(boolean usePoolConnect) {
        if (usePoolConnect) {
            return HttpClients.custom().setConnectionManager(ccm)
                .setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE)
                .setConnectionManagerShared(true).build();
        } else {
            return HttpClients.custom()
                .setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE)
                .setSSLSocketFactory(sslSocketFactory).build();
        }
    }

    public static CloseableHttpClient buildHttpClient(String proxyHost, Integer proxyPort, String proxyName, String proxyPass) {
        return buildHttpClient(true, proxyHost, proxyPort, proxyName, proxyPass, null);
    }

    public static CloseableHttpClient buildHttpClient(boolean usePoolConnect, String proxyHost, Integer proxyPort, String proxyName, String proxyPass, LayeredConnectionSocketFactory userSslSocketFactory) {
        CredentialsProvider credsProvider = new BasicCredentialsProvider();
        credsProvider.setCredentials(new AuthScope(proxyHost, proxyPort), new UsernamePasswordCredentials(proxyName, proxyPass));
        if (usePoolConnect && userSslSocketFactory == null) {
            return HttpClients.custom()
                .setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE)
                .setDefaultCredentialsProvider(credsProvider)
                .setConnectionManager(ccm).setConnectionManagerShared(true).build();
        } else {
            return HttpClients.custom()
                .setRetryHandler(CustomHttpRequestRetryHandler.INSTANCE)
                .setDefaultCredentialsProvider(credsProvider).setSSLSocketFactory(userSslSocketFactory != null ? userSslSocketFactory : sslSocketFactory).build();
        }
    }

    public static Object doGet(String url, Map<String, Object> params, Boolean isFile) throws Exception {
        return doGet(url, params, UTF_8, isFile);
    }

    public static String doGet(String url, Map<String, Object> params) throws Exception {
        return doGet(url, params, UTF_8);
    }


    /**
     * Http Get 获取内容
     *
     * @param url     请求的url地址
     * @param params  请求的参数
     * @param charset 编码格式
     * @return 响应内容
     */
    public static String doGet(String url, Map<String, Object> params, String charset) throws Exception {
        return doGet(null, url, params, charset);
    }

    public static Object doGet(String url, Map<String, Object> params, String charset, Boolean isFile) throws Exception {
        return doGet(null, url, params, charset, getDefaultRequestConfig(), isFile);
    }

    public static String doGet(CloseableHttpClient httpClient, String url, Map<String, Object> params, String charset) throws Exception {
        return (String) doGet(httpClient, url, params, charset, getDefaultRequestConfig(), false);
    }

    public static Object doGetWithRetry(int retryTimes, CloseableHttpClient httpClient, String url, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        return doGetWithRetryAndLoadHeader(retryTimes, httpClient, url, null, params, charset, requestConfig, isFile);
    }

    public static Object doGetWithRetryAndLoadHeader(int retryTimes, CloseableHttpClient httpClient, String url, Map<String, String> headers, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        int index = 1;
        while (index++ <= retryTimes) {
            try {
                return doGet(httpClient, url, headers, params, charset, requestConfig, isFile);
            } catch (UnknownHostException | ConnectTimeoutException | SocketException | SocketTimeoutException |
                     NoHttpResponseException | HttpStatusCodeException e) {
                log.warn("当前地址:{}的GET请求出现网络异常,将进行第{}次重试!", url, index);
                TimeUnit.SECONDS.sleep(index * 5L);
            }
        }
        return null;
    }

    /**
     * @param httpClient 链接
     * @param url        url地址
     * @return 返回结果对象
     * @throws Exception 抛出异常
     */
    public static Object doGet(CloseableHttpClient httpClient, String url) throws Exception {
        return doGet(httpClient, url, null, "UTF-8", null, false);
    }

    public static Object doGet(CloseableHttpClient httpClient, String url, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        return doGet(httpClient, url, null, params, charset, requestConfig, isFile);
    }

    public static Object doGet(CloseableHttpClient httpClient, String url, Map<String, String> headers, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        return doIt(HTTP_GET_METHOD, httpClient, url, headers, params, charset, requestConfig, isFile);
    }

    public static Object doDelete(CloseableHttpClient httpClient, String url, Map<String, String> headers, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        return doIt(HTTP_DELETE_METHOD, httpClient, url, headers, params, charset, requestConfig, isFile);
    }

    private static Object doIt(String requestType, CloseableHttpClient httpClient, String url, Map<String, String> headers, Map<String, Object> params, String charset, RequestConfig requestConfig, Boolean isFile) throws Exception {
        boolean needCloseHttpClient = httpClient == null;
        if (httpClient == null) {
            httpClient = buildHttpClient();
        }
        CloseableHttpResponse response = null;
        String result = "";
        HttpRequestBase httpRequestBase = null;
        try {
            if (MapUtil.isNotEmpty(params)) {
                List<NameValuePair> pairs = new ArrayList<>(params.size());
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    final String key = entry.getKey();
                    if (StringUtils.isNotEmpty(key)) {
                        pairs.add(new BasicNameValuePair(key, entry.getValue() + ""));
                    }
                }
                if (url.contains("?")) {
                    url += "&" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
                } else {
                    url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(pairs, charset));
                }
            }
            if (HTTP_GET_METHOD.equals(requestType)) {
                httpRequestBase = new HttpGet(url);
            } else if (HTTP_DELETE_METHOD.equals(requestType)) {
                httpRequestBase = new HttpDelete(url);
            } else {
                httpRequestBase = new HttpGet(url);
            }
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    httpRequestBase.addHeader(header.getKey(), header.getValue());
                }
            }
            httpRequestBase.addHeader(HttpHeaders.CONNECTION, "close");
            httpRequestBase.removeHeaders(HttpHeaders.CONTENT_LENGTH);
            if (requestConfig == null) {
                requestConfig = defaultRequestConfig;
            }
            httpRequestBase.setConfig(requestConfig);
            long id = _id.incrementAndGet();
            logRequest(url, headers, "", requestType, params, id);
            response = httpClient.execute(httpRequestBase);
            int statusCode = response.getStatusLine().getStatusCode();
            if (REDIRECT_STATUS.contains(statusCode)) {
                return handlerHTTP300(httpRequestBase, httpClient, response, requestConfig, headers, charset);
            } else if (statusCode != 200 && statusCode != 500 && statusCode != 221) {
                if (statusCode == HttpStatus.SC_SERVICE_UNAVAILABLE || statusCode == HttpStatus.SC_NOT_FOUND) {
                    throw new HttpStatusCodeException(statusCode, "http request had failed , failed statusCode :" + statusCode);
                }
                throw new Exception("http request had failed , failed statusCode :" + statusCode);
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                if (!isFile) {
                    result = EntityUtils.toString(entity, charset);
                } else {
                    try (final InputStream stream = entity.getContent()) {
                        return FileUtil.getStreamAsByteArray(stream);
                    }
                }
            }
            logResponse(response, result, url, id);
            return result;
        } catch (Exception e) {
            log.info("HttpClient Get Error:{}", e.getMessage());
            throw e;
        } finally {
            releaseConnection(response, httpRequestBase, needCloseHttpClient, httpClient);
        }
    }

    public static String doPost(String url, String sendStr) throws Exception {
        return doPost(true, url, sendStr, null, JSON_HEADERS, UTF_8, getDefaultRequestConfig());
    }

    public static String doPost(String url, String sendStr, Map<String, String> headers, String charset) throws Exception {
        return doPost(true, url, sendStr, null, headers, charset, defaultRequestConfig);
    }

    public static String doPost(String url, String sendStr, Map<String, String> headers, String charset, RequestConfig requestConfig) throws Exception {
        return doPost(true, url, sendStr, null, headers, charset, requestConfig);
    }

    public static String doPost(String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset) throws Exception {
        return doPost(true, url, sendStr, params, headers, charset, defaultRequestConfig);
    }

    public static String doPost(String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset, RequestConfig requestConfig) throws Exception {
        return doPost(true, url, sendStr, params, headers, charset, requestConfig);
    }

    public static String doPost(boolean usePoolConnect, String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset) throws Exception {
        return doPost(usePoolConnect, url, sendStr, params, headers, charset, getDefaultRequestConfig());
    }

    public static String doPost(boolean usePoolConnect, String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset, RequestConfig requestConfig) throws Exception {
        return doPost(usePoolConnect, url, sendStr, params, headers, charset, requestConfig, "");
    }

    public static String doPost(boolean usePoolConnect, String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey) throws Exception {
        return doPost(null, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, "");
    }

    public static String doPost(CloseableHttpClient closeableHttpClient, String url, String sendStr, Map<String, String> params, Map<String, String> headers, String charset) throws Exception {
        return doPost(closeableHttpClient, true, url, sendStr, params, headers, charset, null, "");
    }

    public static byte[] doPost(CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders) throws Exception {
        return (byte[]) doItWithRepHeaders(HTTP_POST_METHOD, closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, repHeaders, true);
    }

    public static String doPost(CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey) throws Exception {
        return doPostWithRepHeaders(closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, null);
    }

    public static String doPostWithRepHeaders(CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders) throws Exception {
        return doItWithRepHeaders(HTTP_POST_METHOD, closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, repHeaders);
    }

    public static String doPut(CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders) throws Exception {
        return doItWithRepHeaders(HTTP_PUT_METHOD, closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, repHeaders);
    }

    public static String doPut(String url, String sendStr, Map<String, String> headers, String charset) throws Exception {
        return doItWithRepHeaders(HTTP_PUT_METHOD, null, true, url, sendStr, null, headers, charset, null, "", null);
    }

    public static String doPut(String url, Object params, Map<String, String> headers, String charset) throws Exception {
        return doItWithRepHeaders(HTTP_PUT_METHOD, null, true, url, null, params, headers, charset, null, "", null);
    }

    public static Object doPut(String url, String sendStr, Map<String, String> headers, String charset, boolean isFile) throws Exception {
        return doItWithRepHeaders(HTTP_PUT_METHOD, null, true, url, sendStr, null, headers, charset, null, "", null, true);
    }

    public static String doItWithRepHeaders(String requestMethod, CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders) throws Exception {
        return (String) doItWithRepHeaders(requestMethod, closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, "", repHeaders, false);
    }

    private static Object doItWithRepHeaders(String requestMethod, CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders, boolean isFile) throws Exception {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        long id = _id.incrementAndGet();
        logRequest(url, headers, sendStr, requestMethod, params, id);
        CloseableHttpClient httpClient;
        boolean needCloseHttpClient = (closeableHttpClient == null && !usePoolConnect);
        if (closeableHttpClient == null) {
            httpClient = buildHttpClient(usePoolConnect);
        } else {
            httpClient = closeableHttpClient;
        }
        CloseableHttpResponse response = null;
        Object result = null;
        HttpEntityEnclosingRequestBase httpRequestBase = null;
        try {
            if (HTTP_POST_METHOD.equals(requestMethod)) {
                httpRequestBase = new HttpPost(url);
            } else if (HTTP_PUT_METHOD.equals(requestMethod)) {
                httpRequestBase = new HttpPut(url);
            } else {
                throw new Exception("暂不支持该: " + requestMethod);
            }
            if (headers != null) {
                for (String header : headers.keySet()) {
                    httpRequestBase.setHeader(header, headers.get(header));
                }
            }
            httpRequestBase.addHeader(HttpHeaders.CONNECTION, "close");
            httpRequestBase.removeHeaders(HttpHeaders.CONTENT_LENGTH);
            if (params instanceof Map && !((Map<?, ?>) params).isEmpty()) {
                buildEntity(httpRequestBase, (Map) params, charset);
            } else if (params instanceof List && !((List<?>) params).isEmpty()) {
                buildEntity(httpRequestBase, (List) params, charset);
            } else if (StringUtils.isNoneEmpty(sendStr)) {
                httpRequestBase.setEntity(new StringEntity(sendStr, charset));
            }
            if (requestConfig == null) {
                requestConfig = defaultRequestConfig;
            }
            httpRequestBase.setConfig(requestConfig);
            response = httpClient.execute(httpRequestBase);
            int statusCode = response.getStatusLine().getStatusCode();
            if (REDIRECT_STATUS.contains(statusCode)) {
                return handlerHTTP300(httpRequestBase, httpClient, response, requestConfig, headers, charset);
            } else if (statusCode != 200 && statusCode != 500 && statusCode != 221 && statusCode != 201) {
                httpRequestBase.abort();
                if (statusCode == HttpStatus.SC_SERVICE_UNAVAILABLE || statusCode == HttpStatus.SC_NOT_FOUND) {
                    throw new HttpStatusCodeException(statusCode, "http request had failed , failed statusCode :" + statusCode);
                }
                throw new Exception("http request had failed , failed statusCode :" + statusCode);
            }
            HttpEntity entity = response.getEntity();

            Map<String, String> repHeadersTmp = new HashMap<>();
            Arrays.stream(response.getAllHeaders()).forEach(header -> repHeadersTmp.put(header.getName(), header.getValue()));
            if (repHeaders != null) {
                repHeaders.putAll(repHeadersTmp);
            } else {
                repHeaders = repHeadersTmp;
            }
            if (entity != null) {
                if (!isFile) {
                    result = EntityUtils.toString(entity, charset);
                } else {
                    try (final InputStream stream = entity.getContent()) {
                        return FileUtil.getStreamAsByteArray(stream);
                    }
                }
            }
        } catch (Exception e) {
            log.info("HttpClient Post Error:{}", e.getMessage());
            throw e;
        } finally {
            releaseConnection(response, httpRequestBase, needCloseHttpClient, httpClient);
        }
        logResponse(response, result, url, id);
        return result;
    }

    private static void releaseConnection(CloseableHttpResponse response,
                                          HttpRequestBase httpRequestBase,
                                          boolean needCloseHttpClient,
                                          CloseableHttpClient httpClient) {
        try {
            if (response != null) {
                response.close();
            }
            if (httpRequestBase != null) {
                httpRequestBase.abort();
                httpRequestBase.releaseConnection();
            }
            if (needCloseHttpClient) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("回收httpConnection时发生异常", e);
        }
    }

    private static String handlerHTTP300(HttpRequestBase httpRequestBase, CloseableHttpClient httpClient, CloseableHttpResponse response, RequestConfig requestConfig, Map<String, String> headers, String charset) throws Exception {

        int statusCode = response.getStatusLine().getStatusCode();
        String locationUrl = response.getLastHeader("location").getValue();

        // 处理相对调整场景
        if (locationUrl.startsWith("/")) {
            String host = httpRequestBase.getURI().getHost();
            int port = httpRequestBase.getURI().getPort();
            String scheme = httpRequestBase.getURI().getScheme();
            locationUrl = String.format("%s://%s:%d%s", scheme, host, port, locationUrl);
        }
        log.info("请求{},跳转地址:{}", statusCode, locationUrl);
        if (HttpStatus.SC_TEMPORARY_REDIRECT == statusCode) {
            httpRequestBase.abort();
            return doPost(httpClient, locationUrl, null, null, headers, charset);
        } else {
            httpRequestBase.abort();
            return (String) doGet(httpClient, locationUrl, null, charset, requestConfig, false);
        }
    }

    private static void buildEntity(HttpEntityEnclosingRequestBase http, List<Map<String, String>> params, String charset) throws UnsupportedEncodingException {
        List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
        for (Map<String, String> param : params) {
            for (Map.Entry<String, String> paire : param.entrySet()) {
                nameValuePairs.add(new BasicNameValuePair(paire.getKey(), paire.getValue()));
            }
        }
        http.setEntity(new UrlEncodedFormEntity(nameValuePairs, charset));
    }

    private static void buildEntity(HttpEntityEnclosingRequestBase http, Map<String, Object> params, String charset) throws UnsupportedEncodingException {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        builderPair("", params, nameValuePairs);
        http.setEntity(new UrlEncodedFormEntity(nameValuePairs, charset));
    }

    private static void builderPair(String key, Object params, List<NameValuePair> nameValuePairs) {
        if (params == null) {
            return;
        }
        if (params instanceof List) {
            if (((List<?>) params).isEmpty()) {
                return;
            }
            ((List) params).forEach(item -> {
                if (item instanceof Map) {
                    builderPair("", item, nameValuePairs);
                } else if (item instanceof List) {
                    builderPair(key, item, nameValuePairs);
                } else {
                    nameValuePairs.add(new BasicNameValuePair(key, (String) item));
                }
            });

        } else if (params instanceof Map) {
            if (((Map<?, ?>) params).isEmpty()) {
                return;
            }
            ((Map) params).forEach((paramKey, value) -> {
                if (value instanceof Map) {
                    builderPair("", value, nameValuePairs);
                } else if (value instanceof List) {
                    builderPair((String) paramKey, value, nameValuePairs);
                } else {
                    nameValuePairs.add(new BasicNameValuePair((String) paramKey, (String) value));
                }
            });
        }
    }

    public static String doPostWithRetry(int retryTimes, CloseableHttpClient closeableHttpClient, boolean usePoolConnect, String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey, Map<String, String> repHeaders) throws Exception {
        int index = 1;
        while (index++ <= retryTimes) {
            try {
                String rep = doPostWithRepHeaders(closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, repHeaders);
                // 如果返回内容为空，不返回继续重试
                if (StringUtils.isBlank(rep)) {
                    TimeUnit.MILLISECONDS.sleep(500L);
                    continue;
                }
                return rep;
            } catch (UnknownHostException | ConnectTimeoutException | SocketException | SocketTimeoutException |
                     NoHttpResponseException | HttpStatusCodeException e) {
                log.warn("当前地址:{}的POST请求出现异常,将进行第{}次重试!", url, index);
                TimeUnit.SECONDS.sleep(index * 5L);
            }
        }
        return null;
    }

    public static String doPostWithRetry(int retryTimes, CloseableHttpClient closeableHttpClient, boolean usePoolConnect,
                                         String url, String sendStr, Object params, Map<String, String> headers, String charset, RequestConfig requestConfig, String keepSessionKey) throws Exception {
        return doPostWithRetry(retryTimes, closeableHttpClient, usePoolConnect, url, sendStr, params, headers, charset, requestConfig, keepSessionKey, null);
    }

    public static void doPutRep(String url, ByteArrayEntity byteArrayEntity, Map<String, String> headers) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        HttpEntityEnclosingRequestBase httpRequestBase = null;
        try {
            httpRequestBase = new HttpPut(url);
            if (headers != null) {
                for (String header : headers.keySet()) {
                    httpRequestBase.setHeader(header, headers.get(header));
                }
            }
            httpRequestBase.addHeader(HttpHeaders.CONNECTION, "close");
            httpRequestBase.removeHeaders(HttpHeaders.CONTENT_LENGTH);
            if (byteArrayEntity != null) {
                httpRequestBase.setEntity(byteArrayEntity);
            }
            response = httpClient.execute(httpRequestBase);
            log.info("PUT请求返回结果:{},状态码:{}", response.toString(), response.getStatusLine().getStatusCode());
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                throw new Exception("上传obs失败 , 错误码:" + statusCode);
            }
        } catch (Exception e) {
            log.error("HttpClient PUT Error:{}", e.getMessage());
            throw e;
        } finally {
            releaseConnection(response, httpRequestBase, false, httpClient);
        }
    }
}
