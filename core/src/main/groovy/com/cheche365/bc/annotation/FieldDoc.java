package com.cheche365.bc.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Created by austinChen on 2016/12/14 15:53.
 */
@Target({ElementType.FIELD,ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface FieldDoc {


    /**
     * @return 类型
     */
    String type() default "";


    /**
     * @return 关联的类型
     */
    Class<?>[] relatTypes() default {};

    /**
     * @return 字段含义
     */
    String des() default "";

    /**
     * @return 是否必填，默认否
     */
    boolean need() default false;


    /**
     * @return 备注
     */
    String remark() default "";
}
