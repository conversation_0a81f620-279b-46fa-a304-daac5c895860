package com.cheche365.bc.task;

import java.util.Arrays;
import java.util.List;

/**
 * 保持回话的一些约定配置参数
 *
 * <AUTHOR>
 * @date 2017/11/1
 */
public class KeepSessionConfig {

    /**
     * 保持会话的刷新地址
     */
    public static final String KEEP_SESSION_URL = "keepSessionUrl";
    /**
     * 需要保持会话的账号
     */
    public static final String LOGIN = "login";
    /**
     * 保持会话的刷新间隔
     */
    public static final String REFRESH_MIN = "refreshMin";
    /**
     * 保持会话的起始时间
     */
    public static final String KEEP_SESSION_START_HOUR = "keepSessionStartHour";
    /**
     * 保持会话的结束时间
     */
    public static final String KEEP_SESSION_END_HOUR = "keepSessionEndHour";
    /**
     * 保持会话的窗口数
     */
    public static final String KEEP_SESSION_WIN_NUM = "keepSessionWinNum";
    /**
     * 使用httpClient内置连接池的链接保持会话
     */
    public static final String USE_POOL_CONNECT = "usePoolConnect";
    /**
     * 刷新链接的预期成功标志
     */
    public static final String EXPECT_SUCCESS_FLAG = "expectSuccessFlag";
    /**
     * 保持会话的登录模板名称
     */
    public static final String LOGIN_TEMPLATE_NAME = "loginTemplateName";
    /**
     * 是否显示保持会话的刷新内容
     */
    public static final String SHOW_REFRESH_CONTENT = "showRefreshContent";
    /**
     * 多帐号标志
     */
    public static final String MULTI_ACCOUNT_SYMBOL = "multiAccountSymbol";


    /**
     * 报价分钟限制次数
     */
    public static final String QUOTE_LIMIT = "quoteLimit";

    /**
     * 自动提核分钟限制次数
     */
    public static final String AUTO_INSURE_LIMIT = "autoinsureLimit";

    /**
     * 核保查询限制次数
     */
    public static final String INSURE_QUERY_LIMIT = "insurequeryLimit";
    /**
     * 承保查询限制次数
     */
    public static final String APPROVED_QUERY_LIMIT = "approvedqueryLimit";

    /**
     * 是否只使用新协议（TLSv1.2），解决部分公司使用就协议报错问题
     */
    public static final String USE_NEW_SSL = "useNewSSL";

    /**
     * 使用selenium保持登录获取的页面元素
     */
    public static final String KEEP_SESSION_ELEMENT = "keepSessionElement";
    /**
     * 保持会话方法post,get(默认)
     */
    public static final String KEEP_SESSION_METHOD_TYPE = "keepSessionMethodType";
    /**
     *post请求参数
     */
    public static final String KEEP_SESSION_POST_STRING = "keepSessionPostString";

    public static final String KEEP_SESSION_METHOD_POST = "POST";

    /**
     * 数字类型的配置参数集合
     */
    public static final List<String> KEEP_SESSION_NUMERIC_CONFIGS = Arrays.asList(REFRESH_MIN, KEEP_SESSION_START_HOUR, KEEP_SESSION_END_HOUR, KEEP_SESSION_WIN_NUM, QUOTE_LIMIT, AUTO_INSURE_LIMIT, INSURE_QUERY_LIMIT, APPROVED_QUERY_LIMIT);

}
