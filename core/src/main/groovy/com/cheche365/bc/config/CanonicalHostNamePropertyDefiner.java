package com.cheche365.bc.config;

import ch.qos.logback.core.PropertyDefinerBase;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class CanonicalHostNamePropertyDefiner extends PropertyDefinerBase {

    @Override
    public String getPropertyValue() {
        InetAddress ia;
        try {
            ia = InetAddress.getLocalHost();
            //获取计算机主机名
            return ia.getHostName();
        } catch (UnknownHostException e) {
            log.error("获取主机名失败", e);
        }
        return null;
    }
}
