package com.cheche365.bc.config;

import ch.qos.logback.core.status.Status;
import ch.qos.logback.core.status.StatusListener;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystemException;
import java.util.List;
import java.util.Map;

public class CustomStatusListener implements StatusListener {

    private static final String HOSTNAME = getHostName();

    private static final String MESSAGE_TEMPLATE = "message:{}\nhostname:{}\ntimestamp:{}\nstacktrace:{}";

    private static final String MESSAGE_TEMPLATE_START = "hostname:{}\ntimestamp:{}\nmessage:服务器启动";

    private static final List<String> ERROR_KEYWORDS = Lists.newArrayList();

    private static final String URL;

    private static final String SECRET;

    static {
        //获取环境变量
        URL = System.getenv("feishu.url");
        SECRET = System.getenv("feishu.secret");
        String message = createStartText();
        sendMsg(message);
        ERROR_KEYWORDS.add("No space");
        ERROR_KEYWORDS.add("Disk quota exceeded");
    }

    @Override
    public void addStatusEvent(Status status) {
        if (status.getLevel() == Status.ERROR
            && needSend(status.getThrowable())) {
            String message = createErrorText(status);
            sendMsg(message);
        }
    }

    private boolean needSend(Throwable throwable) {
        if (throwable instanceof FileSystemException) {
            return true;
        }
        if (throwable instanceof IOException) {
            String message = throwable.getMessage();
            return message != null && ERROR_KEYWORDS.stream().anyMatch(message::contains);
        }
        return false;
    }

    private static String createStartText() {
        long timestamp = System.currentTimeMillis();
        //时间
        String datetime = DateTime.of(timestamp).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
        //错误信息
        String text = StrUtil.format(MESSAGE_TEMPLATE_START, HOSTNAME, datetime);
        Map<String, Object> body = createBody(text, timestamp);

        return JSON.toJSONString(body);
    }

    private static Map<String, Object> createBody(String text, long timestamp) {
        Map<String, String> content = Maps.newHashMap();
        content.put("text", text);

        Map<String, Object> body = Maps.newHashMap();
        body.put("timestamp", timestamp);
        body.put("sign", genSign(timestamp, SECRET));
        body.put("msg_type", "text");
        body.put("content", content);
        return body;
    }

    private String createErrorText(Status status) {
        long timestamp = status.getTimestamp();
        //时间
        String datetime = DateTime.of(timestamp).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
        //错误信息
        String message = status.getMessage();
        Throwable throwable = status.getThrowable();
        //堆栈信息
        String stacktraceStr = ExceptionUtil.stacktraceToString(throwable);
        String text = StrUtil.format(MESSAGE_TEMPLATE, message, HOSTNAME, datetime, stacktraceStr);

        Map<String, Object> body = createBody(text, timestamp);
        return JSON.toJSONString(body);
    }

    private static void sendMsg(String message) {
        try {
            HttpSender.doPost(URL, message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String getHostName() {
        //主机名
        String hostName = "";
        try {
            InetAddress ia = InetAddress.getLocalHost();
            hostName = ia.getHostName();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return hostName;
    }

    private static String genSign(long timestamp, String secret) {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        // 2. 初始化 HMac 对象，指定算法为 HmacSHA256
        HMac hmac = new HMac(HmacAlgorithm.HmacSHA256);
        return hmac.digestBase64(stringToSign, StandardCharsets.UTF_8, false);
    }
}
