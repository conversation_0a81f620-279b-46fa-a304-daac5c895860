package com.cheche365.bc.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一系统编码规范说明
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/3/23 17:53.
 */
@Setter
@Getter
public class UniqueException extends Exception {


    /**
     * 非法参数
     */
    public static final int IllegalArgs = 1;

    /**
     * 无效授权
     */
    public static final int IllegalAuth = 3;

    /**
     * 签名有误
     */
    public static final int IllegalSign = 4;

    /**
     * 超过限制次数
     */
    public static final int TimeLimit = 6;

    /**
     * 缺失字段
     */
    public static final int MissingField = 11;

    /**
     * 网络错误
     */
    public static final int NetError = 1003;

    /**
     * 错误代码
     */
    private int code;

    /**
     * 详细的多项错误描述
     */
    private Map<String, Object> remarks = new HashMap<String, Object>();

    /**
     * @param msg 默认提示未非法参数
     */
    public UniqueException(String msg) {
        super(msg);
        this.code = IllegalArgs;
    }

    /**
     * @param code 错误代码
     * @param msg  错误信息
     */
    public UniqueException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public UniqueException(int code, String msg, Map<String, Object> remarks) {
        super(msg);
        this.code = code;
        this.remarks = remarks;
    }
}
