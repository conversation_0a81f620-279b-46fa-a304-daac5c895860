package com.cheche365.bc.exception;

import java.util.HashMap;
import java.util.Map;

/**
 * 保险公司异常
 * <AUTHOR>
 * @Created by austinChen on 2015/12/18 14:19.
 */
public class InsReturnException extends Exception {

    /**
     * 需要重试的异常
     */
    public static final int AllowRepeat=1;

    /**
     * 重复投保
     */
    public static final int RepeatInsure=2;

    /**
     * 超出允许报价的时间
     */
    public static final int OverQuoteTime=3;
    /*
    * 该车型无法自动报价
    * */
    public static final int UnableQuoteVehicle = 4;
    /*
    * 保险公司信息校验不一致
    * */
    public static final int UnconsistentQuoteInfo = 5;
    /*
    * 车辆信息有误
    * */
    public static final int IncorrectVehicleInfo = 6;
    /**
     * 其他错误
     */
    public static final int Others=99;

    private int code=0;
    private Map<String,Object> params=new HashMap<String,Object>();

    /**
     * 退回步数
     */
    private int step=1;

    public InsReturnException(int code,String msg)
    {
        super(msg);
        this.code=code;
    }

    public InsReturnException(int code,String msg,Map params)
    {
        super(msg);
        this.code=code;
        this.params=params;
    }
    public InsReturnException(String msg){super(msg);}

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public int getStep() {
        return step;
    }

    public void setStep(int step) {
        this.step = step;
    }
}
