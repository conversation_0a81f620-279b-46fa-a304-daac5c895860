package com.cheche365.bc.exception;

import com.google.common.base.Strings;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2015/12/27.
 */
@Getter
public enum ExceptionCde {

    /**
     *
     */
    QUOTE_FAILED(0, "报价失败"),
    INSURE_FAILED(1, "核保失败"),
    PAY_FAILED(2, "支付失败"),
    APPROVE_FAILED(3, "承保失败"),
    DATA_DEFECT(4, "数据不完整"),
    DATA_TRANS(5, "数据转换异常"),
    HTTP_REQ(6, "网络请求异常"),
    RENDER(7, "模板渲染异常"),
    PARSE(8, "返回报文解析异常"),
    LOAD_CONF(9, "获取模板配置信息失败"),
    VEHICLE_QUERY_FAILED(10, "车辆查询失败"),
    DUPLICATE_INSURE(12, "重复投保"),
    UNABLE_QUOTE_VEHICLE(11, "该车型无法自动报价"),
    UNCONSISTENT_QUOTE_INFO(13, "与保险公司信息校验不一致"),
    INCORRECT_VEHICLE_INFO(14, "车辆信息可能有误"),
    DUPLICATE_PROPOSAL(15, "重复的投保单"),
    TRAFFICQUERY_ERROR(17, "交管查询失败"),
    RENEWAL_ERROR(18, "续保查询失败"),
    APPROVED_QUERY_PAY_SURE_FAIL(66, "承保查询缴费确认失败"),
    DB_ERROR(6000, "数据库操作失败"),
    FORBIDDEN_OPERATION(4003, "无权限操作"),

    PARAM_ERROR(7000, "参数错误");

    private final Integer cde;

    private final String name;

    ExceptionCde(Integer cde, String name) {
        this.cde = cde;
        this.name = name;
    }

    static final Map<Integer, ExceptionCde> EXCEPTION_MAP = new HashMap<>();

    static {
        EXCEPTION_MAP.put(InsReturnException.RepeatInsure, ExceptionCde.DUPLICATE_INSURE);
        EXCEPTION_MAP.put(InsReturnException.UnableQuoteVehicle, ExceptionCde.UNABLE_QUOTE_VEHICLE);
        EXCEPTION_MAP.put(InsReturnException.UnconsistentQuoteInfo, ExceptionCde.UNCONSISTENT_QUOTE_INFO);
        EXCEPTION_MAP.put(InsReturnException.IncorrectVehicleInfo, ExceptionCde.INCORRECT_VEHICLE_INFO);
    }

    public static ExceptionCde getExceptionCde(Integer cde) {
        return EXCEPTION_MAP.get(cde);
    }

    public static Integer getErrorCdeFromTaskType(String taskType) {
        if (Strings.isNullOrEmpty(taskType)) {
            return null;
        }
        return switch (taskType) {
            case "quote", "approved" -> QUOTE_FAILED.cde;
            case "insure" -> INSURE_FAILED.cde;
            case "pay" -> PAY_FAILED.cde;
            default -> null;
        };
    }
}
