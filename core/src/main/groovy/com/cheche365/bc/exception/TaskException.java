package com.cheche365.bc.exception;

import com.cheche365.bc.task.AutoTask;

/**
 * Created by Administrator on 2016/12/19.
 */
public class TaskException extends Exception {
    private AutoTask task;

    public TaskException(AutoTask task, String message,Throwable cause) {
        super(message,cause);
        this.task = task;
    }
    public TaskException(AutoTask task, String message) {
        super(message);
        this.task = task;
    }
    public TaskException(AutoTask task, Throwable cause) {
        super(cause);
        this.task = task;
    }

    public AutoTask getTask() {
        return task;
    }

    public void setTask(AutoTask task) {
        this.task = task;
    }
}
