package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 需要EDI或者精灵处理的任务
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("auto_task")
public class AutoTask extends Model<AutoTask> {

    private static final long serialVersionUID = 1L;

    @TableId("autoTraceId")
    private String autoTraceId;

    @TableField("actionLogs")
    private String actionLogs;

    @TableField("applyJson")
    private String applyJson;

    @TableField("endTime")
    private LocalDateTime endTime;

    @TableField("company_id")
    private String companyId;

    @TableField("plate_no")
    private String plateNo;

    @TableField("resultStr")
    private String resultStr;

    @TableField("startTime")
    private LocalDateTime startTime;

    @TableField("task_status")
    private String taskStatus;

    @TableField("taskType")
    private String taskType;

    @TableField("traceKey")
    private String traceKey;

    @TableField("feedbackJson")
    private String feedbackJson;

    @TableField("biz_propose_no")
    private String bizProposeNo;

    @TableField("efc_propose_no")
    private String efcProposeNo;

    @TableField("biz_policy_no")
    private String bizPolicyNo;

    @TableField("efc_policy_no")
    private String efcPolicyNo;

    @TableField("failureCause")
    private String failureCause;

    @TableField("failureCauseCategory")
    private String failureCauseCategory;


    @TableField(value = "request_source_id")
    private Integer requestSourceId;

    @TableField(value="internet_sales")
    private Integer internetSales = 0;

    @TableField(value = "source_kind")
    private Integer sourceKind;

    @TableField(value = "vin")
    private String vin;

    @TableField(value = "data_source_log_id")
    private Integer dataSourceLogId;

    @Override
    public Serializable pkVal() {
        return this.autoTraceId;
    }

}
