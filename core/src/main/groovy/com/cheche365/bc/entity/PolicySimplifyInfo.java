package com.cheche365.bc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_simplify_info")
public class PolicySimplifyInfo extends Model<PolicySimplifyInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("plate_no")
    private String plateNo;      // 车牌
    @TableField("com_id")
    private String comId;        // 保险公司ID
    @TableField("account_no")
    private String accountNo;    // 账号
    @TableField("policy_type")
    private String policyType;   // 保单类型 1：单商，2：单交，3.混保
    @TableField("policy_no")
    private String policyNo;     // 保单号
    @TableField("proposal_no")
    private String proposalNo;   // 投保单号
    @TableField("detail_id")
    private String detailId;     // 详情id
    @TableField("href")
    private String href;         // 链接
    @TableField("account_type")
    private String accountType;  // 账号类型（掌中宝:1，易宝通:2,...）
    @TableField("account_area")
    private String accountArea;  // 账号地区
    @TableField("downloaded")
    private Boolean downloaded = false; // 已下载标志
    @TableField("underwrite_date")
    private LocalDate underWriteDate;   //承保日期
    private LocalDateTime createTime;
    @TableField("ext1")
    private String ext1;   // 扩展字段
    @TableField("ext2")
    private String ext2;   // 扩展字段

    public boolean noDownload() {
        return !downloaded;
    }

}
