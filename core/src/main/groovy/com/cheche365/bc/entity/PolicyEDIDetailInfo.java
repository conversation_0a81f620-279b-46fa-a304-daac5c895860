package com.cheche365.bc.entity;

import com.cheche365.bc.model.car.Enquiry;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * edi承保回调表
 */
@Document(collection = "policy_info_edi")
public class PolicyEDIDetailInfo {

    private String _id;

    /*交强 商业 保单号*/
    private String policyNo;

    /*投保单号*/
    private String proposalNo;

    /*账号*/
    private String accountNo;

    /*账号类型*/
    private String accountType; // 账号类型（掌中宝:1，易宝通:2,...）

    /*地区 ID*/
    private String accountArea;

    /*保单类型*/
    private String policyType;   // 保单类型 1：单商，2：单交，3.混保

    /*保险公司 ID*/
    private String comId;

    /*enquiry 类*/
    private Enquiry enquiry;

    /*签单日期*/
    private LocalDate underWriteDate;

    /*回写保单时间*/
    private LocalDateTime sendTime;

    /*保存保单时间*/
    private LocalDateTime createTime;

    /*渠道来源*/
    private String source;

    /*承保日期*/
    private String approvedDate;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getProposalNo() {
        return proposalNo;
    }

    public void setProposalNo(String proposalNo) {
        this.proposalNo = proposalNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountArea() {
        return accountArea;
    }

    public void setAccountArea(String accountArea) {
        this.accountArea = accountArea;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    public Enquiry getEnquiry() {
        return enquiry;
    }

    public void setEnquiry(Enquiry enquiry) {
        this.enquiry = enquiry;
    }

    public LocalDate getUnderWriteDate() {
        return underWriteDate;
    }

    public void setUnderWriteDate(LocalDate underWriteDate) {
        this.underWriteDate = underWriteDate;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(String approvedDate) {
        this.approvedDate = approvedDate;
    }
}
