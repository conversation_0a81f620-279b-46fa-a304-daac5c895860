package com.cheche365.bc.serializer;

import org.nustaq.serialization.FSTConfiguration;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

public class FSTSerializer implements RedisSerializer<Object> {
    public static FSTConfiguration configuration = FSTConfiguration.createDefaultConfiguration();

    static {
        configuration.setStructMode(true);
    }

    @Override
    public byte[] serialize(Object t) throws SerializationException {
        return t == null ? null : configuration.asByteArray(t);
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        return bytes == null ? null : configuration.asObject(bytes);
    }
}
