package com.cheche365.bc.tools;

import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.model.EmailConfig;
import com.google.common.collect.Maps;
import com.sun.mail.imap.IMAPStore;
import jakarta.mail.Flags;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.Session;
import jakarta.mail.search.FlagTerm;
import jakarta.mail.search.SearchTerm;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Setter
@Slf4j
public class EmailUtil {

    private IMAPStore store;
    private Folder inbox;


    public EmailUtil(EmailConfig info) {
        Session session = initSession(info.getHost(), info.getPort());
        try {
            store = (IMAPStore) session.getStore("imap");
            store.connect(info.getHost(), info.getEmail(), info.getPassword());
            if (info.getEmail().contains("163.com")) {
                // 网易官方解答https://www.hmail163.com/content/?404.html
                Map<String, String> netParam = Maps.newHashMap();
                netParam.put("name", info.getEmail());
                netParam.put("version", "1.0.0");
                netParam.put("vendor", "myclient");
                netParam.put("support-email", info.getEmail());
                store.id(netParam);
            }
            inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_WRITE);
        } catch (Exception e) {
            log.error("邮箱初始化异常：{}", ExceptionUtils.getStackTrace(e));
        }
    }

    public  Session initSession(String host, String port) {
        Properties properties = new Properties();
        properties.put(Constants.EMAIL_PROTOCOL_KEY, "imap");
        properties.put(Constants.EMAIL_HOST_KEY, host);
        properties.put(Constants.EMAIL_PORT_KEY, port);
        if (!host.contains("163")) {
            properties.put(Constants.EMAIL_SSL_ENABLE_KEY, Boolean.TRUE.toString());
        }
        properties.put(Constants.EMAIL_FETCHSIZE_KEY, (16 * 1024) + "");
        return Session.getDefaultInstance(properties);
    }

    public  Message[] getAllUnreadMessages() throws Exception {
        if (!inbox.isOpen()) {
            inbox.open(Folder.READ_WRITE);
        }
        SearchTerm searchTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);
        return inbox.search(searchTerm);
    }

    public int getUnreadMessageCount() throws Exception {
        return inbox.getUnreadMessageCount();
    }
    public void close() {
        try {
            inbox.close(false);
            store.close();
        } catch (Exception e) {
            log.error("关闭邮箱异常:{}", ExceptionUtils.getStackTrace(e));
        }
    }
}
