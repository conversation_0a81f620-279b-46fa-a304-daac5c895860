package com.cheche365.bc.tools;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 *         Created by austinChen on 2018/11/29 10:51.
 */
@Slf4j
public class PhoneUtil {

    public static int DianXin=1;
    public static int LianTong=2;
    public static int YiDong=3;
    public static int All=4;

    private static String dianXinTel="133,149,153,173,177,180,181,189,199";
    private static String lianTongTel="130,131,132,145,155,156,166,171,175,176,185,186,166";
    private static String yiDongTel="134,135,136,137,138,139,147,150,151,152,157,158,159,172,178,182,183,184,187,188,198";


    private static String getTelStr(int company)
    {
        String outStr="";
        switch (company)
        {
            case 1:
                outStr=dianXinTel;
                break;
            case 2:
                outStr=lianTongTel;
                break;
            case 3:
                outStr=yiDongTel;
                break;
            default:
                outStr=dianXinTel+","+lianTongTel+","+yiDongTel;
                break;
        }
        return outStr;
    }
    private static String getPrefix()
    {
        return getPrefix(All);
    }

    private static String getPrefix(int company)
    {
        String[] tel = getTelStr(company).split(",");
        String prefix = tel[randomNum(0, tel.length - 1)];
        return prefix;
    }



    //随机产生手机号码
    public static String getMobile(String idCard) {
        return getMobile(All,idCard);
    }

    //随机产生手机号码
    public static String getMobile() {
        return getMobile(All);
    }

    //随机产生手机号码
    public static String getMobile(int company,String idCard) {
        return getMobileByPrefix(getPrefix(company),idCard);
    }





    //随机产生手机号码
    public static String getMobile(int company) {
        return getMobileByPrefix(getPrefix(company));
    }


    //随机产生手机号码
    public static String getMobileByPrefix(String prefix) {
        return getMobileByPrefix(prefix,null);
    }


    //随机产生手机号码
    public static String getMobileByPrefix(String prefix,String idCard) {


        if(StringUtils.isNotEmpty(prefix)&&prefix.length()==3)
        {

        }
        else
        {
            prefix=getPrefix();
        }
        String suffix="";

        if(StringUtils.isNotEmpty(idCard)&&idCard.length()==18)
        {
            suffix=idCard.substring(9,17);
        }
        else
        {
            String time=DateUtil.d2SSecond(new Date());
            suffix=time.substring(6);
        }

        return prefix+suffix;

    }
    public static String getMobileByConfig(Map<String,Object> config,int idCardType,String idCard,String defaultPhone)
    {
        //mobileRandom true 为随机号码
       // mobileType 1 为取身份证后8位，不配置或者0为取时间到秒
       // mobilePrefix 手机号码前缀。如果不配置，随机从运营商号段里面随机取3位。如果有配置号码，根据号段取
        if("true".equals(config.getOrDefault("mobileRandom","false").toString()))
        {
            return getMobileByConfig(config.getOrDefault("mobileType","0").toString(),config.getOrDefault("mobilePrefix","").toString(),idCardType,idCard);
        }
        else
        {
            if(StringUtils.isNotEmpty(defaultPhone)&&defaultPhone.length()==11)
            {
                return defaultPhone;
            }
            else
            {
                return getMobile();
            }
        }
    }

    public static String getMobileByConfig(String config,String prefix,int idCardType,String idCard)
    {
        if(0!=idCardType)
        {
            idCard=null;
        }

        if("1".equals(config))
        {
            return getMobileByPrefix(prefix,idCard);
        }
        else
        {
            return getMobileByPrefix(prefix,null);
        }
    }


    //产生指定范围内的随机数
    public static int randomNum(int start, int end) {
        return (int) (Math.random() * (end - start));
    }
}
