package com.cheche365.bc.tools;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 地区工具
 *
 * <AUTHOR>
 * @Created by austinChen on 2015/12/20 17:42.
 */
@Slf4j
public final class AreaUtil {

    private static Map areas = null;

    static {
        String name = "/region.json";
        String res = FileUtil.readResource(name, AreaUtil.class);
        if (!res.isEmpty()) {
            areas = MapUtil.toMap(res);
        } else {
            log.error("读取行政区划城市文件出错");
        }
    }

    public static String getPrvCode(String code) {
        return code.substring(0, 2) + "0000";
    }

    private static Map getNextMap(Map map) {
        if (map.containsKey("regions")) {
            return (Map) map.get("regions");
        } else {
            return new HashMap();
        }
    }

    /**
     * 模板里面使用
     *
     * @param code
     * @return
     */
    public static String getCnName(String code) {
        StringBuffer sb = new StringBuffer();
        Map tmp = null;
        String prv = code.substring(0, 2) + "0000";
        tmp = (Map) areas.get(prv);
        sb.append(tmp.get("name"));
        if (!code.endsWith("0000")) {
            String city = code.substring(0, 4) + "00";
            tmp = getNextMap(tmp);
            sb.append(((Map) tmp.get(city)).get("name"));
            if (!code.endsWith("00")) {
                tmp = getNextMap((Map) tmp.get(city));
                sb.append(((Map) tmp.get(code)).get("name"));
            }
        }
        return sb.toString();

    }

    /**
     * 模板里面使用
     *
     * @param address
     * @return
     */
    public static Map getAddressCode(String address) {
        Map insArea = new HashMap();
        String province = "";
        String city = "";
        String area = "";

        province = getCodeForCn(address, areas);
        insArea.put("province", province);

        Map cityMap = (Map) ((Map) areas.get(province)).get("regions");
        city = getCodeForCn(address, cityMap);
        insArea.put("city", city);

        Map areaMap = (Map) ((Map) cityMap.get(city)).get("regions");
        area = getCodeForCn(address, areaMap);
        insArea.put("area", area);
        return insArea;
    }

    public static String getCodeForCn(String address, Map regions) {
        Iterator entries = regions.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry entry = (Map.Entry) entries.next();
            Map value = (Map) entry.getValue();
            String name = (String) value.get("name");
            if (address.indexOf(name) >= 0) {
                return (String) entry.getKey();
            }
        }
        return null;
    }
}
