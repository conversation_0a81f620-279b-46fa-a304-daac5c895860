package com.cheche365.bc.tools;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 时间工具
 * <AUTHOR>
 * @Created by austinChen on 2015/12/18 15:45.
 */
public final class DateUtil {

    /**
     * 默认的构造函数
     */
    private DateUtil(){

    }
    public static boolean is3Month(String begin) throws Exception
    {
        return diffMonth(begin)>3;
    }

    public static boolean is1Month(String begin) throws Exception
    {

         return diffMonth(begin)>1;
    }

    public static int diffMonth(Date begin) throws  Exception
    {
        return diffMonth(dateThreadLocal.get().format(begin),true);
    }

    public static int diffMonth(String begin) throws  Exception
    {
        return diffMonth(begin,true);
    }

    public static int diffMonth(String begin,boolean moreAddOne) throws  Exception
    {
        String end=dateThreadLocal.get().format(new Date());
        return diffMonth(begin,end,moreAddOne);
    }

    public static int diffMonth(String begin,String end) throws Exception {
        return diffMonth(begin,end,true);
    }

    /**
     * 计算月份差
     * @param begin 开始时间
     * @param end 结束时间
     * @return 计算月份差
     * @throws Exception
     */
    public static int diffMonth(String begin,String end,boolean moreAddOne) throws Exception {
        int result = 0;

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        c1.setTime(dateThreadLocal.get().parse(begin));
        c2.setTime(dateThreadLocal.get().parse(end));

        int days = c2.get(Calendar.DAY_OF_MONTH) - c1.get(Calendar.DAY_OF_MONTH);

        result = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);
        int year = c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR);

        result= result == 0 ? year*12 : year*12+result;
        if(moreAddOne) {
            return days > 0 ? result + 1 : result;
        }
        else {
            return days <= 0 ? (result > 1 ? result - 1 : 0) : result;
        }

    }

    public static int diffDay(String start) throws  Exception{
        String end=dateThreadLocal.get().format(new Date());
        return diffDay(start,end);
    }

    public static int diffDay(Date begin,Date end)  throws Exception {
        Calendar c = Calendar.getInstance();
        c.setTime(begin);
        long beginTime = c.getTimeInMillis();
        c.setTime(end);
        long endTime = c.getTimeInMillis();
        long betweenDays=(endTime-beginTime)/(1000*3600*24);
        return Integer.parseInt(String.valueOf(betweenDays));

    }

    public static int diffDay(String begin,String end)  throws Exception {
        return diffDay(dateThreadLocal.get().parse(begin), dateThreadLocal.get().parse(end));
    }
    public static int diffSecs(Date begin) throws  Exception
    {
        return diffSecs(begin,new Date());
    }

    public static int diffSecs(Date begin,Date end) throws  Exception
    {
        long betweenDays=diffMillis(begin,end)/(1000);
        return Integer.parseInt(String.valueOf(betweenDays));
    }
    public static int diffMillis(Date begin) throws  Exception
    {
        return diffMillis(begin,new Date());
    }

    public static int diffMillis(Date begin,Date end)  throws Exception {
        Calendar c = Calendar.getInstance();
        c.setTime(begin);
        long beginTime = c.getTimeInMillis();
        c.setTime(end);
        long endTime = c.getTimeInMillis();
        return (int)(endTime-beginTime);

    }

    /**
     * @param map 需要放入时间的Map
     * @param key 要放入的key
     * @return
     */
    public static String defaultEffectiveDate(Map map, String key) {
        if (map.containsKey(key)){
            return d2S(getDate(map, key));
        }
        return d2S(addDay(getDate(map, key), 1));
    }

    /**
     * 默认初登
     * @param map
     * @param key
     * @return
     */
    public static String defaultFirstRegDate(Map map, String key) {
        if (map.containsKey(key)){
            return d2S(getDate(map, key));
        }
        return d2S(getDate(map, key));
    }

    /**
     * 默认的短信过期时间为7天
     * @param map
     * @param key
     * @return
     */
    public static String defaultSmsExpireTime(Map map, String key) {
        if (map.containsKey(key)){
            return d2S(getDate(map, key));
        }
        return d2S(addDay(getDate(map, key), 7));
    }

    /**
     * @param date 起保时间
     * @return 返回+1年-1天的时间
     */
    public static String defaultExpireDate(Date date) {
        return d2ES(addDay(addYear(date, 1), -1));
    }

    /**
     * @param date 起保时间
     * @return 返回+1年-1天的时间
     */
    public static String defaultExpireDate(String date) {
        Date tmp=null;
        try {
             tmp=dateThreadLocal.get().parse(date);
        } catch (Exception ex) {
            tmp=new Date();
        }
        return d2ES(addDay(addYear(tmp, 1), -1));
    }

    /**
     * @param map 默认的保险截止时间
     * @param key  如果有就用。没有就自动计算当天+1年-1天
     * @return 返回+1年-1天的时间
     */
    public static String defaultExpireDate(Map map, String key) {
        return d2ES(addDay(addYear(getDate(map, key), 1), -1));
    }

    private static ThreadLocal<DateFormat> secThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMddHHmmss");
        }
    };

    private static ThreadLocal<DateFormat> minuteThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMddHHmm");
        }
    };

    private static ThreadLocal<DateFormat> millisThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMddHHmmssSSS");
        }
    };

    private static ThreadLocal<DateFormat> dateThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        }
    };

    private static ThreadLocal<DateFormat> expDataThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        }
    };

    private static ThreadLocal<DateFormat> dateDayThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };

    private static ThreadLocal<DateFormat> date24ThreadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */
    public static String d2S24H(Date date) {
        return date24ThreadLocal.get().format(date);
    }

    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */



    public static String d2SJustDay(Date date) {
        return dateDayThreadLocal.get().format(date);
    }

    /**
     * @return yyyyMMddHHmmssSSS
     */
    public static String d2SMillis() {
        return d2SMillis(new Date());
    }


    /**
     * @return yyyyMMddHHmmss
     */
    public static String d2SSecond() {
        return d2SSecond(new Date());
    }
    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */
    public static String d2SSecond(Date date) {
        return secThreadLocal.get().format(date);
    }


    /**
     * @return yyyyMMddHHmm
     */
    public static String d2SMinute() {
        return d2SMinute(new Date());
    }
    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */
    public static String d2SMinute(Date date) {
        return minuteThreadLocal.get().format(date);
    }


    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */
    public static String d2SMillis(Date date) {
        return millisThreadLocal.get().format(date);
    }

    /**
     * @param date 时间对象
     * @return 保险起期字符串  yyyy-MM-dd 00:00:00
     */
    public static String d2S(Date date) {
        return dateThreadLocal.get().format(date);
    }

    /**
     * @param date 时间对象
     * @return 保险止期字符串  yyyy-MM-dd 23:59:59
     */
    public static String d2ES(Date date) {
        return expDataThreadLocal.get().format(date);
    }

    public static Date s2D(String date) throws  Exception{
        return dateThreadLocal.get().parse(date);
    }

    /**
     * @param date 时间对象
     * @param days 增加的天数
     * @return 增加天数后的时间对象
     */
    public static synchronized Date addDay(Date date, int days) {
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);
        cd.add(Calendar.DATE, days);
        return cd.getTime();
    }

    /**
     * @param date 时间对象
     * @param year 增加的年份
     * @return 增加年份后的时间对象
     */
    public static synchronized Date addYear(Date date, int year) {
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);
        cd.add(Calendar.YEAR, year);
        return cd.getTime();
    }

    /**
     * @param map 需要取的Map
     * @param key map中的时间key
     * @return 时间格式化为 yyyy-MM-dd 00:00:00
     */
    public static Date getDate(Map map, String key) {
        try {
            if (map.containsKey(key)){
                return dateThreadLocal.get().parse(map.get(key).toString());
            }
            else{
                return new Date();
            }
        } catch (Exception ex) {
            return new Date();
        }
    }
}
