package com.cheche365.bc.tools;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.utils.Util;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.encrypt.MD5;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StringUtil {
    static Map<String, Integer> numberMap = new HashMap<String, Integer>();
    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";
    static Map<String, Character> operatorMap = new HashMap<String, Character>();

    static {
        numberMap.put("0", 0);
        numberMap.put("零", 0);
        numberMap.put("1", 1);
        numberMap.put("一", 1);
        numberMap.put("壹", 1);
        numberMap.put("2", 2);
        numberMap.put("二", 2);
        numberMap.put("贰", 2);
        numberMap.put("3", 3);
        numberMap.put("三", 3);
        numberMap.put("叁", 3);
        numberMap.put("4", 4);
        numberMap.put("四", 4);
        numberMap.put("肆", 4);
        numberMap.put("5", 5);
        numberMap.put("五", 5);
        numberMap.put("伍", 5);
        numberMap.put("6", 6);
        numberMap.put("六", 6);
        numberMap.put("陆", 6);
        numberMap.put("7", 7);
        numberMap.put("七", 7);
        numberMap.put("柒", 7);
        numberMap.put("8", 8);
        numberMap.put("八", 8);
        numberMap.put("捌", 8);
        numberMap.put("9", 9);
        numberMap.put("九", 9);
        numberMap.put("玖", 9);
        numberMap.put("10", 10);
        numberMap.put("十", 10);
        numberMap.put("拾", 10);

        operatorMap.put("+", '+');
        operatorMap.put("加", '+');
        operatorMap.put("-", '-');
        operatorMap.put("减", '-');
        operatorMap.put("*", '*');
        operatorMap.put("乘", '*');
        operatorMap.put("/", '/');
        operatorMap.put("除", '/');
    }

    public static String file2String(File file) {
        Long size = file.length();
        byte[] buff = new byte[size.intValue()];
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            is.read(buff);
            return new String(buff, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static String parseTbCaptchaAnswer(String yzm) {
        String[] strs = yzm.split("请选择：");
        int sum = 0;
        if (strs.length == 2) {
            String problem = strs[0].trim();
            int length = problem.length();
            int number1 = 0;
            int number2 = 0;
            char operator = 0;
            boolean b = false;
            for (int i = 0; i < length; i++) {
                String key = String.valueOf(problem.charAt(i));
                if (numberMap.get(key) == null && operatorMap.get(key) == null) {
                    break;
                }

                if (numberMap.get(key) != null) {
                    int temp = numberMap.get(key);
                    int tempSum = b ? number2 : number1;
                    if (temp == 10) {
                        tempSum = tempSum == 0 ? temp : tempSum * 10;
                    } else {
                        tempSum = tempSum > 9 ? tempSum + temp : tempSum * 10 + temp;
                    }

                    if (b) {
                        number2 = tempSum;
                    } else {
                        number1 = tempSum;
                    }

                } else {
                    operator = operatorMap.get(key);
                    b = true;
                }

            }
            sum = operator(number1, operator, number2);
            String[] answers = strs[1].trim().split(";");
            length = answers.length;
            for (int i = 0; i < length; i++) {
                String answer = answers[i].trim();
                String abs = "+";
                int number = 0;
                for (int j = 1; j < answer.length(); j++) {
                    String temp = String.valueOf(answer.charAt(j));
                    if (" ".equals(temp)) {
                        continue;
                    }
                    if ("-".equals(temp) || "负".equals(temp)) {
                        abs = "-";
                        continue;
                    }
                    int tempNumber = numberMap.get(temp);
                    if (tempNumber == 10) {
                        number = number == 0 ? tempNumber : number * tempNumber;
                    } else {
                        number = number > 9 ? tempNumber + number : number * 10 + tempNumber;
                    }
                }
                if ("-".equals(abs)) {
                    number = number * -1;
                }
                if (sum == number) {
                    return answer.substring(0, 1);
                }
            }
        }
        return "";
    }

    private static int operator(int a, char opeartor, int b) {
        int sum = 0;
        switch (opeartor) {
            case '+':
                sum = a + b;
                break;
            case '-':
                sum = a - b;
                break;
            case '*':
                sum = a * b;
                break;
            case '/':
                sum = a / b;
                break;
        }
        return sum;
    }

    public static boolean isEmpty(String str) {
        if (str == null || "".equals(str) || "null".equalsIgnoreCase(str)) {
            return true;
        }
        return false;
    }

    public static boolean isNoEmpty(String str) {
        return !isEmpty(str);
    }

    /*都不为空时返回true*/
    public static boolean areNotEmpty(String... str) {
        for (String val : str) {
            if (isEmpty(val)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param str     需要处理的字符串
     * @param formate 自定义的规则 如:formate="indexOf:dfdf;length:5;get:[a-zA-z0-9]"
     * @return
     */
    public static String getStringByFormate(String str, String formate) {
        String[] formates = formate.split(";");
        String billo = str == null ? "" : str;
        for (int i = 0; i < formates.length; i++) {
            int spilt = formates[i].indexOf(":");
            String formate_temp = formates[i].substring(0, spilt);
            String result_temp = formates[i].substring(spilt + 1);
            if ("indexOf".equalsIgnoreCase(formate_temp)) {
                billo = billo.substring(billo.indexOf(result_temp) + result_temp.length());
                continue;
            }
            if ("length".equalsIgnoreCase(formate_temp)) {
                billo = billo.substring(0, Integer.parseInt(result_temp));
                continue;
            }
            if ("get".equalsIgnoreCase(formate_temp)) {
                billo = getString(billo, result_temp, true);
            }
        }
        return billo.toString();
    }

    /**
     * @param str     需要处理的字符串
     * @param matches 通过验证的正则表达式
     * @param isLink  是否获取连续的字符串
     * @return
     */
    public static String getString(String str, String matches, boolean isLink) {
        str = str.trim();
        int length = str.length();
        String result = "";
        boolean isLink_temp = false;
        for (int i = 0; i < length; i++) {
            String temp = str.substring(i, i + 1);
            if (temp.matches(matches)) {
                if (isLink) {
                    if (isLink_temp || "".equals(result)) {
                        result += temp;
                    } else {
                        break;
                    }
                } else {
                    result += temp;
                }
                isLink_temp = true;
            } else {
                isLink_temp = false;
            }
        }
        return result.trim();
    }

    /**
     * 通过指定的规则判断某值在某范围 如>=0.05&&<=8
     *
     * @param str   = >=0.05&&<=8
     * @param value
     * @return
     */
    public static boolean compareByFormate(String str, float value) {
        String[] formates = null;
        int type = 0;
        if (str.indexOf("&&") != -1) {
            formates = str.split("&&");
            type = 1;
        } else if (str.indexOf("||") != -1) {
            formates = str.split("||");
            type = 2;
        } else {
            formates = new String[1];
            formates[0] = str;
        }

        boolean flags[] = new boolean[2];
        for (int i = 0; i < formates.length; i++) {
            String formate = formates[i];
            String number = getString(formate, "[-0-9.]", true);
            String key = formate.substring(0, formate.length() - number.length());
            flags[i] = parseSwitch(value, Float.parseFloat(number), key);
        }
        if (formates.length == 1) {
            return flags[0];
        } else {
            if (type == 1) {
                return flags[0] && flags[1];
            } else {
                return flags[0] || flags[1];
            }
        }
    }

    private static Map<String, Integer> typesMap = new HashMap<String, Integer>();

    static {
        typesMap.put(">", 1);
        typesMap.put("<", 2);
        typesMap.put("=", 3);
        typesMap.put(">=", 4);
        typesMap.put("<=", 5);
    }

    private static boolean parseSwitch(float value, float key, String types) {
        boolean flag = false;
        int type = 0;
        type = typesMap.get(types);
        switch (type) {
            case 1:
                if (value > key) {
                    flag = true;
                }
                break;
            case 2:
                if (value < key) {
                    flag = true;
                }
                break;
            case 3:
                if (value == key) {
                    flag = true;
                }
            case 4:
                if (value >= key) {
                    flag = true;
                }
                break;
            case 5:
                if (value <= key) {
                    flag = true;
                }
                break;
        }
        return flag;
    }

    /**
     * 根据字段名从类获取其值
     *
     * @param obj
     * @param name
     * @return
     */
    public static Object field(Object obj, String name) {
        Object t_obj = null;
        try {
            Field field = obj.getClass().getDeclaredField(name);
            field.setAccessible(true);
            t_obj = field.get(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t_obj;
    }

    /**
     * 单位倍数转换
     *
     * @param initial 原始值
     * @param mult    转换倍数
     * @return 计算结果字符串返回
     */
    public static String convertUnit(String initial, double mult) {
        try {
            return "" + Double.parseDouble(initial) * mult;
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 毫秒数转化为日期格式
     *
     * @param milli 毫秒数
     * @return 日期(yyyy - MM - dd)
     */
    public static String convertMilliseconds(String milli) {
        String dateStr = "";
        if (isNoEmpty(milli)) {
            dateStr = parseDate(new Date(Long.parseLong(milli)), "yyyy-MM-dd HH:mm:ss");
        }
        return dateStr;
    }

    /**
     * 日期(date)转化为字符串
     *
     * @param date   日期
     * @param format 转化格式
     * @return 转化后日期字符串
     */
    public static String parseDate(Date date, String format) {
        return Objects.nonNull(date) ? LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern(format)) : "";

    }

    // 计算加密信息
    public static String calcEncInfo(String preauth, String softKey, String uname, String upwd) {
        // 压缩软件KEY为8字节，用作DES加密的KEY
        byte[] key16 = hexString2ByteArray(softKey);
        byte[] key8 = new byte[8];
        for (int i = 0; i < 8; i++) {
            key8[i] = (byte) ((key16[i] ^ key16[i + 8]) & 0xff);
        }

        String pwd_md5_str = MD5.toHex(upwd); // 转为16进制字符串
        String enc_data_str = preauth + "\n" + uname + "\n" + pwd_md5_str;
        String key = Base64.getEncoder().encodeToString(key8);

        try {
            AesDesEncryption build = AesDesEncryption.builder()
                    .key(key)
                    .keyFormat(EncryptEnum.KeyFormatEnum.Base64)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.DES)
                    .build();

            return Hex.encodeToString(Base64.getDecoder().decode(build.encrypt(enc_data_str)));
        } catch (Exception e) {
            log.error("StringUtil encrypt error, source str:{}，error:{}", enc_data_str, Util.getStackTrace(e));
        }
        return null;
    }

    //鼎和MD5加密操作
    public static String md5Turning4dinghe(String str) {
        StringBuffer buf = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update((str).getBytes("UTF-8"));
            byte b[] = md5.digest();

            int i;
            buf = new StringBuffer("");

            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0) {
                    i += 256;
                }
                if (i < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(i));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return buf.toString();
    }

    //太平MD5
    public static String MD5(String sourceStr) {
        try {
            // 获得MD5摘要算法的 MessageDigest对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(sourceStr.getBytes());
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            StringBuffer buf = new StringBuffer();
            for (int i = 0; i < md.length; i++) {
                int tmp = md[i];
                if (tmp < 0) {
                    tmp += 256;
                }
                if (tmp < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(tmp));
            }
            //return buf.toString().substring(8, 24);// 16位加密
            return buf.toString();// 32位加密
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 16进制字符串转为BYTE数组
    public static byte[] hexString2ByteArray(String hexStr) throws NumberFormatException {
        int len = hexStr.length();
        if (len % 2 != 0) {
            throw new NumberFormatException();
        }

        byte[] result = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            String s = hexStr.substring(i, i + 2);
            int n = Integer.parseInt(s, 16);
            result[i / 2] = (byte) (n & 0xff);
        }

        return result;
    }

    // 转化BYTE数组为16进制字符串
    public static String byteArray2HexString(byte[] data) {
        StringBuilder sb = new StringBuilder();
        for (byte b : data) {
            String s = Integer.toHexString(b & 0xff);
            if (s.length() == 1) {
                sb.append("0" + s);
            } else {
                sb.append(s);
            }
        }
        return sb.toString();
    }

    /**
     * 将post数据或者get数据的输出流，转成httpClient的form的条件xml
     *
     * @param data ADAPTER_TYPE=JSON_TYPE >>>><input name='ADAPTER_TYPE'
     *             value='JSON_TYPE' />
     * @return
     */
    public List<String> parseUrlParamsToParamsXml(String data) {
        String[] datas = data.split("&");
        for (int i = 0; i < datas.length; i++) {
            String[] tempNameAndValue = datas[i].split("=");
            String result = "";
            if (tempNameAndValue.length > 1) {
                result = "<input name='" + tempNameAndValue[0] + "' value='" + tempNameAndValue[1] + "' />";
                System.out.println(result);
            } else {
                result = "<input name='" + tempNameAndValue[0] + "' value='' />";
                System.out.println(result);
            }

        }
        return null;
    }

    /**
     * 将中文转化为前缀带"%u"的unicode格式
     *
     * @param strText 中文
     * @return
     */
    public static String string2HexString(String strText) {
        char c;
        String strRet = "";
        int intAsc;
        String strHex;
        for (int i = 0; i < strText.length(); i++) {
            c = strText.charAt(i);
            intAsc = (int) c;
            if (intAsc > 128) {
                strHex = Integer.toHexString(intAsc);
                strRet += "%u" + strHex;
            } else {
                strRet = strRet + c;
            }
        }
        return strRet;
    }

    /**
     * 将前缀带"%u"的unicode格式转化为中文
     *
     * @return
     */
    public static String unicodeToString(String str) {
        Pattern pattern = Pattern.compile("(%u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        try {
            str = URLDecoder.decode(str, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return str;
    }

    public static String Unicode2String(String utfString) {
        StringBuilder sb = new StringBuilder();
        int i = -1;
        int pos = 0;

        while ((i = utfString.indexOf("\\u", pos)) != -1) {
            sb.append(utfString.substring(pos, i));
            if (i + 5 < utfString.length()) {
                pos = i + 6;
                sb.append((char) Integer.parseInt(utfString.substring(i + 2, i + 6), 16));
            }
        }
        sb.append(utfString.substring(pos));
        return sb.toString();
    }

    /**
     * 将中文转换成带\\u的格式
     *
     * @return
     */
    public static String chinesetoUnicode(String str) {
        StringBuffer sb = new StringBuffer();
        char[] charArr = str.toCharArray();
        for (char ch : charArr) {
            if (ch > 127) {
                sb.append("\\u" + Integer.toHexString(ch));
            } else {
                sb.append(ch);
            }
        }
        return sb.toString();
    }

    /**
     * 将前缀"&#"带十进制unicode转换中文
     *
     * @param dataStr
     * @return
     */
    public static String decodeUnicode(final String dataStr) {
        String regExp = "&#\\d*;";
        Matcher m = Pattern.compile(regExp).matcher(dataStr);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            String s = m.group(0);
            s = s.replaceAll("(&#)|;", "");
            char c = (char) Integer.parseInt(s);
            m.appendReplacement(sb, Character.toString(c));
        }
        m.appendTail(sb);
        // System.out.println(sb.toString());
        return sb.toString();
    }

    public static String urlDecode(String dataStr, String charset) throws UnsupportedEncodingException {
        return URLDecoder.decode(dataStr, charset);
    }

    // 首字母转小写
    public static String toLowerCaseFirstOne(String s) {
        if (Character.isLowerCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    // 首字母转大写
    public static String toUpperCaseFirstOne(String s) {
        if (Character.isUpperCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    //日期格式转换
    public static String dateFormat(Date date, String formatStr) {
        if (StringUtils.isBlank(formatStr))
            formatStr = "yyyy-MM-dd HH:mm:ss";
        return parseDate(date, formatStr);
    }

    //true 为空 false 不为空
    public static boolean checkjsonValueEmpty(JSONObject json, String key) {
        boolean result = false;
        if (json == null || json.isEmpty()) {
            result = true;
        }

        if (!json.containsKey(key) || json.get(key) == null || "".equals(json.get(key))) {
            result = true;
        }

        return result;
    }

    public static String getCarCode(String carCode) {
        StringBuffer code = new StringBuffer("");
        if (StringUtil.isNoEmpty(carCode)) {
            char[] codeArr = carCode.toCharArray();
            for (char temp : codeArr) {
                int acs = Integer.valueOf(temp);
                if (acs >= 65 && acs <= 90 || acs >= 97 && acs <= 122 || acs >= 48 && acs <= 57) {
                    code.append(temp);
                }
            }
        }
        return code.toString();
    }

    //随机产生手机号码
    public static String getMobile() {
        String[] tel = "134,135,136,137,138,139,150,151,152,157,158,159,130,131,132,155,156,133,153".split(",");
        String first = tel[intNum(0, tel.length - 1)];
        String second = String.valueOf(intNum(1, 888) + 10000).substring(1);
        String third = String.valueOf(intNum(1, 9100) + 10000).substring(1);
        return first + second + third;
    }

    //产生指定范围内的随机数
    public static int intNum(int start, int end) {
        return (int) (Math.random() * (end - start));
    }

    //太平加千位符
    private static String getqwf(String sz) throws Exception {
        sz = sz.replace(",", "");
        String str = "";
        String ls_return = "";
        if (sz.indexOf(".") > -1) {
            str = sz.substring(sz.indexOf(".") + 1);
            sz = sz.substring(0, sz.indexOf("."));
        }
        if (sz.length() > 3) {
            sz = new StringBuilder(sz).reverse().toString();
            ls_return = sz.replaceAll("[\\w]{3}", "$0,");
            ls_return = new StringBuilder(ls_return).reverse().toString();
            if (ls_return.substring(0, 1).equals(",")) {
                ls_return = ls_return.substring(1);
            }
        } else {
            ls_return = sz;
        }
        if (StringUtil.isNoEmpty(str)) {
            ls_return = ls_return + "." + str;
        }
        return ls_return;
    }


    public static String FormatReplace(String ls_html) {
        boolean lb_replace = true;
        int li_flag = -1;
        //32	(space)	空格  [ ]
        //9	    水平制表符   [\t]
        //34    "   [\"]
        //39    '   [']
        StringBuilder ls_temp = new StringBuilder("");
        for (int i = 0; i < ls_html.length(); i++) {
            char item = ls_html.charAt(i);
            int li_ASCII = (int) item;
            String ls_item = "";
//            System.out.println(i+" = ["+item+"]"+"ASCII["+(int)item+"]");
            if (li_ASCII == 32 || li_ASCII == 9) {
                if (lb_replace) {
                    ls_item = "";
                } else {
                    ls_item = String.valueOf(item);
                }
            } else {
                ls_item = String.valueOf(item);
                if (li_ASCII == 34) {
                    if (li_flag < 0 && lb_replace) {
                        li_flag = 34;
                        lb_replace = false;
                    } else if (li_flag == 34 && !lb_replace) {
                        li_flag = -1;
                        lb_replace = true;
                    }
                } else if (li_ASCII == 39) {
                    if (li_flag < 0 && lb_replace) {
                        li_flag = 39;
                        lb_replace = false;
                    } else if (li_flag == 39 && !lb_replace) {
                        li_flag = -1;
                        lb_replace = true;
                    }
                }
            }
            ls_temp.append(ls_item);
        }
        return ls_temp.toString();
    }


    /**
     * 按表达式regex匹配字符串
     */
    public static List<String> stringMatcher(String data, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);
        List<String> list = Lists.newArrayList();
        while (matcher.find()) {
            list.add(matcher.group());
        }
        return list;
    }

    /**
     * 删除报文中的换行、空格
     *
     * @param data 报文
     * @return 处理后的报文
     */
    public static String deleteSpace(String data) {
        return data.replaceAll("\n|\\s", "").trim();
    }

    /**
     * 将存在于字符串target中的list中的字符串片段逐一替换为replacement
     */
    public static String replaceString(String target, List<String> list, String replacement) {
        if (StringUtils.isNotBlank(target) && CollUtil.isNotEmpty(list)) {
            target = list
                    .stream()
                    .reduce(
                            target,
                            (str, toRep) -> str.replace(toRep, replacement)
                    );
        }
        return target;
    }

    public static boolean isAlphanumeric(String str) {
        Pattern ALPHANUMERIC_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return ALPHANUMERIC_PATTERN.matcher(str).matches();
    }

    public static boolean isValidEmail(String email) {
        return email != null && email.matches(EMAIL_REGEX);
    }
}
