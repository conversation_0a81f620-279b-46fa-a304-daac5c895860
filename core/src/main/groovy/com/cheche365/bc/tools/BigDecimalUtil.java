package com.cheche365.bc.tools;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;

@Slf4j
public class BigDecimalUtil {

    static Map<String, Character> operatorMap = new HashMap<String, Character>();

    static {
        operatorMap.put("+", '+');
        operatorMap.put("加", '+');
        operatorMap.put("-", '-');
        operatorMap.put("减", '-');
        operatorMap.put("*", '*');
        operatorMap.put("乘", '*');
        operatorMap.put("/", '/');
        operatorMap.put("除", '/');
    }

    /**
     * 加法
     *
     * @param arg1
     * @param arg2
     * @param scale 保留的小数点
     * @return
     */
    public static String add(String arg1, String arg2, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        if (null == arg1 || "".equals(arg1) || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "".equals(arg2) || "null".equals(arg1)) {
            arg2 = "0";
        }
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(arg1).add(new BigDecimal(arg2)));
    }

    /**
     * 默认保留两位小数点
     */
    public static String add(String arg1, String arg2) {
        return add(arg1, arg2, 2);
    }

    /**
     * 减法
     *
     * @param arg1
     * @param arg2
     * @param scale 保留小数点
     * @return
     */
    public static String minus(String arg1, String arg2, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        if (null == arg1 || "".equals(arg1) || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "".equals(arg2) || "null".equals(arg1)) {
            arg2 = "0";
        }
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(arg1).subtract(new BigDecimal(arg2)));
    }

    /**
     * 乘法
     *
     * @param arg1
     * @param arg2
     * @param scale
     * @return
     */
    public static String multi(String arg1, String arg2, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        if (null == arg1 || "".equals(arg1) || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "".equals(arg2) || "null".equals(arg1)) {
            arg2 = "0";
        }
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(arg1).multiply(new BigDecimal(arg2)));
    }

    /**
     * 除法
     *
     * @param arg1
     * @param arg2
     * @param scale
     * @return
     */
    public static String divide(String arg1, String arg2, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        if (null == arg1 || "".equals(arg1) || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "".equals(arg2) || "null".equals(arg1)) {
            arg2 = "0";
        }
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(arg1).divide(new BigDecimal(arg2)));
    }

    /**
     * 默认保留两位小数点
     */
    public static String minus(String arg1, String arg2) {
        return minus(arg1, arg2, 2);
    }

    public static boolean equals(String arg1, String arg2, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        if (null == arg1 || "".equals(arg1) || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "".equals(arg2) || "null".equals(arg1)) {
            arg2 = "0";
        }
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(arg1)).equals(
                nf.format(new BigDecimal(arg2)));
    }

    /**
     * 默认保留最传过来的数据最大位数 如0.22,0.2则保留两位
     *
     * @param arg1
     * @param arg2
     * @return
     */
    public static boolean equals(String arg1, String arg2) {
//		if (arg1 == null && arg2 == null)
//			return true;
//		if ((arg1 != null || arg2 == null))
//			return false;
        if (null == arg1 || "null".equals(arg1)) {
            arg1 = "0";
        }
        if (null == arg2 || "null".equals(arg2)) {
            arg2 = "0";
        }
        int point1 = getPointNumber(arg1);
        int point2 = getPointNumber(arg2);
        if (point1 >= point2) {
            return equals(arg1, arg2, point1);
        } else {
            return equals(arg1, arg2, point2);
        }
    }

    public static int getPointNumber(String number) {
        int length = number.length();
        int indexOf = number.indexOf(".");
        if (indexOf == -1) {
            return 0;
        } else {
            return length - indexOf - 1;
        }
    }

    /**
     * 截取数字的浮点长度，取值四舍五入
     *
     * @param number
     * @param scale
     * @return
     */
    public static String getInterceptedNumber(String number, int scale) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setRoundingMode(RoundingMode.HALF_UP);
        nf.setMinimumFractionDigits(scale);
        nf.setMaximumFractionDigits(scale);
        return nf.format(new BigDecimal(number));
    }

    /**
     * 运算保留两个小数
     *
     * @param a
     * @param opeartor 可以填写中文运算符，如减,加,乘,除
     * @param b
     * @return
     */
    public static String operator(String a, String opeartor, String b, int scale) {
        String sum = "";
        char key = operatorMap.get(opeartor);
        switch (key) {
            case '+':
                sum = add(a, b, scale);
                break;
            case '-':
                sum = minus(a, b, scale);
                break;
            case '*':
                sum = multi(a, b, scale);
                break;
            case '/':
                sum = divide(a, b, scale);
                break;
        }
        return sum;
    }

    public static String opearData(String value, int scale) throws Exception {
        // 按顺序转成数字符号list 即中序表达式
        List<String> list = toList(value);
        // 转成逆波兰式数字符号list 即后序表达式
        list = toSuffixExpressionList(list);
        // 求逆波兰式结果
        String result = suffix_excute(list, scale);
        log.info(value + "=" + result);
        return result;
    }

    // 表达式划分成中序list 即从左到右数字符号分开
    private static List<String> toList(String value) {
        // 开始为-时加上0
        if ("-".equals(value.substring(0, 1))) {
            value = "0" + value;
        }
        int begin = 0;
        int end = 0;
        String item;
        List<String> resultList = new ArrayList<String>();
        for (int i = 0, len = value.length(); i < len; i++) {
            item = value.substring(i, i + 1);
            if (isOperator(item)) {
                // 负数跳过
                if ("-".equals(item) && "(".equals(value.substring(i - 1, i))) {
                    continue;
                }
                end = i;
                // 前一个非符号时加上数字
                if (begin != end) {
                    resultList.add(value.substring(begin, end));
                }
                // 加上符号
                resultList.add(value.substring(end, end + 1));
                begin = end + 1;
            }
        }
        // 加上最后一个数字
        if (begin != value.length()) {
            resultList.add(value.substring(begin));
        }
        return resultList;
    }

    // 中序list转换成逆波兰式list 左右根
    private static List<String> toSuffixExpressionList(List<String> list)
            throws Exception {
        Stack<String> operatorStack = new Stack<String>();// 符号栈
        Stack<String> resultStack = new Stack<String>();// 结果栈
        Iterator<String> iter = list.iterator();
        while (iter.hasNext()) {
            String item = iter.next();
            if (isOperator(item)) {
                if (")".equals(item)) {
                    // 遇到)时符号栈一直弹出并压入结果栈直到遇到(，弹出(废弃，结束。
                    while (!(operatorStack.isEmpty() || "("
                            .equals(operatorStack.peek()))) {
                        resultStack.push(operatorStack.pop());
                    }
                    // 弹出(
                    if (!operatorStack.isEmpty()
                            && "(".equals(operatorStack.peek())) {
                        operatorStack.pop();
                    } else {
                        throw new Exception("(少了");
                    }
                } else if ("(".equals(item)) {
                    // 遇到(时直接入符号栈，结束
                    operatorStack.push(item);
                } else {
                    // 遇到其他运算符时与符号栈顶（若符号栈顶为空或(时直接入符号栈，结束）运算比较 若比栈顶高直接入符号栈，结束
                    // 否则符号栈弹出并压入结果栈 并再执行与符号栈顶比较直到弹入符号栈，结束
                    while (!(operatorStack.isEmpty() || "("
                            .equals(operatorStack.peek()))) {
                        if (compareOperator(item, operatorStack.peek()) < 1) {
                            resultStack.push(operatorStack.pop());
                        } else {
                            break;
                        }
                    }
                    operatorStack.push(item);
                }

            } else {
                // 数字时直接入结果栈
                resultStack.push(item);
            }
        }
        // 符号栈全部弹出并压入结果栈
        while (!operatorStack.isEmpty()) {
            if ("(".equals(operatorStack.peek())) {
                throw new Exception("(多了");
            }
            resultStack.push(operatorStack.pop());
        }
        // 结果栈弹出并反序得出最终结果
        iter = resultStack.iterator();
        List<String> resultList = new ArrayList<String>();
        while (iter.hasNext()) {
            resultList.add(iter.next());
        }
        return resultList;
    }

    // 逆波兰式list 求值
    // 从左至右扫描表达式，遇到数字时，将数字压入堆栈，遇到运算符时，弹出栈顶的两个数，用运算符对它们做相应的计算（次顶元素 op
    // 栈顶元素），并将结果入栈；重复上述过程直到表达式最右端，最后运算得出的值即为表达式的结果。
    private static String suffix_excute(List<String> list, int scale) {
        Stack<String> resultStack = new Stack<String>();
        Iterator<String> iter = list.iterator();
        String num1;
        String num2;
        while (iter.hasNext()) {
            String item = iter.next();
            if (isOperator(item)) {
                num2 = resultStack.pop();
                num1 = resultStack.pop();
                resultStack.push(operator(num1, item, num2, scale));
            } else {
                resultStack.push(item);
            }
        }
        return resultStack.pop();
    }

    // 比较两运算高低 1 1>2, 0 1=2 -1 1<2
    private static int compareOperator(String operator1, String operator2) {
        if ("*".equals(operator1) || "/".equals(operator1)) {
            return ("-".equals(operator2) || "+".equals(operator2)) ? 1 : 0;
        } else if ("-".equals(operator1) || "+".equals(operator1)) {
            return ("*".equals(operator2) || "/".equals(operator2)) ? -1 : 0;
        }
        return 1;
    }

    // 是否为运算符
    private static Boolean isOperator(String value) {
        return "(".equals(value) || ")".equals(value) || "+".equals(value)
                || "-".equals(value) || "*".equals(value) || "/".equals(value);
    }

}
