package com.cheche365.bc.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Map操作工具类
 *
 * <AUTHOR>
 * @Created by austinChen on 2015/12/18 15:22.
 */
@Slf4j
public final class MapUtil {

    /**
     * @param json 字符串
     * @return 转换后的Map对象
     */
    public static Map<String, Object> toMap(String json) {
        return toMap(JSON.parseObject(json));
    }


    /**
     * JSONObject转换成Map
     *
     * @param jsonObject
     * @return
     */
    public static Map<String, Object> toMap(JSONObject jsonObject) {
        Map<String, Object> map = new HashMap<String, Object>();
        for (Map.Entry<String, Object> param : jsonObject.entrySet()) {
            if (param.getValue() instanceof JSONObject) {
                map.put(param.getKey(), toMap((JSONObject) param.getValue()));
            } else if (param.getValue() instanceof JSONArray) {
                map.put(param.getKey(), toList((JSONArray) param.getValue()));
            } else {
                map.put(param.getKey(), JSONObject.toJSON(param.getValue()));
            }
        }
        return map;
    }

    /**
     * JSONArray转换成List
     *
     * @param jsonArray
     * @return
     */
    public static List<Object> toList(JSONArray jsonArray) {
        List<Object> list = new ArrayList<Object>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Object object = jsonArray.get(i);
            if (object instanceof JSONObject) {
                object = toMap((JSONObject) object);
            } else if (object instanceof JSONArray) {
                object = toList((JSONArray) object);
            } else {
                object = JSONObject.toJSONString(object, SerializerFeature.WriteClassName);
            }
            list.add(object);
        }
        return list;
    }

    /**
     * 默认的构造函数
     */
    private MapUtil() {

    }

    private static String[] getPaths(String paths) {
        String newPaths = paths;
        if (paths.startsWith("/")) {
            newPaths = paths.substring(1);
        }
        String[] pathArray = newPaths.split("/");
        return pathArray;
    }

    /**
     * 取字符串值方法
     *
     * @param paths
     * @param data
     * @return
     * @ex: getValue(" / dealOffer / carInfo / carModelName ", enquiry)
     * or : getValue("dealOffer/carInfo/carModelName",enquiry)
     */
    public static String getValue(String paths, Map data) {
        if (data instanceof Map) {
            return getMapValue(getPaths(paths), data);
        }
        return "";
    }

    /**
     * 取map对象的值
     *
     * @param strs
     * @param data
     * @return
     */
    private static String getMapValue(String[] strs, Map data) {
        Object tmpMap = null;
        Map temp = data;
        int i = 0;
        for (; i < strs.length; i++) {
            tmpMap = temp.get(strs[i]);
            if (tmpMap instanceof Map) {
                temp = (Map) tmpMap;
            }
        }
        if (i == strs.length && temp.get(strs[i - 1]) != null) {
            return temp.get(strs[i - 1]).toString();
        }
        return "";
    }


    /**
     * @param map   原始的数据Map
     * @param paths 路径  /dealOffer/carInfo/carModelName or dealOffer/carInfo/carModelName
     * @param obj   需要放入的对象
     * @return 放入之后的Map对象
     */
    public static Map putMap(Map map, String paths, Object obj) {
        String newPaths = paths;
        if (paths.startsWith("/")) {
            newPaths = paths.substring(1);
        }
        return putMap(map, Arrays.asList(newPaths.split("/")), obj);
    }

    /**
     * @param map   需要放入数据的原始Map
     * @param paths 路径 /dealOffer/carInfo/carModelName or dealOffer/carInfo/carModelName
     * @param obj   需要放入的对象
     * @return 放入之后的Map对象
     */
    public static Map putMap(Map map, List<String> paths, Object obj) {
        if (paths.size() == 1) {
            map.put(paths.get(0), obj);
            return map;
        } else if (paths.size() > 1) {
            Object objTmp = null;
            if (map.containsKey(paths.get(0))) {
                objTmp = map.get(paths.get(0));
            } else {
                objTmp = new HashMap<String, Object>();
            }
            if (objTmp != null && objTmp instanceof Map) {
                Map mapTmp = (Map) objTmp;
                List<String> tmpHeader = new ArrayList<String>();
                tmpHeader.add(paths.get(0));
                List<String> tmpPaths = new ArrayList<String>();
                for (int i = 1; i < paths.size(); i++) {
                    tmpPaths.add(paths.get(i));
                }
                return putMap(map, tmpHeader, putMap(mapTmp, tmpPaths, obj));
            } else {
                return map;
            }
        } else {
            return map;
        }

    }
}
