package com.cheche365.bc.message;

import com.google.common.base.Function;
import com.google.common.base.Strings;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务类型
 *
 * <AUTHOR>
 */
@Getter
public enum TaskStatus {

    QUOTE_QUERY_SUCCESS("6", "报价查询成功", 2),
    QUOTE_QUERY_FAILED("7", "报价查询失败", 1),
    QUOTE_SUCCESS("A", "报价成功", 2),
    QUOTE_FAILED("A1", "报价失败", 1),
    INSURE_SAVE_SUCCESS("11", "核保暂存成功", 2),
    INSURE_SAVE_FAILED("12", "核保暂存失败", 1),
    INSURE_QUERY_SUCCESS("14", "核保查询成功", 2),
    INSURE_QUERY_FAILED("15", "核保查询失败", 1),
    INSURE_SUCCESS("B", "核保成功", 2),
    INSURE_FAILED("B1", "核保失败", 1),
    INSURE_RETURNMOD("B5", "退回修改", 1),
    PAY_APPLY_FAILED("17", "支付申请失败", 1),
    PAY_APPLY_SUCCESS("18", "支付申请成功", 2),
    APPROVED_QUERY_SUCCESS("20", "承保查询成功", 2),
    APPROVED_QUERY_FAILED("21", "承保查询失败", 1),
    APPROVED_SUCCESS("D", "承保成功", 2),
    APPROVED_FAILED("D1", "承保失败", 1),
    AUTO_INSURE_SUCCESS("32", "自核成功", 2),
    AUTO_INSURE_FAILED("33", "自核失败", 1),
    AUTO_INSURE_WAIT_QUERY("34", "自核待查询", 2),
    AUTO_INSURE_WAIT_VERIFY("35", "自核待认证", 2),
    EXECUTE_SUCCESS("10000", "执行成功", 2),
    EXECUTE_FAILURE("10001", "执行失败", 1);

    private final String state;
    private final String cn;
    /**
     * 1 回调失败组
     * 2 回调成功组
     */
    private final int group;
    public static final List<TaskStatus> CALLBACK_STATUS;
    public static final List<String> SUCCESS_CALLBACK_STATUS;
    public static final HashMap<String, Function<Boolean, String>> TASK_TYPE_STATUS_MAPPINGS;

    static {
        CALLBACK_STATUS = Arrays.stream(values()).collect(Collectors.toList());

        SUCCESS_CALLBACK_STATUS = Arrays
            .stream(values())
            .filter(taskStatus -> taskStatus.getGroup() == 2)
            .map(TaskStatus::getState)
            .collect(Collectors.toList());

        TASK_TYPE_STATUS_MAPPINGS = new HashMap<String, Function<Boolean, String>>() {
            {
                put(TaskType.QUOTE.code, (Boolean success) -> success ? QUOTE_SUCCESS.state : QUOTE_FAILED.state);
                put(TaskType.INSURE.code, (Boolean success) -> success ? INSURE_SUCCESS.state : INSURE_FAILED.state);
                put(TaskType.AUTO_INSURE.code, (Boolean success) -> success ? AUTO_INSURE_SUCCESS.state : AUTO_INSURE_FAILED.state);
                put(TaskType.INSURE_QUERY.code, (Boolean success) -> success ? null : INSURE_FAILED.state);
                put(TaskType.APPROVED.code, (Boolean success) -> success ? APPROVED_SUCCESS.state : APPROVED_FAILED.state);
                put(TaskType.APPROVED_QUERY.code, (Boolean success) -> success ? null : APPROVED_FAILED.state);
                put(TaskType.POLICY_QUERY.code, (Boolean success) -> success ? EXECUTE_SUCCESS.state : EXECUTE_FAILURE.state);
                put(TaskType.SMS_FOR_PAY.code, (Boolean success) -> success ? EXECUTE_SUCCESS.state : EXECUTE_FAILURE.state);
                put(TaskType.IDENTIFY_VERIFY.code, (Boolean success) -> success ? EXECUTE_SUCCESS.state : EXECUTE_FAILURE.state);
                put(TaskType.CANCEL_POLICY.code, (Boolean success) -> success ? EXECUTE_SUCCESS.state : EXECUTE_FAILURE.state);
                put(TaskType.CLUE_CALLBACK.code, (Boolean success) -> success ? EXECUTE_SUCCESS.state : EXECUTE_FAILURE.state);
            }
        };
    }

    TaskStatus(String state, String cn, int group) {
        this.state = state;
        this.cn = cn;
        this.group = group;
    }

    //仅限模拟使用
    public String State() {
        return this.getState();
    }

    //仅限模拟使用
    public String Cn() {
        return this.getCn();
    }

    public static boolean isCallbackStatus(String state) {
        return CALLBACK_STATUS.contains(getTaskStatus(state));
    }

    public static TaskStatus getTaskStatus(String taskStatus) {
        return Arrays
            .stream(values())
            .filter(ts -> ts.getState().equals(taskStatus))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("无效的taskState: " + taskStatus));
    }

    public static String getFailedState(String taskType) {
        if (Strings.isNullOrEmpty(taskType) || !TASK_TYPE_STATUS_MAPPINGS.containsKey(taskType)) {
            return null;
        }
        return TASK_TYPE_STATUS_MAPPINGS.get(taskType).apply(Boolean.FALSE);
    }

    public static String getSuccessState(String taskType) {
        if (Strings.isNullOrEmpty(taskType) || !TASK_TYPE_STATUS_MAPPINGS.containsKey(taskType)) {
            return null;
        }
        return TASK_TYPE_STATUS_MAPPINGS.get(taskType).apply(Boolean.TRUE);
    }
}
