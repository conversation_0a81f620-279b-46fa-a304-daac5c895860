package com.cheche365.bc.message;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class Reply {

    private String taskId;
    private String taskType;
    private int taskStatus;
    private boolean accept;
    private boolean result;
    private String msg;

    public boolean isResult() {
        return result;
    }

    public String getTaskId() {
        return taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public boolean isAccept() {
        return accept;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static Map reply(String status, String message, Object resultData) {
        Map param = new HashMap();
        param.put("status", status);
        param.put("message", message);
        param.put("resultData", resultData);
        return param;
    }

    public static Map reply(String taskId, boolean accept, boolean result, String taskStatus) {
        Map param = new HashMap();
        param.put("taskId", taskId);
        param.put("accept", accept);
        param.put("result", result);
        param.put("taskStatus", taskStatus);
        return param;
    }

    public static Map reply(String taskId, boolean accept, boolean result, String taskStatus, String msg) {
        Map param = new HashMap();
        param.put("taskId", taskId);
        param.put("accept", accept);
        param.put("result", result);
        param.put("taskStatus", taskStatus);
        param.put("msg", msg);
        return param;
    }

    public static Map reply(String taskId, String taskType, String taskStatus) {
        return reply(taskId, taskType, Integer.parseInt(taskStatus));
    }

    public static Map reply(String taskId, String taskType, int taskStatus) {
        Map param = new HashMap();
        param.put("taskId", taskId);
        param.put("taskType", taskType);
        param.put("taskStatus", taskStatus);
        param.put("processType", "edi");
        return param;
    }

}
