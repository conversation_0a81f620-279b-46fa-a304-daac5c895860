package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class DaDiHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.CCIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String responseBody;
        if (autoTask.getConfigs().containsKey("newFlag2021")) {
            responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        } else {
            String insCom = (String) autoTask.getConfigs().get("insCom");
            closeableHttpClient = HttpSender.buildHttpClientByComId(insCom);
            responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        }
        return responseBody;
    }
}
