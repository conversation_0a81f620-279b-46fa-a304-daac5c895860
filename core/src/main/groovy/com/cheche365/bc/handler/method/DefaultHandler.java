package com.cheche365.bc.handler.method;

import com.cheche365.bc.handler.<PERSON>Handler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class DefaultHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return "default";
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        return HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
    }
}
