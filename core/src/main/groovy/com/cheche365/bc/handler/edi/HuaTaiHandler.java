package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Component
public class HuaTaiHandler implements BaseHandler {
    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.HTIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        JSONObject requestJson = JSONObject.parseObject(requestBody);
        JSONObject param = (JSONObject) requestJson.remove("busiContent");
        String busiContentNoSign = param.toJSONString();
        String requestBase64 = Base64.getEncoder().encodeToString(busiContentNoSign.getBytes(StandardCharsets.UTF_8));
        String aesKey = RandomStringUtils.randomAlphanumeric(16);
        AesDesEncryption aesDesEncryption = AesDesEncryption.builder()
                .key(aesKey)
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
                .build();
        String busiContent = aesDesEncryption.encrypt(requestBase64);
        requestJson.put("busiContent", busiContent);
        //华泰公钥
        String publicKey = (String) autoTask.getConfigs().get("publicKey");
        //车车私钥
        String privateKey = (String) autoTask.getConfigs().get("privateKey");
        RSA rsa = RSA.builder()
                .publicKey(publicKey)
                .privateKey(privateKey)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA1WithRSA)
                .build();
        String accessToken = rsa.encrypt(aesKey);
        requestJson.put("accessToken", accessToken);
        requestBody = requestJson.toJSONString();
        String responseBody = HttpSender.doPostWithRetry(1, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        accessToken = responseJson.getString("accessToken");
        aesKey = rsa.decrypt(accessToken);
        aesDesEncryption = AesDesEncryption.builder()
                .key(aesKey)
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
                .build();
        busiContent = aesDesEncryption.decrypt((String) responseJson.remove("busiContent"));
        busiContent = new String(Base64.getDecoder().decode(busiContent));
        responseJson.put("busiContent", JSONObject.parse(busiContent));
        return responseJson.toJSONString();
    }
}
