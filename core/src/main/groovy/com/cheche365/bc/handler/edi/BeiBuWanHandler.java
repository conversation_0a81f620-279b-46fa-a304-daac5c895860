package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.utils.sender.HttpSender;
import com.cheche365.bc.utils.tai_bao.MessageConstants;
import com.cheche365.bc.utils.tai_bao.SignatureUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class BeiBuWanHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.BWBX.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        JSONObject requestObject = JSONObject.parseObject(requestBody);
        String bizContent = requestObject.getString(MessageConstants.BIZ_CONTENT);

        //北部湾公钥
        String publicKey = (String) autoTask.getConfigs().get("bbwPublicKey");
        //车车私钥
        String privateKey = (String) autoTask.getConfigs().get("chechePrivateKey");
        RSA rsa = RSA.builder()
                .publicKey(publicKey)
                .privateKey(privateKey)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .signAlgorithm(EncryptEnum.SignAlgorithmEnum.MD5withRSA)
                .build();

        requestObject.put("bizContent", rsa.encrypt(bizContent));
        requestBody = requestObject.toJSONString();

        String signContent = SignatureUtils.getSignContent(requestObject);
        String signStr = rsa.sign(signContent);

        Map headers = autoTask.getReqHeaders();
        if (Objects.isNull(headers)) {
            headers = new HashMap();
        }

        headers.put("Signature", signStr);

        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        JSONObject obj = JSONObject.parseObject(responseBody);
        String responseBizContent = rsa.decrypt(obj.getString("bizContent"));
        obj.put("bizContent", responseBizContent);

        return obj.toJSONString();
    }
}
