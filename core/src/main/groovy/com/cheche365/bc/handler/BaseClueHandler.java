package com.cheche365.bc.handler;

import com.cheche365.bc.handler.clue.ClueCallbackDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
abstract public class BaseClueHandler {

    /**
     * 获取请求方式
     */
    public abstract String companyId();

    /**
     * 获取请求方式
     */
    protected abstract ClueCallbackDTO execute(String responseBody) throws Exception;

    /**
     * 处理保司返回的数据
     *
     * @param responseBody 保司返回的数据
     * @return 处理后的数据
     */
    public ClueCallbackDTO decrypt(String responseBody) throws Exception {

        log.info("收到{}公司线索数据回调,回调内容为:{}", companyId(), responseBody);

        ClueCallbackDTO callbackDTO = execute(responseBody);

        log.info("收到{}公司线索数据回调,回调内容解密后为:{}", companyId(), callbackDTO);

        return callbackDTO;
    }
}
