package com.cheche365.bc.handler.method;

import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.CompactUtil;
import com.cheche365.bc.utils.TaskUtil;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

@Component
public class PostHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return Constants.HTTPCLIENT_CHANNEL_POST;
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {

        String comId = autoTask.getCompanyId().substring(0, 4);
        String processType = (String) autoTask.getTempValues().get("processType");
        BaseHandler strategy = null;
        strategy = TaskUtil.requestMethodMap.get(processType + comId);
        if (Objects.isNull(strategy))
            strategy = TaskUtil.requestMethodMap.get("default");
        if ("edi".equals(processType) && requestBody.trim().startsWith("<") && Arrays.asList("2044", "2050", "2088", "2095").contains(comId)) {
            requestBody = CompactUtil.compactXml(requestBody);
        }
        return strategy.execute(autoTask, requestBody, charSet, url, closeableHttpClient);
    }
}
