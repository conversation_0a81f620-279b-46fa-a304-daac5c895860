package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * @Description:
 * @author: wangj
 * @date: 2023.05.22
 */
@Component
public class TaiKangHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.TKCX.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        decode("ad");
        String result = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getArrayParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        JSONObject resultObject = JSONObject.parseObject(result);
        if ("KindsInit".equals(resultObject.getJSONObject("head").getString("function"))) {
            JSONArray prpallDutysArray = resultObject.getJSONObject("apply_content").getJSONObject("data").getJSONArray("prpallDutys");
            if(null != prpallDutysArray) {
                prpallDutysArray.forEach(prpallDuty -> {
                    JSONObject prpallDutyObject = ((JSONObject) prpallDuty).getJSONObject("prpallDuty");
                    if(null != prpallDutyObject) {
                        prpallDutyObject.put("productName", decode(prpallDutyObject.getString("productName")));
                    }
                    JSONArray dutysArray = prpallDutyObject.getJSONArray("dutys");
                    if(null != dutysArray) {
                        dutysArray.forEach(duty -> {
                            JSONObject dutyObject = ((JSONObject) duty).getJSONObject("duty");
                            dutyObject.put("prpallDutyName", decode(dutyObject.getString("prpallDutyName")));
                        });
                    }
                });
            }
            return JSONObject.toJSONString(resultObject);
        }
        return result;
    }

    private String decode(String code) {
        try {
            return URLDecoder.decode(code, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}
