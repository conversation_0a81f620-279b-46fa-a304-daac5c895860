package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import org.apache.http.HttpHeaders;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DaJiaHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.DAIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {

        //秘钥
        String secretKey = (String) autoTask.getConfigs().get("secretKey");
        //偏移量
        String ivKey = (String) autoTask.getConfigs().get("ivKey");
        AesDesEncryption build = AesDesEncryption.builder()
                .key(secretKey)
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .ivKey(ivKey)
                .ivKeyFormat(EncryptEnum.IvKeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_CFB_PKCS5Padding)
                .build();

        requestBody = build.encrypt(requestBody);
        Map<String, String> headers = autoTask.getReqHeaders();
        if (Objects.isNull(headers)) {
            headers = Maps.newHashMap();
        }
        //保司要求必须加上此头，否则会报418，需加上此头才能正常
        headers.put(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.48");
        return HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), headers, charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
    }
}
