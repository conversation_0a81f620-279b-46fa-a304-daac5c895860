package com.cheche365.bc.handler.method;

import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class DeleteHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return Constants.HTTPCLIENT_CHANNEL_DELETE;
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {

        return (String) HttpSender.doDelete(closeableHttpClient, url, autoTask.getReqHeaders(), autoTask.getParams(), charSet, TaskUtil.buildReqConfig(autoTask), false);
    }
}
