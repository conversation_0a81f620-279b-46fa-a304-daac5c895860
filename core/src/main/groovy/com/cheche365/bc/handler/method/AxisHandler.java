package com.cheche365.bc.handler.method;

import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class AxisHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return Constants.AXIS2_CHANNEL;
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String comId = autoTask.getCompanyId().substring(0, 4);
        String processType = (String) autoTask.getTempValues().get("processType");
        BaseHandler strategy = TaskUtil.requestMethodMap.get(processType + comId);
        if (Objects.isNull(strategy))
            throw new RuntimeException("axis2交互方式未匹配到处理方法");
        return strategy.execute(autoTask, requestBody, charSet, url, closeableHttpClient);
    }
}
