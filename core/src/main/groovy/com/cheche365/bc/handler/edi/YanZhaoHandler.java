package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;


@Component
public class YanZhaoHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.YZIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        // 燕赵发送报文加密
        AesDesEncryption build = AesDesEncryption.builder()
                .key((String) autoTask.getConfigs().get("secretKey"))
                .keyFormat(EncryptEnum.KeyFormatEnum.Base64)
                .ivKey((String) autoTask.getConfigs().get("secretKeyVi"))
                .ivKeyFormat(EncryptEnum.IvKeyFormatEnum.Base64)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.DESEDE_CBC_PKCS5Padding)
                .build();

        requestBody = build.encrypt(requestBody);
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        // 燕赵响应报文解密
        if (responseBody != null && !responseBody.isEmpty()) {
            responseBody = build.decrypt(responseBody.replaceAll("\n", ""));
        }
        return responseBody;
    }
}
