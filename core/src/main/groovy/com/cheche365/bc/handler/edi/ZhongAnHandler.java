package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.zhongan.scorpoin.biz.common.CommonRequest;
import com.zhongan.scorpoin.biz.common.CommonResponse;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ZhongAnHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.ZAIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        Map<String, Object> orgConfig = autoTask.getConfigs();
        String env = (String) orgConfig.get("env");
        String version = (String) orgConfig.get("version");
        String appKey = (String) orgConfig.get("appkey");
        String privateKey = (String) orgConfig.get("privateKey");
        ZhongAnApiClient client = new ZhongAnApiClient(env, appKey, privateKey, version);
        CommonRequest e = new CommonRequest(url);
        JSONObject params = JSONObject.parseObject(requestBody);
        e.setParams(params);
        CommonResponse rResponse = (CommonResponse) client.call(e);
        return JSONObject.toJSONString(rResponse).replace("\\", "").replace("\"{", "{").replace("}\"", "}");
    }
}
