package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class TaiPingHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.TPIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        //太平EDI生成签名摘要
        RSA build = RSA.builder()
                .privateKey((String) autoTask.getConfigs().get("privateKey"))
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA256WithRSA)
                .build();
        String requestSign = build.sign(Base64.getEncoder().encode(requestBody.getBytes(StandardCharsets.UTF_8)));
        //将签名摘要设置到请求头中
        Map<String, String> headers = autoTask.getReqHeaders();
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put("TP-SIGN", requestSign);
        //报文密钥
        String AESKey= (String) autoTask.getConfigs().get("AESKey");
        log.info("获取密钥:" + AESKey);
        Boolean needEncryption = MapUtils.getBoolean(autoTask.getTempValues(), "needEncryption", true);
        if (needEncryption) {
            //加密
            requestBody = encrypt(Base64.getEncoder().encodeToString(requestBody.getBytes()), AESKey);
        }
        String response = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getArrayParams(), headers, charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        if (needEncryption) {
            //解密
            assert response != null;
            response = new String(Base64.getDecoder().decode(decrypt(response, AESKey)));
        }
        return response;
    }

    private static final String KEY_ALGORITHM = "AES";
    private static final String CHARSET_NAME = "utf-8";
    public static String encrypt(String content, String key) throws NoSuchAlgorithmException,
            NoSuchPaddingException, UnsupportedEncodingException, InvalidKeyException, IllegalBlockSizeException,
            BadPaddingException, NoSuchProviderException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(generateSecretKey(key), KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        byte[] byteContent = content.getBytes(CHARSET_NAME);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] byteRresult = cipher.doFinal(byteContent);
        StringBuilder sb = new StringBuilder();
        StringBuilder hex = new StringBuilder();
        for (byte b : byteRresult) {
            hex.setLength(0);
            hex.append(Integer.toHexString(b & 0xFF));
            if (hex.length() == 1) {
                hex.insert(0, '0');
            }
            sb.append(hex);
        }
        return sb.toString().toUpperCase();
    }

    public static byte[] generateSecretKey(String key) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        digest.update(key.getBytes(CHARSET_NAME));
        return digest.digest();
    }
    public static String decrypt(String content, String key) throws NoSuchAlgorithmException,
            NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, UnsupportedEncodingException {
        if (content.length() < 1)
            return null;
        byte[] byteResult = new byte[content.length() / 2];
        for (int i = 0; i < content.length() / 2; i++) {
            int high = Integer.parseInt(content.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(content.substring(i * 2 + 1, i * 2 + 2), 16);
            byteResult[i] = (byte) (high * 16 + low);
        }
        SecretKeySpec secretKeySpec = new SecretKeySpec(generateSecretKey(key), KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);//修改这里
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] result = cipher.doFinal(byteResult);
        return new String(result, Charset.forName(CHARSET_NAME));
    }
}
