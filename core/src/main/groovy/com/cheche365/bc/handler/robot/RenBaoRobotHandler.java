package com.cheche365.bc.handler.robot;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Component
public class RenBaoRobotHandler implements BaseHandler {
    @Override
    public String getMethodType() {
        return ProcessTypeEnum.ROBOT.getKey() + InsCompanyEnum.PICC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String responseBody;
        if (Objects.requireNonNull(autoTask.getConfigs()).containsKey("market") && MapUtils.getBoolean(autoTask.getConfigs(), "market", false)) {
            Map<String, String> header = autoTask.getReqHeaders();
            HttpPost post = new HttpPost(url);
            header.forEach(post::addHeader);
            String boundary = (String) autoTask.getTempValues().get("boundary");
            MultipartEntityBuilder multipart = MultipartEntityBuilder.create();
            multipart.setBoundary(boundary);
            multipart.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            ContentType contentType = ContentType.create(HTTP.PLAIN_TEXT_TYPE, HTTP.UTF_8);
            Map<String, Object> param = autoTask.getParams();
            if (Objects.nonNull(param) && param.size() > 0) {
                param.forEach((k, v) ->
                        multipart.addPart(k, new StringBody(Objects.nonNull(v) ? (String) v : "", contentType))
                );
            }
            post.setEntity(multipart.build());
            CloseableHttpResponse httpResponse = ((CloseableHttpClient) autoTask.getHttpClient()).execute(post);
            responseBody = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
        } else {
            responseBody = HttpSender.doPostWithRetry(1, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey(), autoTask.getRepHeaders());
        }
        return responseBody;
    }
}
