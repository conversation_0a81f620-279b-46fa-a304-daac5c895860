package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * <AUTHOR> zhangying
 * @date 2024-05-23
 * @descript :中煤加解密工具类
 */
@Component
public class ZhongMeiHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.ZMBX.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        AesDesEncryption aes = AesDesEncryption.builder()
                .key(autoTask.getConfigs().get("AesKey").toString())
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
                .build();

        requestBody = Hex.encodeToString(Base64.getDecoder().decode(aes.encrypt(requestBody)));
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, null, autoTask.getReqHeaders(), "UTF-8", TaskUtil.buildReqConfig(autoTask), "", autoTask.getRepHeaders());
        responseBody = responseBody.replaceAll("\\r|\\n|\\s", "");    // 勿删，保司返回加密字符串末尾有换行符会导致解密失败
        return aes.decrypt(Hex.decode(responseBody));
    }

}
