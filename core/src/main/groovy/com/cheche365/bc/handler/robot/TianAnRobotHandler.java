package com.cheche365.bc.handler.robot;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-09-20
 */
@Component
public class TianAnRobotHandler implements BaseHandler {

    private static final List<String> IGNORE_TEMPLATES = Arrays.asList("robot-2045-login_new", "robot-2045-getCarInfo_new");

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.ROBOT.getKey() + InsCompanyEnum.TAIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        if (!IGNORE_TEMPLATES.contains(autoTask.getTempValues().get("curTemplateName").toString())) {
            String jsonKey = Base64.encode(requestBody.getBytes(StandardCharsets.UTF_8)).replaceAll("\n", "").replaceAll("\r", "");
            JSONObject params = new JSONObject();
            params.put("jsonKey", jsonKey);
            requestBody = JSONObject.toJSONString(params);
        }
        return HttpSender.doPostWithRetry(1, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey(), autoTask.getRepHeaders());
    }
}
