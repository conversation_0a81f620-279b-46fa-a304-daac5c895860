package com.cheche365.bc.constants;

public class TaskEntryKey {

    /**
     * 车辆信息-车牌
     */
    public final static String CAR_SPECIFIC_LICENSE = "car.specific.license";

    /**
     * 车辆信息-行驶证车型
     */
    public final static String CAR_SPECIFIC_MODELNAME = "car.specific.modelName";

    /**
     * 车辆信息-座位数
     */
    public final static String CAR_MODEL_SEATS = "car.model.seats";

    /**
     * 车辆信息-核定载质量
     */
    public final static String CAR_MODEL_MODELLOAD = "car.model.modelLoad";

    /**
     * 车辆信息-整备质量
     */
    public final static String CAR_MODEL_FULLLOAD = "car.model.fullLoad";

    /**
     * 车辆信息-排气量
     */
    public final static String CAR_MODEL_DISPLACEMENT = "car.model.displacement";

    /**
     * 车辆信息-新车购置价
     */
    public final static String CAR_SPECIFIC_PRICE = "car.specific.price";

    /**
     * 车辆信息-新车购置价(含税)
     */
    public final static String CAR_SPECIFIC_TAXPRICE = "car.specific.taxPrice";

    /**
     * 车辆信息-车辆折旧价
     */
    public final static String CAR_SPECIFIC_DEPPRICE = "car.specific.depPrice";

    /**
     * 车辆信息-车辆浮动价上限
     */
    public final static String CAR_SPECIFIC_MAXPRICE = "car.specific.maxPrice";

    /**
     * 车辆信息-车辆浮动价下限
     */
    public final static String CAR_SPECIFIC_MINPRICE = "car.specific.minPrice";

    /**
     * 车辆信息-发动机号
     */
    public final static String CAR_SPECIFIC_ENGINENUM = "car.specific.engineNum";

    /**
     * 车辆信息-车架号
     */
    public final static String CAR_SPECIFIC_VIN = "car.specific.vin";

    /**
     * 车辆信息-车辆初登日期
     */
    public final static String CAR_SPECIFIC_REGDATE = "car.specific.regDate";

    /**
     * 车辆信息-过户车
     */
    public final static String CAR_SPECIFIC_ISTRANSFERCAR = "car.specific.isTransferCar";

    /**
     * 车辆信息-平均行驶里程
     */
    public final static String CAR_SPECIFIC_AVGMILEAGE = "car.specific.avgMileage";

    /**
     * 车辆信息-行驶区域
     */
    public final static String CAR_SPECIFIC_DRIVINGAREA = "car.specific.drivingArea";

    /**
     * 车辆信息-号牌底色
     */
    public final static String CAR_SPECIFIC_LICENSECOLOR = "car.specific.licenseColor";

    /**
     * 车辆信息-号牌种类
     */
    public final static String CAR_SPECIFIC_LICENSETYPE = "car.specific.licenseType";

    /**
     * 车辆信息-车辆用户类型
     */
    public final static String CAR_SPECIFIC_USERTYPE = "car.specific.userType";

    /**
     * 车辆信息-使用性质
     */
    public final static String CAR_SPECIFIC_USEPROPS = "car.specific.useProps";

    /**
     * 车辆信息-车身颜色
     */
    public final static String CAR_SPECIFIC_BODYCOLOR = "car.specific.bodyColor";

    /**
     * 车辆信息-车辆描述
     */
    public final static String CAR_SPECIFIC_ALIASNAME = "car.specific.aliasName";

    /**
     * 车辆信息-车辆备注
     */
    public final static String CAR_SPECIFIC_REMARK = "car.specific.remark";

    /**
     * 车辆信息-品牌名称
     */
    public final static String CAR_MODEL_BRANDNAME = "car.model.brandName";

    /**
     * 车辆信息-车型系列
     */
    public final static String CAR_MODEL_FAMILYNAME = "car.model.familyName";

    /**
     * 车辆信息-管控车型
     */
    public final static String CAR_SPECIFIC_ISSPECIAL = "car.specific.isSpecial";

    /**
     * 车辆信息-高危限制车型
     */
    public final static String CAR_SPECIFIC_ISDANGER = "car.specific.isDanger";

    /**
     * 车辆信息-易盗车型
     */
    public final static String CAR_SPECIFIC_ISROB = "car.specific.isRob";

    /**
     * 车辆信息-车型准确度
     */
    public final static String CAR_SPECIFIC_MODELACCURACY = "car.specific.modelAccuracy";

    /**
     * 车辆信息-玻璃类型
     */
    public final static String CAR_SPECIFIC_GLASSTYPE = "car.specific.glassType";

    /**
     * 车辆信息-产地类型
     */
    public final static String CAR_MODEL_VEHICLEORIGIN = "car.model.vehicleOrigin";

    /**
     * 车辆信息-涉农标志
     */
    public final static String CAR_SPECIFIC_ISAGRICULTURE = "car.specific.isAgriculture";

    /**
     * 车辆信息-外地车
     */
    public final static String CAR_SPECIFIC_FIELDCAR = "car.specific.fieldCar";

    /**
     * 车辆信息-新车
     */
    public final static String CAR_SPECIFIC_ISNEW = "car.specific.isNew";

    /**
     * 车辆信息-是否还清贷款
     */
    public final static String CAR_SPECIFIC_ISPAYOFFLOAN = "car.specific.isPayOffLoan";

    /**
     * 车辆信息-变速箱
     */
    public final static String CAR_MODEL_GEARBOX = "car.model.gearBox";

    /**
     * 车辆信息-车型年份
     */
    public final static String CAR_MODEL_CARMODELDATE = "car.model.carModelDate";

    /**
     * 车辆信息-保网车型编码
     */
    public final static String CAR_SPECIFIC_MODELID = "car.specific.modelID";

    /**
     * 车辆信息-保险公司车型编码
     */
    public final static String CAR_SPECIFIC_INSMODELID = "car.specific.insModelID";

    /**
     * 车辆信息-车辆浮动价
     */
    public final static String CAR_SPECIFIC_FLOATPRICE = "car.specific.floatPrice";

    /**
     * 车辆信息-最小排气量
     */
    public final static String CAR_SPECIFIC_ISMINCAR = "car.specific.isMinCar";

    /**
     * 车辆信息-营业转非营业
     */
    public final static String CAR_MODEL_ISCHANGEKIND = "car.model.isChangeKind";

    /**
     * 车辆信息-功率
     */
    public final static String CAR_MODEL_POWER = "car.model.power";

    /**
     * 车辆信息-车辆种类
     */
    public final static String CAR_MODEL_TYPE = "car.model.type";

    /**
     * 车辆信息-类比价
     */
    public final static String CAR_SPECIFIC_ANALOGYPRICE = "car.specific.analogyPrice";

    /**
     * 车辆信息-类比价(含税)
     */
    public final static String CAR_SPECIFIC_ANALOGYTAXPRICE = "car.specific.analogyTaxPrice";

    /**
     * 车辆信息-特种车类型
     */
    public final static String CAR_SPECIFIC_SPECIALVEHICLETYPE = "car.specific.specialVehicleType";

    /**
     * 车辆信息-车辆用途
     */
    public final static String CAR_SPECIFIC_VEHICULARAPPLICATIONS = "car.specific.vehicularApplications";

    /**
     * 车辆信息-车辆浮动价上限比率
     */
    public final static String CAR_SPECIFIC_MAXPRICERATE = "car.specific.maxPriceRate";

    /**
     * 车辆信息-车辆浮动价下限比率
     */
    public final static String CAR_SPECIFIC_MINPRICERATE = "car.specific.minPriceRate";

    /**
     * 车辆信息-车型库车型代码
     */
    public final static String CAR_MODEL_MODELCODE = "car.model.modelCode";

    /**
     * 车辆信息-车型库车型名称
     */
    public final static String CAR_MODEL_MODELNAME = "car.model.modelName";

    /**
     * 车辆信息-检验有效日期
     */
    public final static String CAR_SPECIFIC_INEFFECTUALDATE = "car.specific.ineffectualDate";

    /**
     * 车辆信息-强制有效期
     */
    public final static String CAR_SPECIFIC_REJECTDATE = "car.specific.rejectDate";

    /**
     * 车辆信息-最近定检日期
     */
    public final static String CAR_SPECIFIC_LASTCHECKDATE = "car.specific.lastCheckDate";

    /**
     * 车辆信息-转移登记日期
     */
    public final static String CAR_SPECIFIC_TRANSFERDATE = "car.specific.transferDate";

    /**
     * 车辆信息-行驶证车型编码
     */
    public final static String CAR_SPECIFIC_LICENSEMODELCODE = "car.specific.licenseModelCode";

    /**
     * 车辆信息-制造厂名称
     */
    public final static String CAR_SPECIFIC_MADEFACTORY = "car.specific.madeFactory";

    /**
     * 车辆信息-车辆型号
     */
    public final static String CAR_SPECIFIC_MODEL = "car.specific.model";

    /**
     * 车辆信息-准牵引总质量
     */
    public final static String CAR_SPECIFIC_HAULAGE = "car.specific.haulage";

    /**
     * 车辆信息-未上牌车辆标志
     */
    public final static String CAR_SPECIFIC_NOLICENSEFLAG = "car.specific.noLicenseFlag";

    /**
     * 车辆信息-风险标志
     */
    public final static String CAR_SPECIFIC_RISKFLAG = "car.specific.riskFlag";

    /**
     * 车辆信息-车贷投保多年标志
     */
    public final static String CAR_SPECIFIC_LOANVEHICLEFLAG = "car.specific.loanVehicleFlag";

    /**
     * 车辆信息-车队标志
     */
    public final static String CAR_SPECIFIC_FLEETFLAG = "car.specific.fleetFlag";

    /**
     * 车辆信息-车队号
     */
    public final static String CAR_SPECIFIC_FLEETNO = "car.specific.fleetNo";

    /**
     * 车辆信息-交管车辆查询码
     */
    public final static String CAR_SPECIFIC_PMQUERYNO = "car.specific.pmQueryNo";

    /**
     * 车辆信息-出厂日期
     */
    public final static String CAR_SPECIFIC_LFDATE = "car.specific.lfDate";

    /**
     * 车辆信息-跨省首年投保未出险证明的年数
     */
    public final static String CAR_SPECIFIC_NODAMAGEYEARS = "car.specific.noDamageYears";

    /**
     * 车辆信息-机动车序号
     */
    public final static String CAR_SPECIFIC_VEHICLEID = "car.specific.vehicleId";

    /**
     * 车辆信息-车辆登记注册地
     */
    public final static String CAR_SPECIFIC_REGISTERADDRESS = "car.specific.registerAddress";

    /**
     * 车辆信息-行驶地点
     */
    public final static String CAR_SPECIFIC_DRIVINGADDRESS = "car.specific.drivingAddress";

    /**
     * 车辆信息-已行驶里程范围
     */
    public final static String CAR_SPECIFIC_DRIVENDISTANCERANGE = "car.specific.drivenDistanceRange";

    /**
     * 车辆信息-
     */
    public final static String CAR_SPECIFIC_VEHICLETYPE = "car.specific.vehicleType";

    /**
     * 车辆信息-特殊车投保标志
     */
    public final static String CAR_SPECIFIC_SPECIALCARFLAG = "car.specific.specialCarFlag";

    /**
     * 车辆信息-是否有ABS
     */
    public final static String CAR_SPECIFIC_ABSFLAG = "car.specific.absFlag";

    /**
     * 车辆信息-是否有防盗装备
     */
    public final static String CAR_SPECIFIC_ALARMFLAG = "car.specific.alarmFlag";

    /**
     * 车辆信息-安全气囊数
     */
    public final static String CAR_SPECIFIC_AIRBAGNUM = "car.specific.airbagNum";

    /**
     * 车辆信息-税务车辆类型代码
     */
    public final static String CAR_SPECIFIC_TAXVEHICLEMODECODE = "car.specific.taxVehicleModeCode";

    /**
     * 车辆信息-税务车辆类型名 称
     */
    public final static String CAR_SPECIFIC_TAXVEHICLEMODENAME = "car.specific.taxVehicleModeName";

    /**
     * 车辆信息-承保车辆风险系数
     */
    public final static String CAR_SPECIFIC_VEHICLERISKITEMFACTOR = "car.specific.vehicleRiskItemFactor";

    /**
     * 车辆信息-风险警示理赔信息
     */
    public final static String CAR_SPECIFIC_VEHICLERISKWARNINGCLAIMITEM = "car.specific.vehicleRiskWarningClaimItem";

    /**
     * 车辆信息-车辆的风险警示信息
     */
    public final static String CAR_SPECIFIC_VEHICLERISKWARNINGINFO = "car.specific.vehicleRiskWarningInfo";

    /**
     * 车辆信息-已行驶里程
     */
    public final static String CAR_SPECIFIC_DRIVENDISTANCE = "car.specific.drivenDistance";

    /**
     * 车辆信息-速查码
     */
    public final static String CAR_SPECIFIC_SEARCHCODE = "car.specific.searchCode";

    /**
     * 车辆信息-车龄(年)
     */
    public final static String CAR_SPECIFIC_CARAGE = "car.specific.carAge";

    /**
     * 车辆信息-道路交通事故信息
     */
    public final static String CAR_SPECIFIC_TRAFFICACCIDENT = "car.specific.trafficAccident";

    /**
     * 车辆信息-报废年限
     */
    public final static String CAR_SPECIFIC_SCRAPDATE = "car.specific.scrapDate";

    /**
     * 车辆信息-转保公司
     */
    public final static String CAR_SPECIFIC_TRANSFERCOMPANY = "car.specific.transferCompany";

    /**
     * 车辆信息-能源类型
     */
    public final static String CAR_SPECIFIC_ENERGYTYPE = "car.specific.energyType";

    /**
     * 车辆信息-两地牌照
     */
    public final static String CAR_SPECIFIC_MUTILLICENSE = "car.specific.mutilLicense";

    /**
     * 车辆信息-平台车型代码
     */
    public final static String CAR_SPECIFIC_PLATFORMCODE = "car.specific.platformCode";
    /**
     * 车辆信息-平台车型代码
     */
    public final static String CAR_SPECIFIC_TRANSFER_DATE = "car.specific.transferDate";



    /********************  保网车险统一命名空间Version200-分类二——人的信  ******************/
    /**
     * 被保险人-姓名
     */
    public final static String INSURED_NAME = "insured.name";

    /**
     * 被保险人-英文名
     */
    public final static String INSURED_ENGLISHNAME = "insured.englishName";

    /**
     * 被保险人-性别
     */
    public final static String INSURED_GENDER = "insured.gender";

    /**
     * 被保险人-出生日期
     */
    public final static String INSURED_BIRTHDAY = "insured.birthday";

    /**
     * 被保险人-年龄
     */
    public final static String INSURED_AGE = "insured.age";

    /**
     * 被保险人-证件类型
     */
    public final static String INSURED_CERTIFICATE = "insured.certificate";

    /**
     * 被保险人-证件号码
     */
    public final static String INSURED_IDNUMBER = "insured.IDNumber";

    /**
     * 被保险人-证件名称
     */
    public final static String INSURED_CERTNAME = "insured.certName";

    /**
     * 被保险人-电话号码
     */
    public final static String INSURED_PHONENUMBER = "insured.phoneNumber";

    /**
     * 被保险人-手机号码
     */
    public final static String INSURED_MOBILENUMBER = "insured.mobileNumber";

    /**
     * 被保险人-血型
     */
    public final static String INSURED_BLOODTYPE = "insured.bloodType";

    /**
     * 被保险人-婚姻状况
     */
    public final static String INSURED_MARITALSTATUS = "insured.maritalStatus";

    /**
     * 被保险人-住址
     */
    public final static String INSURED_LIVINGADDRESS = "insured.livingAddress";

    /**
     * 被保险人-教育状况
     */
    public final static String INSURED_EDUCATION = "insured.education";

    /**
     * 被保险人-电子邮件
     */
    public final static String INSURED_EMAIL = "insured.email";

    /**
     * 被保险人-邮编
     */
    public final static String INSURED_POSTCODE = "insured.postcode";

    /**
     * 被保险人-民族
     */
    public final static String INSURED_NATION = "insured.nation";

    /**
     * 被保险人-身高
     */
    public final static String INSURED_HEIGHT = "insured.height";

    /**
     * 被保险人-体重
     */
    public final static String INSURED_WEIGHT = "insured.weight";

    /**
     * 被保险人-籍贯
     */
    public final static String INSURED_NATIVEPLACE = "insured.nativePlace";

    /**
     * 被保险人-户籍
     */
    public final static String INSURED_CENSUSREGISTER = "insured.censusRegister";

    /**
     * 被保险人-通讯地址
     */
    public final static String INSURED_MAILINGADDRESS = "insured.mailingAddress";

    /**
     * 投保人-姓名
     */
    public final static String PROPOSER_NAME = "proposer.name";

    /**
     * 投保人-英文名
     */
    public final static String PROPOSER_ENGLISHNAME = "proposer.englishName";

    /**
     * 投保人-性别
     */
    public final static String PROPOSER_GENDER = "proposer.gender";

    /**
     * 投保人-出生日期
     */
    public final static String PROPOSER_BIRTHDAY = "proposer.birthday";

    /**
     * 投保人-年龄
     */
    public final static String PROPOSER_AGE = "proposer.age";

    /**
     * 投保人-证件类型
     */
    public final static String PROPOSER_CERTIFICATE = "proposer.certificate";

    /**
     * 投保人-证件名称
     */
    public final static String PROPOSER_CERTNAME = "proposer.certName";

    /**
     * 投保人-证件号码
     */
    public final static String PROPOSER_IDNUMBER = "proposer.IDNumber";

    /**
     * 投保人-电话号码
     */
    public final static String PROPOSER_PHONENUMBER = "proposer.phoneNumber";

    /**
     * 投保人-手机号码
     */
    public final static String PROPOSER_MOBILENUMBER = "proposer.mobileNumber";

    /**
     * 投保人-血型
     */
    public final static String PROPOSER_BLOODTYPE = "proposer.bloodType";

    /**
     * 投保人-婚姻状况
     */
    public final static String PROPOSER_MARITALSTATUS = "proposer.maritalStatus";

    /**
     * 投保人-住址
     */
    public final static String PROPOSER_LIVINGADDRESS = "proposer.livingAddress";

    /**
     * 投保人-教育状况
     */
    public final static String PROPOSER_EDUCATION = "proposer.education";

    /**
     * 投保人-电子邮件
     */
    public final static String PROPOSER_EMAIL = "proposer.email";

    /**
     * 投保人-邮编
     */
    public final static String PROPOSER_POSTCODE = "proposer.postcode";

    /**
     * 投保人-民族
     */
    public final static String PROPOSER_NATION = "proposer.nation";

    /**
     * 投保人-籍贯
     */
    public final static String PROPOSER_NATIVEPLACE = "proposer.nativePlace";

    /**
     * 投保人-户籍
     */
    public final static String PROPOSER_CENSUSREGISTER = "proposer.censusRegister";

    /**
     * 投保人-通讯地址
     */
    public final static String PROPOSER_MAILINGADDRESS = "proposer.mailingAddress";

    /**
     * 权益索赔人-姓名
     */
    public final static String BENEFICIARY_NAME = "beneficiary.name";

    /**
     * 权益索赔人-英文名
     */
    public final static String BENEFICIARY_ENGLISHNAME = "beneficiary.englishName";

    /**
     * 权益索赔人-性别
     */
    public final static String BENEFICIARY_GENDER = "beneficiary.gender";

    /**
     * 权益索赔人-出生日期
     */
    public final static String BENEFICIARY_BIRTHDAY = "beneficiary.birthday";

    /**
     * 权益索赔人-年龄
     */
    public final static String BENEFICIARY_AGE = "beneficiary.age";

    /**
     * 权益索赔人-证件类型
     */
    public final static String BENEFICIARY_CERTIFICATE = "beneficiary.certificate";

    /**
     * 权益索赔人-证件号码
     */
    public final static String BENEFICIARY_IDNUMBER = "beneficiary.IDNumber";

    /**
     * 权益索赔人-证件名称
     */
    public final static String BENEFICIARY_CERTNAME = "beneficiary.certName";

    /**
     * 权益索赔人-电话号码
     */
    public final static String BENEFICIARY_PHONENUMBER = "beneficiary.phoneNumber";

    /**
     * 权益索赔人-手机号码
     */
    public final static String BENEFICIARY_MOBILENUMBER = "beneficiary.mobileNumber";

    /**
     * 权益索赔人-血型
     */
    public final static String BENEFICIARY_BLOODTYPE = "beneficiary.bloodType";

    /**
     * 权益索赔人-婚姻状况
     */
    public final static String BENEFICIARY_MARITALSTATUS = "beneficiary.maritalStatus";

    /**
     * 权益索赔人-住址
     */
    public final static String BENEFICIARY_LIVINGADDRESS = "beneficiary.livingAddress";

    /**
     * 权益索赔人-教育状况
     */
    public final static String BENEFICIARY_EDUCATION = "beneficiary.education";

    /**
     * 权益索赔人-电子邮件
     */
    public final static String BENEFICIARY_EMAIL = "beneficiary.email";

    /**
     * 权益索赔人-邮编
     */
    public final static String BENEFICIARY_POSTCODE = "beneficiary.postcode";

    /**
     * 权益索赔人-民族
     */
    public final static String BENEFICIARY_NATION = "beneficiary.nation";

    /**
     * 权益索赔人-身高
     */
    public final static String BENEFICIARY_HEIGHT = "beneficiary.height";

    /**
     * 权益索赔人-体重
     */
    public final static String BENEFICIARY_WEIGHT = "beneficiary.weight";

    /**
     * 权益索赔人-籍贯
     */
    public final static String BENEFICIARY_NATIVEPLACE = "beneficiary.nativePlace";

    /**
     * 权益索赔人-户籍
     */
    public final static String BENEFICIARY_CENSUSREGISTER = "beneficiary.censusRegister";

    /**
     * 权益索赔人-通讯地址
     */
    public final static String BENEFICIARY_MAILINGADDRESS = "beneficiary.mailingAddress";

    /**
     * 车主-姓名
     */
    public final static String CAR_OWNER_NAME = "car.owner.name";

    /**
     * 车主-英文名
     */
    public final static String CAR_OWNER_ENGLISHNAME = "car.owner.englishName";

    /**
     * 车主-性别
     */
    public final static String CAR_OWNER_GENDER = "car.owner.gender";

    /**
     * 车主-出生日期
     */
    public final static String CAR_OWNER_BIRTHDAY = "car.owner.birthday";

    /**
     * 车主-年龄
     */
    public final static String CAR_OWNER_AGE = "car.owner.age";

    /**
     * 车主-证件类型
     */
    public final static String CAR_OWNER_CERTIFICATE = "car.owner.certificate";

    /**
     * 车主-证件号码
     */
    public final static String CAR_OWNER_IDNUMBER = "car.owner.IDNumber";

    /**
     * 车主-证件名称
     */
    public final static String CAR_OWNER_CERTNAME = "car.owner.certName";

    /**
     * 车主-电话号码
     */
    public final static String CAR_OWNER_PHONENUMBER = "car.owner.phoneNumber";

    /**
     * 车主-手机号码
     */
    public final static String CAR_OWNER_MOBILENUMBER = "car.owner.mobileNumber";

    /**
     * 车主-血型
     */
    public final static String CAR_OWNER_BLOODTYPE = "car.owner.bloodType";

    /**
     * 车主-婚姻状况
     */
    public final static String CAR_OWNER_MARITALSTATUS = "car.owner.maritalStatus";

    /**
     * 车主-住址
     */
    public final static String CAR_OWNER_LIVINGADDRESS = "car.owner.livingAddress";

    /**
     * 车主-教育状况
     */
    public final static String CAR_OWNER_EDUCATION = "car.owner.education";

    /**
     * 车主-电子邮件
     */
    public final static String CAR_OWNER_EMAIL = "car.owner.email";

    /**
     * 车主-邮编
     */
    public final static String CAR_OWNER_POSTCODE = "car.owner.postcode";

    /**
     * 车主-民族
     */
    public final static String CAR_OWNER_NATION = "car.owner.nation";

    /**
     * 车主-行驶证车主所属性质
     */
    public final static String CAR_OWNER_PROPERTY = "car.owner.property";

    /**
     * 车主-体重
     */
    public final static String CAR_OWNER_WEIGHT = "car.owner.weight";

    /**
     * 车主-籍贯
     */
    public final static String CAR_OWNER_NATIVEPLACE = "car.owner.nativePlace";

    /**
     * 车主-户籍
     */
    public final static String CAR_OWNER_CENSUSREGISTER = "car.owner.censusRegister";

    /**
     * 车主-通讯地址
     */
    public final static String CAR_OWNER_MAILINGADDRESS = "car.owner.mailingAddress";

    /**
     * 指定驾驶人-姓名
     */
    public final static String DRIVER_NAME = "driver.name";

    /**
     * 指定驾驶人-英文名
     */
    public final static String DRIVER_ENGLISHNAME = "driver.englishName";

    /**
     * 指定驾驶人-年龄
     */
    public final static String DRIVER_AGE = "driver.age";

    /**
     * 指定驾驶人-性别
     */
    public final static String DRIVER_GENDER = "driver.gender";

    /**
     * 指定驾驶人-出生日期
     */
    public final static String DRIVER_BIRTHDAY = "driver.birthday";

    /**
     * 指定驾驶人-驾龄
     */
    public final static String DRIVER_DRIVINGAGE = "driver.drivingAge";

    /**
     * 指定驾驶人-驾驶证类型
     */
    public final static String DRIVER_LICENCETYPE = "driver.licenceType";

    /**
     * 指定驾驶人-驾驶证号码
     */
    public final static String DRIVER_LICENCENUM = "driver.licenceNum";

    /**
     * 指定驾驶人-初次领证日期
     */
    public final static String DRIVER_LICENSEDDATE = "driver.licensedDate";

    /**
     * 指定驾驶人-驾驶证有效截止日期
     */
    public final static String DRIVER_EXPIRYDATE = "driver.expiryDate";

    /**
     * 指定驾驶人-是否主驾驶人
     */
    public final static String DRIVER_MAJORDRIVER = "driver.majorDriver";

    /**
     * 指定驾驶人-驾驶证描述
     */
    public final static String DRIVER_LICENSEDESC = "driver.licenseDesc";

    /**
     * 联系人-姓名
     */
    public final static String CONTACT_NAME = "contact.name";

    /**
     * 联系人-英文名
     */
    public final static String CONTACT_ENGLISHNAME = "contact.englishName";

    /**
     * 联系人-性别
     */
    public final static String CONTACT_GENDER = "contact.gender";

    /**
     * 联系人-出生日期
     */
    public final static String CONTACT_BIRTHDAY = "contact.birthday";

    /**
     * 联系人-年龄
     */
    public final static String CONTACT_AGE = "contact.age";

    /**
     * 联系人-证件类型
     */
    public final static String CONTACT_CERTIFICATE = "contact.certificate";

    /**
     * 联系人-证件号码
     */
    public final static String CONTACT_IDNUMBER = "contact.IDNumber";

    /**
     * 联系人-证件名称
     */
    public final static String CONTACT_CERTNAME = "contact.certName";

    /**
     * 联系人-电话号码
     */
    public final static String CONTACT_PHONENUMBER = "contact.phoneNumber";

    /**
     * 联系人-手机号码
     */
    public final static String CONTACT_MOBILENUMBER = "contact.mobileNumber";

    /**
     * 联系人-血型
     */
    public final static String CONTACT_BLOODTYPE = "contact.bloodType";

    /**
     * 联系人-婚姻状况
     */
    public final static String CONTACT_MARITALSTATUS = "contact.maritalStatus";

    /**
     * 联系人-居住地址
     */
    public final static String CONTACT_LIVINGADDRESS = "contact.livingAddress";

    /**
     * 联系人-教育状况
     */
    public final static String CONTACT_EDUCATION = "contact.education";

    /**
     * 联系人-电子邮件
     */
    public final static String CONTACT_EMAIL = "contact.email";

    /**
     * 联系人-邮编
     */
    public final static String CONTACT_POSTCODE = "contact.postcode";

    /**
     * 联系人-民族
     */
    public final static String CONTACT_NATION = "contact.nation";

    /**
     * 联系人-身高
     */
    public final static String CONTACT_HEIGHT = "contact.height";

    /**
     * 联系人-体重
     */
    public final static String CONTACT_WEIGHT = "contact.weight";

    /**
     * 联系人-籍贯
     */
    public final static String CONTACT_NATIVEPLACE = "contact.nativePlace";

    /**
     * 联系人-户籍
     */
    public final static String CONTACT_CENSUSREGISTER = "contact.censusRegister";

    /**
     * 联系人-通讯地址
     */
    public final static String CONTACT_MAILINGADDRESS = "contact.mailingAddress";

    /**
     * 纳税人-姓名
     */
    public final static String TAXER_NAME = "taxer.name";

    /**
     * 纳税人-英文名
     */
    public final static String TAXER_ENGLISHNAME = "taxer.englishName";

    /**
     * 纳税人-性别
     */
    public final static String TAXER_GENDER = "taxer.gender";

    /**
     * 纳税人-出生日期
     */
    public final static String TAXER_BIRTHDAY = "taxer.birthday";

    /**
     * 纳税人-年龄
     */
    public final static String TAXER_AGE = "taxer.age";

    /**
     * 纳税人-证件类型
     */
    public final static String TAXER_CERTIFICATE = "taxer.certificate";

    /**
     * 纳税人-纳税人识别号
     */
    public final static String TAXER_IDNUMBER = "taxer.IDNumber";

    /**
     * 纳税人-证件名称
     */
    public final static String TAXER_CERTNAME = "taxer.certName";

    /**
     * 纳税人-电话号码
     */
    public final static String TAXER_PHONENUMBER = "taxer.phoneNumber";

    /**
     * 纳税人-手机号码
     */
    public final static String TAXER_MOBILENUMBER = "taxer.mobileNumber";

    /**
     * 纳税人-血型
     */
    public final static String TAXER_BLOODTYPE = "taxer.bloodType";

    /**
     * 纳税人-婚姻状况
     */
    public final static String TAXER_MARITALSTATUS = "taxer.maritalStatus";

    /**
     * 纳税人-居住地址
     */
    public final static String TAXER_LIVINGADDRESS = "taxer.livingAddress";

    /**
     * 纳税人-教育状况
     */
    public final static String TAXER_EDUCATION = "taxer.education";

    /**
     * 纳税人-电子邮件
     */
    public final static String TAXER_EMAIL = "taxer.email";

    /**
     * 纳税人-邮编
     */
    public final static String TAXER_POSTCODE = "taxer.postcode";

    /**
     * 纳税人-民族
     */
    public final static String TAXER_NATION = "taxer.nation";

    /**
     * 纳税人-身高
     */
    public final static String TAXER_HEIGHT = "taxer.height";

    /**
     * 纳税人-体重
     */
    public final static String TAXER_WEIGHT = "taxer.weight";

    /**
     * 纳税人-籍贯
     */
    public final static String TAXER_NATIVEPLACE = "taxer.nativePlace";

    /**
     * 纳税人-户籍
     */
    public final static String TAXER_CENSUSREGISTER = "taxer.censusRegister";

    /**
     * 纳税人-通讯地址
     */
    public final static String TAXER_MAILINGADDRESS = "taxer.mailingAddress";

    /**
     * 收件人-姓名
     */
    public final static String ADDRESSEE_NAME = "addressee.name";

    /**
     * 收件人-英文名
     */
    public final static String ADDRESSEE_ENGLISHNAME = "addressee.englishName";

    /**
     * 收件人-性别
     */
    public final static String ADDRESSEE_GENDER = "addressee.gender";

    /**
     * 收件人-出生日期
     */
    public final static String ADDRESSEE_BIRTHDAY = "addressee.birthday";

    /**
     * 收件人-年龄
     */
    public final static String ADDRESSEE_AGE = "addressee.age";

    /**
     * 收件人-证件类型
     */
    public final static String ADDRESSEE_CERTIFICATE = "addressee.certificate";

    /**
     * 收件人-证件号码
     */
    public final static String ADDRESSEE_IDNUMBER = "addressee.IDNumber";

    /**
     * 收件人-证件名称
     */
    public final static String ADDRESSEE_CERTNAME = "addressee.certName";

    /**
     * 收件人-电话号码
     */
    public final static String ADDRESSEE_PHONENUMBER = "addressee.phoneNumber";

    /**
     * 收件人-手机号码
     */
    public final static String ADDRESSEE_MOBILENUMBER = "addressee.mobileNumber";

    /**
     * 收件人-血型
     */
    public final static String ADDRESSEE_BLOODTYPE = "addressee.bloodType";

    /**
     * 收件人-婚姻状况
     */
    public final static String ADDRESSEE_MARITALSTATUS = "addressee.maritalStatus";

    /**
     * 收件人-居住地址
     */
    public final static String ADDRESSEE_LIVINGADDRESS = "addressee.livingAddress";

    /**
     * 收件人-教育状况
     */
    public final static String ADDRESSEE_EDUCATION = "addressee.education";

    /**
     * 收件人-电子邮件
     */
    public final static String ADDRESSEE_EMAIL = "addressee.email";

    /**
     * 收件人-邮编
     */
    public final static String ADDRESSEE_POSTCODE = "addressee.postcode";

    /**
     * 收件人-民族
     */
    public final static String ADDRESSEE_NATION = "addressee.nation";

    /**
     * 收件人-身高
     */
    public final static String ADDRESSEE_HEIGHT = "addressee.height";

    /**
     * 收件人-体重
     */
    public final static String ADDRESSEE_WEIGHT = "addressee.weight";

    /**
     * 收件人-籍贯
     */
    public final static String ADDRESSEE_NATIVEPLACE = "addressee.nativePlace";

    /**
     * 收件人-户籍
     */
    public final static String ADDRESSEE_CENSUSREGISTER = "addressee.censusRegister";

    /**
     * 收件人-通讯地址
     */
    public final static String ADDRESSEE_MAILINGADDRESS = "addressee.mailingAddress";

    /**
     * 经办人-姓名
     */
    public final static String OPERATOR_NAME = "operator.name";

    /**
     * 经办人-英文名
     */
    public final static String OPERATOR_ENGLISHNAME = "operator.englishName";

    /**
     * 经办人-性别
     */
    public final static String OPERATOR_GENDER = "operator.gender";

    /**
     * 经办人-出生日期
     */
    public final static String OPERATOR_BIRTHDAY = "operator.birthday";

    /**
     * 经办人-年龄
     */
    public final static String OPERATOR_AGE = "operator.age";

    /**
     * 经办人-证件类型
     */
    public final static String OPERATOR_CERTIFICATE = "operator.certificate";

    /**
     * 经办人-证件号码
     */
    public final static String OPERATOR_IDNUMBER = "operator.IDNumber";

    /**
     * 经办人-证件名称
     */
    public final static String OPERATOR_CERTNAME = "operator.certName";

    /**
     * 经办人-电话号码
     */
    public final static String OPERATOR_PHONENUMBER = "operator.phoneNumber";

    /**
     * 经办人-手机号码
     */
    public final static String OPERATOR_MOBILENUMBER = "operator.mobileNumber";

    /**
     * 经办人-血型
     */
    public final static String OPERATOR_BLOODTYPE = "operator.bloodType";

    /**
     * 经办人-婚姻状况
     */
    public final static String OPERATOR_MARITALSTATUS = "operator.maritalStatus";

    /**
     * 经办人-居住地址
     */
    public final static String OPERATOR_LIVINGADDRESS = "operator.livingAddress";

    /**
     * 经办人-教育状况
     */
    public final static String OPERATOR_EDUCATION = "operator.education";

    /**
     * 经办人-电子邮件
     */
    public final static String OPERATOR_EMAIL = "operator.email";

    /**
     * 经办人-邮编
     */
    public final static String OPERATOR_POSTCODE = "operator.postcode";

    /**
     * 经办人-民族
     */
    public final static String OPERATOR_NATION = "operator.nation";

    /**
     * 经办人-身高
     */
    public final static String OPERATOR_HEIGHT = "operator.height";

    /**
     * 经办人-体重
     */
    public final static String OPERATOR_WEIGHT = "operator.weight";

    /**
     * 经办人-籍贯
     */
    public final static String OPERATOR_NATIVEPLACE = "operator.nativePlace";

    /**
     * 经办人-户籍
     */
    public final static String OPERATOR_CENSUSREGISTER = "operator.censusRegister";

    /**
     * 经办人-通讯地址
     */
    public final static String OPERATOR_MAILINGADDRESS = "operator.mailingAddress";

    /**
     * 经办人-经办人代码
     */
    public final static String OPERATOR_ID = "operator.ID";

    /**
     * 代理人-姓名
     */
    public final static String AGENT_NAME = "agent.name";

    /**
     * 代理人-英文名
     */
    public final static String AGENT_ENGLISHNAME = "agent.englishName";

    /**
     * 代理人-性别
     */
    public final static String AGENT_GENDER = "agent.gender";

    /**
     * 代理人-出生日期
     */
    public final static String AGENT_BIRTHDAY = "agent.birthday";

    /**
     * 代理人-年龄
     */
    public final static String AGENT_AGE = "agent.age";

    /**
     * 代理人-证件类型
     */
    public final static String AGENT_CERTIFICATE = "agent.certificate";

    /**
     * 代理人-证件号码
     */
    public final static String AGENT_IDNUMBER = "agent.IDNumber";

    /**
     * 代理人-证件名称
     */
    public final static String AGENT_CERTNAME = "agent.certName";

    /**
     * 代理人-电话号码
     */
    public final static String AGENT_PHONENUMBER = "agent.phoneNumber";

    /**
     * 代理人-手机号码
     */
    public final static String AGENT_MOBILENUMBER = "agent.mobileNumber";

    /**
     * 代理人-血型
     */
    public final static String AGENT_BLOODTYPE = "agent.bloodType";

    /**
     * 代理人-婚姻状况
     */
    public final static String AGENT_MARITALSTATUS = "agent.maritalStatus";

    /**
     * 代理人-居住地址
     */
    public final static String AGENT_LIVINGADDRESS = "agent.livingAddress";

    /**
     * 代理人-教育状况
     */
    public final static String AGENT_EDUCATION = "agent.education";

    /**
     * 代理人-电子邮件
     */
    public final static String AGENT_EMAIL = "agent.email";



    /********************  保网车险统一命名空间Version200-分类三——保险配置信  ******************/


    /********************  保网车险统一命名空间Version200-分类四——险别信  ******************/


    /********************  保网车险统一命名空间Version200-分类五——投保单信  ******************/
    /**
     * 投保单信息-客户忠诚度
     */
    public final static String APPLICATION_LOYALTY = "application.loyalty";

    /**
     * 投保单信息-投保地区
     */
    public final static String APPLICATION_INSUREAREA = "application.insureArea";

    /**
     * 投保单信息-商业险投保公司
     */
    public final static String APPLICATION_COMMERCIAL_INSURECO = "application.commercial.insureCo";

    /**
     * 投保单信息-交强险投保公司
     */
    public final static String APPLICATION_COMPULSORY_INSURECO = "application.compulsory.insureCo";

    /**
     * 投保单信息-商业险生效日期
     */
    public final static String APPLICATION_COMMERCIAL_EFFECTIVEDATE = "application.commercial.effectiveDate";

    /**
     * 投保单信息-交强险生效日期
     */
    public final static String APPLICATION_COMPULSORY_EFFECTIVEDATE = "application.compulsory.effectiveDate";

    /**
     * 投保单信息-商业险终止日期
     */
    public final static String APPLICATION_COMMERCIAL_EXPIRYDATE = "application.commercial.expiryDate";

    /**
     * 投保单信息-交强险终止日期
     */
    public final static String APPLICATION_COMPULSORY_EXPIRYDATE = "application.compulsory.expiryDate";

    /**
     * 投保单信息-商业险投保单号
     */
    public final static String APPLICATION_COMMERCIAL_NUM = "application.commercial.num";

    /**
     * 投保单信息-交强险投保单号
     */
    public final static String APPLICATION_COMPULSORY_NUM = "application.compulsory.num";

    /**
     * 投保单信息-上年商业险投保公司
     */
    public final static String APPLICATION_COMMERCIAL_LASTINSURECO = "application.commercial.lastInsureCo";

    /**
     * 投保单信息-上年商业险保单号
     */
    public final static String APPLICATION_COMMERCIAL_LASTPOLICEYNUM = "application.commercial.lastPoliceyNum";

    /**
     * 投保单信息-上年交强险保单号
     */
    public final static String COMPULSORY_LASTPOLICEYNUM = "application.compulsory.lastPoliceyNum";

    /**
     * 投保单信息-投保日期
     */
    public final static String APPLICATION_INSUREDATE = "application.insureDate";

    /**
     * 投保单信息-签单日期
     */
    public final static String APPLICATION_SIGNDATE = "application.signDate";

    /**
     * 投保单信息-争议解决
     */
    public final static String APPLICATION_DISPUTE = "application.dispute";

    /**
     * 投保单信息-是否验车
     */
    public final static String APPLICATION_VALIDATECAR = "application.validateCar";

    /**
     * 投保单信息-验车时间
     */
    public final static String APPLICATION_VALIDATECARDATE = "application.validateCarDate";

    /**
     * 投保单信息-商业险理赔次数
     */
    public final static String APPLICATION_COMMERCIAL_CLAIMTIMES = "application.commercial.claimTimes";

    /**
     * 投保单信息-交强险理赔次数
     */
    public final static String APPLICATION_COMPULSORY_CLAIMTIMES = "application.compulsory.claimTimes";

    /**
     * 投保单信息-商业险赔付率
     */
    public final static String APPLICATION_COMMERCIAL_CLAIMRATE = "application.commercial.claimRate";

    /**
     * 投保单信息-交强险赔付率
     */
    public final static String APPLICATION_COMPULSORY_CLAIMRATE = "application.compulsory.claimRate";

    /**
     * 投保单信息-交通违法次数
     */
    public final static String APPLICATION_TRAFFICOFFENCE = "application.trafficOffence";

    /**
     * 投保单信息-上年商业险理赔金额
     */
    public final static String APPLICATION_COMMERCIAL_LASTCLAIMSUM = "application.commercial.lastClaimSum";

    /**
     * 投保单信息-上年交强险理赔金额
     */
    public final static String APPLICATION_COMPULSORY_LASTCLAIMSUM = "application.compulsory.lastClaimSum";

    /**
     * 投保单信息-行驶区域
     */
    public final static String APPLICATION_DRIVINGAREA = "application.drivingArea";

    /**
     * 投保单信息-指定驾驶人
     */
    public final static String APPLICATION_ISSPECIFYDRIVER = "application.isSpecifyDriver";

    /**
     * 投保单信息-税务机关编码
     */
    public final static String APPLICATION_TAXAUTHORITYID = "application.taxAuthorityID";

    /**
     * 投保单信息-完税凭证号
     */
    public final static String APPLICATION_PAYTAXESNUM = "application.payTaxesNum";

    /**
     * 投保单信息-合计标准保费
     */
    public final static String APPLICATION_PREMIUM = "application.premium";

    /**
     * 投保单信息-合计折扣保费
     */
    public final static String APPLICATION_DISCOUNTPREMIUM = "application.discountPremium";

    /**
     * 投保单信息-特别约定
     */
    public final static String APPLICATION_SPECIALAGREEMENT = "application.specialAgreement";

    /**
     * 投保单信息-纳税情况
     */
    public final static String APPLICATION_TAXTYPE = "application.taxType";

    /**
     * 投保单信息-使用车队优惠
     */
    public final static String APPLICATION_USEMOTORCADEMODE = "application.useMotorcadeMode";

    /**
     * 投保单信息-商业险与去年一致
     */
    public final static String APPLICATION_COMMERCIALINSSAME = "application.commercialInsSame";

    /**
     * 投保单信息-指定驾驶人与去年一致
     */
    public final static String APPLICATION_DRIVERSAME = "application.driverSame";

    /**
     * 投保单信息-行驶区域与去年一致
     */
    public final static String APPLICATION_DRIVEREASAME = "application.drivereaSame";

    /**
     * 投保单信息-风险类别
     */
    public final static String APPLICATION_KINDOFRISK = "application.kindOfRisk";

    /**
     * 投保单信息-上年商业险保费
     */
    public final static String APPLICATION_COMMERCIAL_LASTPREMIUM = "application.commercial.lastPremium";



    /********************  保网车险统一命名空间Version200-分类六——保单信  ******************/
    /**
     * 保单信息-商业险保单号
     */
    public final static String POLICEY_COMMERCIAL_NUM = "policey.commercial.num";

    /**
     * 保单信息-交强险保单号
     */
    public final static String POLICEY_COMPULSORY_NUM = "policey.compulsory.num";

    /**
     * 保单信息-商业险投保公司
     */
    public final static String POLICEY_COMMERCIAL_INSURECO = "policey.commercial.insureCo";

    /**
     * 保单信息-交强险投保公司
     */
    public final static String POLICEY_COMPULSORY_INSURECO = "policey.compulsory.insureCo";

    /**
     * 保单信息-商业险生效日期
     */
    public final static String POLICEY_COMMERCIAL_EFFECTIVEDATE = "policey.commercial.effectiveDate";

    /**
     * 保单信息-交强险生效日期
     */
    public final static String POLICEY_COMPULSORY_EFFECTIVEDATE = "policey.compulsory.effectiveDate";

    /**
     * 保单信息-商业险终止日期
     */
    public final static String POLICEY_COMMERCIAL_EXPIRYDATE = "policey.commercial.expiryDate";

    /**
     * 保单信息-交强险终止日期
     */
    public final static String POLICEY_COMPULSORY_EXPIRYDATE = "policey.compulsory.expiryDate";

    /**
     * 保单信息-车船税当年应缴
     */
    public final static String POLICEY_VEHICLETAXSHOULDPAY = "policey.VehicleTaxShouldPay";

    /**
     * 保单信息-车船税往年补缴
     */
    public final static String POLICEY_VEHICLETAXMAKEUP = "policey.VehicleTaxMakeUp";

    /**
     * 保单信息-车船税滞纳金
     */
    public final static String POLICEY_VEHICLETAXOVERDUEFINE = "policey.VehicleTaxOverdueFine";

    /**
     * 保单信息-特别约定
     */
    public final static String POLICEY_SPECIALAGREEMENT = "policey.specialAgreement";

    /**
     * 保单信息-单证编号
     */
    public final static String POLICEY_DOCUMENTSID = "policey.documentsID";

    /**
     * 保单信息-保单生成时间
     */
    public final static String POLICEY_MAKEDATE = "policey.makeDate";

    /**
     * 保单信息-保单打印时间
     */
    public final static String POLICEY_PRINTDATE = "policey.printDate";

    /**
     * 投保单信息-签单日期
     */
    public final static String POLICEY_SIGNDATE = "policey.signDate";

    /**
     * 投保单信息-争议解决
     */
    public final static String POLICEY_DISPUTE = "policey.dispute";



    /********************  保网车险统一命名空间Version200-分类七——订单信  ******************/
    /**
     * 订单信息-业务跟踪号
     */
    public final static String ORDER_ID = "order.ID";

    /**
     * 订单信息-支付金额
     */
    public final static String ORDER_PAYMENT_SUM = "order.payment.sum";

    /**
     * 订单信息-支付方式
     */
    public final static String ORDER_PAYMENT_TERMS = "order.payment.terms";

    /**
     * 订单信息-配送方式
     */
    public final static String ORDER_DELIVERY_TERMS = "order.delivery.terms";

    /**
     * 订单信息-配送费用
     */
    public final static String ORDER_DELIVERY_EXPENSES = "order.delivery.expenses";

    /**
     * 订单信息-支付状态
     */
    public final static String ORDER_PAYMENT_STATE = "order.payment.state";

    /**
     * 订单信息-承保状态
     */
    public final static String ORDER_UNDERWRITE_STATE = "order.underwrite.state";

    /**
     * 订单信息-配送状态
     */
    public final static String ORDER_DELIVERY_STATE = "order.delivery.state";

    /**
     * 订单信息-支付号
     */
    public final static String ORDER_PAYMENT_NUM = "order.payment.num";

    /**
     * 订单信息-校验码
     */
    public final static String ORDER_CHECKNUM = "order.checkNum";

    /**
     * 订单信息-订单创建日期
     */
    public final static String ORDER_MAKEDATE = "order.makeDate";

    /**
     * 订单信息-付款时间
     */
    public final static String ORDER_PAYOFFDATE = "order.payoffDate";



    /********************  保网车险统一命名空间Version200-分类八——机构信  ******************/
    /**
     * 机构信息-机构代码
     */
    public final static String ORGANIZATION_ORGCODE = "organization.orgCode";

    /**
     * 机构信息-机构类型
     */
    public final static String ORGANIZATION_ORGTYPE = "organization.orgType";

    /**
     * 机构信息-全称
     */
    public final static String ORGANIZATION_FULLNAME = "organization.fullName";

    /**
     * 机构信息-简称
     */
    public final static String ORGANIZATION_SHORTNAME = "organization.shortName";

    /**
     * 机构信息-机构组织地址
     */
    public final static String ORGANIZATION_ADDRESS = "organization.address";

    /**
     * 机构信息-邮编
     */
    public final static String ORGANIZATION_ZIPCODE = "organization.zipCode";

    /**
     * 机构信息-电话
     */
    public final static String ORGANIZATION_PHONE = "organization.phone";

    /**
     * 机构信息-传真
     */
    public final static String ORGANIZATION_FAX = "organization.fax";

    /**
     * 机构信息-电子邮件
     */
    public final static String ORGANIZATION_EMAIL = "organization.email";

    /**
     * 机构信息-网址
     */
    public final static String ORGANIZATION_WEBSITE = "organization.website";

    /**
     * 机构信息-机构联系人
     */
    public final static String ORGANIZATION_CONTACT = "organization.contact";



    /********************  保网车险统一命名空间Version200-分类九——其他信  ******************/
    /**
     * 其他信息-被保人姓名
     */
    public final static String OTHERS_INSURED_NAME = "others.insured.name";

    /**
     * 其他信息-车主姓名
     */
    public final static String OTHERS_CAR_OWNER_NAME = "others.car.owner.name";

    /**
     * 其他信息-车牌号
     */
    public final static String OTHERS_CAR_SPECIFIC_LICENSE = "others.car.specific.license";

    /**
     * 其他信息-供应商id
     */
    public final static String OTHERS_PROVIDERID = "others.providerId";

    /**
     * 其他信息-保险公司流水号
     */
    public final static String OTHERS_INSCOMPANY_FLOWID = "others.insCompany.flowId";

    /**
     * 其他信息-投保方式
     */
    public final static String OTHERS_INSUREWAY = "others.insureWay";

    /**
     * 其他信息-第1级代理机构ID
     */
    public final static String OTHERS_AGENCY_ORGID1 = "others.agency.orgId1";

    /**
     * 其他信息-第2级代理机构ID
     */
    public final static String OTHERS_AGENCY_ORGID2 = "others.agency.orgId2";

    /**
     * 其他信息-第3级代理机构ID
     */
    public final static String OTHERS_AGENCY_ORGID3 = "others.agency.orgId3";

    /**
     * 其他信息-第4级代理机构ID
     */
    public final static String OTHERS_AGENCY_ORGID4 = "others.agency.orgId4";

    /**
     * 其他信息-第5级代理机构ID
     */
    public final static String OTHERS_AGENCY_ORGID5 = "others.agency.orgId5";

    /**
     * 其他信息-第1级代理机构名称
     */
    public final static String OTHERS_AGENCY_ORGNAME1 = "others.agency.orgName1";

    /**
     * 其他信息-第2级代理机构名称
     */
    public final static String OTHERS_AGENCY_ORGNAME2 = "others.agency.orgName2";

    /**
     * 其他信息-第3级代理机构名称
     */
    public final static String OTHERS_AGENCY_ORGNAME3 = "others.agency.orgName3";

    /**
     * 其他信息-第4级代理机构名称
     */
    public final static String OTHERS_AGENCY_ORGNAME4 = "others.agency.orgName4";

    /**
     * 其他信息-第5级代理机构名称
     */
    public final static String OTHERS_AGENCY_ORGNAME5 = "others.agency.orgName5";

    /**
     * 其他信息-其他属性
     */
    public final static String OTHERS_ATTRS = "others.attrs";

    /**
     * 其他信息-返回渠道
     */
    public final static String OTHERS_CHANNEL = "others.channel";

    /**
     * 其他信息-总价
     */
    public final static String OTHERS_TOTALPRICECALC = "others.totalPriceCalc";

    /**
     * 其他信息-折扣
     */
    public final static String OTHERS_DISCOUNTCALC = "others.discountCalc";

    /**
     * 其他信息-折扣率
     */
    public final static String OTHERS_DISCOUNTRATECALC = "others.discountRateCalc";

    /**
     * 其他信息-原价
     */
    public final static String OTHERS_ORIGINALCALC = "others.originalCalc";

    /**
     * 其他信息-不计免赔总保费
     */
    public final static String OTHERS_AVOIDPRICECALC = "others.avoidPriceCalc";

    /**
     * 其他信息-商业险折后总金额
     */
    public final static String OTHERS_COMMERCIAL_PRICECALC = "others.commercial.priceCalc";

    /**
     * 其他信息-交强险折后总金额
     */
    public final static String OTHERS_COMPULSORY_PRICECALC = "others.compulsory.priceCalc";

    /**
     * 其他信息-车船税折后总金额
     */
    public final static String OTHERS_TAX_PRICECALC = "others.tax.priceCalc";

    /**
     * 其他信息-商业险原价
     */
    public final static String OTHERS_COMMERCIAL_ORIGINALCALC = "others.commercial.originalCalc";

    /**
     * 其他信息-交强险原价
     */
    public final static String OTHERS_COMPULSORY_ORIGINALCALC = "others.compulsory.originalCalc";

    /**
     * 其他信息-车船税原价
     */
    public final static String OTHERS_TAX_ORIGINALCALC = "others.tax.originalCalc";

    /**
     * 其他信息-商业险折扣
     */
    public final static String OTHERS_COMMERCIAL_DISCOUNTCALC = "others.commercial.discountCalc";

    /**
     * 其他信息-交强险折扣
     */
    public final static String OTHERS_COMPULSORY_DISCOUNTCALC = "others.compulsory.discountCalc";

    /**
     * 其他信息-车船税折扣
     */
    public final static String OTHERS_TAX_DISCOUNTCALC = "others.tax.discountCalc";

    /**
     * 其他信息-商业险折扣率
     */
    public final static String OTHERS_COMMERCIAL_DISCOUNTRATECALC = "others.commercial.discountRateCalc";

    /**
     * 其他信息-交强险折扣率
     */
    public final static String OTHERS_COMPULSORY_DISCOUNTRATECALC = "others.compulsory.discountRateCalc";

    /**
     * 其他信息-车船税折扣率
     */
    public final static String OTHERS_TAX_DISCOUNTRATECALC = "others.tax.discountRateCalc";

    /**
     * 其他信息-结果是成功还是失败
     */
    public final static String OTHERS_RESULT_SUCCESS = "others.result.success";

    /**
     * 其他信息-结果状态码
     */
    public final static String OTHERS_RESULT_STATUSCODE = "others.result.statusCode";

    /**
     * 其他信息-结果信息
     */
    public final static String OTHERS_RESULT_MSG = "others.result.msg";

    /**
     * 其他信息-商业险投保状态
     */
    public final static String OTHERS_COMMERCIAL_INSSTATUS = "others.commercial.insStatus";

    /**
     * 其他信息-交强险投保状态
     */
    public final static String OTHERS_COMPULSORY_INSSTATUS = "others.compulsory.insStatus";

    /**
     * 其他信息-保险公司代码
     */
    public final static String OTHERS_INSCOMPANY_CODE = "others.insCompany.code";

    /**
     * 其他信息-保险公司名称
     */
    public final static String OTHERS_INSCOMPANY_NAME = "others.insCompany.name";

    /**
     * 其他信息-业务类型
     */
    public final static String OTHERS_BUZZCATEGORY = "others.buzzCategory";

    /**
     * 其他信息-商业险和交强险是否必须同时投保
     */
    public final static String OTHERS_MUSTINSURESAMETIME = "others.mustInsureSameTime";

    /**
     * 其他信息-业务询价号
     */
    public final static String OTHERS_ENQUIRYID = "others.enquiryId";

    /**
     * 其他信息-JIRA任务key
     */
    public final static String OTHERS_JIRAKEY = "others.jiraKey";

    /**
     * 其他信息-任务类型
     */
    public final static String OTHERS_TASKTYPE = "others.taskType";

    /**
     * 其他信息-用户会话ID
     */
    public final static String OTHERS_USER_SESSIONID = "others.user.sessionid";



    /********************  保网车险统一命名空间Version200-分类十——规则信  ******************/
    /**
     * String-符合规则约束
     */
    public final static String RULEITEM_ISRULECHECKED = "ruleItem.isRuleChecked";

    /**
     * String-规则提示内容
     */
    public final static String RULEITEM_RULEVALUE = "ruleItem.ruleValue";

    /**
     * String-代缴车船税
     */
    public final static String RULEITEM_ISAGENTTAX = "ruleItem.isAgentTax";

    /**
     * String(Number暂用String代替)-商业险折扣系数
     */
    public final static String RULEITEM_DISCOUNT = "ruleItem.discount";

    /**
     * String(Number暂用String代替)-交强险折扣系数
     */
    public final static String RULEITEM_COMPULSORYDISCOUNT = "ruleItem.compulsoryDiscount";

    /**
     * String-车船税车型编码
     */
    public final static String RULEITEM_TAXCARTYPE = "ruleItem.taxCarType";

    /**
     * String(Number暂用String代替)-规则集编号
     */
    public final static String RULEITEM_RULEID = "ruleItem.ruleID";

    /**
     * String(Number暂用String代替)-专修保障费率
     */
    public final static String RULEITEM_REPAIRDISCOUNT = "ruleItem.repairDiscount";

    /**
     * String(Number暂用String代替)-车价浮动比率
     */
    public final static String RULEITEM_FLOATPRICERATE = "ruleItem.floatPriceRate";

    /**
     * String-车辆价格类型
     */
    public final static String RULEITEM_CARPRICETYPE = "ruleItem.carPriceType";

    /**
     * String-限制车型年款
     */
    public final static String RULEITEM_INMAKEDATE = "ruleItem.inMakeDate";



    /********************  保险配置信息 和 险别 组合生成  ******************/
    /**
     * 车辆损失险-险种名称
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_NAME = "insureItem.vehicleDemageIns.name";

    /**
     * 车辆损失险-险种代码
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_CODE = "insureItem.vehicleDemageIns.code";

    /**
     * 车辆损失险-价格计算公式
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_PRICECALCULATOR = "insureItem.vehicleDemageIns.priceCalculator";

    /**
     * 车辆损失险-折扣
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_DISCOUNTCALC = "insureItem.vehicleDemageIns.discountCalc";

    /**
     * 车辆损失险-折扣率
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_DISCOUNTRATECALC = "insureItem.vehicleDemageIns.discountRateCalc";

    /**
     * 车辆损失险-原价
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_ORIGINALCALC = "insureItem.vehicleDemageIns.originalCalc";

    /**
     * 车辆损失险-当前保额
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_CURRENT = "insureItem.vehicleDemageIns.current";

    /**
     * 车辆损失险-不计免赔险代码
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_AVOIDLOSSCODE = "insureItem.vehicleDemageIns.avoidLossCode";

    /**
     * 车辆损失险-是否不计免赔
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_ISAVOIDLOSS = "insureItem.vehicleDemageIns.isAvoidLoss";

    /**
     * 车辆损失险-保费
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS_PREMIUM = "insureItem.vehicleDemageIns.premium";

    /**
     * 第三者责任险-险种名称
     */
    public final static String INSUREITEM_THIRDPARTYINS_NAME = "insureItem.thirdPartyIns.name";

    /**
     * 第三者责任险-险种代码
     */
    public final static String INSUREITEM_THIRDPARTYINS_CODE = "insureItem.thirdPartyIns.code";

    /**
     * 第三者责任险-价格计算公式
     */
    public final static String INSUREITEM_THIRDPARTYINS_PRICECALCULATOR = "insureItem.thirdPartyIns.priceCalculator";

    /**
     * 第三者责任险-折扣
     */
    public final static String INSUREITEM_THIRDPARTYINS_DISCOUNTCALC = "insureItem.thirdPartyIns.discountCalc";

    /**
     * 第三者责任险-折扣率
     */
    public final static String INSUREITEM_THIRDPARTYINS_DISCOUNTRATECALC = "insureItem.thirdPartyIns.discountRateCalc";

    /**
     * 第三者责任险-原价
     */
    public final static String INSUREITEM_THIRDPARTYINS_ORIGINALCALC = "insureItem.thirdPartyIns.originalCalc";

    /**
     * 第三者责任险-当前保额
     */
    public final static String INSUREITEM_THIRDPARTYINS_CURRENT = "insureItem.thirdPartyIns.current";

    /**
     * 第三者责任险-不计免赔险代码
     */
    public final static String INSUREITEM_THIRDPARTYINS_AVOIDLOSSCODE = "insureItem.thirdPartyIns.avoidLossCode";

    /**
     * 第三者责任险-是否不计免赔
     */
    public final static String INSUREITEM_THIRDPARTYINS_ISAVOIDLOSS = "insureItem.thirdPartyIns.isAvoidLoss";

    /**
     * 第三者责任险-保费
     */
    public final static String INSUREITEM_THIRDPARTYINS_PREMIUM = "insureItem.thirdPartyIns.premium";

    /**
     * 司机责任险-险种名称
     */
    public final static String INSUREITEM_DRIVERINS_NAME = "insureItem.driverIns.name";

    /**
     * 司机责任险-险种代码
     */
    public final static String INSUREITEM_DRIVERINS_CODE = "insureItem.driverIns.code";

    /**
     * 司机责任险-价格计算公式
     */
    public final static String INSUREITEM_DRIVERINS_PRICECALCULATOR = "insureItem.driverIns.priceCalculator";

    /**
     * 司机责任险-折扣
     */
    public final static String INSUREITEM_DRIVERINS_DISCOUNTCALC = "insureItem.driverIns.discountCalc";

    /**
     * 司机责任险-折扣率
     */
    public final static String INSUREITEM_DRIVERINS_DISCOUNTRATECALC = "insureItem.driverIns.discountRateCalc";

    /**
     * 司机责任险-原价
     */
    public final static String INSUREITEM_DRIVERINS_ORIGINALCALC = "insureItem.driverIns.originalCalc";

    /**
     * 司机责任险-当前保额
     */
    public final static String INSUREITEM_DRIVERINS_CURRENT = "insureItem.driverIns.current";

    /**
     * 司机责任险-不计免赔险代码
     */
    public final static String INSUREITEM_DRIVERINS_AVOIDLOSSCODE = "insureItem.driverIns.avoidLossCode";

    /**
     * 司机责任险-是否不计免赔
     */
    public final static String INSUREITEM_DRIVERINS_ISAVOIDLOSS = "insureItem.driverIns.isAvoidLoss";

    /**
     * 司机责任险-保费
     */
    public final static String INSUREITEM_DRIVERINS_PREMIUM = "insureItem.driverIns.premium";

    /**
     * 乘客责任险-险种名称
     */
    public final static String INSUREITEM_PASSENGERINS_NAME = "insureItem.passengerIns.name";

    /**
     * 乘客责任险-险种代码
     */
    public final static String INSUREITEM_PASSENGERINS_CODE = "insureItem.passengerIns.code";

    /**
     * 乘客责任险-价格计算公式
     */
    public final static String INSUREITEM_PASSENGERINS_PRICECALCULATOR = "insureItem.passengerIns.priceCalculator";

    /**
     * 乘客责任险-折扣
     */
    public final static String INSUREITEM_PASSENGERINS_DISCOUNTCALC = "insureItem.passengerIns.discountCalc";

    /**
     * 乘客责任险-折扣率
     */
    public final static String INSUREITEM_PASSENGERINS_DISCOUNTRATECALC = "insureItem.passengerIns.discountRateCalc";

    /**
     * 乘客责任险-原价
     */
    public final static String INSUREITEM_PASSENGERINS_ORIGINALCALC = "insureItem.passengerIns.originalCalc";

    /**
     * 乘客责任险-当前保额
     */
    public final static String INSUREITEM_PASSENGERINS_CURRENT = "insureItem.passengerIns.current";

    /**
     * 乘客责任险-不计免赔险代码
     */
    public final static String INSUREITEM_PASSENGERINS_AVOIDLOSSCODE = "insureItem.passengerIns.avoidLossCode";

    /**
     * 乘客责任险-是否不计免赔
     */
    public final static String INSUREITEM_PASSENGERINS_ISAVOIDLOSS = "insureItem.passengerIns.isAvoidLoss";

    /**
     * 乘客责任险-保费
     */
    public final static String INSUREITEM_PASSENGERINS_PREMIUM = "insureItem.passengerIns.premium";

    /**
     * 全车盗抢险-险种名称
     */
    public final static String INSUREITEM_THEFTINS_NAME = "insureItem.theftIns.name";

    /**
     * 全车盗抢险-险种代码
     */
    public final static String INSUREITEM_THEFTINS_CODE = "insureItem.theftIns.code";

    /**
     * 全车盗抢险-价格计算公式
     */
    public final static String INSUREITEM_THEFTINS_PRICECALCULATOR = "insureItem.theftIns.priceCalculator";

    /**
     * 全车盗抢险-折扣
     */
    public final static String INSUREITEM_THEFTINS_DISCOUNTCALC = "insureItem.theftIns.discountCalc";

    /**
     * 全车盗抢险-折扣率
     */
    public final static String INSUREITEM_THEFTINS_DISCOUNTRATECALC = "insureItem.theftIns.discountRateCalc";

    /**
     * 全车盗抢险-原价
     */
    public final static String INSUREITEM_THEFTINS_ORIGINALCALC = "insureItem.theftIns.originalCalc";

    /**
     * 全车盗抢险-当前保额
     */
    public final static String INSUREITEM_THEFTINS_CURRENT = "insureItem.theftIns.current";

    /**
     * 全车盗抢险-不计免赔险代码
     */
    public final static String INSUREITEM_THEFTINS_AVOIDLOSSCODE = "insureItem.theftIns.avoidLossCode";

    /**
     * 全车盗抢险-是否不计免赔
     */
    public final static String INSUREITEM_THEFTINS_ISAVOIDLOSS = "insureItem.theftIns.isAvoidLoss";

    /**
     * 全车盗抢险-保费
     */
    public final static String INSUREITEM_THEFTINS_PREMIUM = "insureItem.theftIns.premium";

    /**
     * 玻璃单独破碎险-险种名称
     */
    public final static String INSUREITEM_GLASSINS_NAME = "insureItem.glassIns.name";

    /**
     * 玻璃单独破碎险-险种代码
     */
    public final static String INSUREITEM_GLASSINS_CODE = "insureItem.glassIns.code";

    /**
     * 玻璃单独破碎险-价格计算公式
     */
    public final static String INSUREITEM_GLASSINS_PRICECALCULATOR = "insureItem.glassIns.priceCalculator";

    /**
     * 玻璃单独破碎险-折扣
     */
    public final static String INSUREITEM_GLASSINS_DISCOUNTCALC = "insureItem.glassIns.discountCalc";

    /**
     * 玻璃单独破碎险-折扣率
     */
    public final static String INSUREITEM_GLASSINS_DISCOUNTRATECALC = "insureItem.glassIns.discountRateCalc";

    /**
     * 玻璃单独破碎险-原价
     */
    public final static String INSUREITEM_GLASSINS_ORIGINALCALC = "insureItem.glassIns.originalCalc";

    /**
     * 玻璃单独破碎险-当前保额
     */
    public final static String INSUREITEM_GLASSINS_CURRENT = "insureItem.glassIns.current";

    /**
     * 玻璃单独破碎险-不计免赔险代码
     */
    public final static String INSUREITEM_GLASSINS_AVOIDLOSSCODE = "insureItem.glassIns.avoidLossCode";

    /**
     * 玻璃单独破碎险-是否不计免赔
     */
    public final static String INSUREITEM_GLASSINS_ISAVOIDLOSS = "insureItem.glassIns.isAvoidLoss";

    /**
     * 玻璃单独破碎险-保费
     */
    public final static String INSUREITEM_GLASSINS_PREMIUM = "insureItem.glassIns.premium";

    /**
     * 自燃损失险-险种名称
     */
    public final static String INSUREITEM_COMBUSTIONINS_NAME = "insureItem.combustionIns.name";

    /**
     * 自燃损失险-险种代码
     */
    public final static String INSUREITEM_COMBUSTIONINS_CODE = "insureItem.combustionIns.code";

    /**
     * 自燃损失险-价格计算公式
     */
    public final static String INSUREITEM_COMBUSTIONINS_PRICECALCULATOR = "insureItem.combustionIns.priceCalculator";

    /**
     * 自燃损失险-折扣
     */
    public final static String INSUREITEM_COMBUSTIONINS_DISCOUNTCALC = "insureItem.combustionIns.discountCalc";

    /**
     * 自燃损失险-折扣率
     */
    public final static String INSUREITEM_COMBUSTIONINS_DISCOUNTRATECALC = "insureItem.combustionIns.discountRateCalc";

    /**
     * 自燃损失险-原价
     */
    public final static String INSUREITEM_COMBUSTIONINS_ORIGINALCALC = "insureItem.combustionIns.originalCalc";

    /**
     * 自燃损失险-当前保额
     */
    public final static String INSUREITEM_COMBUSTIONINS_CURRENT = "insureItem.combustionIns.current";

    /**
     * 自燃损失险-不计免赔险代码
     */
    public final static String INSUREITEM_COMBUSTIONINS_AVOIDLOSSCODE = "insureItem.combustionIns.avoidLossCode";

    /**
     * 自燃损失险-是否不计免赔
     */
    public final static String INSUREITEM_COMBUSTIONINS_ISAVOIDLOSS = "insureItem.combustionIns.isAvoidLoss";

    /**
     * 自燃损失险-保费
     */
    public final static String INSUREITEM_COMBUSTIONINS_PREMIUM = "insureItem.combustionIns.premium";

    /**
     * 车身划痕险-险种名称
     */
    public final static String INSUREITEM_SCRATCHINS_NAME = "insureItem.scratchIns.name";

    /**
     * 车身划痕险-险种代码
     */
    public final static String INSUREITEM_SCRATCHINS_CODE = "insureItem.scratchIns.code";

    /**
     * 车身划痕险-价格计算公式
     */
    public final static String INSUREITEM_SCRATCHINS_PRICECALCULATOR = "insureItem.scratchIns.priceCalculator";

    /**
     * 车身划痕险-折扣
     */
    public final static String INSUREITEM_SCRATCHINS_DISCOUNTCALC = "insureItem.scratchIns.discountCalc";

    /**
     * 车身划痕险-折扣率
     */
    public final static String INSUREITEM_SCRATCHINS_DISCOUNTRATECALC = "insureItem.scratchIns.discountRateCalc";

    /**
     * 车身划痕险-原价
     */
    public final static String INSUREITEM_SCRATCHINS_ORIGINALCALC = "insureItem.scratchIns.originalCalc";

    /**
     * 车身划痕险-当前保额
     */
    public final static String INSUREITEM_SCRATCHINS_CURRENT = "insureItem.scratchIns.current";

    /**
     * 车身划痕险-不计免赔险代码
     */
    public final static String INSUREITEM_SCRATCHINS_AVOIDLOSSCODE = "insureItem.scratchIns.avoidLossCode";

    /**
     * 车身划痕险-是否不计免赔
     */
    public final static String INSUREITEM_SCRATCHINS_ISAVOIDLOSS = "insureItem.scratchIns.isAvoidLoss";

    /**
     * 车身划痕险-保费
     */
    public final static String INSUREITEM_SCRATCHINS_PREMIUM = "insureItem.scratchIns.premium";

    /**
     * 涉水损失险-险种名称
     */
    public final static String INSUREITEM_WADINGINS_NAME = "insureItem.wadingIns.name";

    /**
     * 涉水损失险-险种代码
     */
    public final static String INSUREITEM_WADINGINS_CODE = "insureItem.wadingIns.code";

    /**
     * 涉水损失险-价格计算公式
     */
    public final static String INSUREITEM_WADINGINS_PRICECALCULATOR = "insureItem.wadingIns.priceCalculator";

    /**
     * 涉水损失险-折扣
     */
    public final static String INSUREITEM_WADINGINS_DISCOUNTCALC = "insureItem.wadingIns.discountCalc";

    /**
     * 涉水损失险-折扣率
     */
    public final static String INSUREITEM_WADINGINS_DISCOUNTRATECALC = "insureItem.wadingIns.discountRateCalc";

    /**
     * 涉水损失险-原价
     */
    public final static String INSUREITEM_WADINGINS_ORIGINALCALC = "insureItem.wadingIns.originalCalc";

    /**
     * 涉水损失险-当前保额
     */
    public final static String INSUREITEM_WADINGINS_CURRENT = "insureItem.wadingIns.current";

    /**
     * 涉水损失险-不计免赔险代码
     */
    public final static String INSUREITEM_WADINGINS_AVOIDLOSSCODE = "insureItem.wadingIns.avoidLossCode";

    /**
     * 涉水损失险-是否不计免赔
     */
    public final static String INSUREITEM_WADINGINS_ISAVOIDLOSS = "insureItem.wadingIns.isAvoidLoss";

    /**
     * 涉水损失险-保费
     */
    public final static String INSUREITEM_WADINGINS_PREMIUM = "insureItem.wadingIns.premium";

    /**
     * 指定专修厂-险种名称
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_NAME = "insureItem.specifyingPlantCla.name";

    /**
     * 指定专修厂-险种代码
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_CODE = "insureItem.specifyingPlantCla.code";

    /**
     * 指定专修厂-价格计算公式
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_PRICECALCULATOR = "insureItem.specifyingPlantCla.priceCalculator";

    /**
     * 指定专修厂-折扣
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_DISCOUNTCALC = "insureItem.specifyingPlantCla.discountCalc";

    /**
     * 指定专修厂-折扣率
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_DISCOUNTRATECALC = "insureItem.specifyingPlantCla.discountRateCalc";

    /**
     * 指定专修厂-原价
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_ORIGINALCALC = "insureItem.specifyingPlantCla.originalCalc";

    /**
     * 指定专修厂-当前保额
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_CURRENT = "insureItem.specifyingPlantCla.current";

    /**
     * 指定专修厂-不计免赔险代码
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_AVOIDLOSSCODE = "insureItem.specifyingPlantCla.avoidLossCode";

    /**
     * 指定专修厂-是否不计免赔
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_ISAVOIDLOSS = "insureItem.specifyingPlantCla.isAvoidLoss";

    /**
     * 指定专修厂-保费
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA_PREMIUM = "insureItem.specifyingPlantCla.premium";

    /**
     * 车碰车损失险-险种名称
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_NAME = "insureItem.carToCarDamageIns.name";

    /**
     * 车碰车损失险-险种代码
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_CODE = "insureItem.carToCarDamageIns.code";

    /**
     * 车碰车损失险-价格计算公式
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_PRICECALCULATOR = "insureItem.carToCarDamageIns.priceCalculator";

    /**
     * 车碰车损失险-折扣
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_DISCOUNTCALC = "insureItem.carToCarDamageIns.discountCalc";

    /**
     * 车碰车损失险-折扣率
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_DISCOUNTRATECALC = "insureItem.carToCarDamageIns.discountRateCalc";

    /**
     * 车碰车损失险-原价
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_ORIGINALCALC = "insureItem.carToCarDamageIns.originalCalc";

    /**
     * 车碰车损失险-当前保额
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_CURRENT = "insureItem.carToCarDamageIns.current";

    /**
     * 车碰车损失险-不计免赔险代码
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_AVOIDLOSSCODE = "insureItem.carToCarDamageIns.avoidLossCode";

    /**
     * 车碰车损失险-是否不计免赔
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_ISAVOIDLOSS = "insureItem.carToCarDamageIns.isAvoidLoss";

    /**
     * 车碰车损失险-保费
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS_PREMIUM = "insureItem.carToCarDamageIns.premium";

    /**
     * 自燃免除特约-险种名称
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_NAME = "insureItem.combustionExclusionCla.name";

    /**
     * 自燃免除特约-险种代码
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_CODE = "insureItem.combustionExclusionCla.code";

    /**
     * 自燃免除特约-价格计算公式
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_PRICECALCULATOR = "insureItem.combustionExclusionCla.priceCalculator";

    /**
     * 自燃免除特约-折扣
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_DISCOUNTCALC = "insureItem.combustionExclusionCla.discountCalc";

    /**
     * 自燃免除特约-折扣率
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_DISCOUNTRATECALC = "insureItem.combustionExclusionCla.discountRateCalc";

    /**
     * 自燃免除特约-原价
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_ORIGINALCALC = "insureItem.combustionExclusionCla.originalCalc";

    /**
     * 自燃免除特约-当前保额
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_CURRENT = "insureItem.combustionExclusionCla.current";

    /**
     * 自燃免除特约-不计免赔险代码
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_AVOIDLOSSCODE = "insureItem.combustionExclusionCla.avoidLossCode";

    /**
     * 自燃免除特约-是否不计免赔
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_ISAVOIDLOSS = "insureItem.combustionExclusionCla.isAvoidLoss";

    /**
     * 自燃免除特约-保费
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA_PREMIUM = "insureItem.combustionExclusionCla.premium";

    /**
     * 涉水免除特约-险种名称
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_NAME = "insureItem.wadingExclusionCla.name";

    /**
     * 涉水免除特约-险种代码
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_CODE = "insureItem.wadingExclusionCla.code";

    /**
     * 涉水免除特约-价格计算公式
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_PRICECALCULATOR = "insureItem.wadingExclusionCla.priceCalculator";

    /**
     * 涉水免除特约-折扣
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_DISCOUNTCALC = "insureItem.wadingExclusionCla.discountCalc";

    /**
     * 涉水免除特约-折扣率
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_DISCOUNTRATECALC = "insureItem.wadingExclusionCla.discountRateCalc";

    /**
     * 涉水免除特约-原价
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_ORIGINALCALC = "insureItem.wadingExclusionCla.originalCalc";

    /**
     * 涉水免除特约-当前保额
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_CURRENT = "insureItem.wadingExclusionCla.current";

    /**
     * 涉水免除特约-不计免赔险代码
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_AVOIDLOSSCODE = "insureItem.wadingExclusionCla.avoidLossCode";

    /**
     * 涉水免除特约-是否不计免赔
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_ISAVOIDLOSS = "insureItem.wadingExclusionCla.isAvoidLoss";

    /**
     * 涉水免除特约-保费
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA_PREMIUM = "insureItem.wadingExclusionCla.premium";

    /**
     * 可选免赔额特约-险种名称
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_NAME = "insureItem.optionalDeductiblesCla.name";

    /**
     * 可选免赔额特约-险种代码
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_CODE = "insureItem.optionalDeductiblesCla.code";

    /**
     * 可选免赔额特约-价格计算公式
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_PRICECALCULATOR = "insureItem.optionalDeductiblesCla.priceCalculator";

    /**
     * 可选免赔额特约-折扣
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_DISCOUNTCALC = "insureItem.optionalDeductiblesCla.discountCalc";

    /**
     * 可选免赔额特约-折扣率
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_DISCOUNTRATECALC = "insureItem.optionalDeductiblesCla.discountRateCalc";

    /**
     * 可选免赔额特约-原价
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_ORIGINALCALC = "insureItem.optionalDeductiblesCla.originalCalc";

    /**
     * 可选免赔额特约-当前保额
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_CURRENT = "insureItem.optionalDeductiblesCla.current";

    /**
     * 可选免赔额特约-不计免赔险代码
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_AVOIDLOSSCODE = "insureItem.optionalDeductiblesCla.avoidLossCode";

    /**
     * 可选免赔额特约-是否不计免赔
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_ISAVOIDLOSS = "insureItem.optionalDeductiblesCla.isAvoidLoss";

    /**
     * 可选免赔额特约-保费
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA_PREMIUM = "insureItem.optionalDeductiblesCla.premium";

    /**
     * 多次事故免赔特约-险种名称
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_NAME = "insureItem.accidentDeductiblesCla.name";

    /**
     * 多次事故免赔特约-险种代码
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_CODE = "insureItem.accidentDeductiblesCla.code";

    /**
     * 多次事故免赔特约-价格计算公式
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_PRICECALCULATOR = "insureItem.accidentDeductiblesCla.priceCalculator";

    /**
     * 多次事故免赔特约-折扣
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_DISCOUNTCALC = "insureItem.accidentDeductiblesCla.discountCalc";

    /**
     * 多次事故免赔特约-折扣率
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_DISCOUNTRATECALC = "insureItem.accidentDeductiblesCla.discountRateCalc";

    /**
     * 多次事故免赔特约-原价
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_ORIGINALCALC = "insureItem.accidentDeductiblesCla.originalCalc";

    /**
     * 多次事故免赔特约-当前保额
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_CURRENT = "insureItem.accidentDeductiblesCla.current";

    /**
     * 多次事故免赔特约-不计免赔险代码
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_AVOIDLOSSCODE = "insureItem.accidentDeductiblesCla.avoidLossCode";

    /**
     * 多次事故免赔特约-是否不计免赔
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_ISAVOIDLOSS = "insureItem.accidentDeductiblesCla.isAvoidLoss";

    /**
     * 多次事故免赔特约-保费
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA_PREMIUM = "insureItem.accidentDeductiblesCla.premium";

    /**
     * 新增设备损失险-险种名称
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_NAME = "insureItem.newEquipmentIns.name";

    /**
     * 新增设备损失险-险种代码
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_CODE = "insureItem.newEquipmentIns.code";

    /**
     * 新增设备损失险-价格计算公式
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_PRICECALCULATOR = "insureItem.newEquipmentIns.priceCalculator";

    /**
     * 新增设备损失险-折扣
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_DISCOUNTCALC = "insureItem.newEquipmentIns.discountCalc";

    /**
     * 新增设备损失险-折扣率
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_DISCOUNTRATECALC = "insureItem.newEquipmentIns.discountRateCalc";

    /**
     * 新增设备损失险-原价
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_ORIGINALCALC = "insureItem.newEquipmentIns.originalCalc";

    /**
     * 新增设备损失险-当前保额
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_CURRENT = "insureItem.newEquipmentIns.current";

    /**
     * 新增设备损失险-不计免赔险代码
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_AVOIDLOSSCODE = "insureItem.newEquipmentIns.avoidLossCode";

    /**
     * 新增设备损失险-是否不计免赔
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_ISAVOIDLOSS = "insureItem.newEquipmentIns.isAvoidLoss";

    /**
     * 新增设备损失险-保费
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS_PREMIUM = "insureItem.newEquipmentIns.premium";

    /**
     * 车上货物责任险-险种名称
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_NAME = "insureItem.goodsOnTruckIns.name";

    /**
     * 车上货物责任险-险种代码
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_CODE = "insureItem.goodsOnTruckIns.code";

    /**
     * 车上货物责任险-价格计算公式
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_PRICECALCULATOR = "insureItem.goodsOnTruckIns.priceCalculator";

    /**
     * 车上货物责任险-折扣
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_DISCOUNTCALC = "insureItem.goodsOnTruckIns.discountCalc";

    /**
     * 车上货物责任险-折扣率
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_DISCOUNTRATECALC = "insureItem.goodsOnTruckIns.discountRateCalc";

    /**
     * 车上货物责任险-原价
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_ORIGINALCALC = "insureItem.goodsOnTruckIns.originalCalc";

    /**
     * 车上货物责任险-当前保额
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_CURRENT = "insureItem.goodsOnTruckIns.current";

    /**
     * 车上货物责任险-不计免赔险代码
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_AVOIDLOSSCODE = "insureItem.goodsOnTruckIns.avoidLossCode";

    /**
     * 车上货物责任险-是否不计免赔
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_ISAVOIDLOSS = "insureItem.goodsOnTruckIns.isAvoidLoss";

    /**
     * 车上货物责任险-保费
     */
    public final static String INSUREITEM_GOODSONTRUCKINS_PREMIUM = "insureItem.goodsOnTruckIns.premium";

    /**
     * 随车行李物品损失保险-险种名称
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_NAME = "insureItem.lossOfBaggageIns.name";

    /**
     * 随车行李物品损失保险-险种代码
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_CODE = "insureItem.lossOfBaggageIns.code";

    /**
     * 随车行李物品损失保险-价格计算公式
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_PRICECALCULATOR = "insureItem.lossOfBaggageIns.priceCalculator";

    /**
     * 随车行李物品损失保险-折扣
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_DISCOUNTCALC = "insureItem.lossOfBaggageIns.discountCalc";

    /**
     * 随车行李物品损失保险-折扣率
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_DISCOUNTRATECALC = "insureItem.lossOfBaggageIns.discountRateCalc";

    /**
     * 随车行李物品损失保险-原价
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_ORIGINALCALC = "insureItem.lossOfBaggageIns.originalCalc";

    /**
     * 随车行李物品损失保险-当前保额
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_CURRENT = "insureItem.lossOfBaggageIns.current";

    /**
     * 随车行李物品损失保险-不计免赔险代码
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_AVOIDLOSSCODE = "insureItem.lossOfBaggageIns.avoidLossCode";

    /**
     * 随车行李物品损失保险-是否不计免赔
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_ISAVOIDLOSS = "insureItem.lossOfBaggageIns.isAvoidLoss";

    /**
     * 随车行李物品损失保险-保费
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS_PREMIUM = "insureItem.lossOfBaggageIns.premium";

    /**
     * 教练车特约-险种名称
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_NAME = "insureItem.trainnigCarCla.name";

    /**
     * 教练车特约-险种代码
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_CODE = "insureItem.trainnigCarCla.code";

    /**
     * 教练车特约-价格计算公式
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_PRICECALCULATOR = "insureItem.trainnigCarCla.priceCalculator";

    /**
     * 教练车特约-折扣
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_DISCOUNTCALC = "insureItem.trainnigCarCla.discountCalc";

    /**
     * 教练车特约-折扣率
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_DISCOUNTRATECALC = "insureItem.trainnigCarCla.discountRateCalc";

    /**
     * 教练车特约-原价
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_ORIGINALCALC = "insureItem.trainnigCarCla.originalCalc";

    /**
     * 教练车特约-当前保额
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_CURRENT = "insureItem.trainnigCarCla.current";

    /**
     * 教练车特约-不计免赔险代码
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_AVOIDLOSSCODE = "insureItem.trainnigCarCla.avoidLossCode";

    /**
     * 教练车特约-是否不计免赔
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_ISAVOIDLOSS = "insureItem.trainnigCarCla.isAvoidLoss";

    /**
     * 教练车特约-保费
     */
    public final static String INSUREITEM_TRAINNIGCARCLA_PREMIUM = "insureItem.trainnigCarCla.premium";

    /**
     * 机动车停驶损失险-险种名称
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_NAME = "insureItem.vehicleSuspendedIns.name";

    /**
     * 机动车停驶损失险-险种代码
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_CODE = "insureItem.vehicleSuspendedIns.code";

    /**
     * 机动车停驶损失险-价格计算公式
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_PRICECALCULATOR = "insureItem.vehicleSuspendedIns.priceCalculator";

    /**
     * 机动车停驶损失险-折扣
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_DISCOUNTCALC = "insureItem.vehicleSuspendedIns.discountCalc";

    /**
     * 机动车停驶损失险-折扣率
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_DISCOUNTRATECALC = "insureItem.vehicleSuspendedIns.discountRateCalc";

    /**
     * 机动车停驶损失险-原价
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_ORIGINALCALC = "insureItem.vehicleSuspendedIns.originalCalc";

    /**
     * 机动车停驶损失险-当前保额
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_CURRENT = "insureItem.vehicleSuspendedIns.current";

    /**
     * 机动车停驶损失险-不计免赔险代码
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_AVOIDLOSSCODE = "insureItem.vehicleSuspendedIns.avoidLossCode";

    /**
     * 机动车停驶损失险-是否不计免赔
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_ISAVOIDLOSS = "insureItem.vehicleSuspendedIns.isAvoidLoss";

    /**
     * 机动车停驶损失险-保费
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS_PREMIUM = "insureItem.vehicleSuspendedIns.premium";

    /**
     * 附加车辆损失险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_NAME = "insureItem.ncfVehicleDemageIns.name";

    /**
     * 附加车辆损失险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_CODE = "insureItem.ncfVehicleDemageIns.code";

    /**
     * 附加车辆损失险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_PRICECALCULATOR = "insureItem.ncfVehicleDemageIns.priceCalculator";

    /**
     * 附加车辆损失险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_DISCOUNTCALC = "insureItem.ncfVehicleDemageIns.discountCalc";

    /**
     * 附加车辆损失险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_DISCOUNTRATECALC = "insureItem.ncfVehicleDemageIns.discountRateCalc";

    /**
     * 附加车辆损失险不计免赔-原价
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_ORIGINALCALC = "insureItem.ncfVehicleDemageIns.originalCalc";

    /**
     * 附加车辆损失险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_CURRENT = "insureItem.ncfVehicleDemageIns.current";

    /**
     * 附加车辆损失险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_AVOIDLOSSCODE = "insureItem.ncfVehicleDemageIns.avoidLossCode";

    /**
     * 附加车辆损失险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_ISAVOIDLOSS = "insureItem.ncfVehicleDemageIns.isAvoidLoss";

    /**
     * 附加车辆损失险不计免赔-保费
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS_PREMIUM = "insureItem.ncfVehicleDemageIns.premium";

    /**
     * 附加第三者责任险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_NAME = "insureItem.ncfThirdPartyIns.name";

    /**
     * 附加第三者责任险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_CODE = "insureItem.ncfThirdPartyIns.code";

    /**
     * 附加第三者责任险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_PRICECALCULATOR = "insureItem.ncfThirdPartyIns.priceCalculator";

    /**
     * 附加第三者责任险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_DISCOUNTCALC = "insureItem.ncfThirdPartyIns.discountCalc";

    /**
     * 附加第三者责任险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_DISCOUNTRATECALC = "insureItem.ncfThirdPartyIns.discountRateCalc";

    /**
     * 附加第三者责任险不计免赔-原价
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_ORIGINALCALC = "insureItem.ncfThirdPartyIns.originalCalc";

    /**
     * 附加第三者责任险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_CURRENT = "insureItem.ncfThirdPartyIns.current";

    /**
     * 附加第三者责任险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_AVOIDLOSSCODE = "insureItem.ncfThirdPartyIns.avoidLossCode";

    /**
     * 附加第三者责任险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_ISAVOIDLOSS = "insureItem.ncfThirdPartyIns.isAvoidLoss";

    /**
     * 附加第三者责任险不计免赔-保费
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS_PREMIUM = "insureItem.ncfThirdPartyIns.premium";

    /**
     * 附加司机责任险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFDRIVERINS_NAME = "insureItem.ncfDriverIns.name";

    /**
     * 附加司机责任险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFDRIVERINS_CODE = "insureItem.ncfDriverIns.code";

    /**
     * 附加司机责任险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFDRIVERINS_PRICECALCULATOR = "insureItem.ncfDriverIns.priceCalculator";

    /**
     * 附加司机责任险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFDRIVERINS_DISCOUNTCALC = "insureItem.ncfDriverIns.discountCalc";

    /**
     * 附加司机责任险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFDRIVERINS_DISCOUNTRATECALC = "insureItem.ncfDriverIns.discountRateCalc";

    /**
     * 附加司机责任险不计免赔-原价
     */
    public final static String INSUREITEM_NCFDRIVERINS_ORIGINALCALC = "insureItem.ncfDriverIns.originalCalc";

    /**
     * 附加司机责任险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFDRIVERINS_CURRENT = "insureItem.ncfDriverIns.current";

    /**
     * 附加司机责任险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFDRIVERINS_AVOIDLOSSCODE = "insureItem.ncfDriverIns.avoidLossCode";

    /**
     * 附加司机责任险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFDRIVERINS_ISAVOIDLOSS = "insureItem.ncfDriverIns.isAvoidLoss";

    /**
     * 附加司机责任险不计免赔-保费
     */
    public final static String INSUREITEM_NCFDRIVERINS_PREMIUM = "insureItem.ncfDriverIns.premium";

    /**
     * 附加乘客责任险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFPASSENGERINS_NAME = "insureItem.ncfPassengerIns.name";

    /**
     * 附加乘客责任险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFPASSENGERINS_CODE = "insureItem.ncfPassengerIns.code";

    /**
     * 附加乘客责任险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFPASSENGERINS_PRICECALCULATOR = "insureItem.ncfPassengerIns.priceCalculator";

    /**
     * 附加乘客责任险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFPASSENGERINS_DISCOUNTCALC = "insureItem.ncfPassengerIns.discountCalc";

    /**
     * 附加乘客责任险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFPASSENGERINS_DISCOUNTRATECALC = "insureItem.ncfPassengerIns.discountRateCalc";

    /**
     * 附加乘客责任险不计免赔-原价
     */
    public final static String INSUREITEM_NCFPASSENGERINS_ORIGINALCALC = "insureItem.ncfPassengerIns.originalCalc";

    /**
     * 附加乘客责任险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFPASSENGERINS_CURRENT = "insureItem.ncfPassengerIns.current";

    /**
     * 附加乘客责任险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFPASSENGERINS_AVOIDLOSSCODE = "insureItem.ncfPassengerIns.avoidLossCode";

    /**
     * 附加乘客责任险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFPASSENGERINS_ISAVOIDLOSS = "insureItem.ncfPassengerIns.isAvoidLoss";

    /**
     * 附加乘客责任险不计免赔-保费
     */
    public final static String INSUREITEM_NCFPASSENGERINS_PREMIUM = "insureItem.ncfPassengerIns.premium";

    /**
     * 附加全车盗抢险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFTHEFTINS_NAME = "insureItem.ncfTheftIns.name";

    /**
     * 附加全车盗抢险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFTHEFTINS_CODE = "insureItem.ncfTheftIns.code";

    /**
     * 附加全车盗抢险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFTHEFTINS_PRICECALCULATOR = "insureItem.ncfTheftIns.priceCalculator";

    /**
     * 附加全车盗抢险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFTHEFTINS_DISCOUNTCALC = "insureItem.ncfTheftIns.discountCalc";

    /**
     * 附加全车盗抢险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFTHEFTINS_DISCOUNTRATECALC = "insureItem.ncfTheftIns.discountRateCalc";

    /**
     * 附加全车盗抢险不计免赔-原价
     */
    public final static String INSUREITEM_NCFTHEFTINS_ORIGINALCALC = "insureItem.ncfTheftIns.originalCalc";

    /**
     * 附加全车盗抢险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFTHEFTINS_CURRENT = "insureItem.ncfTheftIns.current";

    /**
     * 附加全车盗抢险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFTHEFTINS_AVOIDLOSSCODE = "insureItem.ncfTheftIns.avoidLossCode";

    /**
     * 附加全车盗抢险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFTHEFTINS_ISAVOIDLOSS = "insureItem.ncfTheftIns.isAvoidLoss";

    /**
     * 附加全车盗抢险不计免赔-保费
     */
    public final static String INSUREITEM_NCFTHEFTINS_PREMIUM = "insureItem.ncfTheftIns.premium";

    /**
     * 附加车身划痕险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFSCRATCHINS_NAME = "insureItem.ncfScratchIns.name";

    /**
     * 附加车身划痕险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFSCRATCHINS_CODE = "insureItem.ncfScratchIns.code";

    /**
     * 附加车身划痕险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFSCRATCHINS_PRICECALCULATOR = "insureItem.ncfScratchIns.priceCalculator";

    /**
     * 附加车身划痕险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFSCRATCHINS_DISCOUNTCALC = "insureItem.ncfScratchIns.discountCalc";

    /**
     * 附加车身划痕险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFSCRATCHINS_DISCOUNTRATECALC = "insureItem.ncfScratchIns.discountRateCalc";

    /**
     * 附加车身划痕险不计免赔-原价
     */
    public final static String INSUREITEM_NCFSCRATCHINS_ORIGINALCALC = "insureItem.ncfScratchIns.originalCalc";

    /**
     * 附加车身划痕险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFSCRATCHINS_CURRENT = "insureItem.ncfScratchIns.current";

    /**
     * 附加车身划痕险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFSCRATCHINS_AVOIDLOSSCODE = "insureItem.ncfScratchIns.avoidLossCode";

    /**
     * 附加车身划痕险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFSCRATCHINS_ISAVOIDLOSS = "insureItem.ncfScratchIns.isAvoidLoss";

    /**
     * 附加车身划痕险不计免赔-保费
     */
    public final static String INSUREITEM_NCFSCRATCHINS_PREMIUM = "insureItem.ncfScratchIns.premium";

    /**
     * 附加车上人员责任险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_NAME = "insureItem.ncfDriverPassengerIns.name";

    /**
     * 附加车上人员责任险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_CODE = "insureItem.ncfDriverPassengerIns.code";

    /**
     * 附加车上人员责任险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_PRICECALCULATOR = "insureItem.ncfDriverPassengerIns.priceCalculator";

    /**
     * 附加车上人员责任险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_DISCOUNTCALC = "insureItem.ncfDriverPassengerIns.discountCalc";

    /**
     * 附加车上人员责任险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_DISCOUNTRATECALC = "insureItem.ncfDriverPassengerIns.discountRateCalc";

    /**
     * 附加车上人员责任险不计免赔-原价
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_ORIGINALCALC = "insureItem.ncfDriverPassengerIns.originalCalc";

    /**
     * 附加车上人员责任险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_CURRENT = "insureItem.ncfDriverPassengerIns.current";

    /**
     * 附加车上人员责任险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_AVOIDLOSSCODE = "insureItem.ncfDriverPassengerIns.avoidLossCode";

    /**
     * 附加车上人员责任险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_ISAVOIDLOSS = "insureItem.ncfDriverPassengerIns.isAvoidLoss";

    /**
     * 附加车上人员责任险不计免赔-保费
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS_PREMIUM = "insureItem.ncfDriverPassengerIns.premium";

    /**
     * 附加自燃损失险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_NAME = "insureItem.ncfCombustionIns.name";

    /**
     * 附加自燃损失险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_CODE = "insureItem.ncfCombustionIns.code";

    /**
     * 附加自燃损失险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_PRICECALCULATOR = "insureItem.ncfCombustionIns.priceCalculator";

    /**
     * 附加自燃损失险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_DISCOUNTCALC = "insureItem.ncfCombustionIns.discountCalc";

    /**
     * 附加自燃损失险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_DISCOUNTRATECALC = "insureItem.ncfCombustionIns.discountRateCalc";

    /**
     * 附加自燃损失险不计免赔-原价
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_ORIGINALCALC = "insureItem.ncfCombustionIns.originalCalc";

    /**
     * 附加自燃损失险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_CURRENT = "insureItem.ncfCombustionIns.current";

    /**
     * 附加自燃损失险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_AVOIDLOSSCODE = "insureItem.ncfCombustionIns.avoidLossCode";

    /**
     * 附加自燃损失险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_ISAVOIDLOSS = "insureItem.ncfCombustionIns.isAvoidLoss";

    /**
     * 附加自燃损失险不计免赔-保费
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS_PREMIUM = "insureItem.ncfCombustionIns.premium";

    /**
     * 附加涉水损失险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFWADINGINS_NAME = "insureItem.ncfWadingIns.name";

    /**
     * 附加涉水损失险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFWADINGINS_CODE = "insureItem.ncfWadingIns.code";

    /**
     * 附加涉水损失险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFWADINGINS_PRICECALCULATOR = "insureItem.ncfWadingIns.priceCalculator";

    /**
     * 附加涉水损失险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFWADINGINS_DISCOUNTCALC = "insureItem.ncfWadingIns.discountCalc";

    /**
     * 附加涉水损失险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFWADINGINS_DISCOUNTRATECALC = "insureItem.ncfWadingIns.discountRateCalc";

    /**
     * 附加涉水损失险不计免赔-原价
     */
    public final static String INSUREITEM_NCFWADINGINS_ORIGINALCALC = "insureItem.ncfWadingIns.originalCalc";

    /**
     * 附加涉水损失险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFWADINGINS_CURRENT = "insureItem.ncfWadingIns.current";

    /**
     * 附加涉水损失险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFWADINGINS_AVOIDLOSSCODE = "insureItem.ncfWadingIns.avoidLossCode";

    /**
     * 附加涉水损失险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFWADINGINS_ISAVOIDLOSS = "insureItem.ncfWadingIns.isAvoidLoss";

    /**
     * 附加涉水损失险不计免赔-保费
     */
    public final static String INSUREITEM_NCFWADINGINS_PREMIUM = "insureItem.ncfWadingIns.premium";

    /**
     * 附加车上货物责任险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_NAME = "insureItem.ncfGoodsOnVehicleIns.name";

    /**
     * 附加车上货物责任险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_CODE = "insureItem.ncfGoodsOnVehicleIns.code";

    /**
     * 附加车上货物责任险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_PRICECALCULATOR = "insureItem.ncfGoodsOnVehicleIns.priceCalculator";

    /**
     * 附加车上货物责任险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_DISCOUNTCALC = "insureItem.ncfGoodsOnVehicleIns.discountCalc";

    /**
     * 附加车上货物责任险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_DISCOUNTRATECALC = "insureItem.ncfGoodsOnVehicleIns.discountRateCalc";

    /**
     * 附加车上货物责任险不计免赔-原价
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_ORIGINALCALC = "insureItem.ncfGoodsOnVehicleIns.originalCalc";

    /**
     * 附加车上货物责任险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_CURRENT = "insureItem.ncfGoodsOnVehicleIns.current";

    /**
     * 附加车上货物责任险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_AVOIDLOSSCODE = "insureItem.ncfGoodsOnVehicleIns.avoidLossCode";

    /**
     * 附加车上货物责任险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_ISAVOIDLOSS = "insureItem.ncfGoodsOnVehicleIns.isAvoidLoss";

    /**
     * 附加车上货物责任险不计免赔-保费
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS_PREMIUM = "insureItem.ncfGoodsOnVehicleIns.premium";

    /**
     * 附加新增设备损失险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_NAME = "insureItem.ncfNewEquipmentIns.name";

    /**
     * 附加新增设备损失险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_CODE = "insureItem.ncfNewEquipmentIns.code";

    /**
     * 附加新增设备损失险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_PRICECALCULATOR = "insureItem.ncfNewEquipmentIns.priceCalculator";

    /**
     * 附加新增设备损失险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_DISCOUNTCALC = "insureItem.ncfNewEquipmentIns.discountCalc";

    /**
     * 附加新增设备损失险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_DISCOUNTRATECALC = "insureItem.ncfNewEquipmentIns.discountRateCalc";

    /**
     * 附加新增设备损失险不计免赔-原价
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_ORIGINALCALC = "insureItem.ncfNewEquipmentIns.originalCalc";

    /**
     * 附加新增设备损失险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_CURRENT = "insureItem.ncfNewEquipmentIns.current";

    /**
     * 附加新增设备损失险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_AVOIDLOSSCODE = "insureItem.ncfNewEquipmentIns.avoidLossCode";

    /**
     * 附加新增设备损失险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_ISAVOIDLOSS = "insureItem.ncfNewEquipmentIns.isAvoidLoss";

    /**
     * 附加新增设备损失险不计免赔-保费
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS_PREMIUM = "insureItem.ncfNewEquipmentIns.premium";

    /**
     * 附加险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_NAME = "insureItem.ncfaddtionalclause.name";

    /**
     * 附加险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_CODE = "insureItem.ncfaddtionalclause.code";

    /**
     * 附加险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_PRICECALCULATOR = "insureItem.ncfaddtionalclause.priceCalculator";

    /**
     * 附加险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_DISCOUNTCALC = "insureItem.ncfaddtionalclause.discountCalc";

    /**
     * 附加险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_DISCOUNTRATECALC = "insureItem.ncfaddtionalclause.discountRateCalc";

    /**
     * 附加险不计免赔-原价
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_ORIGINALCALC = "insureItem.ncfaddtionalclause.originalCalc";

    /**
     * 附加险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_CURRENT = "insureItem.ncfaddtionalclause.current";

    /**
     * 附加险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_AVOIDLOSSCODE = "insureItem.ncfaddtionalclause.avoidLossCode";

    /**
     * 附加险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_ISAVOIDLOSS = "insureItem.ncfaddtionalclause.isAvoidLoss";

    /**
     * 附加险不计免赔-保费
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE_PREMIUM = "insureItem.ncfaddtionalclause.premium";

    /**
     * 交强险-险种名称
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_NAME = "insureItem.vehicleCompulsoryIns.name";

    /**
     * 交强险-险种代码
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_CODE = "insureItem.vehicleCompulsoryIns.code";

    /**
     * 交强险-价格计算公式
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_PRICECALCULATOR = "insureItem.vehicleCompulsoryIns.priceCalculator";

    /**
     * 交强险-折扣
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_DISCOUNTCALC = "insureItem.vehicleCompulsoryIns.discountCalc";

    /**
     * 交强险-折扣率
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_DISCOUNTRATECALC = "insureItem.vehicleCompulsoryIns.discountRateCalc";

    /**
     * 交强险-原价
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_ORIGINALCALC = "insureItem.vehicleCompulsoryIns.originalCalc";

    /**
     * 交强险-当前保额
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_CURRENT = "insureItem.vehicleCompulsoryIns.current";

    /**
     * 交强险-不计免赔险代码
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_AVOIDLOSSCODE = "insureItem.vehicleCompulsoryIns.avoidLossCode";

    /**
     * 交强险-是否不计免赔
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_ISAVOIDLOSS = "insureItem.vehicleCompulsoryIns.isAvoidLoss";

    /**
     * 交强险-保费
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS_PREMIUM = "insureItem.vehicleCompulsoryIns.premium";

    /**
     * 车船税-险种名称
     */
    public final static String INSUREITEM_VEHICLETAX_NAME = "insureItem.vehicleTax.name";

    /**
     * 车船税-险种代码
     */
    public final static String INSUREITEM_VEHICLETAX_CODE = "insureItem.vehicleTax.code";

    /**
     * 车船税-价格计算公式
     */
    public final static String INSUREITEM_VEHICLETAX_PRICECALCULATOR = "insureItem.vehicleTax.priceCalculator";

    /**
     * 车船税-折扣
     */
    public final static String INSUREITEM_VEHICLETAX_DISCOUNTCALC = "insureItem.vehicleTax.discountCalc";

    /**
     * 车船税-折扣率
     */
    public final static String INSUREITEM_VEHICLETAX_DISCOUNTRATECALC = "insureItem.vehicleTax.discountRateCalc";

    /**
     * 车船税-原价
     */
    public final static String INSUREITEM_VEHICLETAX_ORIGINALCALC = "insureItem.vehicleTax.originalCalc";

    /**
     * 车船税-当前保额
     */
    public final static String INSUREITEM_VEHICLETAX_CURRENT = "insureItem.vehicleTax.current";

    /**
     * 车船税-不计免赔险代码
     */
    public final static String INSUREITEM_VEHICLETAX_AVOIDLOSSCODE = "insureItem.vehicleTax.avoidLossCode";

    /**
     * 车船税-是否不计免赔
     */
    public final static String INSUREITEM_VEHICLETAX_ISAVOIDLOSS = "insureItem.vehicleTax.isAvoidLoss";

    /**
     * 车船税-保费
     */
    public final static String INSUREITEM_VEHICLETAX_PREMIUM = "insureItem.vehicleTax.premium";

    /**
     * 商业险印花税-险种名称
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_NAME = "insureItem.commercialStamTax.name";

    /**
     * 商业险印花税-险种代码
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_CODE = "insureItem.commercialStamTax.code";

    /**
     * 商业险印花税-价格计算公式
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_PRICECALCULATOR = "insureItem.commercialStamTax.priceCalculator";

    /**
     * 商业险印花税-折扣
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_DISCOUNTCALC = "insureItem.commercialStamTax.discountCalc";

    /**
     * 商业险印花税-折扣率
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_DISCOUNTRATECALC = "insureItem.commercialStamTax.discountRateCalc";

    /**
     * 商业险印花税-原价
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_ORIGINALCALC = "insureItem.commercialStamTax.originalCalc";

    /**
     * 商业险印花税-当前保额
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_CURRENT = "insureItem.commercialStamTax.current";

    /**
     * 商业险印花税-不计免赔险代码
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_AVOIDLOSSCODE = "insureItem.commercialStamTax.avoidLossCode";

    /**
     * 商业险印花税-是否不计免赔
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_ISAVOIDLOSS = "insureItem.commercialStamTax.isAvoidLoss";

    /**
     * 商业险印花税-保费
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX_PREMIUM = "insureItem.commercialStamTax.premium";

    /**
     * 交强险印花税-险种名称
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_NAME = "insureItem.compulsoryStamTax.name";

    /**
     * 交强险印花税-险种代码
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_CODE = "insureItem.compulsoryStamTax.code";

    /**
     * 交强险印花税-价格计算公式
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_PRICECALCULATOR = "insureItem.compulsoryStamTax.priceCalculator";

    /**
     * 交强险印花税-折扣
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_DISCOUNTCALC = "insureItem.compulsoryStamTax.discountCalc";

    /**
     * 交强险印花税-折扣率
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_DISCOUNTRATECALC = "insureItem.compulsoryStamTax.discountRateCalc";

    /**
     * 交强险印花税-原价
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_ORIGINALCALC = "insureItem.compulsoryStamTax.originalCalc";

    /**
     * 交强险印花税-当前保额
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_CURRENT = "insureItem.compulsoryStamTax.current";

    /**
     * 交强险印花税-不计免赔险代码
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_AVOIDLOSSCODE = "insureItem.compulsoryStamTax.avoidLossCode";

    /**
     * 交强险印花税-是否不计免赔
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_ISAVOIDLOSS = "insureItem.compulsoryStamTax.isAvoidLoss";

    /**
     * 交强险印花税-保费
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX_PREMIUM = "insureItem.compulsoryStamTax.premium";

    /**
     * 商业险-险种名称
     */
    public final static String INSUREITEM_COMMERCIALINS_NAME = "insureItem.commercialIns.name";

    /**
     * 商业险-险种代码
     */
    public final static String INSUREITEM_COMMERCIALINS_CODE = "insureItem.commercialIns.code";

    /**
     * 商业险-价格计算公式
     */
    public final static String INSUREITEM_COMMERCIALINS_PRICECALCULATOR = "insureItem.commercialIns.priceCalculator";

    /**
     * 商业险-折扣
     */
    public final static String INSUREITEM_COMMERCIALINS_DISCOUNTCALC = "insureItem.commercialIns.discountCalc";

    /**
     * 商业险-折扣率
     */
    public final static String INSUREITEM_COMMERCIALINS_DISCOUNTRATECALC = "insureItem.commercialIns.discountRateCalc";

    /**
     * 商业险-原价
     */
    public final static String INSUREITEM_COMMERCIALINS_ORIGINALCALC = "insureItem.commercialIns.originalCalc";

    /**
     * 商业险-当前保额
     */
    public final static String INSUREITEM_COMMERCIALINS_CURRENT = "insureItem.commercialIns.current";

    /**
     * 商业险-不计免赔险代码
     */
    public final static String INSUREITEM_COMMERCIALINS_AVOIDLOSSCODE = "insureItem.commercialIns.avoidLossCode";

    /**
     * 商业险-是否不计免赔
     */
    public final static String INSUREITEM_COMMERCIALINS_ISAVOIDLOSS = "insureItem.commercialIns.isAvoidLoss";

    /**
     * 商业险-保费
     */
    public final static String INSUREITEM_COMMERCIALINS_PREMIUM = "insureItem.commercialIns.premium";

    /**
     * 倒车镜车灯损坏险-险种名称
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_NAME = "insureItem.mirrorLightIns.name";

    /**
     * 倒车镜车灯损坏险-险种代码
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_CODE = "insureItem.mirrorLightIns.code";

    /**
     * 倒车镜车灯损坏险-价格计算公式
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_PRICECALCULATOR = "insureItem.mirrorLightIns.priceCalculator";

    /**
     * 倒车镜车灯损坏险-折扣
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_DISCOUNTCALC = "insureItem.mirrorLightIns.discountCalc";

    /**
     * 倒车镜车灯损坏险-折扣率
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_DISCOUNTRATECALC = "insureItem.mirrorLightIns.discountRateCalc";

    /**
     * 倒车镜车灯损坏险-原价
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_ORIGINALCALC = "insureItem.mirrorLightIns.originalCalc";

    /**
     * 倒车镜车灯损坏险-当前保额
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_CURRENT = "insureItem.mirrorLightIns.current";

    /**
     * 倒车镜车灯损坏险-不计免赔险代码
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_AVOIDLOSSCODE = "insureItem.mirrorLightIns.avoidLossCode";

    /**
     * 倒车镜车灯损坏险-是否不计免赔
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_ISAVOIDLOSS = "insureItem.mirrorLightIns.isAvoidLoss";

    /**
     * 倒车镜车灯损坏险-保费
     */
    public final static String INSUREITEM_MIRRORLIGHTINS_PREMIUM = "insureItem.mirrorLightIns.premium";

    /**
     * 基本险不计免赔-险种名称
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_NAME = "insureItem.ncfBasicClause.name";

    /**
     * 基本险不计免赔-险种代码
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_CODE = "insureItem.ncfBasicClause.code";

    /**
     * 基本险不计免赔-价格计算公式
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_PRICECALCULATOR = "insureItem.ncfBasicClause.priceCalculator";

    /**
     * 基本险不计免赔-折扣
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_DISCOUNTCALC = "insureItem.ncfBasicClause.discountCalc";

    /**
     * 基本险不计免赔-折扣率
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_DISCOUNTRATECALC = "insureItem.ncfBasicClause.discountRateCalc";

    /**
     * 基本险不计免赔-原价
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_ORIGINALCALC = "insureItem.ncfBasicClause.originalCalc";

    /**
     * 基本险不计免赔-当前保额
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_CURRENT = "insureItem.ncfBasicClause.current";

    /**
     * 基本险不计免赔-不计免赔险代码
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_AVOIDLOSSCODE = "insureItem.ncfBasicClause.avoidLossCode";

    /**
     * 基本险不计免赔-是否不计免赔
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_ISAVOIDLOSS = "insureItem.ncfBasicClause.isAvoidLoss";

    /**
     * 基本险不计免赔-保费
     */
    public final static String INSUREITEM_NCFBASICCLAUSE_PREMIUM = "insureItem.ncfBasicClause.premium";

    /**
     * 不计免赔险-险种名称
     */
    public final static String INSUREITEM_NCFCLAUSE_NAME = "insureItem.ncfClause.name";

    /**
     * 不计免赔险-险种代码
     */
    public final static String INSUREITEM_NCFCLAUSE_CODE = "insureItem.ncfClause.code";

    /**
     * 不计免赔险-价格计算公式
     */
    public final static String INSUREITEM_NCFCLAUSE_PRICECALCULATOR = "insureItem.ncfClause.priceCalculator";

    /**
     * 不计免赔险-折扣
     */
    public final static String INSUREITEM_NCFCLAUSE_DISCOUNTCALC = "insureItem.ncfClause.discountCalc";

    /**
     * 不计免赔险-折扣率
     */
    public final static String INSUREITEM_NCFCLAUSE_DISCOUNTRATECALC = "insureItem.ncfClause.discountRateCalc";

    /**
     * 不计免赔险-原价
     */
    public final static String INSUREITEM_NCFCLAUSE_ORIGINALCALC = "insureItem.ncfClause.originalCalc";

    /**
     * 不计免赔险-当前保额
     */
    public final static String INSUREITEM_NCFCLAUSE_CURRENT = "insureItem.ncfClause.current";

    /**
     * 不计免赔险-不计免赔险代码
     */
    public final static String INSUREITEM_NCFCLAUSE_AVOIDLOSSCODE = "insureItem.ncfClause.avoidLossCode";

    /**
     * 不计免赔险-是否不计免赔
     */
    public final static String INSUREITEM_NCFCLAUSE_ISAVOIDLOSS = "insureItem.ncfClause.isAvoidLoss";

    /**
     * 不计免赔险-保费
     */
    public final static String INSUREITEM_NCFCLAUSE_PREMIUM = "insureItem.ncfClause.premium";



    /********************  map型常量生成  ******************/
    /**
     * 附加车辆损失险不计免赔
     */
    public final static String INSUREITEM_NCFVEHICLEDEMAGEINS = "insureItem.ncfVehicleDemageIns";

    /**
     * 保单信息
     */
    public final static String POLICEY = "policey";

    /**
     * 其他信息
     */
    public final static String OTHERS = "others";

    /**
     * 机动车停驶损失险
     */
    public final static String INSUREITEM_VEHICLESUSPENDEDINS = "insureItem.vehicleSuspendedIns";

    /**
     * 经办人
     */
    public final static String OPERATOR = "operator";

    /**
     * 自燃免除特约
     */
    public final static String INSUREITEM_COMBUSTIONEXCLUSIONCLA = "insureItem.combustionExclusionCla";

    /**
     * 全车盗抢险
     */
    public final static String INSUREITEM_THEFTINS = "insureItem.theftIns";

    /**
     * 涉水损失险
     */
    public final static String INSUREITEM_WADINGINS = "insureItem.wadingIns";

    /**
     * 车辆信息
     */
    public final static String CAR_SPECIFIC = "car.specific";

    /**
     * 纳税人
     */
    public final static String TAXER = "taxer";

    /**
     * 车辆损失险
     */
    public final static String INSUREITEM_VEHICLEDEMAGEINS = "insureItem.vehicleDemageIns";

    /**
     * 订单信息
     */
    public final static String ORDER = "order";

    /**
     * 附加自燃损失险不计免赔
     */
    public final static String INSUREITEM_NCFCOMBUSTIONINS = "insureItem.ncfCombustionIns";

    /**
     * 自燃损失险
     */
    public final static String INSUREITEM_COMBUSTIONINS = "insureItem.combustionIns";

    /**
     * 第三者责任险
     */
    public final static String INSUREITEM_THIRDPARTYINS = "insureItem.thirdPartyIns";

    /**
     * 机构信息
     */
    public final static String ORGANIZATION = "organization";

    /**
     * 附加险不计免赔
     */
    public final static String INSUREITEM_NCFADDTIONALCLAUSE = "insureItem.ncfaddtionalclause";

    /**
     * 倒车镜车灯损坏险
     */
    public final static String INSUREITEM_MIRRORLIGHTINS = "insureItem.mirrorLightIns";

    /**
     * 联系人
     */
    public final static String CONTACT = "contact";

    /**
     * 乘客责任险
     */
    public final static String INSUREITEM_PASSENGERINS = "insureItem.passengerIns";

    /**
     * 附加车上货物责任险不计免赔
     */
    public final static String INSUREITEM_NCFGOODSONVEHICLEINS = "insureItem.ncfGoodsOnVehicleIns";

    /**
     * 交强险印花税
     */
    public final static String INSUREITEM_COMPULSORYSTAMTAX = "insureItem.compulsoryStamTax";

    /**
     * 收件人
     */
    public final static String ADDRESSEE = "addressee";

    /**
     * 基本险不计免赔
     */
    public final static String INSUREITEM_NCFBASICCLAUSE = "insureItem.ncfBasicClause";

    /**
     * 不计免赔险
     */
    public final static String INSUREITEM_NCFCLAUSE = "insureItem.ncfClause";

    /**
     * 商业险
     */
    public final static String INSUREITEM_COMMERCIALINS = "insureItem.commercialIns";

    /**
     * 涉水免除特约
     */
    public final static String INSUREITEM_WADINGEXCLUSIONCLA = "insureItem.wadingExclusionCla";

    /**
     * 附加车身划痕险不计免赔
     */
    public final static String INSUREITEM_NCFSCRATCHINS = "insureItem.ncfScratchIns";

    /**
     * 附加司机责任险不计免赔
     */
    public final static String INSUREITEM_NCFDRIVERINS = "insureItem.ncfDriverIns";

    /**
     * 随车行李物品损失保险
     */
    public final static String INSUREITEM_LOSSOFBAGGAGEINS = "insureItem.lossOfBaggageIns";

    /**
     * 交强险
     */
    public final static String INSUREITEM_VEHICLECOMPULSORYINS = "insureItem.vehicleCompulsoryIns";

    /**
     * 教练车特约
     */
    public final static String INSUREITEM_TRAINNIGCARCLA = "insureItem.trainnigCarCla";

    /**
     * 附加全车盗抢险不计免赔
     */
    public final static String INSUREITEM_NCFTHEFTINS = "insureItem.ncfTheftIns";

    /**
     * 附加涉水损失险不计免赔
     */
    public final static String INSUREITEM_NCFWADINGINS = "insureItem.ncfWadingIns";

    /**
     * 代理人
     */
    public final static String AGENT = "agent";

    /**
     * 附加第三者责任险不计免赔
     */
    public final static String INSUREITEM_NCFTHIRDPARTYINS = "insureItem.ncfThirdPartyIns";

    /**
     * 玻璃单独破碎险
     */
    public final static String INSUREITEM_GLASSINS = "insureItem.glassIns";

    /**
     * 附加车上人员责任险不计免赔
     */
    public final static String INSUREITEM_NCFDRIVERPASSENGERINS = "insureItem.ncfDriverPassengerIns";

    /**
     * 司机责任险
     */
    public final static String INSUREITEM_DRIVERINS = "insureItem.driverIns";

    /**
     * 多次事故免赔特约
     */
    public final static String INSUREITEM_ACCIDENTDEDUCTIBLESCLA = "insureItem.accidentDeductiblesCla";

    /**
     * 规则信息
     */
    public final static String RULEITEM = "ruleItem";

    /**
     * 车上货物责任险
     */
    public final static String INSUREITEM_GOODSONTRUCKINS = "insureItem.goodsOnTruckIns";

    /**
     * 附加新增设备损失险不计免赔
     */
    public final static String INSUREITEM_NCFNEWEQUIPMENTINS = "insureItem.ncfNewEquipmentIns";

    /**
     * 投保单信息
     */
    public final static String APPLICATION = "application";

    /**
     * 车船税
     */
    public final static String INSUREITEM_VEHICLETAX = "insureItem.vehicleTax";

    /**
     * 车碰车损失险
     */
    public final static String INSUREITEM_CARTOCARDAMAGEINS = "insureItem.carToCarDamageIns";

    /**
     * 车身划痕险
     */
    public final static String INSUREITEM_SCRATCHINS = "insureItem.scratchIns";

    /**
     * 可选免赔额特约
     */
    public final static String INSUREITEM_OPTIONALDEDUCTIBLESCLA = "insureItem.optionalDeductiblesCla";

    /**
     * 指定专修厂
     */
    public final static String INSUREITEM_SPECIFYINGPLANTCLA = "insureItem.specifyingPlantCla";

    /**
     * 车主
     */
    public final static String CAR_OWNER = "car.owner";

    /**
     * 附加乘客责任险不计免赔
     */
    public final static String INSUREITEM_NCFPASSENGERINS = "insureItem.ncfPassengerIns";

    /**
     * 投保人
     */
    public final static String PROPOSER = "proposer";

    /**
     * 商业险印花税
     */
    public final static String INSUREITEM_COMMERCIALSTAMTAX = "insureItem.commercialStamTax";

    /**
     * 新增设备损失险
     */
    public final static String INSUREITEM_NEWEQUIPMENTINS = "insureItem.newEquipmentIns";

    /**
     * 被保险人
     */
    public final static String INSURED = "insured";

    /**
     * 指定驾驶人
     */
    public final static String DRIVER = "driver";

    /**
     * 权益索赔人
     */
    public final static String BENEFICIARY = "beneficiary";

}
