package com.cheche365.bc.constants;

import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PlatformConstants {


    public static final HashMap<String, List<String>> INS_CODE_NAME_MAPPING;

    static {
        INS_CODE_NAME_MAPPING = new HashMap<>() {
            {
                put("2002", List.of("中国人寿财产保险公司", "中国人寿财产保险股份有限公司", "人寿", "GPIC"));
                put("2005", List.of("中国人民财产保险股份有限公司", "PICC"));
                put("2007", List.of("中国平安财产保险股份有限公司", "平安", "PAIC"));
                put("2011", List.of("中国太平洋财产保险股份有限公司", "CPIC"));
                put("2016", List.of("太平保险有限公司", "太平财产保险有限公司", "TPIC"));
                put("2019", List.of("阳光财产保险股份有限公司", "阳光财产", "SMIC", "YGBX"));
                put("2020", List.of("阳光农业相互保险公司", "阳光农业"));
                put("2021", List.of("中国大地财产保险股份有限公司", "大地", "CCIC"));
                put("2022", List.of("永诚财产保险股份有限公司", "永诚", "AICS"));
                put("2023", List.of("华泰财产保险股份有限公司", "华泰财产保险有限公司", "华泰", "HTIC"));
                put("2024", List.of("大家财产保险有限责任公司", "大家", "ABIC"));
                put("2026", List.of("安盛天平财产保险股份有限公司", "安盛天平", "天平汽车保险股份有限公司", "TPBX"));
                put("2027", List.of("中华联合财产保险公司", "中华联合财产保险股份有限公司", "中华联合", "CICP"));
                put("2028", List.of("中银保险有限公司", "中银", "BOCI"));
                put("2029", List.of("中意财产保险公司", "中意", "ZYIC"));
                put("2034", List.of("美亚财产保险股份有限公司", "美亚"));
                put("2038", List.of("友邦保险有限公司"));
                put("2039", List.of("易安财产保险股份有限公司", "易安"));
                put("2040", List.of("英大泰和财产保险股份有限公司", "英大", "YDCX"));
                put("2041", List.of("渤海财产保险股份有限公司", "渤海", "BPIC"));
                put("2042", List.of("都邦财产保险股份有限公司", "都邦", "DBIC"));
                put("2043", List.of("华安财产保险股份有限公司", "华安", "HAIC"));
                put("2044", List.of("众诚财产保险股份有限公司", "众诚汽车保险股份有限公司", "众诚", "UTIC"));
                put("2045", List.of("天安保险股份有限公司", "天安财产保险股份有限公司", "天安", "TAIC"));
                put("2046", List.of("永安财产保险股份有限公司", "永安", "YAIC"));
                put("2047", List.of("东海航运保险股份有限公司"));
                put("2049", List.of("安联保险公司", "安联财产保险中国有限公司", "安联", "AZCN"));
                put("2050", List.of("锦泰财产保险股份有限公司", "锦泰", "JTIC"));
                put("2051", List.of("长江财产保险股份有限公司", "长江", "CJCX"));
                put("2054", List.of("民安保险股份有限公司", "民安"));
                put("2055", List.of("珠峰保险股份有限公司", "珠峰"));
                put("2056", List.of("众安在线财产保险股份有限公司", "众安在线"));
                put("2058", List.of("安诚财产保险股份有限公司", "安诚", "ACIC"));
                put("2059", List.of("合众财产保险股份有限公司", "合众"));
                put("2060", List.of("安心财产保险有限责任公司", "安心"));
                put("2061", List.of("中路财产保险股份有限公司", "中路"));
                put("2062", List.of("恒邦财产保险股份有限公司", "恒邦"));
                put("2063", List.of("燕赵财产保险股份有限公司", "燕赵"));
                put("2064", List.of("华农财产保险股份有限公司", "华农", "HNIC"));
                put("2065", List.of("诚泰财产保险股份有限公司", "诚泰", "CHAC"));
                put("2066", List.of("亚太保险(中国)有限公司", "亚太财产保险有限公司", "亚太", "MACN"));
                put("2067", List.of("日本兴亚财产保险中国有限责任公司"));
                put("2068", List.of("安华农业保险股份有限公司", "安华", "AHIC"));
                put("2069", List.of("都帮财产保险股份有限公司", "都帮"));
                put("2071", List.of("史带财产保险股份有限公司"));
                put("2072", List.of("北部湾财产保险股份有限公司", "北部湾"));
                put("2074", List.of("太阳联合保险中国有限公司"));
                put("2075", List.of("三星财产保险中国有限公司"));
                put("2076", List.of("中煤财产保险股份有限公司", "中煤", "ZMBX"));
                put("2077", List.of("富德财产保险股份有限公司", "富德", "CRIC"));
                put("2078", List.of("中国出口信用保险公司"));
                put("2079", List.of("中航安盟财产保险有限公司", "安盟保险有限公司", "安盟", "AMIC"));
                put("2080", List.of("鑫安汽车保险股份有限公司", "鑫安", "XAIC"));
                put("2081", List.of("三井住友海上火灾保险", "三井住友海上火灾保险有限公司", "MSIC"));
                put("2082", List.of("上海安信农业保险股份有限公司", "上海安信", "AAIC"));
                put("2083", List.of("邱博保险中国有限公司"));
                put("2084", List.of("现代财产保险（中国）有限公司", "现代", "HYIC"));
                put("2085", List.of("利宝财险", "利宝保险有限公司", "利宝", "LIHI"));
                put("2086", List.of("长安责任保险股份有限公司", "长安", "CAIC"));
                put("2087", List.of("国泰财产保险有限责任公司", "国泰财险", "CATH"));
                put("2088", List.of("鼎和保险", "鼎和财险保险股份有限公司", "鼎和财产保险股份有限公司", "鼎和", "鼎和财险", "DHIC"));
                put("2089", List.of("法国安盟保险公司", "法国安盟"));
                put("2090", List.of("浙商财产保险股份有限公司", "浙商", "浙商财险", "ZSIC"));
                put("2091", List.of("苏黎世财产保险中国有限公司", "苏黎世财险"));
                put("2092", List.of("丘博保险中国有限公司", "丘博保险中国"));
                put("2093", List.of("东京海上日动火灾保险（中国）有限公司", "日本东京海上火灾保险株式会社", "东京海上火灾", "TMNF"));
                put("2094", List.of("日本财产保险中国有限公司", "日本财险中国"));
                put("2095", List.of("紫金财产保险公司", "紫金财产保险股份有限公司", "紫金", "紫金财险", "ZKIC"));
                put("2096", List.of("信达财产保险股份有限公司", "信达", "国任财险保险股份有限公司", "国任财险", "XDCX"));
                put("2097", List.of("安盛保险有限公司", "安盛保险"));
                put("2098", List.of("乐爱金财产保险中国有限公司", "乐爱金财险中国"));
                put("2099", List.of("美亚财产保险有限公司", "美亚财险", "AIGC"));
                put("2100", List.of("国元农业保险股份有限公司", "国元", "国元农业", "GYIC"));
                put("2101", List.of("富邦保险公司", "富邦", "富邦财产保险有限公司", "富邦财险", "FPIC"));
                put("2102", List.of("泰山财产保险股份有限公司", "泰山", "泰山财险", "TSBX"));
                put("2103", List.of("华海财产保险股份有限公司", "华海", "华海财险"));
                put("2104", List.of("海峡金桥财产保险股份有限公司", "海峡金桥"));
                put("2105", List.of("新疆前海联合财产保险股份有限公司", "新疆前海联合财产"));
                put("2106", List.of("安达保险有限公司", "安达财险"));
                put("2107", List.of("国任财产保险股份有限公司"));
                put("2108", List.of("建信财产保险有限公司", "建信财险"));
                put("2109", List.of("融盛财产保险股份有限公司", "融盛财险"));
                put("2110", List.of("太保安联健康保险股份有限公司"));
                put("3001", List.of("瑞士再保险公司"));
                put("3002", List.of("慕尼黑再保险公司"));
                put("3003", List.of("中国铁路财产保险自保有限公司", "中铁财产自保"));
                put("3004", List.of("瑞再企商保险有限公司", "瑞再企商保险"));
                put("4001", List.of("平安保险代理有限公司", "平安保险代理"));
                put("4002", List.of("泰康在线财产保险股份有限公司", "泰康在线"));
                put("4003", List.of("久隆财产保险有限公司", "久隆财产"));
                put("4004", List.of("众惠财产相互保险社", "众惠财产"));
                put("4005", List.of("和泰人寿保险股份有限公司", "和泰人寿"));
                put("4006", List.of("广州市公度保险公估有限公司"));
                put("5005", List.of("中汇国际保险经纪股份有限公司", "中汇国际保险"));
                put("5006", List.of("长城保险经纪有限公司"));
                put("5007", List.of("中原农业保险股份有限公司", "中原农业保险"));
                put("6001", List.of("中信保诚人寿保险有限公司"));
                put("6002", List.of("上海人寿保险股份有限公司"));
                put("6003", List.of("北大方正人寿保险有限公司", "北大方正人寿"));
                put("6004", List.of("安盛天平保险股份有限公司", "安盛天平保险"));
                put("6006", List.of("黄河财产保险股份有限公司", "黄河财产"));
                put("6007", List.of("国富人寿保险股份有限公司"));
            }
        };
    }

    public static String getInsCode(String nameOrCode) {
        if (StrUtil.isNotBlank(nameOrCode)) {
            for (Map.Entry<String, List<String>> entry : INS_CODE_NAME_MAPPING.entrySet()) {
                if (entry.getValue().contains(nameOrCode)) {
                    return entry.getKey();
                }
            }
        }
        return null;
    }
}
