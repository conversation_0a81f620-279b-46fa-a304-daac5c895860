package com.cheche365.bc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ClaimStatusEnum {

    REPORTED("reported", "已报案"),
    SURVEYED("surveyed", "已查勘"),
    ASSESSED_LOSS("assessedLoss", "已定损"),
    VERIFIED_LOSS("verifiedLoss", "已核损"),
    FINISH_ZERO("finishZero", "0结案"),
    FINISH("finish", "正常结案"),
    WAIVE_CLAIM("waiveClaim", "放弃索赔"),
    REFUSE_CLAIM("refuseClaim", "拒赔"),
    LOG_OUT("LogOut", "已注销");

    private final String code;
    private final String desc;
}
