package com.cheche365.bc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum InsCompanyEnum {

    /**
     * 保险公司
     */
    HULIANHUTONG(1111, "hulianhutong"),
    YOUBAOLIAN(1112, "youbaolian"),
    GPIC(2002, "guoshou"),
    PICC(2005, "renbao"),
    PAIC(2007, "pingan"),
    CPIC(2011, "taipingyang"),
    TPIC(2016, "taiping"),
    SMIC(2019, "yangguang"),
    CCIC(2021, "dadi"),
    AICS(2022, "yongcheng"),
    HTIC(2023, "huatai"),
    DAIC(2024, "dajia"),
    TPBX(2026, "tianping"),
    CICP(2027, "zhonghua"),
    BOIC(2028, "zhongyin"),
    YDCX(2040, "yingda"),
    BPIC(2041, "bohai"),
    DBIC(2042, "dubang"),
    HAIC(2043, "huaan"),
    UTIC(2044, "zhong<PERSON>"),
    TAIC(2045, "tianan"),
    YAIC(2046, "yongan"),
    JTIC(2050, "jintai"),
    ZFIC(2055, "zhufeng"),
    ZAIC(2056, "zhongan"),
    ACIC(2058, "ancheng"),
    AXIC(2060, "anxin"),
    ZLIC(2061, "zhonglu"),
    HBIC(2062, "hengbang"),
    HNIC(2064, "huanong"),
    CHAC(2065, "chengtai"),
    YTIC(2066, "yatai"),
    BWBX(2072, "beibuwan"),
    ZMBX(2076, "zhongmei"),
    CRIC(2077, "fude"),
    LIHI(2085, "libao"),
    CAIC(2086, "changan"),
    DHIC(2088, "dinghe"),
    ZKIC(2095, "zijin"),
    XDCX(2096, "xinda"),
    GRCX(2107, "guoren"),
    TKCX(4002, "taikang"),
    HZBX(2111, "hezhong"),
    QHIC(1078, "qianhai"),
    ZYBX(5007, "zhongyuan"),
    RSIC(2109, "rongsheng"),
    AHIC(2068, "anhua"),
    CJCX(2051, "changjiang"),
    HHBX(2103, "huahai"),
    AMIC(2079, "zhonghanganmeng"),
    YZIC(2063, "yanzhao"),
    ALIC(2049, "anlian"),
    XAIC(2080, "xinan"),
    ULIC(2059, "hezhong")
    ;

    @EnumValue
    private final int code;

    private final String text;

    InsCompanyEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public static InsCompanyEnum get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static InsCompanyEnum get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
